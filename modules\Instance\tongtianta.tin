#CLASS tongtianta KILL;
#CLASS tongtianta OPEN;

#NOP {快捷命令，直接前往通天塔并开始挑战};
#ALIAS {tt.go} {
    #VARIABLE {tongtianta} {
        #NOP {当前层数};
        {floor} {1};
        #NOP {固定为最高1000层};
        {max_floor} {1000};
        #NOP {通天塔是否在运行中};     
        {running} {0};   
        #NOP {开始时间戳};  
        {start_time} {0};
        #NOP {结束时间戳}; 
        {end_time} {0};
        #NOP {起始层数};
        {start_floor} {1};
        #NOP {上一层时间戳};
        {last_floor_time} {0}
    };
    #ECHO {<aff>正在前往通天塔...};
    tbbuy {da huandan} {$conf[ttt_dahuandan_num]} {
        gotodo {扬州城} {车马行} {            
            tongtianta;                            
            #DELAY {2} {                
                #DELAY {1} {
                    tt.ready;
                };
            };                      
        };
    };
};

#NOP {在热身室准备好后开始挑战};
#ALIAS {tt.ready} {
    #NOP {记录开始层数};
    #CLASS tongtianta_start_floor KILL;
    #CLASS tongtianta_start_floor OPEN;
    #ACTION {^你的通天塔当前层数：%d层。} {
        #VARIABLE {tongtianta[start_floor]} {%%1};
        #VARIABLE {tongtianta[floor]} {$tongtianta[start_floor]};            
        joblog {<aff>正在挑战通天塔，起始层数: $tongtianta[start_floor]。} {通天塔};
        #NOP {在不同层数范围自动使用不同的pfm};
        #IF {$tongtianta[floor] >= 1 && $tongtianta[floor] <= 799} {
            #ECHO {<aff>当前层数($tongtianta[floor])，使用初级pfm...};            
            createpfm {$conf[pfm][scene][ttt1]} {1};
            joblog {<aff>开始层数$tongtianta[floor]，使用初级pfm！} {通天塔};
            #CLASS tongtianta_start_floor KILL;                        
        }; 
        #ELSEIF {$tongtianta[floor] >= 800 && $tongtianta[floor] <= 999} {
            #ECHO {<aff>当前层数($tongtianta[floor])，使用中级pfm...};            
            createpfm {$conf[pfm][scene][ttt2]} {1};
            joblog {<aff>开始层数$tongtianta[floor]，使用中级pfm！} {通天塔}; 
            #CLASS tongtianta_start_floor KILL;                       
        };
        #ELSEIF {$tongtianta[floor] == 1000} {
            #ECHO {<aff>当前层数(1000)，使用最强pfm...};            
            createpfm {$conf[pfm][scene][ttt3]} {1};
            joblog {<aff>开始层数$tongtianta[floor]，使用最强pfm！} {通天塔};
            #CLASS tongtianta_start_floor KILL;                       
        };        
    };    
    #CLASS tongtianta_start_floor CLOSE;   
    #ECHO {<aff>准备开始挑战通天塔...};
    #NOP {记录开始时间};
    #VARIABLE {tongtianta[start_time]} {@now{}};               
    #DELAY {2} {
        up;
        jobtimes;
        tt.start;
    };
};

#ALIAS {tt.start} {
    #VARIABLE {tongtianta[last_floor_time]} {@now{}};
    #VARIABLE {tongtianta[running]} {1};        
    #VARIABLE {tongtianta[guard_index]} {0};    
    closewimpy;    
    tt.check;
};

#ALIAS {tt.stop} {
    #VARIABLE {tongtianta[running]} {0};
    #CLASS tongtianta_fight KILL;
};

#ALIAS {tt.check} {
    #IF {$tongtianta[running] == 0} {
        #RETURN;
    };
    
    #VARIABLE idle {0};
    #VARIABLE {workingflag} {2};
    #CLASS tongtianta_fight KILL;
    #CLASS tongtianta_fight OPEN;    
       
    #NOP {添加向上走的触发器};
    #ACTION {^你匆匆忙忙又向上走了一层！！！} {               
        joblog {<aff>当前通天塔第$tongtianta[floor]层！耗时: @elapsed{$tongtianta[last_floor_time]}秒。} {通天塔};
        #VARIABLE {tongtianta[last_floor_time]} {@now{}};        
        #ECHO {<aff>正在前往下一层...};          
        dohalt {
            tt.up;
        };      
    };
    
    #NOP {添加到达通天塔顶层的触发器};
    #ACTION {^你已走到了通天塔顶了，静下心来欣赏塔顶美景吧！！！} {
        #ECHO {<aff>恭喜！你已到达通天塔顶层！};
        #VARIABLE {tongtianta[floor]} {1000};       
        leave;        
        #ECHO {<aff>通天塔】副本已完成，停止挑战。};
    };

    #NOP {添加中途离开通天塔的触发器};
    #CLASS tongtianta_leave_time KILL;
    #CLASS tongtianta_leave_time OPEN;    
    #ACTION {^突然深邃的天空中传来一声娇媚：“欢迎早日回来，通天塔顶有无限美好的风光等着你”！} {
        #ECHO {<aff>你已离开通天塔，挑战已中断};
        #NOP {记录结束时间};
        #VARIABLE {tongtianta[end_time]} {@now{}};  
        #LOCAL {duration} {@eval{$tongtianta[end_time] - $tongtianta[start_time]}};
        #LOCAL {floors_climbed} {@eval{$tongtianta[floor] - $tongtianta[start_floor] + 1}};
        joblog {<aff>通天塔】挑战中断！从第$tongtianta[start_floor]层到第$tongtianta[floor]层，共爬升$floors_climbed层。} {通天塔};
        joblog {<aff>总用时: @timeFormatCN{$duration}，平均每层用时: @timeFormatCN{@eval{$duration / $floors_climbed}}。} {通天塔};
        tt.stop;
        dohalt {jobprepare};
        #CLASS tongtianta_leave_time KILL;
    };
    #CLASS tongtianta_leave_time CLOSE;
    
    #NOP {添加通天塔奖励触发器};
    #ACTION {^你在通天塔顶感悟成功，获得了%d实战经验！！！} {
        joblog {<aff>恭喜获得奖励：%%1实战经验！} {通天塔};
        #ACTION {^你得到了%d枚情怀币！} {            
            joblog {<aff>恭喜获得奖励：%%%1枚情怀币！} {通天塔};
        };
        #NOP {记录结束时间};
        #VARIABLE {tongtianta[end_time]} {@now{}};  
        #LOCAL {duration} {@eval{$tongtianta[end_time] - $tongtianta[start_time]}};
        #LOCAL {floors_climbed} {@eval{1000 - $tongtianta[start_floor] + 1}};
        joblog {<aff>通天塔挑战完成！从第$tongtianta[start_floor]层到第1000层，共爬升$floors_climbed层。} {通天塔};
        joblog {<aff>总用时: @timeFormatCN{$duration}，平均每层用时: @timeFormatCN{@eval{$duration / $floors_climbed}}。} {通天塔};
        tt.stop;         
        dohalt {jobprepare};
    };  
   
    
    #NOP {添加对"闪身离开"类型NPC的处理};
    #ACTION {^%*一个闪身就不见了。} {
        #VARIABLE {workingflag} {2};             
        dohalt {
            yun jingli;
            yun qi;
            hp;
            dohalt {
                tt.up;                
            };
        };       
    };

    #ACTION {^你想利用bug吗？} {
        #VARIABLE {idle} {-100};        
        openwimpy;        
        kill guard;
    };   

    #NOP {利用up来判断是否还有NPC，这里设置一个alias来实现UP主要是为了sta可以正常停止工作};
    #ALIAS {tt.up} {
        #CLASS tongtianta_up KILL;
        #CLASS tongtianta_up OPEN;
        #ACTION {^{设定环境变量：action \= \"checkup\"|你设定checkup为反馈信息}} {
            #CLASS tongtianta_up KILL;
            up;       
        };
        #CLASS tongtianta_up CLOSE;         
        echo {checkup};
    };

    #ACTION {^你的气力恢复了} {
        yun jingli;
    }; 
   
    #ACTION {^通天塔 第%d层} {        
        #ECHO {<aff>DEBUG: 正在处理层数信息...};
        #VARIABLE {tongtianta[floor]} {%%1};            
        #ECHO {<aff>DEBUG: 当前层数: $tongtianta[floor]};
        #ECHO {<aff>DEBUG: 已重置所有相关变量};         
        #NOP {直接使用1000作为最大层数};
        #IF {$tongtianta[floor] > 1000} {  
            tt.stop;
            #ECHO {<aff>已达到最大层数1000，停止通天塔副本};
            #RETURN;
        };        
        
        #NOP {在不同层数范围自动使用不同的pfm};
        #IF {$tongtianta[floor] >= 1 && $tongtianta[floor] <= 799} {
            #ECHO {<aff>当前层数($tongtianta[floor])，使用初级pfm...};            
            createpfm {$conf[pfm][scene][ttt1]} {1};
            joblog {<aff>开始层数$tongtianta[floor]，使用初级pfm！} {通天塔};                               
            }; 
        #ELSEIF {$tongtianta[floor] >= 800 && $tongtianta[floor] <= 999} {
            #ECHO {<aff>当前层数($tongtianta[floor])，使用中级pfm...};            
            createpfm {$conf[pfm][scene][ttt2]} {1};
            joblog {<aff>开始层数$tongtianta[floor]，使用中级pfm！} {通天塔};                                   
        };
        #ELSEIF {$tongtianta[floor] == 1000} {
            #ECHO {<aff>当前层数(1000)，使用最强pfm...};            
            createpfm {$conf[pfm][scene][ttt3]} {1};
            joblog {<aff>开始层数$tongtianta[floor]，使用最强pfm！} {通天塔};                                
        };        
    };   

    #ACTION {^你吃下一颗大还丹，觉得丹田处有暖流涌上，顿时伤势痊愈气血充盈。} {
        tt.ready;
    }; 
    
    #ACTION {^你只觉得头昏脑胀，眼前一黑，接着什么也不知道了……} {
        tt.stop;
        joblog {<aff>你已经死亡，停止通天塔副本} {通天塔};
    };

    #ACTION {^你的内力不够} {
        leave;
        joblog {<aff>内力不足，停止通天塔副本} {通天塔};
    };

    #TICKER {tt.check_status} {
        #IF {$hp[qi_per] < 60} {
            fudahuan;
            #ECHO {<aff>血量过低，吃药恢复};
        };
        
        #IF {$hp[neili] < 5000} {
            #IF {$hp[neili] < 3000} {
                #ECHO {<aff>内力严重不足，尝试恢复...};
                fudahuan;              
            };
            #ELSE {
                #ECHO {<aff>内力不足，吃药恢复};
                fudahuan;
            };
        };
    } {2};
    tt.up;
    openwimpy;        
    #CLASS tongtianta_fight CLOSE;
};

#NOP {显示帮助信息};
#ALIAS {tt.help} {
    #ECHO {<aff>通天塔副本帮助：};
    #ECHO {<aff>  tt.go     - 直接前往通天塔并开始挑战};    
    #ECHO {<aff>  tt.ready  - 在热身室设置好后开始挑战};
    #ECHO {<aff>  tt.start  - 开始/继续挑战};
    #ECHO {<aff>  tt.stop   - 停止挑战};    
    #ECHO {<aff>  tt.help   - 显示此帮助信息};
};

#CLASS tongtianta CLOSE;
#SHOWME {<fac>@padRight{{通天塔副本}{12}}<fac> <cfa>模块加载完毕<cfa>};