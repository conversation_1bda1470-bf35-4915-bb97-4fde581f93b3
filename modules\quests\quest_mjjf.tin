#NOP {苗家剑法,%1:后续指令};
#ALIAS {goquest_mjjf} {
  #VARIABLE {questmodule} {苗家剑法};
  gotonpc {苗人凤} {mjjf_askmiao {%1}}
};
#ALIAS {mjjf_askmiao} {
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你向苗人凤打听有关『苗家剑法』的消息。} {
    #CLASS questresponseclass OPEN;
    #ACTION {^你向苗人凤请教有关苗家剑法的奥妙。} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questsuccess {$questmodule};
      dohalt {%1}
    };
    #ACTION {^苗人凤说道：「不是刚学完吗，还是以后切磋吧} {
      #NOP {刚招待过玩家,等会再来};
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questupdate {$questmodule};
      dohalt {%1}
    };
    #ACTION {^苗人凤说道：「现在时间紧急，这剑法还是以后切磋吧} {
      #NOP {复仇篇开始,解药篇未结束};
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questupdate {$questmodule};
      dohalt {%1}
    };
    #ACTION {^苗人凤说道：「{今天先切磋到这里吧，明天吧|以你当前的经验恐怕还是难以领悟}} {
      #NOP {经验或时间不足,一般不会出现};
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questdelay {$questmodule} {} {10800};
      dohalt {%1}
    };
    #ACTION {^苗人凤说道：「这位%*武功未能返璞归真，只怕难以领悟苗家剑法的绝技} {
      #NOP {没撑住};
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questupdate {$questmodule};
      dohalt {%1}
    };
    #ACTION {^你拼命地回忆瞬间交手的一幕，回忆着刚才的招数，你开始认真思索着} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      #VARIABLE {idle} {0};
      pray pearl;
      mjjf_thinking {%1};
    };
    #CLASS questresponseclass CLOSE;
  };
  #CLASS questclass CLOSE;
  dohalt {
    ask miao renfeng about 苗家剑法
  }
};
#ALIAS {mjjf_thinking} {
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你于%*解开雪山飞狐苗家剑法篇} {
    #NOP {成功};
    #CLASS questclass KILL;
    questsuccess {$questmodule};
    %1
  };
  #ACTION {^你脑海里似乎抓住了什么，可是依然不是很明白} {
    #NOP {失败};
    #CLASS questclass KILL;
    questfail {$questmodule};
    %1
  };
  #CLASS questclass CLOSE;
  #VARIABLE {idle} {-30}
};