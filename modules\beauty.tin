#NOP {美颜模块};
#ALIAS {beautify} {
  #CLASS {beautify} {OPEN};
  #SUBSTITUTE {{┌|┎|└|┖|─|┬|┭|┰|┱|├|┞|┟|┠|┴|┵|┸|┹|┼|╁|╀|╂|┽|╃|╅|╉|╓|╙|╥|╟|╨|╫|╭|╰}} {%%1─};
  #SUBSTITUTE {{┏|┍|┗|┕|━|┳|┲|┯|┮|┣|┢|┡|┝|┻|┺|┷|┶|╋|╇|╈|┿|╊|╆|╄|┾}} {%%1━};
  #SUBSTITUTE {{╔|╦|╠|╬|╚|╩|═|╒|╤|╞|╪|╘|╧}} {%%1═};
  #SUBSTITUTE {{▁|▂|▃|▄|▅|▆|▇|█|▀|▔|┄|┅|┈|┉|—}} {%%1%%1};
  #SUBSTITUTE {{■|‖|▉|▊|▋|▌|▍|▎|▏}}                       {%%1 };
  #SUBSTITUTE {{※|☆|Ψ|★|□|◎|⊙|●|·|×|∶|∷|≈|√|⌒|Ω}}  {%%1 };
  #SUBSTITUTE {{▼|Ж|ξ|ф|∩|⊥|♀|∞|◇|◆|}} {%%1 };
  #SUBSTITUTE {{①|②|③|④|⑤|⑥|⑦|⑧|⑨|“|”}}                 {%%1 };
  #SUBSTITUTE {{│|┃|║|┆|┇|┊|┋|┤|┨|┫|╣|╢|╡}}     {%%1 };
  #SUBSTITUTE {{┐|┓|┒|╮|╗|┘|┛|┚|╯|╝}}              {%%1 };
  #SUBSTITUTE {{╲|╱|≤|≥|≮|≯|↙|↘|↗|↖|←|↑|→|↓|∨|∧}}  {%%1 };
  #SUBSTITUTE {} {  };
  #SUBSTITUTE {→ }{->};
  #CLASS {beautify} {CLOSE}
};
#ALIAS {beautify.off} {
  #CLASS {beautify} {KILL}
};
#FUNCTION {Beautify} {
  #LOCAL text {%1};
  #REPLACE text {{┌|┎|└|┖|─|┬|┭|┰|┱|├|┞|┟|┠|┴|┵|┸|┹|┼|╁|╀|╂|┽|╃|╅|╉|╓|╙|╥|╟|╨|╫|╭|╰}} {&1─};
  #REPLACE text {{┏|┍|┗|┕|━|┳|┲|┯|┮|┣|┢|┡|┝|┻|┺|┷|┶|╋|╇|╈|┿|╊|╆|╄|┾}} {&1━};
  #REPLACE text {{╔|╦|╠|╬|╚|╩|═|╒|╤|╞|╪|╘|╧}} {&1═};
  #REPLACE text {{▁|▂|▃|▄|▅|▆|▇|█|▀|▔|┄|┅|┈|┉|—}} {&1&1};
  #REPLACE text {{■|‖|▉|▊|▋|▌|▍|▎|▏}}                      {&1 };
  #REPLACE text {{※|☆|Ψ|★|□|◎|⊙|●|·|×|∶|∷|≈|√|⌒|Ω}}  {&1 };
  #REPLACE text {{▼|Ж|ξ|ф|∩|⊥|♀|∞|◇|◆|}} {&1 };
  #REPLACE text {{①|②|③|④|⑤|⑥|⑦|⑧|⑨|“|”}}                {&1 };
  #REPLACE text {{│|┃|║|┆|┇|┊|┋|┤|┨|┫|╣|╢|╡}}    {&1 };
  #REPLACE text {{┐|┓|┒|╮|╗|┘|┛|┚|╯|╝}}             {&1 };
  #REPLACE text {{╲|╱|≤|≥|≮|≯|↙|↘|↗|↖|←|↑|→|↓|∨|∧}} {&1 };
  #FORMAT result {%s} {$text}
};
beautify;