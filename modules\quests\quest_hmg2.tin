#NOP {蛤蟆功2,%1:后续指令};
#ALIAS {goquest_hmg2} {
  #VARIABLE {questmodule} {蛤蟆功二};
  gotodo {终南山} {灌木丛} {hmg2_wait {%1}}
};
#NOP {等待触发,%1:后续指令};
#ALIAS {hmg2_wait} {
  #VARIABLE {questts} {@now{}};
  #VARIABLE {okflag} {0};
  #VARIABLE {checkcount} {0};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你突然觉得心中烦恼之极，便坐在草地上发起呆来。} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^你猛然想起江湖世界变幻莫测，义父又不知道去了哪里，一阵伤感涌上心头。} {
    #NOP {失败后重新走流程，不全珍珠};
    #CLASS questclass KILL;
    questdelay {$questmodule} {0} {5};
    %1;
  };
  #ACTION {^你猛地跳了起来，两个起落，已窜进了树林深处。} {
    #CLASS questclass KILL;
    hmg2_meet {%1};
  };
  #ACTION {^{设定环境变量：action \= \"checkwait\"|你设定checkwait为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {$env[pray] == 0} {
      #IF {@carryqty{pearl} > 0} {
        pray pearl;
        i;
        #DELAY {2} {
          echo {checkwait};
        };
      };
      #ELSE {
        #CLASS questclass KILL;
        getpearl {goquest_hmg2 {%1}};
      };
    };
    #ELSEIF {$okflag == 0} {
      #MATH {checkcount} {$checkcount + 1};
      #IF {@elapsed{$questts} > 3600} {
        #CLASS questclass KILL;
        questdelay {$questmodule} {0} {5};
        %1;
      };
      #ELSEIF {$checkcount >= 5} {
        #VARIABLE {checkcount} {0};
        e;w;
        echo {checkwait};
      };
      #ELSE {
        #DELAY {2} {
          echo {checkwait};
        };
      };
    };
  };
  #CLASS questclass CLOSE;
  pray pearl;
  i;
  echo {checkwait};
};
#ALIAS {hmg2_meet} {
  #VARIABLE {okflag} {0};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkdone\"|你设定checkdone为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {$okflag == 0} {
      #DELAY {2} {
        echo {checkdone}
      };
    };
    #ELSE {
      questsuccess {$questmodule};
      dohalt {
        out;
        loc {quest_end {learnhmg {hmg_guide}}};
      };
    };
  };
  #ACTION {^欧阳锋说道：「看起来你的武功倒练得不错，再好也没有了。你演练(yanlian)蛤蟆功给我瞧瞧} {
    dohalt {yanlian};
  };
  #ACTION {^接着欧阳锋双掌乱舞，身子急转，以手行路，其快如风的冲出树林消失了。} {
    #VARIABLE {okflag} {1};
  };
  #CLASS questclass CLOSE;
  echo {checkdone}
};