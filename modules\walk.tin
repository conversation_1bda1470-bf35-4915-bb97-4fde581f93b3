#NOP {快速行走模块};
#NOP {当前城市};
#VARIABLE {city} {};
#NOP {最后一次访问的城市};
#VARIABLE {lastcity} {};
#NOP {当前房间名称};
#VARIABLE {room} {};
#NOP {当前房间ID};
#VARIABLE {roomid} {};
#NOP {房间出口s};
#LIST {roomexits} {clear};
#NOP {房间连通的房间名称};
#LIST {roomways} {clear};
#NOP {目标城市};
#VARIABLE {aimcity} {};
#NOP {目标房间名称};
#VARIABLE {aimroom} {};
#NOP {目标房间ID};
#VARIABLE {aimroomid} {};
#NOP {目标操作，到达目标位置时会执行};
#VARIABLE {aimdo} {};
#NOP {期望到达的房间,在checkroom中进行处理,用于记录将要到达的房间,用于处理被先手打晕或者打死后捡尸体的逻辑，已废弃};
#VARIABLE {expectcity} {};
#VARIABLE {expectroomid} {};
#VARIABLE {errorcount} {0};
#VARIABLE {afterlocdo} {NULL};
#VARIABLE {idle} {0};
#VARIABLE {lastcmd} {};
#NOP {发呆房间统计，如果连续在同一个房间发呆超过5次则重新启动脚本};
#VARIABLE {idleroom} {
  {name} {}
  {count} {0}
}
#NOP {快速行走狀況錯誤，如果连续在同一个房间多次错误，那么应特别处理};
#VARIABLE {errorroom} {
  {name} {}
  {count} {0}
};
#NOP {一次最大行走的步数，根据网络条件不同推荐设置15~20之间为宜。};
#VARIABLE {max_walk_steps} {10};
#NOP {快速行走分段行走最大延时};
#VARIABLE {max_walk_delay} {1};
#VARIABLE {min_walk_delay} {1};
#NOP {快速行走每指令延时,原来设置是0.1感觉太快了改成1};
#VARIABLE {timepercmd} {1};
#NOP {上一次执行的指令数，原来是0情怀设置为1};
#VARIABLE {prevcmdcount} {1};
#VARIABLE {targetnpc} {NULL};
#VARIABLE {walkstoppedts} {1};

#NOP {是否特殊行走步骤};
#FUNCTION isSpecialStep {
  #IF {@startWiths{{%1}{river_}} == 1} {#RETURN {1};};
  #ELSEIF {@startWiths{{%1}{matrix_}} == 1} {#RETURN {1};};
  #ELSEIF {@startWiths{{%1}{killnpc}} == 1} {#RETURN {1};};
  #ELSEIF {@startWiths{{%1}{night_}} == 1} {#RETURN {1};};
  #ELSE {#RETURN {0};};
};
#NOP {回复定位信息,查询后任务完成去扬州钱庄等待};
#LIST {common[responseusers]} {create} {};
#VARIABLE {env[waitresponse]} {0};
#ACTION {^%*(%*)告诉你：where are you} {
  reply 当前位于:$city $room;
  #IF {@contains{{common[responseusers]}{@lower{%2}}}} {
    #VARIABLE {env[waitresponse]} {1};
  };
};
#CLASS walkmodule OPEN;
#CLASS walkmodule KILL;
#NOP {强制优先触发文件读取错误信息};
#NOP {捕捉房间出口};
#VARIABLE {passedrooms} {0};
#ACTION {^%u - %*$} {
  #NOP {排除文件读取错误导致的误触发};
  #IF {"%2" != "FILE NOT FOUND."} {
    #VARIABLE {room} {%1};
    #LIST {roomexits} {clear};
    #IF {@len{%2} > 0} {
      #MATH {passedrooms} {$passedrooms + 1};
      #LIST {roomexits} {sort} {@formatExits{%2}}
    };
    #IF {@len{%2} == 0 && @eval{$conf[extrememode]} != 1} {
      #SWITCH {"%1"} {
        #CASE {"天阁斋"} {balance};
        #CASE {"天音阁"} {balance};
        #CASE {"威信钱庄"} {balance};
        #CASE {"墨玉斋"} {balance};
        #CASE {"大理钱庄"} {balance};
        #CASE {"通宝斋"} {balance};
        #CASE {"金华斋"} {balance};
        #CASE {"勒马斋"} {balance};
        #CASE {"聚宝斋"} {balance};
        #CASE {"宝龙斋"} {balance};
        #CASE {"万宝斋"} {balance};
      };
    };
  };
};
#NOP {捕捉房间出口};
#ACTION {这里{唯一|明显}的出口是 %*。} {
  #MATH {passedrooms} {$passedrooms + 1};
  #LIST {roomexits} {clear};
  #LIST {roomexits} {sort} {@formatExits{%2}};
};
#NOP {捕捉房间出口};
#ACTION {^    这里没有任何明显的出路。} {
  #MATH {passedrooms} {$passedrooms + 1};
  #LIST {roomexits} {clear};
};
#ACTION {^【你现在正处于%1】} {
  #NOP {别名处理};
  #VARIABLE {lastcity} {$city};
  #SWITCH {"%1"} {
    #CASE {"襄阳郊外"} {#VARIABLE {city} {襄阳城}};
    #CASE {"大理城东"} {#VARIABLE {city} {大理城}};
    #CASE {"大理城西"} {#VARIABLE {city} {大理城}};
    #CASE {"大理城南"} {#VARIABLE {city} {大理城}};
    #CASE {"大理王府"} {#VARIABLE {city} {大理城}};
    #CASE {"大理皇宫"} {#VARIABLE {city} {大理城}};
    #CASE {"玉虚观"} {#VARIABLE {city} {大理城}};
    #CASE {"伊犁城"} {#VARIABLE {city} {星宿海}};
    #CASE {"大草原"} {#VARIABLE {city} {回疆}};
    #CASE {"武当后山"} {#VARIABLE {city} {武当山}};
    #CASE {"柳宗镇"} {#VARIABLE {city} {襄阳城}};
    #CASE {"萧府"} {#VARIABLE {city} {黄河流域}};
    #CASE {"极乐世界"} {#VARIABLE {city} {扬州城}};
    #CASE {"中原"} {#VARIABLE {city} {成都城}};
    #DEFAULT {#VARIABLE {city} {%1};}
  };

  #LIST {roomways} {clear};
  #LIST {entrylines} {clear};
  #CLASS captureclass KILL;
  #CLASS captureclass OPEN;
  #ACTION {%*} {
    #LIST {entrylines} {add} {%%1};
  };
  #NOP {这里通过房间名描述行结束出口捕捉，普通房间5行，无出口房间1行};
  #ACTION {^%u -} {
    #CLASS captureclass KILL;
    #IF {@eval{$conf[extrememode]} != 1} {
      #SWITCH {"%%1"} {
        #CASE {"天阁斋"} {balance};
        #CASE {"天音阁"} {balance};
        #CASE {"威信钱庄"} {balance};
        #CASE {"墨玉斋"} {balance};
        #CASE {"大理钱庄"} {balance};
        #CASE {"通宝斋"} {balance};
        #CASE {"金华斋"} {balance};
        #CASE {"勒马斋"} {balance};
        #CASE {"聚宝斋"} {balance};
        #CASE {"宝龙斋"} {balance};
        #CASE {"万宝斋"} {balance};
      };
    };
    #CLASS captureclass CLOSE;
    #VARIABLE {room} {%%1};
    #NOP {处理出口数据，仅处理奇数行数据};
    #LOOP 1 &entrylines {i} {
      #IF {@eval{$i % 2} == 0} {
        #CONTINUE;
      };
      #IF {@len{$entrylines[+$i]} == 0} {
        #CONTINUE;
      };
      #LIST {templist} {create} {@fetchWays{@trim{$entrylines[+$i]}}};
      #NOP {普通房间3行,无出口房间1行才排除房间自身};
      #IF {(&entrylines[] == 5 && $i == 3) || &entrylines[] ==1} {
        #LOCAL {roomindex} {@contains{{templist}{$room}}};
        #IF {$roomindex > 0} {
          #LIST {templist} {delete} {$roomindex};
        };
      };
      #FOREACH {$templist[]} {w} {
        #LIST {roomways} {add} {$w};
      };
    };
  } {1};
};
#VARIABLE {adoerror} {0};
#ACTION {^系统在处理你的串连指令} {
  #IF {$adoerror == 0} {
    #VARIABLE {adoerror} {1};
    stopwalk;
    #DELAY {2} {
      logbuff {adoerror};
      dohalt {quit}
    };
  };
};
#VARIABLE {walkexception} {0};
#ACTION {^你从山上滚了下来，只觉得浑身无处不疼。} {
  #VARIABLE {walkexception} {1};
};
#NOP {护送时改出口关闭};
#ACTION {^那处强盗出没，比较危险，还是走大道吧} {
  #MAP AT {781} {#MAP UNLINK {e} {782}};
};
#NOP {未完成射雕流程时无法进入杨家里屋};
#ACTION {^没有主人的允许你不可以去人家家里} {
  stopwalk;
  jobclear;
  #DELAY {1} {doabort}
};
#NOP {雪山飞狐未到复仇篇进不去};
#ACTION {^苗家庄早已经封闭，无法进去} {
  stopwalk;
  jobclear;
  #DELAY {1} {doabort}
};
#NOP {天山异常需要丢掉铁链才能离开};
#ACTION {^你手上拿着铁链，怎么离开！} {
  drop tielian;
};
#ACTION {^由于你已经听了神雕侠杨过的指导，所以不用多想，信步就走出了林海} {
  #VARIABLE {env[yangpass]} {1};
  #NOP {简单粗暴};
  #IF {@contains{{unreachrooms[xuantie-jianfa]}{$aimroomid}} > 0} {
    stopwalk;
    #IF {"$currentjob" != ""} {
      #DELAY {1} {doabort}
    };
  };
};
#NOP {防止指令输入过多，延迟执行,%1为要执行的动作};
#ALIAS {runwait} {
  #MATH {runtime} {$prevcmdcount * $timepercmd};
  #IF {$runtime > $max_walk_delay} {#VARIABLE {runtime} {$max_walk_delay}};
  #IF {$runtime < $min_walk_delay} {#VARIABLE {runtime} {$min_walk_delay}};
  #IF {$prevcmdcount < 5} {
    #DELAY {0.1} {%1}    
  };
  #ELSEIF {$prevcmdcount < 10} {
    #DELAY {0.5} {%1}
  };
  #ELSE {
    #DELAY {$runtime} {%1}
  };
};
#NOP {发呆处理机制};
#VARIABLE {idle} {0};
#NOP {发呆检测时间(秒)};
#VARIABLE {idletick} {10};
#NOP {停止echo触发，临时中止触发};
#VARIABLE {interrupt} {0};
#NOP {停止echo触发的时间戳，默认5秒后复位};
#VARIABLE {interruptts} {0};
#NOP {脚本中止标识，脚本中止后不会进行发呆判定};
#VARIABLE {scriptpause} {0};
#NOP {脚本中止标识时间戳};
#VARIABLE {scriptpausets} {0};
#NOP {反馈指令发起的时间戳};
#VARIABLE {echots} {0};
#NOP {指令溢出先行走的步数};
#VARIABLE {overflowsteps} {0};
#VARIABLE {workingflag} {0};
#NOP {workingflag变量是可能的耗时操作标识变量如坐船、黑木崖做竹篓、打坐(激发低级特殊内容)、疗伤，其时间可能超过发呆检测时间};
#NOP {执行操作时变量置为1，结束时置为0。为防止意外，这里在超过一分钟时会自动重置该变量。};
#UNTICKER {idleticker};
#TICKER {idleticker} {
  #IF {$interrupt == 0 && $scriptpause == 0} {
    #MATH {idle} {$idle + 1};
    #LOCAL {threshold} {60};
    #IF {$workingflag > 1} {
    #LOCAL {threshold} {120};
    };
    #NOP {正在忙碌时不判定};
    #IF {$idle > $idletick && $workingflag == 0} {
      #VARIABLE {idle} {0};
      checkidle;
    };
    #ELSEIF {$idle >= $threshold} {
      #VARIABLE {workingflag} {0};
      checkidle;
    };
  };
  #ELSE {
    #IF {$interruptts == 0} {
      #VARIABLE {interruptts} {@now{}};
    };
    #ELSEIF {@elapsed{$interruptts} >= 5} {
      #VARIABLE {interrupt} {0};
      #VARIABLE {interruptts} {0};
    };
  };
  #NOP {echo指令的检查，一般echo指令不应超过3s};
  #FOREACH {*env[echo][]} {echoinfo} {
    #IF {"$env[echo][$echoinfo][timestamp]" == ""} {
      resonate {$echoinfo};
    };
    #ELSEIF {@nowms{} > $env[echo][$echoinfo][timestamp]} {
      #IF {"$env[echo][$echoinfo][cmds]" != ""} {
        $env[echo][$echoinfo][cmds]
      };
      resonate {$echoinfo};
      echo {$echoinfo}
    };
  };
} {1};
#NOP {快速行走};
#ALIAS {walk} {
  #VARIABLE {walkexception} {0};
  #VARIABLE {idle} {0};
  #VARIABLE {interrupt} {0};
  #VARIABLE {walkstoppedts} {0};
  #CLASS walkclass KILL;
  #CLASS boatclass KILL;
  #CLASS riverclass KILL;
  #CLASS matrixclass KILL;
  #CLASS fightclass KILL;
  #CLASS nightclass KILL;
  #CLASS fullclass KILL;
  #CLASS dzclass KILL;
  #class getbookclass kill;
  #CLASS staminaclass KILL;

  #IF {"$aimcity" == ""} {
    #SHOWME {<ffa>目标城市不明确};
  };
  #ELSEIF {"$aimroom" == ""} {
    #SHOWME {<ffa>目标房间不明确};
  };
  #ELSEIF {"$roomid" == "$aimroomid"} {
    $aimdo;
  };
  #ELSE {
    #LOCAL {tempaimroomid} {$aimroomid};
    #IF {"$conf[extrememode]" != "1"} {
      #IF {"$city" != "$aimcity"} {
        #NOP {按区域行走};
        #LOCAL {nextcity} {@getNextCity{{$city}{$aimcity}}};
        #IF {"$nextcity" != "$aimcity"} {
          #LOCAL {tempaimroomid} {$cities[$nextcity][cityroomid]};
        };
      };
    };
    #LOCAL {temppath} {@getWalkPath{{$roomid}{$tempaimroomid}}};
    #IF {$__DEBUG__ == 1} {
      #SHOWME {<ffa>路径为: $temppath};
    };
    #IF {"$temppath" == "" && "$roomid" != "$tempaimroomid"} {
      #NOP {起止不同但路径为空，要不解析问题，要不地图问题};
      dowalkerror
    };
    #ELSE {
      #LIST {fullpath} {clear};
      #LIST {fullpath} {create} {$temppath};
      #IF {"$roomid" != "1373" } {
        addcmd {halt};  
      };
      #NOP {几个特殊位置};
      #IF {"$room" != "广场空地"} {
        addcmd {yun qi};
        addcmd {yun jing};
        addcmd {yun jingli}
      };
      execute;
      runpath;
    };
  };
};
#NOP {执行快速行走};
#ALIAS {runpath} {
  #CLASS walkclass OPEN;
  #ACTION {^你太累了，休息一下再走吧} {
    #CLASS walkclass KILL;
    checkstamina;
  };
  #ACTION {^{设定环境变量：action \= \"nextstep\"|你设定nextstep为反馈信息}} {
    resonate {nextstep};
    #IF {$walkexception == 1} {
      loc {walk};
    };
    #ELSE {
      halt;
      runpath;
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkroom\"|你设定checkroom为反馈信息}} {
    resonate {checkroom};
    #CLASS walkclass KILL;
    loc {checkroom} {1};
  };
  #ACTION {^这个方向没有出路。} {
    #MATH {errorcount} {$errorcount + 1};
    #IF {$errorcount > 10} {
      #CLASS walkclass KILL;
      checkerror;
    };
  };
  #CLASS walkclass CLOSE;
  #NOP {执行快速行走路径,遇到过河,迷宫,拦路或夜间关门路径截断};
  #LIST {cmdqueue} {clear} {};
  #VARIABLE {prevcmdcount} {0};
  #LOCAL {specialflag} {0};
  #LOCAL {pl} {&fullpath[]};
  #IF {$pl == 0} {
    echo {checkroom} {2};
  };
  #ELSE {
    #LOOP {1} {$pl} {i} {
      #MATH {prevcmdcount} {$prevcmdcount + 1};
      #LOCAL {cmd} {$fullpath[+1]};
      #LIST {fullpath} {delete} {1};
      #LIST {cmdqueue} {add} {$cmd};
      #VARIABLE {lastcmd} {$cmd};
      #MATH {prevcmdcount} {$prevcmdcount+1};
      #IF {@isSpecialStep{$cmd} == 1} {
        #LOCAL {specialflag} {1};
        #BREAK;
      };
      #ELSEIF {$i == $max_walk_steps} {
        #LOCAL {tempflag} {1};
        #BREAK;
      };
    };
    #IF {$specialflag == 1} {
      #LOCAL {specialstep} {$cmdqueue[+&cmdqueue[]]};
      #LIST {cmdqueue} {delete} {&cmdqueue[]};
      execute;
      $specialstep;
    };
    #ELSE {
      execute;
    };
    #MATH {overflowsteps} {$overflowsteps + $prevcmdcount};
    #IF {$specialflag != 1} {
      #IF {&fullpath[] == 0} {
        runwait {
          echo {checkroom} {2};
        };
      };
      #ELSE {
        runwait {
          echo {nextstep} {2};
        };
      };
    };
  };
};
#NOP {检查是否到达目的地};
#ALIAS {checkroom} {
  #VARIABLE {expectcity} {$aimcity};
  #VARIABLE {expectroomid} {$aimroomid};
  #IF {"$city" == "$aimcity" && "$room" == "$aimroom"} {
    #CLASS jobwalkclass KILL;
    #VARIABLE {errorcount} {0};
    #VARIABLE {roomid} {$aimroomid};
    #MAP goto {$roomid};
    #VARIABLE {errorroom} {
      {room} {}
      {count} {0}
    };
    #IF {$__DEBUG__ == 1} {
      #SHOWME {<ffa>执行$aimdo};
    };
    #IF {"$idleroom[name]" != "" && "$idleroom[name]" != "$room"} {
      #VARIABLE {idleroom} {
        {name} {}
        {count} {0}
      };
    };
    $aimdo
  };
  #ELSE {
    walk
  };
};
#NOP {快速行走时错误处理，一般为路径为空的情况};
#VARIABLE {walkerrorts} {0};
#ALIAS {dowalkerror} {
  #NOP {打印变量};
  #SHOWME {<fff>任务：$currentjob};
  #SHOWME {<fff>地点：@getJobLocation{}};
  #SHOWME {<fff>人员：@getJobNpc{}};
  #SHOWME {<fff>路线：$roomid -> $aimroomid};
  #SHOWME {<fff>操作：$aimdo};
  #SHOWME {<fff>房间：@join{jobroomlist}};
  stopwalk;
  jobclear;
  #NOP {延迟两秒记录日志};
  #DELAY {2} {
    #IF {@elapsed{walkerrorts} > 3600} {
      BLE {walkerrorts} {@now{}};
      logbuff {walkerror};
    };
    #IF {"$currentjob" != ""} {
      doabort
    };
    #ELSE {
      jobcheck
    };
  }
};
#NOP {发呆处理流程,如果在一个房间短期内数次触发发呆，则说明已经进入了某种错误逻辑,记录日志并重启};
#ALIAS {checkidle} {
  #IF {"$room" == "$idleroom[name]"} {
    #VARIABLE {idleroom[count]} {@eval{$idleroom[count] + 1}};
  };
  #ELSE {
    #VARIABLE {idleroom} {
      {name} {$room}
      {count} {0}
    };
  };
  #IF {$idleroom[count] >= 4} {
    loc {doabort};
  };
  #ELSE {
    loc {walk};
  };
};
#ALIAS {checkstamina} {
  #CLASS staminaclass KILL;
  #CLASS staminaclass OPEN;
  #ACTION {^设定环境变量：action \= \"checkstamina\"} {
    #VARIABLE {idle} {0};
    #IF {$hp[jingli] > 500 || $hp[jingli] >= $hp[jingli_max]} {
      #CLASS staminaclass KILL;
      loc {walk};
    };
    #ELSE {
      #DELAY {6} {
        hp;
        l;
        set action checkstamina;
      };
    };
  };
  #CLASS staminaclass CLOSE;
  stopwalk;
  #DELAY {6} {
    fu neixi wan;
    fu chuanbei wan;
    i;
    yun jingli;
    hp;
    set action checkstamina;
  };
};
#NOP {错误处理,这里加入一些无地图房间的渡船的处理};
#VARIABLE {yttlerr} {0};
#ALIAS {checkerror} {
  stopwalk;
  #VARIABLE {laststep} {};
  #VARIABLE {errorcount} {0};
  #LIST {boatroom} {create} {渡船;小舟;长江渡船;黄河渡船;木筏;竹篓};
  #CLASS walkerrorclass OPEN;
  #ACTION {^只听院内传来一个浑厚的声音说道} {
    #CLASS walkerrorclass KILL;
    #DELAY {1} {
      loc {walk}
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkerror\"|你设定checkerror为反馈信息}} {
    #VARIABLE {env[echo]} {};
    #VARIABLE {idle} {0};
    #IF {@contains{{boatroom}{$room}} > 0} {
      #IF {@contains{{roomexits}{out}} > 0} {
        #CLASS walkerrorclass KILL;
        out;
        loc {
          walk
        };
      };
      #ELSE {
        #DELAY {2} {
          look;
          set action checkerror
        }
      };
    };
    #ELSEIF {"$city" == "桃源县" && "$room" == "瀑布中"} {
      #CLASS walkerrorclass KILL;
      dohalt {
        tiao anbian;
        loc {walk};
      };
    };
    #ELSEIF {"$room" == "铁舟上"} {
      wwp {tie jiang};
      hua boat;
      tiao shandong;
      #DELAY {1} {
        loc {walk};
      };
    };
    #ELSEIF {"$room" == "小木筏"} {
      hua mufa;
      #DELAY {1} {
        loc {walk};
      };
    };
    #ELSEIF {"$room" == "九老洞"} {
      #CLASS walkerrorclass KILL;
      loc {walk}
    };
    #ELSEIF {"$room" == "海船"} {
      #CLASS walkerrorclass KILL;
      loc {walk}
    };
    #ELSEIF {"$room" == "树林深处"} {
      #CLASS walkerrorclass KILL;
      loc {walk}
    };
    #ELSEIF {"$room" == "吐谷浑伏俟城"} {
      #CLASS walkerrorclass KILL;
      loc {walk}
    };
    #ELSEIF {"$room" == "采矿场入口"} {
      #VARIABLE {city} {扬州城};
      #VARIABLE {roomid} {815};
      walk
    };
    #ELSEIF {"$room" == "高山脚下"} {
      #VARIABLE {成都城} {扬州城};
      #VARIABLE {roomid} {1021};
    };
    #ELSEIF {"$room" == "十偃镇"} {
      #CLASS walkerrorclass KILL;
      #IF {$yttlerr == 0} {
        #VARIABLE {yttlerr} {1};
        logbuff {yttlerr}
      };
      loc {yttlg_saveyu {jobprepare} {1}}
    };
    #ELSE {
      #CLASS walkerrorclass KILL;
      #IF {"$errorroom[room]" == "$room"} {
        #VARIABLE {errorroom[count]} {@eval{$errorroom[count] + 1}};
      };
      #ELSE {
        #VARIABLE {errorroom} {
          {room} {$room}
          {count} {1}
        };
      };
      #LOCAL {locflag} {};
      #IF {$errorroom[count] >= 3} {
        #LOCAL {locflag} {1};
        #VARIABLE {errorroom} {
          {room} {}
          {count} {0}
        };
      };
      #LOCAL {locdo} {%1};
      #IF {"$locdo" == ""} {
        #LOCAL {locdo} {walk};
      };
      checklocation {$locdo} {$locflag}
    };
  };
  #CLASS walkerrorclass CLOSE;
  #VARIABLE {city} {};
  #DELAY {1} {loc {echo {checkerror}} {2}}
};
#NOP {定位位置,%1:后续的指令，如为空则执行walk,%2:定位标识，非空必须走到唯一房间};
#ALIAS {checklocation} {
  #LOCAL {locateok} {1};
  #IF {"$city" == ""} {
    #LOCAL {locateok} {2};
    #IF {"$room" == "采矿场入口"} {
      #VARIABLE {roomid} {815};
      #LOCAL {locateok} {3};
    };
    #IF {"$room" == "高山脚下"} {
      #VARIABLE {成都城} {扬州城};
      #VARIABLE {roomid} {1021};
      #LOCAL {locateok} {3};
    };
  };
  #ELSEIF {"%2" != "" && @isUniqueRoom{{$city}{$room}} == 0} {
    #LOCAL {locateok} {2};
  };
  #IF {$locateok == 1} {
    #IF {"%1" != ""} {
      %1
    };
    #ELSE {
      loc {walk}
    };
  };
  #ELSEIF {$locateok == 3} {
    #IF {"%1" != ""} {
      %1
    };
    #ELSE {
      walk
    };
  };
  #ELSE {
    #IF {"$laststep" != ""} {
      #LOCAL {tempindex} {@contains{{roomexits}{@reverseDir{$laststep}}}};
      #IF {$tempindex > 0} {
        #LIST {roomexits} {delete} {$tempindex};
      };
    };
    #LOCAL {tempindex} {@rnd{{1}{&roomexits[]}}};
    #VARIABLE {laststep} {$roomexits[+$tempindex]};
    $laststep;
    #DELAY {0.5} {loc {%1}}
  };
};
#NOP {停止所有触发,%1:非空会一直停下来};
#ALIAS {sta} {
  #CLASS serviceclass KILL;
  #CLASS haltclass KILL;
  stopwalk;
  jobclear;
  commonclear;
  kungfuclear;
  #UNTICKER {dzticker};
  #VARIABLE {interrupt} {1};
  #UNTICKER {tickerassert};
  #IF {"%1" != ""} {
    #VARIABLE {interrupt} {1};
    #VARIABLE {interruptts} {@now{}};
    #VARIABLE {scriptpause} {1};
    #VARIABLE {scriptpausets} {@now{}};
  };
  #VARIABLE {idle} {-300};
};
#NOP {停止脚本中止，并执行walk};
#ALIAS {rest} {
  #IF {$scriptpause == 1} {
    #VARIABLE {interrupt} {0};
    #VARIABLE {interruptts} {0};
    #VARIABLE {scriptpause} {0};
    #VARIABLE {scriptpausets} {0};
    #SHOWME {<faa>脚本已从中止中恢复，手动输入指令开始。};
  };
};
#NOP {停止行走,关闭相关触发器类};
#ALIAS {stopwalk} {
  #CLASS walkclass KILL;
  #CLASS walkerrorclass KILL;
  #CLASS gpsclass KILL;
  #CLASS matrixclass KILL;
  #CLASS riverclass KILL;
  #CLASS boatclass KILL;
  #CLASS nightclass KILL;
  #CLASS haltclass KILL;
  #CLASS yidengclass KILL;
  #CLASS killstopclass KILL;
  #VARIABLE {env[echo]} {};
  #VARIABLE {walkstoppedts} {@now{}};
};
#NOP {停止行走};
#ALIAS {stop} {
  stopwalk
};
#NOP {发起定位，%1:后续指令，%2:定位标识,1-快速行走使用，如区域未名则强制认同为目标城市,2-checkerror专用，city为空时不会重复触发};
#ALIAS {loc} {
  #CLASS gpsclass KILL;
  #CLASS gpsclass OPEN;
  #ACTION {^{设定环境变量：action \= \"locate\"|你设定locate为反馈信息}} {
    #CLASS gpsclass KILL;
    resonate {locate};
    #LOCAL {errorflag} {0};
    #NOP {快速行走遍历房间时，可能目标房间是不带区域的。这里如果是不带区域且房间名称与目标房间名称相同就默认为目标区域};
    #IF {"%2" == "1" && "$city" == "" && "$room" == "$aimroom"} {
      #VARIABLE {city} {$aimcity};
    };
    #IF {"$city" == ""} {
      #LOCAL {errorflag} {1};
    };
    #ELSE {
      #LOCAL {tempid} {@getRoomId{{$city}{$room}{roomexits}{roomways}}};
      #IF {$tempid > 0 } {
        #VARIABLE {roomid} {$tempid};
        #MAP goto {$roomid};
      };
      #ELSE {
        #VARIABLE {roomid} {-1};
        #LOCAL {errorflag} {1};
      };
    };
    #IF {"%2" == "2"} {
      %1
    };
    #ELSEIF {$roomid == -1} {
      checkerror
    };
    #ELSEIF {$errorflag == 1} {
      checklocation {%1}
    };
    #ELSE {
      #VARIABLE {roomid} {$tempid};
      #VARIABLE {city} {@getRoomInfo{{$roomid}{ROOMAREA}}};
      #IF {$__DEBUG__ == 1} {
        #SHOWME {<ffa>定位后准备执行%1};
      };
      %1
    };
  };
  #CLASS gpsclass CLOSE;
  #VARIABLE {city} {};
  ensure {look} {locate}
};
#NOP {杀拦路NPC,%1:npcid};
#ALIAS {killnpc} {
  #VARIABLE {passexp} {300000};
  #VARIABLE {performexp} {10000000};
  #VARIABLE {passflag} {0};
  #VARIABLE {killflag} {0};
  #VARIABLE {passaction} {};
  #VARIABLE {targetnpc} {NULL};
  #CLASS killstopclass open;
  #ACTION {^你要对谁做这个动作？} {
    #CLASS killstopclass kill;
    #IF {$killflag == 1} {
      #NOP {如果杀过人且使用了pfm，那么等一下cd};
      #IF {$hp[exp] < $performexp} {
        checkperformcd {ensure {openwimpy} {nextstep}}
      };
      #ELSE {
        dohalt {ensure {openwimpy} {nextstep}}
      };
    };
    #ELSE {
      #DELAY {0.5} {dohalt {ensure {openwimpy} {nextstep}}}
    };
  } {1};
  #ACTION {^你双手抱拳，对%%*作了个揖道：} {
    #VARIABLE {targetnpc} {%%1};
    #NOP {默认未指定的拦路NPC参数};
    #IF {"$common[blocker][%1]" != ""} {
      #VARIABLE {passexp} {$common[blocker][%1][passexp]};
      #VARIABLE {performexp} {$common[blocker][%1][performexp]};
    };
    #NOP {几个门派特殊情况处理};
    #IF {"$hp[party]" == "昆仑派" && "%1" == "xi huazi"} {
      #VARIABLE {passflag} {1};
    };
    #ELSEIF {"$hp[party]" == "铁掌帮" && ("%1" == "hong xiaotian" || "%1" == "huang lingtian" || "%1" == "ling zhentian")} {
      #VARIABLE {passflag} {1};
    };
    #ELSEIF {"$hp[party]" == "嵩山派" && "%1" == "ding mian"} {
      #VARIABLE {passflag} {1};
    };
    #ELSEIF {"$hp[party]" == "武当派" && ("%1" == "zhang songxi" || "%1" == "yu lianzhou" || "%1" == "yin liting")} {
      #VARIABLE {passflag} {1};
    };
    #ELSEIF {"$hp[party]" == "姑苏慕容" && "%1" == "a bi"} {
      #VARIABLE {passflag} {1};
    };
    #ELSEIF {"$hp[party]" == "大轮寺" && ("%1" == "hu bayin" || "%1" == "lama")} {
      #VARIABLE {passflag} {1};
    };
    #ELSEIF {"$hp[party]" == "明教" && ("%1" == "yin wushou" || "%1" == "yang xiao" || "%1" == "fan yao")} {
      #VARIABLE {passflag} {1};
    };
    #ELSEIF {"$hp[party]" == "星宿派" && "%1" == "caihua zi"} {
      #VARIABLE {passflag} {1};
      #VARIABLE {passaction} {give 1 silver to caihua zi};
    };
    #ELSEIF {"$hp[party]" == "星宿派" && ("%1" == "chuchen zi" || "%1" == "shihou zi")} {
      #VARIABLE {passflag} {1};
    };
    #ELSEIF {"$hp[party]" == "峨嵋派" && "%1" == "jingfeng shitai"} {
      #VARIABLE {passflag} {1};
    };
    #ELSEIF {"$hp[party]" == "少林派" && "%1" == "xuansheng dashi"} {
      #VARIABLE {passflag} {1};
    };
    #ELSEIF {"$hp[shen]" == "戾气" && "%1" == "ding mian"} {
      #VARIABLE {passflag} {1};
    };
    #ELSEIF {"$hp[party]" == "少林派" && "$hp[master][name]" == "无名老僧" && "%1" == "murong bo"} {
      #VARIABLE {passflag} {1};
    };
    #ELSEIF {"%1" == "dingmian" && "$hp[shen]" == "戾气"} {
      #VARIABLE {passflag} {1};
    };
    #ELSEIF {"%2" != ""} {
      #LIST {passpartylist} {create} {%2};
      #IF {@contains{{passpartylist}{$hp[party]}} > 0} {
        #VARIABLE {passflag} {1};
      };
    };
    #ELSEIF {"%3" != ""} {
      #LOCAL {expr} {%3};
      #IF {$expr} {
        #VARIABLE {passflag} {1};
      };
    };
    #IF {$passflag == 1} {
      #CLASS killstopclass kill;
      #IF {"$passaction" != ""} {
        $passaction
      };
      #DELAY {0.5} {
        set action nextstep
      };
    };
    #ELSEIF {$hp[exp] < $passexp} {
      #CLASS killstopclass kill;
      #NOP {扔掉锦盒，不然死循环};
      #IF {@carryqty{jin he} > 0} {
        drop jin he
      };
      #DELAY {0.5} {
        loc {doabort}
      };
    };
    #ELSE {
      #IF {$hp[exp] < $performexp} {
        openwimpy;
        createpfm {} {1};
        startfight;
      };
      #ELSE {
        closewimpy
      };
      yield no;
      kill %1;
    };
  } {1};
  #ACTION {^这里没有这个人。} {
    #CLASS killstopclass kill;
    runwait {ensure {openwimpy} {nextstep}};
  } {1};
  #ACTION {^$targetnpc神志迷糊，脚下一个不稳，倒在地上昏了过去。} {
    kill %1;
  };
  #ACTION {^{巨蟒|毒蟒}抽搐了几下，身体缩在一起，死了} {
    #VARIABLE {killflag} {1};
    stopfight;
    get gold from corpse;
    get silver from corpse;
    loc {#SEND {hi %1}};
  };
  #ACTION {^$targetnpc「啪」的一声倒在地上，挣扎着抽动了几下就死了。} {
    #VARIABLE {killflag} {1};
    stopfight;
    get gold from corpse;
    get silver from corpse;
    #SEND {hi %1}
  };
  #ACTION {^神雕悲鸣数声，足步迅捷异常，行走疾如奔马往深谷中走去} {
    #VARIABLE {killflag} {1};
    loc {#SEND {hi %1}};
  };
  #CLASS killstopclass close;
  time;
  hi %1
};
#NOP {去城市};
#ALIAS {gotocity} {
  #IF {"%1" == ""} {
    #SHOWME {<ffa>缺少目标城市};
  };
  #ELSE {
    #VARIABLE {aimcity} {%1};
    #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
    #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
    walk;
  };
};
#NOP {去房间，优先在当前城市搜索，如仅存在于一个城市则去第一个，否则中止,%2:做什么};
#ALIAS {gotoroom} {
  #IF {"%1" == ""} {
    #SHOWME {<ffa>缺少目标房间};
  };
  #ELSE {
    #LOCAL {temprooms} {};
    #MAP list {%1} {roomarea} {$city} {VARIABLE} {temprooms};
    #IF {&temprooms[] == 0} {
      #MAP list {%1} {VARIABLE} {temprooms};
      #IF {&temprooms[] == 0} {
        #SHOWME {<faa>未找到房间%1};
      };
      #ELSE {
        #FOREACH {*temprooms[]} {r} {
          #LOCAL {tempcities[@getRoomInfo{{$r}{ROOMAREA}}]} {1};
        };
        #IF {&tempcities[] > 1} {
          #SHOWME {<faa>房间%1存在于*tempcities[]中，请细化条件。};
        };
        #ELSE {
          #VARIABLE {aimroom} {@getRoomInfo{{*temprooms[+1]}{ROOMNAME}}};
          #VARIABLE {aimroomid} {*temprooms[+1]};
          #VARIABLE {aimcity} {*tempcities[+1]};
          #VARIABLE {aimdo} {%2};
          walk;
        };
      };
    };
    #ELSE {
      #VARIABLE {aimroom} {@getRoomInfo{{*temprooms[+1]}{ROOMNAME}}};
      #VARIABLE {aimroomid} {*temprooms[+1]};
      #VARIABLE {aimcity} {@getRoomInfo{{$aimroomid}{ROOMAREA}}};
      #VARIABLE {aimdo} {%2};
      walk;
    };
  };
};
#NOP {去NPC,%1npc名字,%2做什么};
#ALIAS {gotonpc} {
  #IF {"%1" == ""} {
    #SHOWME {<ffa>缺少目标NPC};
  };
  #ELSE {
    #LOCAL {location} {$mapnpcs[%1]};
    #IF {"$location" == ""} {
      #SHOWME {<faa>未找到NPC %1};
    };
    #ELSE {
      #VARIABLE {aimcity} {$location[city]};
      #VARIABLE {aimroom} {$location[room]};
      #VARIABLE {aimroomid} {$location[roomid]};
      #VARIABLE {aimdo} {%2};
      walk;
    };
  };
};
#NOP {去做某件事，%1:城市，%2:房间，%3:动作，%4};
#ALIAS {gotodo} {
  #IF {"%1" == ""} {
    #SHOWME {<ffa>缺少目标城市};
  };
  #ELSEIF {"%2" == ""} {
    #SHOWME {<ffa>缺少目标房间};
  };
  #LOCAL {temproomlist} {@findRooms{{%1}{%2}}};
  #IF {&temproomlist[] == 0} {
    #SHOWME {<faa>在%1中未检索到房间%2};
  };
  #ELSE {
    #VARIABLE {aimcity} {%1};
    #VARIABLE {aimroomid} {$temproomlist[+1]};
    #IF {@isNumber{%2}} {
      #VARIABLE {aimroom} {@getRoomInfo{{$aimroomid}{ROOMNAME}}};
    };
    #ELSE {
      #VARIABLE {aimroom} {%2};
    };
    #VARIABLE {aimdo} {%3};
    walk
  };
};
#NOP {去做某件事,%1城市,%2房间,%3动作,保留现场};
#ALIAS {gotofor} {
  #IF {"%1" == ""} {
    #SHOWME {<ffa>缺少目标城市};
  };
  #ELSEIF {"%2" == ""} {
    #SHOWME {<ffa>缺少目标房间};
  };
  #VARIABLE {aimbackup} {
    {city}{$aimcity}
    {room}{$aimroom}
    {roomid}{$aimroomid}
    {do}{$aimdo}
  };
  gotodo {%1} {%2} {%3}
};
#NOP {从备份的目标中恢复行走};
#ALIAS {restorewalk} {
  #SHOWME {$aimbackup};
  #VARIABLE {aimcity} {$aimbackup[city]};
  #VARIABLE {aimroom} {$aimbackup[room]};
  #VARIABLE {aimroomid} {$aimbackup[roomid]};
  #VARIABLE {aimdo} {$aimbackup[do]};
  #VARIABLE {aimbackup} {};
  walk
};
#NOP {去师门,%1:后续指令};
#ALIAS {gomaster} {
  #IF {"$mapnpcs[$hp[master][name]]" == ""} {
		#SHOWME {<faa>未找到师傅资料};
	};
  #ELSE {
    gotonpc {$hp[master][name]} {%1};
  };
};
#NOP {初始化加载个性化地图};
#ALIAS {setuppersonalmap} {
  #IF {"$hp[sex]" == "n"} {
    #NOP {割了的人不能进丽春院及内部房间};
    #MAP SET {roomdata} {l} {494};
    #MAP SET {roomdata} {l} {495};
    #MAP SET {roomdata} {l} {496};
  };
  #IF {"$hp[party]" == "姑苏慕容" || "$hp[party]" == "普通百姓"} {
    #NOP {开启慕容地道};
    #MAP AT {1964} {#MAP LINK {zuan didao} {2981}};
    #MAP AT {1915} {#MAP LINK {zuan didao} {2981}};
    #MAP AT {1870} {#MAP LINK {zuan didao} {2981}};
    #MAP AT {293} {#MAP LINK {push 桥栏;d} {2981}};
  };
  #IF {"$questlist[独孤九剑][done]" == "YES" || "$hp[master][name]" == "风清扬"} {
    #NOP {开启夹山壁出口};
    #MAP AT {2254} {#MAP LINK {river_sgy_wait} {3724}};
    #MAP AT {2254} {#MAP UNLINK {river_sgy_wait} {2255}};

    #NOP {忽略思过崖洞口房间，因为到达不了};
    #MAP AT {2255} {#MAP SET {ROOMDATA} {n}}
  };
  #ELSE {
    #NOP {开启思过崖洞口出口};
    #MAP AT {2254} {#MAP UNLINK {river_sgy_wait} {3724}};
    #MAP AT {2254} {#MAP LINK {river_sgy_wait} {2255}};
  };
  #IF {"$hp[master][name]" == "杨过" || "$hp[master][name]" == "小龙女"} {
		#MAP AT 3319 {#MAP LINK {xiao} {3320}};
		#MAP AT 3320 {#MAP LINK {xiao} {3319}};
    #MAP AT 3346 {#MAP UNLINK {river_jqg_shuitan_qian {up}}};
    #MAP AT 3347 {#MAP UNLINK {river_jqg_shuitan_qian {down}}}
	};
  #ELSE {
    #MAP AT 3319 {#MAP UNLINK {xiao} {3320}};
		#MAP AT 3320 {#MAP UNLINK {xiao} {3319}};
    #MAP AT 3346 {#MAP LINK {river_jqg_shuitan_qian {up}}};
    #MAP AT 3347 {#MAP LINK {river_jqg_shuitan_qian {down}}}
  };

  #NOP {大渡口路径问题};
  #NOP {1404->1011 983可保证从嘉峪关西门往西走捷径};
};
#NOP {各区域ALIAS};
#ALIAS {bts} {
  #VARIABLE {aimcity} {白驼山};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {cz} {
  #VARIABLE {aimcity} {沧州城};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {ca} {
  #VARIABLE {aimcity} {长安城};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {clb} {
  #VARIABLE {aimcity} {长乐帮};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {cd} {
  #VARIABLE {aimcity} {成都城};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {dl} {
  #VARIABLE {aimcity} {大理城};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {dlhg} {
  #VARIABLE {aimcity} {大理城};
  #VARIABLE {aimroom} {皇宫正门};
  #VARIABLE {aimroomid} {@getRoomId{{$aimcity}{$aimroom}}};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {dlwf} {
  #VARIABLE {aimcity} {大理城};
  #VARIABLE {aimroom} {王府大厅};
  #VARIABLE {aimroomid} {@getRoomId{{$aimcity}{$aimroom}}};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {dxs} {
  #VARIABLE {aimcity} {大雪山};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {dls} {
  #VARIABLE {aimcity} {大雪山};
  #VARIABLE {aimroom} {大轮寺山门};
  #VARIABLE {aimroomid} {@getRoomId{{$aimcity}{$aimroom}}};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {ems} {
  #VARIABLE {aimcity} {峨嵋山};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {fs} {
  #VARIABLE {aimcity} {佛山镇};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {fz} {
  #VARIABLE {aimcity} {福州城};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {gb} {
  #VARIABLE {aimcity} {丐帮};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {};
  walk;
};
#ALIAS {gyz} {
  #VARIABLE {aimcity} {归云庄};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {hz} {
  #VARIABLE {aimcity} {杭州城};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {hmy} {
  #VARIABLE {aimcity} {黑木崖};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {};
  walk;
};
#ALIAS {hengs} {
  #VARIABLE {aimcity} {恒山};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {hh} {
  #VARIABLE {aimcity} {黄河流域};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {hs} {
  #VARIABLE {aimcity} {华山};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {hsc} {
  #VARIABLE {aimcity} {华山村};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {hdg} {
  #VARIABLE {aimcity} {蝴蝶谷};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {hj} {
  #VARIABLE {aimcity} {回疆};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {jx} {
  #VARIABLE {aimcity} {嘉兴城};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {jqg} {
  #VARIABLE {aimcity} {绝情谷};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {kls} {
  #VARIABLE {aimcity} {昆仑山};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {lz} {
  #VARIABLE {aimcity} {兰州城};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {mz} {
  #VARIABLE {aimcity} {梅庄};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {wdj} {
  #VARIABLE {aimcity} {苗疆};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {mj} {
  #VARIABLE {aimcity} {明教};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {mr} {
  #VARIABLE {aimcity} {姑苏慕容};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {ny} {
  #VARIABLE {aimcity} {南阳城};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {nb} {
  #VARIABLE {aimcity} {宁波城};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {njc} {
  #VARIABLE {aimcity} {牛家村};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {pdz} {
  #VARIABLE {aimcity} {平定州};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {ptsl} {
  #VARIABLE {aimcity} {莆田少林};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {qzj} {
  #VARIABLE {aimcity} {全真教};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {mg} {
  #VARIABLE {aimcity} {蒙古};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {mtl} {
  #VARIABLE {aimcity} {曼佗罗山庄};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {sld} {
  #VARIABLE {aimcity} {神龙岛};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {sls} {
  #VARIABLE {aimcity} {嵩山少林};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {sz} {
  #VARIABLE {aimcity} {苏州城};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {tais} {
  #VARIABLE {aimcity} {泰山};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {tg} {
  #VARIABLE {aimcity} {塘沽城};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {thd} {
  #VARIABLE {aimcity} {桃花岛};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {tyx} {
  #VARIABLE {aimcity} {桃源县};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {tls} {
  #VARIABLE {aimcity} {天龙寺};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {tians} {
  #VARIABLE {aimcity} {天山};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {tzs} {
  #VARIABLE {aimcity} {铁掌山};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {wds} {
  #VARIABLE {aimcity} {武当山};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {wls} {
  #VARIABLE {aimcity} {无量山};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {xxh} {
  #VARIABLE {aimcity} {星宿海};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {xy} {
  #VARIABLE {aimcity} {襄阳城};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {xyp} {
  #VARIABLE {aimcity} {逍遥派};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {yz} {
  #VARIABLE {aimcity} {扬州城};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {yzw} {
  #VARIABLE {aimcity} {燕子坞};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {zns} {
  #VARIABLE {aimcity} {终南山};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {zhp} {
  gotodo {扬州城} {杂货铺} {dlist}
};
#NOP {测试天地会预设房间，%1:城市};
#ALIAS {tdhtest} {
  #LIST {illegalrooms} {clear};
  #LIST {tdhpoolrooms} {clear};
  #IF {"$common[tdhwanderroom][%1]" == ""} {
    #SHOWME {<faa>这个区域没有预设房间};
  };
  #ELSE {
    #LOCAL {roomlist} {$common[tdhwanderroom][%1][default]};
    #FOREACH {*common[tdhwanderroom][%1][partition][]} {p} {
      #IF {"$common[tdhwanderroom][%1][partition][$p][places]" == ""} {
        #CONTINUE;
      };

      #IF {"$roomlist" == ""} {
        #LOCAL {roomlist} {$common[tdhwanderroom][%1][partition][$p][places]};
      };
      #ELSE {
        #LOCAL {roomlist} {$roomlist;$common[tdhwanderroom][%1][partition][$p][places]};
      };
    };
    #LIST {jobroomlist} {create} {$roomlist};
    #VARIABLE {tdhpoolrooms} {$jobroomlist};
    #VARIABLE {jobroomlist} {@getNearRoomsEx{{jobroomlist}{1}}};
    jobnextroom {tdhclear_start {%1}} {tdhclear {%1}} {1}
  };
};
#CLASS walkmodule CLOSE;
#SHOWME {<fac>@padRight{{行走}{12}}<fac> <cfa>模块加载完毕<cfa>};