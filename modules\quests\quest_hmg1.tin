#NOP {蛤蟆功,%1:后续指令};
#ALIAS {goquest_hmg1} {
  #VARIABLE {questmodule} {蛤蟆功一};
  gotodo {嘉兴城} {269} {hmg1_ask_li {%1}}
};
#NOP {找李莫愁,%1:后续指令};
#NOP {李莫愁(<PERSON> mochou)告诉你：我最恨你们这些薄情的臭男人！去死吧！ };
#ALIAS {hmg1_ask_li} {
  #VARIABLE {askresult} {0};
  #VARIABLE {questdo} {
    e;l li;halt;w;
    echo {checkyinzhen}
  };
  #CLASS questclass OPEN;
  #ACTION {^你要看什么？} {
    #VARIABLE {askresult} {2};
  };
  #ACTION {^李莫愁%*我最恨你们这些薄情的臭男人} {
    #VARIABLE {askresult} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkyinzhen\"|你设定checkyinzhen为反馈信息}} {
    #CLASS questclass KILL;
    #SWITCH {$askresult} {
      #CASE {1} {
        #NOP {正常};
        gotodo {嘉兴城} {277} {hmg1_wait_meet {%1}}
      };
      #CASE {2} {
        #NOP {人死了等刷新};
        questupdate {$questmodule};
        runwait {%1}
      };
      #DEFAULT {
        #NOP {没骂人，也没丢针,退出};
        runwait {doquit}
      }
    };
  };
  #CLASS questclass CLOSE;
  $questdo
};
#NOP {等待欧阳锋,%1:后续指令};
#ALIAS {hmg1_wait_meet} {
  #VARIABLE {askresult} {0};
  #VARIABLE {questdo} {
    #VARIABLE {idle} {0};
    yun jingli;
    hp;
    w;
    e;
    echo {checkmeet};
  };
  #CLASS questclass OPEN;
  #ACTION {^你突然觉得好象有人} {
  };
  #ACTION {^忽听呼的一声响，一个人影从你头顶跃过，落在你身前} {
    #VARIABLE {askresult} {1};
  };
  #ACTION {^你脚下突然一软，骨碌碌地滚出了数十丈} {
    #CLASS questclass KILL;
    questupdate {$questmodule};
    questfail {$questmodule};
    dohalt {
      remove bingpo yinzhen;
      drop bingpo yinzhen;
      loc {curepoison {%1}};
    };
  };
  #ACTION {^你只觉手臂麻木，早已不听使唤，只急得大汗淋漓，不知如何是好，慌乱中跑进了柳树林子} {
    #CLASS questclass KILL;
    hmg1_bai_oyf {%1};
  };
  #ACTION {^{设定环境变量：action \= \"checkmeet\"|你设定checkmeet为反馈信息}} {
    #IF {$hp[neili] < 1000} {
      #IF {@carryqty{chuanbei wan} > 0} {
        fu wan;
        i;
      };
      #ELSE {
        #NOP {药都吃光了,有问题延迟再搞};
        questdelay $questmodule {0} {86400};
        #CLASS questclass KILL;
        %1;
      };
    };
    #IF {$askresult == 1} {
      pray pearl;
      i
    };
    #ELSE {
      #DELAY {0.5} {$questdo}
    };
  };
  #CLASS questclass CLOSE;
  $questdo
};
#NOP {拜欧阳锋,%1:后续指令};
#ALIAS {hmg1_bai_oyf} {
  #VARIABLE {variable name} {text to fill variable};
  #CLASS questclass OPEN;
  #ACTION {^怪人摇了摇头道} {
    #VARIABLE {idle} {0};
    #DELAY {0.5} {
      dohalt {kneel man}
    };
  };
  #ACTION {^怪人甚是高兴，微微一笑} {
    #VARIABLE {idle} {0};
    #DELAY {0.5} {
      dohalt {kneel man}
    };
  };
  #ACTION {^怪人哈哈大笑，声震林梢} {
    #VARIABLE {idle} {0};
    #DELAY {0.5} {
      dohalt {turn}
    };
  };
  #ACTION {^怪人斜眼瞧向你，脸有喜色，显得极是满意} {
    #VARIABLE {idle} {0};
    #DELAY {0.5} {
      dohalt {jiao 爸爸}
    };
  };
  #ACTION {^你学会了蛤蟆功} {
    #VARIABLE {idle} {0};
    skills
  };
  #ACTION {^空中忽然几声雕唳，两头大雕在半空飞掠而过} {
    #VARIABLE {idle} {0};
  };
  #ACTION {^怪人在你的耳边悄声说道：我好象记得一个叫“白陀山”的地方} {
    #CLASS questclass KILL;
    #VARIABLE {idle} {0};
    #VARIABLE {questlist[%1][fail]} {0};
    questsuccess {$questmodule};
    #SEND {shout 哈哈哈，我开了$questmodule，羡慕嫉妒恨吧！！！};
    remove bingpo yinzhen;
    drop bingpo yinzhen;
    loc {quest_end {learnhmg {jobcheck}}};
  };
  #CLASS questclass CLOSE;
  dohalt {kneel man}
};