#NOP {金蛇剑法};
#ALIAS {goquest_jsjf} {
  #VARIABLE {questmodule} {金蛇剑法};
  #SWITCH {$questlist[$questmodule][laststep]} {
    #CASE {2} {
      jsjf_extreme {%1}
    };
    #DEFAULT {
      questfail {$questmodule};
      %1;
    };
  };
};
#NOP {进山洞内准备};
#ALIAS {jsjf_sword_prepare} {
  #IF {@carryqty{fire} == 0} {
    buyfire {jsjf_sword_prepare {%1}}
  };
  #ELSEIF {@carryqty{tie chu} == 0} {
    gettiechu {jsjf_sword_prepare {%1}}
  };
  #ELSEIF {@carryqty{xiao shuzhi} == 0} {
    findweapon_xsz {jsjf_sword_prepare {%1}}
  };
  #ELSEIF {@carryqty{jinshe sword} == 0 && "$conf[nanny][zhanbu]" != ""} {
    jsjf_findxia {jsjf_jinshesword_kill {gotoroom {3931} {jsjf_sword_do {%1}}}} {%1}
  };
  #ELSE {
    gotoroom {3931} {jsjf_sword_do {%1}}
  };
};
#NOP {寻找夏雪宜，%1-成功执行，%2-失败执行};
#ALIAS {jsjf_findxia} {
  #VARIABLE {zhanbuflag} {0};
  #VARIABLE {zhanbuloc} {};
  #VARIABLE {zhanbuts} {@now{}};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^没有这个人。} {
    #VARIABLE {idle} {0};
    #CLASS questclass KILL;
    %1;
  };
  #ACTION {^%*(%*)告诉你：zhanbu_wait} {
    #CLASS questclass KILL;
    #VARIABLE {idle} {-30};
    #DELAY {10} {
      jsjf_jinshesword_find {%1};
    };
  };
  #ACTION {^%!*(%!*)告诉你：zhanbu_come} {
    #VARIABLE {idle} {0};
    echo {checkzhanbu}
  };
  #ACTION {^%!*(%!*)告诉你：zhanbu_success %*} {
    #VARIABLE {zhanbuflag} {1};
    #VARIABLE {zhanbuloc} {%%1};
  };
  #ACTION {^%!*(%!*)告诉你：zhanbu_fail} {
    #VARIABLE {zhanbuflag} {2};
  };
  #ACTION {^{设定环境变量：action \= \"checkzhanbu\"|你设定checkzhanbu为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {@elapsed{$zhanbuts} > 300} {
      #CLASS questclass KILL;
      %2
    };
    #ELSEIF {$zhanbuflag == 2} {
      #CLASS questclass KILL;
      %2
    };
    #ELSEIF {$zhanbuflag == 1} {
      #CLASS questclass KILL;
      follow none;
      parsejoblocation {$zhanbuloc} {jobnextroom {jsjf_checkxia {%1} {%2}} {%2}} {%2} {2}
    };
    #ELSE {
      #DELAY {2} {echo {checkzhanbu}}
    };
  };
  #CLASS questclass CLOSE;
  zhanbu_call {xia xueyi};
};
#ALIAS {jsjf_checkxia} {
  #VARIABLE {findok} {0};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^这里没有 xia xueyi} {
    #VARIABLE {findok} {0};
  };
  #ACTION {^你{决定跟随|已经这样}} {
    #VARIABLE {findok} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkxia\"|你设定checkxia为反馈信息}} {
    #CLASS questclass KILL;
    resonate {checkxia};
    #IF {$findok == 0} {
      runwait {jobnextroom {jsjf_checkxia {%1}} {%2}}
    };
    #ELSE {
      %1
    };
  };
  #CLASS questclass CLOSE;
  ensure {follow xia xueyi} {checkxia}
};
#ALIAS {jsjf_jinshesword_kill} {
  #VARIABLE {findok} {0};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^夏雪宜「啪」的一声倒在地上，挣扎着抽动了几下就死了。} {
    #CLASS questclass KILL;
    dohalt {
      get jinshe sword from corpse;
      i;
      %1
    }
  };
  #CLASS questclass CLOSE;
  kill xia xueyi;
  createpfm {} {1};
  startfight
};
#ALIAS {jsjf_jinshesword} {
  #VARIABLE {zhanbutimes} {0};
  #IF {@carryqty{jinshe sword} == 0} {
    jsjf_jinshesword_find {gotoroom {3930} {%1}}
  };
  #ELSE {
    gotoroom {3930} {%1}
  };
};
#NOP {解剑法};
#ALIAS {jsjf_sword_do} {
};
#ALIAS {jsjf_extreme} {
  jsjf_findxia {jsjf_extreme_ask {%1}} {
    questdelay {$questmodule} {} {7200};
    %1
  }
};
#ALIAS {jsjf_extreme_ask} {
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你向夏雪宜打听有关『金蛇剑法』的消息} {
    #CLASS questresponseclass OPEN;
		#ACTION {^夏雪宜说道：「今天先这里吧，有什么事情明天再说吧} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questdelay {questmodule} {} {7200};
      dohalt {%1}
    };
    #ACTION {^夏雪宜说道：「{今天先这里吧|以你当前的经验恐怕还是难以领悟要诀}} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questdelay {questmodule} {} {7200};
      dohalt {%1}
    };
    #ACTION {^%*发现自己依然无法理解夏雪宜所传秘诀} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questfail {$questmodule};
      dohalt {%1}
    };
    #ACTION {^你听了夏雪宜的指点，再与金蛇秘笈中不解之处一加参照} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questupdate {$questmodule} {3};
      dohalt {%1}
    };
		#CLASS questresponseclass CLOSE;
  };
  #CLASS questclass CLOSE;
  ask xie xueyi about 金蛇剑法
};