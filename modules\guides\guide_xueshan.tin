#NOP {铁掌拜师指南};
#NOP {各级别师傅的拜师限制和需要关注的技能};
#VARIABLE {guide[大轮寺][masters]} {
  {requireskills} {
    {longxiang-boruo} {huanxi-chan}
  }
  {sequence} {
    {1} {摩诃巴思}
    {2} {呼巴音}
    {3} {灵智上人}
    {4} {善勇}
    {5} {胜谛}
    {6} {桑结}
    {7} {鸠摩智}
    {8} {金轮法王}
    {9} {血刀老祖}
  }
  {摩诃巴思} {
    {id} {mohe basi}
    {conditions} {
      {gift} {
        {str} {20}
        {con} {20}
        {dex} {10}
        {int} {30}
      }
    }
    {favourites} {
      {1} {force}
      {2} {huanxi-chan}
      {3} {longxiang-boruo}
      {4} {dodge}
      {5} {yuxue-dunxing}
      {6} {parry}
    }
    {afterdo} {guide_xs_tidu {#}}
  }
  {呼巴音} {
    {id} {hu bayin}
    {conditions} {
      {skills} {
        {parry} {50}
      }
    }
    {favourites} {
      {1} {force}
      {2} {huanxi-chan}
      {3} {longxiang-boruo}
      {4} {dodge}
      {5} {yuxue-dunxing}
      {6} {parry}
    }
    {beforedo} {}
  }
  {灵智上人} {
    {id} {lingzhi shangren}
    {conditions} {
      {gender} {m}
      {skills} {
        {parry} {80}
      }
    }
    {favourites} {
      {1} {force}
      {2} {huanxi-chan}
      {3} {longxiang-boruo}
      {4} {dodge}
      {5} {yuxue-dunxing}
      {6} {hand}
      {7} {dashou-yin}
      {8} {parry}
      {9} {poison}
    }
    {beforedo} {}
  }
  {善勇} {
    {id} {shan yong}
    {conditions} {
      {gender} {f}
      {skills} {
        {parry} {80}
      }
    }
    {favourites} {
      {1} {force}
      {2} {huanxi-chan}
      {3} {longxiang-boruo}
      {4} {dodge}
      {5} {yuxue-dunxing}
      {6} {parry}
    }
    {beforedo} {}
  }
  {胜谛} {
    {id} {sheng di}
    {conditions} {
      {gender} {f}
      {skills} {
        {parry} {100}
      }
    }
    {favourites} {
      {1} {force}
      {2} {huanxi-chan}
      {3} {longxiang-boruo}
      {4} {dodge}
      {5} {yuxue-dunxing}
      {6} {blade}
      {7} {xuedao-jing}
      {8} {parry}
    }
    {beforedo} {}
  }
  {桑结} {
    {id} {sang jie}
    {conditions} {
      {shen} {-10000}
      {skills} {
        {longxiang-boruo} {120}
      }
    }
    {favourites} {
      {1} {force}
      {2} {huanxi-chan}
      {3} {longxiang-boruo}
      {4} {dodge}
      {5} {yuxue-dunxing}
      {6} {hand}
      {7} {dashou-yin}
      {8} {parry}
      {9} {poison}
    }
    {beforedo} {}
  }
  {鸠摩智} {
    {id} {jiumo zhi}
    {conditions} {
      {skills} {
        {parry} {160}
      }
    }
    {favourites} {
      {1} {force}
      {2} {huanxi-chan}
      {3} {longxiang-boruo}
      {4} {dodge}
      {5} {yuxue-dunxing}
      {6} {hand}
      {7} {dashou-yin}
      {8} {strike}
      {9} {huoyan-dao}
      {10} {parry}
    }
    {beforedo} {}
  }
  {金轮法王} {
    {id} {jinlun fawang}
    {conditions} {
      {skills} {
        {parry} {200}
      }
    }
    {favourites} {
      {1} {force}
      {2} {huanxi-chan}
      {3} {longxiang-boruo}
      {4} {dodge}
      {5} {yuxue-dunxing}
      {6} {hand}
      {7} {dashou-yin}
      {8} {hammer}
      {9} {xiangfu-lun}
      {10} {parry}
    }
    {beforedo} {guide_xs_fight {#}}
  }
  {血刀老祖} {
    {id} {xuedao laozu}
    {conditions} {
      {shen} {-100000}
      {skills} {
        {parry} {220}
      }
    }
    {favourites} {
      {1} {force}
      {2} {huanxi-chan}
      {3} {longxiang-boruo}
      {4} {dodge}
      {5} {yuxue-dunxing}
      {6} {hand}
      {7} {dashou-yin}
      {8} {blade}
      {9} {xuedao-jing}
      {10} {parry}
    }
    {beforedo} {}
  }
};
#NOP {按照指定技能的各个等级(>=0)决定任务使用的pfm及其要做的任务};
#VARIABLE {guide[大轮寺][pfms]} {
  {110} {
    {pfmskill} {xuedao-jing}
    {jobs} {华山;送信}
    {weapon} {
      {primary} {xue sui};
      {secondary} {}
    }
    {beforedo} {
      bei none;
      jifa hand dashou-yin;
      jifa blade xuedao-jing;
      jifa parry xuedao-jing;
      bei hand
    }
    {fightbuff} {}
    {attack} {
      jiali max;
      perform xuedao-jing.shendao #p;
      jiali 10
    }
  }
  {111} {
    {pfmskill} {dashou-yin}
    {jobs} {武当;送信2}
    {beforedo} {
      bei none;
      jifa hand dashou-yin;
      jifa parry dashou-yin;
      bei hand
    }
    {fightbuff} {}
    {attack} {
      jiali max;
      perform dashou-yin.tianyin #p
    }
  }
  {200} {
    {pfmskill} {dashou-yin}
    {jobs} {武当;送信2}
    {beforedo} {
      bei none;
      jifa strike huoyan-dao;
      jifa hand dashou-yin;
      jifa parry dashou-yin;
      bei strike
    }
    {fightbuff} {}
    {attack} {
      yun longxiang;
      jiali max;
      perform dashou-yin.tianyin #p
    }
  }
};
#NOP {门派武器武功需要的武器};
#VARIABLE {guide[大轮寺][weapons]} {
  {xuedao-jing} {blade}
  {xiangfu-lun} {hammer}
};
#NOP {剃度};
#ALIAS {guide_xs_tidu} {
  #CLASS guidedoclass KILL;
  #CLASS guidedoclass OPEN;
  #ACTION {^你向黑林钵夫打听有关『出家』的消息} {
    dohalt {
      kneel;
    };
  };
  #ACTION {^你弯腰低头，恭恭敬敬地跪了下来。} {
    #CLASS guidedoclass KILL;
    dohalt {%1}
  };
  #CLASS guidedoclass CLOSE;
  gotonpc {黑林钵夫} {ask heilin bofu about 出家};
};
#NOP {和达尔巴比武};
#ALIAS {guide_xs_fight} {
  gotonpc {达尔巴} {startfull {guide_xs_fight_start {%1}}}
};
#ALIAS {guide_xs_fight_start} {
  #VARIABLE {okflag} {0};
  #CLASS guidedoclass KILL;
  #CLASS guidedoclass OPEN;
  #ACTION {^这里没有这个人} {
    #VARIABLE {idle} {0};
    #DELAY {6} {ask daer ba about 金轮法王}
  };
  #ACTION {^你向达尔巴打听有关『金轮法王』的消息} {
    dohalt {
      closewimpy;
      jiali max;
      fight daer ba;
      echo {checkfight}
    }
  };
  #ACTION {^达尔巴点点头，说道：我的无上大力杵法厉害之极，打死你可别怨} { 
    #VARIABLE {okflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkfight\"|你设定checkfight为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {$okflag == 0} {
      setpfmtarget {daer ba};
      startfight;
      kill daer ba;
    };
    #ELSEIF {$okflag == 2} {
      #CLASS guidedoclass KILL;
      %1
    };
    #ELSE {
      #VARIABLE {okflag} {0};
      echo {checkhp}
    };
  };
  #ACTION {^达尔巴「啪」的一声倒在地上，挣扎着抽动了几下就死了} {
    #CLASS guidedoclass KILL;
    dohalt {
      get gold from corpse;
      guide_xs_fight
    };
  };
  #ACTION {^达尔巴%*说道：「%*可以去见师傅做俺的师兄了} {
    #DELAY {2} {
      #VARIABLE {okflag} {1};
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkhp\"|你设定checkhp为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {$okflag == 1} {
      #CLASS guidedoclass KILL;
      %1
    };
    #ELSEIF {@eval{$hp[qi_max] - $hp[qi]} > @eval{$hp[qi_max] / 4} || @eval{$hp[qi_max] - $hp[qi]} > 1000} {
      yun qi;
    };
    #ELSEIF {@eval{$hp[jingli_max] - $hp[jingli]} > @eval{$hp[jing_max] / 4}} {
      yun jingli;
    };
    #DELAY {1} {echo {checkhp}}
  };
  #CLASS guidedoclass CLOSE;
  ask daer ba about 金轮法王
};