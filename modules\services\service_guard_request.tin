#NOP {护卫模块};
#NOP {呼叫护卫,%1:city:%2:room,%3:目标id,%4:护卫就绪的操作,%5:护卫未就绪的操作};
#ALIAS {guard_call} {
  guard_call_start {%1} {%2} {%3} {%4} {%5}
};
#NOP {开始呼叫护卫};
#ALIAS {guard_call_start} {
  #VARIABLE {okflag} {0};
  #VARIABLE {startts} {@now{}};
  #CLASS serviceclass KILL;
  #CLASS serviceclass OPEN;
  #ACTION {^没有这个人。} {
    #CLASS serviceclass KILL;
    #SHOWME {<faa>保姆$conf[nanny][guard]人不在};
    #IF {"%5" == ""} {
      %4;
    };
    #ELSE {
      %5;
    };
  };
  #NOP {等待};
  #ACTION {%*(%*)告诉你：guard_wait} {
    #CLASS serviceclass KILL;
    #VARIABLE {idle} {0};
    #DELAY {5} {guard_call {%1} {%2} {%3} {%4} {%5}};
  };
  #NOP {护卫已接收任务,等待其就位};
  #ACTION {%*(%*)告诉你：guard_come} {
    #VARIABLE {waiter} {
      {id} {@lower{%%2}}
      {name} {%%1}
    };
    echo {checkonposition};
  };
  #ACTION {%*(%*)告诉你：guard_onposition} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkonposition\"|你设定checkonposition为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {@elapsed{$startts} > 240} {
      #CLASS serviceclass KILL;
      #VARIABLE {waiter} {};
      #IF {"%5" == ""} {
        %4;
      };
      #ELSE {
        %5;
      };
    };
    #ELSEIF {$okflag == 1} {
      #CLASS serviceclass KILL;
      %4
    };
    #ELSE {
      #DELAY {2} {
        echo {checkonposition};
      };
    };
  };
  #CLASS serviceclass CLOSE;
  #IF {"$conf[nanny][guard]" == ""} {
    #CLASS serviceclass KILL;
    #SHOWME {<faa>未配置guard保姆,请自行处理};
    #IF {"%5" == ""} {
      %4;
    };
    #ELSE {
      %5;
    };
  };
  #ELSE {
    tell $conf[nanny][guard] guard_request_%1_%2_%3;
  };
};
#ALIAS {guard_over} {
  #IF {"$waiter[id]" != ""} {
    bye $waiter[id];
  };
};
#NOP {取消护卫请求,%1:后续指令};
#ALIAS {guard_cancel} {
  #IF {"$conf[nanny][guard]" != ""} {
    tell $conf[nanny][guard] guard_cancel
  };
};