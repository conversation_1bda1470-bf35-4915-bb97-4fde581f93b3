#NOP {古墓乐音绝技,%1:后续指令};
#ALIAS {goquest_yyjj} {
  #VARIABLE {questmodule} {乐音绝技};
  gotonpc {小龙女} {yyjj_asklongnv {%1}}
};
#NOP {问小龙女乐音绝技,%1:后续指令};
#ALIAS {yyjj_asklongnv} {
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你向小龙女打听有关『乐音绝技』的消息。} {
    #VARIABLE {idle} {0};
    #VARIABLE {askresult} {0};
    #CLASS questresponseclass OPEN;
    #ACTION {^小龙女说道：「%*不是已经掌握了乐音绝技了么} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questsuccess {$questmodule};
      dohalt {%1};
    };
    #ACTION {^小龙女说道：「嗯，我现在比较忙，你等会来打听吧。} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questdelay {$questmodule} {0} {7200};
      dohalt {%1};
    };
    #ACTION {^小龙女说道：「出大屋有片花丛，比较适合修炼，你专心去修炼吧，可不要忘记带一根鞭子哦。} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questdelay {$questmodule} {0} {7200};
      dohalt {
        wwp {@getBaseWeapon{yinsuo-jinling}};
        gotodo {绝情谷} {花丛} {yyjj_startlian {%1}};
      };
    };
    #CLASS questresponseclass CLOSE;
  };
  #CLASS questclass CLOSE;
  ask xiao longnv about 乐音绝技;
};
#NOP {乐音绝技开始练,%1:后续指令};
#ALIAS {yyjj_startlian} {
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你听了小龙女的指点，来到此僻静处，准备修炼乐音绝技。} {
    echo {checkwait};
  };
  #ACTION {^你发现有人在这里修炼武功，还是不打扰为好，于是静静地离开了。} {
    #CLASS questclass KILL;
      #DELAY {10} {
        yun qi;
        yun jing;
        startfull {
          loc {gotodo {绝情谷} {花丛} {yyjj_startlian {%1}}};
        };
      };
  };
  #ACTION {^你耳中闻着%*随风发出的声音，有如乐曲，听了几下，不由神情有些恍惚。} {
    #VARIABLE {idle} {0};
    xiulian yueyin;
    dohalt {
      yun qi;
      yun jing;
    };
  };
  #ACTION {^恭喜！你学会了乐音绝技！} {
    #CLASS questclass KILL;
    questsuccess {$questmodule};
    dohalt {%1};
  };
  #ACTION {^你顿时心灰意冷，颓然而立，觉得自己实在是没有天份，可能永远学不会乐音绝技了} {
    #CLASS questclass KILL;
    dohalt {
      loc {gotodo {绝情谷} {花丛} {yyjj_startlian {%1}}};
    }
  };
  #CLASS questclass CLOSE;
};