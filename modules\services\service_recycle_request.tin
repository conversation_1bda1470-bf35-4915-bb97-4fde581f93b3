#NOP {发起垃圾回收请求,%1:后续指令};
#ALIAS {recycle_call} {
  yz {recycle_call_start {%1}}
};
#NOP {呼叫垃圾回收,%1:后续指令};
#ALIAS {recycle_call_start} {
  #CLASS serviceclass KILL;
  #CLASS serviceclass OPEN;
  #ACTION {^没有这个人。} {
    #CLASS serviceclass KILL;
    #SHOWME {<faa>保姆$conf[nanny][recycle]人不在};
    %1
  };
  #ACTION {%*(%*)告诉你：recycle_wait} {
    #CLASS serviceclass KILL;
    #VARIABLE {idle} {0};
    #DELAY {5} {recycle_call {%1}};
  };
  #ACTION {%*(%*)告诉你：recycle_come} {
    #CLASS serviceclass KILL;
    #VARIABLE {waiter} {
      {id} {@lower{%%2}}
      {name} {%%1}
    };
    yz {recycle_request_gold {%1}}
  };
  #CLASS serviceclass CLOSE;
  #IF {"$conf[nanny][recycle]" == ""} {
    #SHOWME {<faa>未配置recycle保姆,请自行处理};
    %1
  };
  #ELSE {
    tell $conf[nanny][recycle] recycle_request
  };
};
#NOP {去扬州等待交易,%:后续指令};
#ALIAS {recycle_request_gold} {
  #VARIABLE {startts} {@now{}};
  #VARIABLE {okflag} {0};
  #VARIABLE {arrived} {0};
  #CLASS serviceclass KILL;
  #CLASS serviceclass OPEN;
  #ACTION {^$waiter[name]轻轻地拍了拍你的头} {
    #VARIABLE {arrived} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkresponse\"|你设定checkresponse为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {@elapsed{$startts} > 600} {
      #CLASS serviceclass KILL;
      loc {
        recycle_call {%1};
      };
    };
    #ELSEIF {$arrived == 0} {
      #DELAY {5} {
        echo {checkresponse};
      }
    };
    #ELSE {
      #IF {$hp[balance] > 111} {
        #VARIABLE {okflag} {0};
        qu 100 gold;
        echo {checkqu};
      };
      #ELSE {
        #CLASS serviceclass KILL;
        addoil $waiter[id];
        gotodo {扬州城} {杂货铺} {recycle_request_things {%1}}
      };
    };
  };
  #ACTION {^你从银号里取出} {
    #VARIABLE {okflag} {1};
    score
  };
  #ACTION {^{设定环境变量：action \= \"checkqu\"|你设定checkqu为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {$okflag == 1} {
      #VARIABLE {okflag} {0};
      give 100 gold to $waiter[id];
      echo {checkgive};
    };
    #ELSE {
      #DELAY {1} {
        #VARIABLE {okflag} {0};
        qu 100 gold;
        echo {checkqu};
      };
    };
  };
  #ACTION {^你给$waiter[name]} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkgive\"|你设定checkgive为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {$okflag == 0} {
      #DELAY {1} {
        #VARIABLE {okflag} {0};
        give 100 gold to $waiter[id];
        echo {checkgive};
      };
    };
  };
  #ACTION {^$waiter[name]对着你点了点头。} {
    echo {checkresponse};
  };
  #CLASS serviceclass CLOSE;
  echo {checkresponse};
};
#NOP {去扬州等待交易,%:后续指令};
#ALIAS {recycle_request_things} {
  #VARIABLE {okflag} {0};
  #VARIABLE {arrived} {0};
  #VARIABLE {favourite} {};
  #CLASS serviceclass KILL;
  #CLASS serviceclass OPEN;
  #ACTION {^$waiter[name]轻轻地拍了拍你的头} {
    #VARIABLE {arrived} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkresponse\"|你设定checkresponse为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {$arrived == 0} {
      #DELAY {5} {
        echo {checkresponse};
      }
    };
    #ELSE {
      dlist;
      echo {checkstock};
    };
  };
  #ACTION {^┃       ID             货  物               价  格 } {
    #VARIABLE {favourite} {};
    #CLASS dlistclass OPEN;
    #ACTION {^┃%!s{fenglei yu|longling yu|xiangni yu|lvyu sui|fengling yu|yitian canpian|tulong canpian|weilan's hammer|tianqi}} {  
      #VARIABLE {favourite} {%%%1};
    };
    #ACTION {^┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛} {
      #CLASS dlistclass KILL;
    };
    #CLASS dlistclass CLOSE;
  };
  #ACTION {^你没有保存任何物品} {
    #VARIABLE {favourite} {};
  };
  #ACTION {^{设定环境变量：action \= \"checkstock\"|你设定checkstock为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {"$favourite" == ""} {
      #CLASS serviceclass KILL;
      #IF {$hp[exp] >= 3000000} {
        weilan {sword} {gotodo {扬州城} {杂货铺} {recycle_weilan_things {%1}}};
      };
      #ELSE {
        bye $waiter[id];
        %1;
      };
    };
    #ELSE {
      qu $favourite;
      i;
      echo {checkthings};
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkthings\"|你设定checkthings为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {@carryqty{$favourite} == 0} {
      #DELAY {1} {
        qu $favourite;
        i;
        echo {checkthings};
      };
    };
    #ELSE {
      dohalt {
        give $favourite to $waiter[id]
      }
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkgive\"|你设定checkgive为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {$okflag == 0} {
      #DELAY {1} {
        #VARIABLE {okflag} {0};
        give 100 gold to $waiter[id];
        echo {checkgive};
      };
    };
  };
  #ACTION {^$waiter[name]对着你点了点头。} {
    echo {checkresponse};
  };
  #CLASS serviceclass CLOSE;
  echo {checkresponse};
};
#NOP {回收weilan打造的武器};
#ALIAS {recycle_weilan_things} {
  #VARIABLE {tempweapon} {};
  #CLASS serviceclass KILL;
  #CLASS serviceclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkthings\"|你设定checkthings为反馈信息}} {
    #CLASS serviceclass KILL;
    #FOREACH {$superlist} {w} {
      #IF {"$id[things][$w sword]" != ""} {
        #VARIABLE {tempweapon} {$w sword};
        #BREAK;
      };
    };
    #IF {"$tempweapon" != ""} {
      give $tempweapon to $waiter[id];
    };
    bye $waiter[id];
    %1;
  };
  #CLASS serviceclass CLOSE;
  i;
  echo {checkthings};
};