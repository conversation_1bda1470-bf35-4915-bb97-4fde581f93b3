#NOP {凌虚指力,%1:后续指令};
#ALIAS {goquest_lxzl} {
  #VARIABLE {questmodule} {凌虚指力};
  questdelay {$questmodule} {0} {86400};
  gotonpc {慕容博} {lxzl_askbo {%1}};
};
#ALIAS {lxzl_askbo} {
  #VARIABLE {okflag} {0};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你向慕容博打听有关『凌虚指力』的消息} {
    #VARIABLE {idle} {-30};
    #VARIABLE {okflag} {1};
  };
  #ACTION {^慕容博说道：「%*你还是再努力段时间，再来领悟吧！」} {
    #VARIABLE {okflag} {2};
  };
  #ACTION {^你想了很久，可惜还是不能领会} {
    #VARIABLE {okflag} {0};
  };
  #ACTION {^{设定环境变量：action \= \"checkask\"|你设定checkask为反馈信息}} {
    #CLASS questclass KILL;
    #IF {$okflag == 1} {
      questsuccess {$questmodule};
    };
    #ELSEIF {$okflag == 2} {
      questdelay {$questmodule} {} {7200};
    };
    #ELSE {
      questfail {$questmodule};
    };
    dohalt {%1};
  };
  #CLASS questclass CLOSE;
  ask murong bo about 凌虚指力;
  echo {checkask}
};