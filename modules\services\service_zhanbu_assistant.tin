#NOP {占卜助手模块};
#ALIAS {initassistantservice} {
  #CLASS serviceassistclass KILL;
  #CLASS serviceassistclass OPEN;
  #ACTION {^%*(%*)告诉你：zhanbu_help %* %*} {
    #VARIABLE {caller} {
      {request} {assist}
      {timestamp} {@now{}}
      {id} {@lower{%%2}}
      {name} {%%1}
      {city} {%%3}
      {room} {%%4}
    };
  };
  #ACTION {^! %*(%*)告诉你：zhanbu_help %* %*} {
    #VARIABLE {caller} {
      {request} {assist}
      {timestamp} {@now{}}
      {id} {@lower{%%2}}
      {name} {%%1}
      {city} {%%3}
      {room} {%%4}
    };
  };
  #CLASS serviceassistclass CLOSE;
};
#ALIAS {assist_response} {
  gotodo {$caller[city]} {$caller[room]} {assist_start {%1}};
};
#ALIAS {assist_start} {
  #VARIABLE {startts} {@now{}};
  #VARIABLE {okflag} {0};
  #CLASS serviceclass KILL;
  #CLASS serviceclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkzhanbu\"|你设定checkzhanbu为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {$okflag == 0} {
      #IF {@elapsed{$startts} > 300} {
        #CLASS serviceclass KILL;
        jobcheck;
      };
      #ELSE {
        #DELAY {2} {
          hi $caller[id];
          echo {checkzhanbu};
        };
      };
    };
  };
  #ACTION {^$caller[name]捏着手指，按照子、丑、寅、卯、辰、巳、午、未、申、酉、戌、亥的方位，仔细的掐算着} {
    #VARIABLE {okflag} {1};
    #VARIABLE {idle} {0};
    dohalt {yun zhanbu help $caller[id]}
  };
  #ACTION {^$caller[name]对你抱拳道：“青山不改，绿水常流，咱们后会有期} {
    #CLASS serviceclass KILL;
    #VARIABLE {caller} {};
    dohalt {%1};
  };
  #CLASS serviceclass CLOSE;
  startfull {
    echo {checkzhanbu}
  };
};
initassistantservice;