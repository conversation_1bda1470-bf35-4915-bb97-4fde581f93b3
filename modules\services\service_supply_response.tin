#NOP {初始化供应商服务};
#ALIAS {initsupplyservice} {
  #CLASS servicesupplyclass KILL;
  #CLASS servicesupplyclass OPEN;
  #ACTION {^%*(%*)告诉你：supply_request_%*} {
    supply_accept {@lower{%%2}} {%%1} {%%3}
  };
  #ACTION {^! %*(%*)告诉你：supply_request_%*} {
    supply_accept {@lower{%%2}} {%%1} {%%3}
  };
  #ACTION {^%*(%*)告诉你：supply_cancel} {
    #VARIABLE {caller} {};
  };
  #ACTION {^! %*(%*)告诉你：supply_cancel} {
    #VARIABLE {caller} {};
  };
  #CLASS servicesupplyclass CLOSE;
};
#NOP {注册物品请求,%1:id,%2:name,%3:物品};
#ALIAS {supply_accept} {
  #NOP {超过五分钟重置};
  #IF {"$caller" != "" && @elapsed{$caller[timestamp]} > 300} {
    #VARIABLE {caller} {};
  };
  #NOP {更新请求};
  #IF {"$caller" != "" && "$caller[id]" != "%1"} {
    tell %1 get_wait
  };
  #ELSE {
    #VARIABLE {caller} {
      {request} {supply}
      {timestamp} {@now{}}
      {id} {%1}
      {name} {%2}
      {item} {%3}
    };
    tell %1 supply_come
  };
};
#NOP {去扬州钱庄响应,%1:后续指令};
#ALIAS {supply_response} {
  #NOP {懒得判断了，直接去一趟杂货铺};
  gotoroom {442} {supply_response_withdraw {%1}};
};
#ALIAS {supply_response_withdraw} {
  #CLASS serviceclass KILL;
  #CLASS serviceclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkitem\"|你设定checkitem为反馈信息}} {
    #IF {@carryqty{$caller[item]} > 0} {
      #CLASS serviceclass KILL;
      yz {supply_response_start {%1}}
    };
    #ELSE {
      #CLASS serviceclass KILL;
      qu $caller[item];
      i;
      dohalt {yz {supply_response_start {%1}}}
    };
  };
  #CLASS serviceclass CLOSE;
  echo {checkitem}
};
#NOP {检查呼叫者,%1:后续指令};
#ALIAS {supply_response_start} {
  #VARIABLE {startts} {@now{}};
  #VARIABLE {arrived} {0};
  #VARIABLE {gived} {0};
  #CLASS serviceclass KILL;
  #CLASS serviceclass OPEN;
  #ACTION {^你给$caller[name]} {
    #VARIABLE {gived} {1};
  };
  #ACTION {^你轻轻地拍了拍$caller[name]的头。} {
    #VARIABLE {arrived} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkcaller\"|你设定checkcaller为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {@elapsed{$startts} > 120} {
      #CLASS serviceclass KILL;
      #VARIABLE {caller} {};
      %1
    };
    #ELSEIF {$arrived == 1} {
      #CLASS serviceclass KILL;
      #IF {$gived == 1} {
        #NOP {减少库存};
        #IF {"$stockitems[$caller[item]]" != ""} {
          #VARIABLE {stockitems[$caller[item]]} {@eval{$stockitems[$caller[item]] - 1}}
        };
      };
      #VARIABLE {caller} {};
      %1
    };
    #ELSE {
      #DELAY {2} {
        give $caller[id] $caller[item];
        pat $caller[id];
        echo {checkcaller};
      }
    };
  };
  #CLASS serviceclass CLOSE;
  give $caller[id] $caller[item];
  pat $caller[id];
  echo {checkcaller};
};
initsupplyservice;
