#NOP {门童模块};
#ALIAS {initdoormanservice} {
  #CLASS servicedoormanclass KILL;
  #CLASS servicedoormanclass OPEN;
  #ACTION {^%*(%*)告诉你：doorman_request_%*} {
    doorman_accept {@lower{%%2}} {%%1} {%%3}
  };
  #ACTION {^! %*(%*)告诉你：doorman_request_%*} {
    doorman_accept {@lower{%%2}} {%%1} {%%3}
  };
  #ACTION {^%*(%*)告诉你：doorman_cancel} {
    #VARIABLE {caller} {};
  };
  #ACTION {^! %*(%*)告诉你：doorman_cancel} {
    #VARIABLE {caller} {};
  };
  #CLASS servicedoormanclass CLOSE;
};
#NOP {注册门童请求,%1:id,%2:name,%3:使用的钥匙};
#ALIAS {doorman_accept} {
  #NOP {超过五分钟重置};
  #IF {"$caller" != "" && @elapsed{$caller[timestamp]} > 300} {
    #VARIABLE {caller} {};
  };
  #NOP {更新请求};
  #IF {"$caller" != "" && "$caller[id]" != "%1"} {
    tell %1 doorman_wait
  };
  #ELSE {
    #VARIABLE {caller} {
      {request} {doorman}
      {timestamp} {@now{}}
      {id} {%1}
      {name} {%2}
      {key} {%3}
    };
    tell %1 doorman_come
  };
};
#NOP {响应开门请求,%1:后续指令};
#ALIAS {doorman_response} {
  #SWITCH {"$caller[key]"} {
    #CASE {"tong yaoshi"} {opendoor_tongyaoshi {%1}};
    #DEFAULT {%1};
  };
};
#NOP {苏州闺房开门，%1:后续指令,%2:是否已经访问过杂货铺};
#ALIAS {opendoor_tongyaoshi} {
  #IF {@carryqty{tong yaoshi} == 0} {
    #IF {"%2" == ""} {
      qusomething {tong yaoshi} {opendoor_tongyaoshi {%1} {1}}
    };
    #ELSE {
      #VARIABLE {caller} {};
      %1
    };
  };
  #ELSE {
    gotodo {苏州城} {后院} {opendoor_tongyaoshi_start {%1}}
  };
};
#ALIAS {opendoor_tongyaoshi_start} {
  #CLASS serviceclass KILL;
  #CLASS serviceclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkkaimen\"|你设定checkkaimen为反馈信息}} {
    #CLASS serviceclass KILL;
    #VARIABLE {caller} {};
    #IF {@contains{{roomexits}{n}} > 0} {
      #SHOWME {<afa>芝麻开门};
      dohalt {%1}
    };
    #ELSE {
      doorman_response {%1}
    };
  };
  #CLASS serviceclass CLOSE;
  kai men;
  echo {checkkaimen}
};
initdoormanservice