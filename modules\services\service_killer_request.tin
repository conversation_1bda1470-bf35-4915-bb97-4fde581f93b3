#NOP {护卫模块};
#NOP {呼叫杀手,%1:要杀的人,%2:后续指令,%3:标识,当有标识时到达地点会一直kill一段时间或呼叫着复位};
#ALIAS {killer_call} {
  killer_call_start {%1} {%2} {%3}
};
#NOP {开始呼叫杀手,%1:要杀的人,%2:后续指令,%3:标识};
#ALIAS {killer_call_start} {
  #CLASS serviceclass KILL;
  #CLASS serviceclass OPEN;
  #ACTION {^没有这个人。} {
    #CLASS serviceclass KILL;
    #SHOWME {<faa>保姆$conf[nanny][killer]人不在};
    %2
  };
  #ACTION {%*(%*)告诉你：killer_wait} {
    #CLASS serviceclass KILL;
    #VARIABLE {idle} {0};
    #DELAY {5} {killer_call {%1} {%2} {%3} {%4}};
  };
  #ACTION {%*(%*)告诉你：killer_come} {
    #VARIABLE {waiter} {
      {id} {@lower{%%2}}
      {name} {%%1}
    };
    #IF {"%3" == ""} {
      #CLASS serviceclass KILL;
      #NOP {直接溜,让保姆杀人去};
      %2
    };
  };
  #ACTION {%*(%*)告诉你：killer_onposition} {
    #CLASS serviceclass KILL;
    %2
  };
  #CLASS serviceclass CLOSE;
  #IF {"$conf[nanny][killer]" == ""} {
    #SHOWME {<faa>未配置killer保姆,请自行处理};
    %2
  };
  #ELSE {
    tell $conf[nanny][killer] killer_request_%1_%3
  };
};
#NOP {取消杀人请求,%1:后续指令};
#ALIAS {killer_cancel} {
  #IF {"$conf[nanny][killer]" != ""} {
    tell $conf[nanny][killer] killer_cancel
  };
};