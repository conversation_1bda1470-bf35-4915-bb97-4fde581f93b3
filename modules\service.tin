#NOP {服务模块};
import {services/service_funds_request};
import {services/service_recycle_request};
import {services/service_guard_request};
import {services/service_killer_request};
import {services/service_zhanbu_request};
import {services/service_library_request};
import {services/service_buff_request};
import {services/service_recycle_request};
import {services/service_cure_request};
import {services/service_doorman_request};
import {services/service_supply_request};
import {services/service_visit_request};
import {services/service_selfbuff_request};

#CLASS servicemodule KILL;
#CLASS servicemodule OPEN;
#VARIABLE {caller} {};
#VARIABLE {waiter} {};
#ACTION {^%!*(%*)告诉你：pd_jade_%*_%*_%*_%*_%*_%*_%*_%*_%*_%*} {
  #IF {"@lower{%1}" == "%2"} {
    #VARIABLE {idle} {0};
    #LOCAL {jc} {@eval{&pdjades[]+1}};
    #VARIABLE {pdjades[$jc]} {
      {账号} {%2}
      {名称} {%3}
      {护甲} {%4}
      {伤害} {%5}
      {命中} {%6}
      {臂力} {%7}
      {根骨} {%8}
      {身法} {%9}
      {悟性} {%10}
      {三才} {%11}
    };
  };
};
#ALIAS {initservices} {
  #FOREACH {$conf[services][]} {srv} {
    #SWITCH {"$srv"} {
      #CASE {"funds"} {
        import {services/service_funds_response};
      };
      #CASE {"recycle"} {
        import {services/service_recycle_response};
      };
      #CASE {"guard"} {
        import {services/service_guard_response};
      };
      #CASE {"killer"} {
        import {services/service_killer_response};
      };
      #CASE {"zhanbu"} {
        import {services/service_zhanbu_response};
      };
      #CASE {"assist"} {
        import {services/service_zhanbu_assistant};
      };
      #CASE {"library"} {
        import {services/service_library_response};
      };
      #CASE {"buff"} {
        import {services/service_buff_response};
      };
      #CASE {"doctor"} {
        import {services/service_cure_response};
      };
      #CASE {"doorman"} {
        import {services/service_doorman_response};
      };
      #CASE {"supply"} {
        import {services/service_supply_response};
      };
      #CASE {"visit"} {
        import {services/service_visit_response};
      };
    };
  };
};
#ALIAS {serviceclear} {
  #CLASS servicefundsclass KILL;
  #CLASS servicerecycleclass KILL;
  #CLASS serviceguardclass KILL;
  #CLASS servicekillerclass KILL;
  #CLASS servicezhanbuclass KILL;
  #CLASS servicelibraryclass KILL;
  #CLASS serviceassistclass KILl;
  #CLASS servicebuffclass KILl;
  #CLASS servicecureclass KILl;
  #CLASS servicedoormanclass KILl;
  #CLASS servicesupplyclass KILl;
  #CLASS serviceclass KILL;
};
#CLASS servicemodule CLOSE;
serviceclear;
initservices;
#SHOWME {<fac>@padRight{{服务}{12}}<fac> <cfa>模块加载完毕<cfa>};
