#NOP {越女剑法,%1:后续指令};
#ALIAS {goquest_yttlg} {
  #VARIABLE {questmodule} {倚天屠龙功};
  gotodo {武当山} {山脚下} {yttlg_saveyu {%1}}
};
#NOP {等待俞岱岩出现，%1:后续，%2:标识};
#ALIAS {yttlg_saveyu} {
  #VARIABLE {okflag} {0};
  #IF {"%2" != ""} {
    #VARIABLE {okflag} {1};
  };
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你听得师哥身受重伤，又落入了不明来历之人手中，不由的心急如焚。} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkget\"|你设定checkget为反馈信息}} {
    #VARIABLE {idle} {0};
    #DELAY {4} {
      #IF {$okflag == 0 && "$room" != "十偃镇"} {
        look;
        echo {checkget}
      };
      #ELSE {
        #VARIABLE {okflag} {0};
        dohalt {
          l dache;
          l caocong;
          bo 长草;
          get 俞岱岩;
          echo {checkmeet}
        };
      };
    }
  };
  #ACTION {^你奔到张三丰面前一跪，泣不成声，叫道：“师父，师……父………三……三哥受人暗算} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkmeet\"|你设定checkmeet为反馈信息}} {
    #VARIABLE {idle} {0};
    #DELAY {4} {
      #IF {$okflag == 0} {
        open door;
        n;
        s;
        echo {checkmeet}
      };
      #ELSE {
        #VARIABLE {okflag} {0};
        echo {checkliao}
      };
    }
  };
  #ACTION {^张三丰大袖一挥,说道:你且退下吧} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkliao\"|你设定checkliao为反馈信息}} {
    #VARIABLE {idle} {0};
    #DELAY {4} {
      #IF {$okflag == 0} {
        echo {checkliao}
      };
      #ELSE {
        #VARIABLE {okflag} {0};
        gotodo {武当山} {三清殿} {
          #IF {"$hp[sex]" == "m"} {
            s;e;s;echo {checkthink}
          };
          #ELSE {
            s;w;s;echo {checkthink}
          };
        }
        
      };
    }
  };
  #ACTION {^张三丰袍袖一挥，说道:你也下去吧。说罢进了内堂} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkthink\"|你设定checkthink为反馈信息}} {
    #VARIABLE {idle} {0};
    #DELAY {4} {
      @@;
      #IF {$okflag == 0} {
        echo {checkthink}
      };
      #ELSE {
        #CLASS questclass KILL;
        loc {getpearl {gotonpc {张三丰} {yttlg_ask {%1}}}}
      };
    }
  };
  #CLASS questclass CLOSE;
  echo {checkget}
};
#ALIAS {yttlg_ask} {
  #VARIABLE {okflag} {0};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^张三丰说道：「你问这个做什么} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^你脑海里似乎抓住了什么，可是依然不是很明白} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^你听了张三丰的指点,终于领悟了倚天屠龙功的精华所在。} {
    #VARIABLE {okflag} {2};
  };
  #ACTION {^{设定环境变量：action \= \"checkresult\"|你设定checkresult为反馈信息}} {
    #DELAY {2} {
      @@;
      #IF {$okflag == 1} {
        #CLASS questclass KILL;
        questfail {$questmodule};
        dohalt {%1}
      };
      #ELSEIF {$okflag == 2} {
        #CLASS questclass KILL;
        questsuccess {$questmodule};
        dohalt {%1}
      };
      #ELSE {
        echo {checkresult}
      };
    }
  };
  #CLASS questclass CLOSE;
  unset env_yttl;
  #VARIABLE {env[yttl]} {};
  ask zhang sanfeng about 倚天屠龙功;
  echo {checkresult}
};