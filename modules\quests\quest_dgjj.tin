#NOP {独孤九剑,%1:后续指令};
#ALIAS {goquest_dgjj} {
  #VARIABLE {questmodule} {独孤九剑};
  gotodo {华山} {后山} {dgjj_hanfeng {%1}}
};
#NOP {大战田伯光等风清扬出来};
#ALIAS {dgjj_hanfeng} {
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你正忙着呢，不能喊叫} {
    dohalt {han 风老前辈}
  };
  #ACTION {^你运用丹田之气，纵声长啸道:“风老前辈} {
    #CLASS questresponseclass KILL;
    #CLASS questresponseclass OPEN;
    #ACTION {^你觉得索然无趣，又回到了思过崖} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      #VARIABLE {env[dgjj]} {};
      unset env_dgjj;
      questfail {$questmodule};
      logbuff {dgjj};
      loc {%1}
    };
    #ACTION {^四野寂静无声，唯有空谷回音随风飘散} {
      #CLASS questresponseclass KILL;
      pray pearl;
      dohalt {han 风老前辈}
    };
    #ACTION {^一条黑影从山后闪出,刀光霍霍，已经将你笼罩在狂风快刀刀光之下} {
      #CLASS questresponseclass KILL;
      echo {checkhp};
    };
    #CLASS questresponseclass OPEN;
  };
  #ACTION {^{设定环境变量：action \= \"checkhp\"|你设定checkhp为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {@eval{$hp[qi_max]-$hp[qi]} > @eval{$hp[qi_max]/4}} {
      yun qi
    };
    #DELAY {1} {echo {checkhp}}
  };
  #ACTION {^一道青影飘然而落，淡淡道：“我要指点你几招} {
    questsuccess {$questmodule};
    dohalt {
      fangqi zixia-gong;
      dohalt {
        gotodo {华山} {夹山壁} {bai feng qingyang}
      };
    };
  };
  #ACTION {^风清扬说道：「我华山派乃是堂堂名门正派，对弟子要求极严。」} {
    dohalt {
      gozshen {100000} {gotodo {华山} {夹山壁} {bai feng qingyang}}
    }
  };
  #ACTION {^风清扬说道：「好吧，我就收下你了,你可要为我们华山派争口气} {
    #VARIABLE {idle} {0};
    score;
    wwp;
    xue feng dugu-jiujian;
    skills;
    echo {checkdgjj}
  };
  #ACTION {^{设定环境变量：action \= \"checkdgjj\"|你设定checkdgjj为反馈信息}} {
    #CLASS questclass KILL;
    dohalt {golearn {fulldgjj {golian {%1}}}}
  };
  #CLASS questclass CLOSE;
  closewimpy;
  pray pearl;
  han 风老前辈
};
