#NOP {明教巡逻任务模块};
#NOP {每个人需要巡逻的路径};
#VARIABLE {xldest} {
  {1} {
    {name} {张中}
    {id} {zhang zhong}
    {rooms} {1651;1655;1641;2904}
  }
  {2} {
    {name} {颜垣}
    {id} {yan tan}
    {rooms} {1679}
  }
  {3} {
    {name} {辛然}
    {id} {xin ran}
    {rooms} {1680}
  }
  {4} {
    {name} {唐洋}
    {id} {tang yang}
    {rooms} {1677}
  }
  {5} {
    {name} {庄铮}
    {id} {zhuang zheng}
    {rooms} {1678}
  }
  {6} {
    {name} {闻苍松}
    {id} {wen cangsong}
    {rooms} {3318}
  }
  {7} {
    {name} {殷野王}
    {id} {yin yewang}
    {rooms} {1633;1627;1634}
  }
};
#NOP {%1:是否需要pray，一般选择被下次任务完成被20整除则会判断九阳};
#ALIAS {jobgo_xunluo} {
  #IF {"%1" == ""} {
    gotonpc {殷无禄} {startfull {jobask_xunluo}};
  };
  #ELSEIF {$env[pray] == 0} {
    dopray {storethings {pearl} {jobgo_xunluo}}
  };
  #ELSE {
    gotonpc {殷无禄} {startfull {jobask_xunluo}};
  };
};
#ALIAS {jobask_xunluo} {
  #CLASS jobrequestclass KILL;
  #CLASS jobrequestclass OPEN;
  #ACTION {^你向殷无禄打听有关『巡逻』的消息} {
    #VARIABLE {idle} {0};
    #CLASS jobresponseclass OPEN;
    #ACTION {^殷无禄说道：「大胆!你竟然敢同时做别的任务！} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {
        startfull {jobask_xunluo} {1}
      };
    };
    #ACTION {^殷无禄说道：「你不是在巡逻吗，怎么还呆在这里} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      #VARIABLE {jobindex_xunluo} {0};
      dohalt {jobdo_xunluo};
    };
    #ACTION {^殷无禄说道：「你上次明教巡逻任务刚做完，还是先休息一下吧} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {jobprepare};
    };
    #ACTION {^殷无禄说道：「你刚刚放弃了，还不去休息一会} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {
        startfull {jobask_xunluo} {1}
      };
    };
    #ACTION {^殷无禄说道：「嗯，你刚刚巡逻完毕，还是先去休息休息吧} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {
        startfull {jobask_xunluo} {1}
      };
    };
    #ACTION {^殷无禄对$user_name哈哈一笑，说道：既然这样，你就在光明顶和小沙丘之间好好巡逻。} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      #VARIABLE {jobindex_xunluo} {0};
      dohalt {jobdo_xunluo};
    };
  };
  #CLASS jobrequestclass CLOSE;
  ask yin wulu about 巡逻;
};
#NOP {开始巡逻任务};
#ALIAS {jobdo_xunluo} {
  #MATH {jobindex_xunluo} {$jobindex_xunluo + 1};
  #IF {$jobindex_xunluo > 7} {
    gotonpc {殷无禄} {jobfinish_xunluo_askyin};
  };
  #ELSE {
    jobvisit_xunluo;
  };
};
#NOP {访问巡逻地点};
#ALIAS {jobvisit_xunluo} {
  #CLASS jobdoclass KILL;
  #CLASS jobdoclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkvisit\"|你设定checkvisit为反馈信息}} {
    #VARIABLE {echots} {0};
    jobnextroom {echo {checkvisit}} {gotonpc {$jobdest_xunluo[name]} {jobdraw_xunluo}};
  };
  #CLASS jobdoclass CLOSE;
  #VARIABLE {jobdest_xunluo} {$xldest[+$jobindex_xunluo]};
  #LIST {jobroomlist} {create} {$xldest[+$jobindex_xunluo][rooms]};
  jobnextroom {echo {checkvisit}} {gotonpc {$jobdest_xunluo[name]} {jobdraw_xunluo}};
};
#NOP {画印};
#ALIAS {jobdraw_xunluo} {
  #CLASS jobrequestclass KILL;
  #CLASS jobrequestclass OPEN;
  #ACTION {^你向$jobdest_xunluo[name]打听有关『画印』的消息} {
    #VARIABLE {idle} {0};
    #VARIABLE {workingflag} {1};
    #CLASS jobresponseclass KILL;
    #CLASS jobresponseclass OPEN;
    #ACTION {^$jobdest_xunluo[name]说道：「先去别的地方看看，等会儿再来巡逻这儿。」} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      #VARIABLE {idle} {0};
      dohalt {startfull {jobdraw_xunluo} {1}}
    };
    #ACTION {^$jobdest_xunluo[name]说道：「等等，我正在检查呢。」} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      #VARIABLE {idle} {0};
      dohalt {startfull {jobdraw_xunluo} {1}}
    };
    #ACTION {^$jobdest_xunluo[name]说道：「这边你已经巡逻完了，再去别的地方看看吧} {
      #VARIABLE {idle} {0};
      #VARIABLE {workingflag} {0};
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {jobdo_xunluo};
    };
    #ACTION {^$jobdest_xunluo[name]说道：「{嗯，刚刚有弟子来报告说好象看见了不明身份的人|咦，不是叫你去看看有没有可疑的人吗}} {
      #VARIABLE {idle} {0};
      #VARIABLE {workingflag} {0};
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {jobsearch_xunluo};
    };
    #ACTION {^$jobdest_xunluo[name]说道：「%*让我看看你巡逻得怎么样了} {
      dohalt {dzn};
      #DELAY {3} {#VARIABLE {idle} {-60}};
    };
    #ACTION {^$jobdest_xunluo[name]对着你竖起了右手大拇指，好样的。} {
      #VARIABLE {idle} {0};
      #VARIABLE {workingflag} {0};
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {jobdo_xunluo};
    };
    #CLASS jobresponseclass CLOSE;
  };
  #CLASS jobcheckclass CLOSE;
  ask $jobdest_xunluo[id] about 画印;
};
#NOP {搜索杀手};
#ALIAS {jobsearch_xunluo} {
  #VARIABLE {searchdo} {};
  #VARIABLE {jobkiller_xunluo} {0};
  #CLASS jobfightclass KILL;
  #CLASS jobfightclass OPEN;
  #ACTION {^一个人影突然从旁跳了出来，拦住你的去路} {
    #VARIABLE {jobkiller_xunluo} {1};
    stopwalk;
    #DELAY {1} {echo {checkattacker} {2}}
  } {1};
  #ACTION {^{设定环境变量：action \= \"checkattacker\"|你设定checkattacker为反馈信息}} {
    resonate {checkattacker};
    #VARIABLE {echots} {0};
    #IF {$jobkiller_xunluo == 0} {
      #DELAY {0.2} {
        execute {$searchdo};
        echo {checkattacker}
      };
    };
    #ELSE {
      startfight;
      kill attacker;
    };
  };
  #ACTION {^{蒙面客|黑衣人|神秘人|黑衣忍者|山贼|探子|死士}{转身几个起落就不见了|「啪」的一声倒在地上，挣扎着抽动了几下就死了}} {
    #CLASS jobfightclass KILL;
    dohalt {
      loc {gotonpc {$jobdest_xunluo[name]} {jobdraw_xunluo}}
    };
  } {1};
  #CLASS jobfightclass CLOSE;
  pfm_buff_normal;
  #VARIABLE {searchdo} {se;nw};
  gotoroom {1630} {
    execute {$searchdo};
    echo {checkattacker}
  };
};
#NOP {给殷无禄令牌};
#ALIAS {jobfinish_xunluo_askyin} {
  #CLASS jobfinishclass KILL;
  #CLASS jobfinishclass OPEN;
  #ACTION {^你向殷无禄打听有关『完成』的消息} {
    dohalt {
      give xunluo ling to yin wulu;
    }
  };
  #ACTION {^你身上没有这样东西} {
    #CLASS jobfinishclass KILL;
    dohalt {jobprepare};
  };
  #ACTION {^殷无禄给你一片巡逻令} {
    #CLASS jobfinishclass KILL;
    dohalt {
      gotonpc {韦一笑} {jobfinish_xunluo_askwei};
    };
  };
  #CLASS jobfinishclass CLOSE;
  ask yin wulu about 完成;
};
#NOP {给殷无禄令牌};
#ALIAS {jobfinish_xunluo_askwei} {
  #VARIABLE {jiuyangflag} {0};
  #CLASS jobfinishclass KILL;
  #CLASS jobfinishclass OPEN;
  #ACTION {^韦一笑在你的耳边悄声说道：你快去张教主那里一次，他好象有什么传闻要告诉你} {
    #VARIABLE {jiuyangflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkjiuyang\"|你设定checkjiuyang为反馈信息}} {
    #VARIABLE {echots} {0};
    #CLASS jobdoclass KILL;
    #CLASS jobfinishclass KILL;
    drop xunluo ling;
    #IF {$jiuyangflag == 1} {
      set env_jysg;
      #VARIABLE {env[jysg]} {YES};
    };
    dohalt {jobprepare}
  };
  #CLASS jobfinishclass CLOSE;
  give ling to wei yixiao;
  echo {checkjiuyang};
};
#NOP {放弃任务};
#ALIAS {jobfangqi_xunluo} {
  gotonpc {殷无禄} {jobfangqi_askyin};
};
#ALIAS {jobfangqi_askyin} {
  #CLASS jobfangqiclass KILL;
  #CLASS jobfangqiclass OPEN;
  #ACTION {^你向殷无禄打听有关『放弃』的消息} {
    #CLASS jobdoclass KILL;
    #CLASS jobfangqiclass KILL;
    dohalt {jobprepare};
  };
  #CLASS jobfangqiclass CLOSE;
  ask yin wulu about 放弃;
};