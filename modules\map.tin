#NOP {地图模块};
#NOP 键盘宏
#MACRO {\eOt}{w};
#MACRO {\eOv}{e};
#MACRO {\eOx}{n};
#MACRO {\eOr}{s};
#MACRO {\eOq}{sw};
#MACRO {\eOs}{se};
#MACRO {\eOw}{nw};
#MACRO {\eOy}{ne};
#MACRO {\eOu}{look};

#PATHDIR {n} {s} {1};
#PATHDIR {e} {w} {2};
#PATHDIR {s} {n} {4};
#PATHDIR {w} {e} {8};
#PATHDIR {u} {d} {16};
#PATHDIR {d} {u} {32};
#PATHDIR {ne} {sw} {3};
#PATHDIR {se} {nw} {6};
#PATHDIR {nw} {se} {9};
#PATHDIR {sw} {ne} {12};
#PATHDIR {nu} {sd} {17};
#PATHDIR {eu} {wd} {18};
#PATHDIR {su} {nd} {20};
#PATHDIR {wu} {ed} {24};
#PATHDIR {nd} {su} {33};
#PATHDIR {ed} {wu} {34};
#PATHDIR {sd} {nu} {36};
#PATHDIR {wd} {eu} {40};
#nop 自定义命令范围 1-63;
#PATHDIR {out} {enter} {61};
#PATHDIR {enter} {out} {62};

#NOP {将区域分为几个大陆};
#VARIABLE {common[blocks]} {
};
#NOP {房间出口通用序列};
#LIST {exitsequence} {create} {nw;n;ne;w;e;sw;s;se};
#NOP {初始化城市列表};
#LOOP {10000} {10056} {i} {
  #MAP goto {$i};
  #MAP get {ROOMNAME} {tempcityname};
  #MAP get {ROOMDESC} {tempcityroomname};
  #MAP get {ROOMAREA} {tempcityroomid};  
  #VARIABLE {cities[$tempcityname]} {
    {cityid}{$i} {cityroom}{$tempcityroomname} {cityroomid}{$tempcityroomid}
  };
};
#NOP {初始化地图npc};
#LOOP {1} {4000} {i} {
  #MAP goto {10051};
  #MAP AT {$i} {
    #MAP get {ROOMVNUM} {temproomid};
    #IF {$temproomid == 10051} {#CONTINUE;};
    #MAP get {ROOMNOTE} {temproomnote};
    #IF {"$temproomnote" == ""} {#CONTINUE;};
    #MAP get {ROOMNAME} {temproomname};
    #MAP get {ROOMAREA} {tempcityname};
    #FOREACH {$temproomnote[]} {n} {
      #VARIABLE {mapnpcs[$n]} {
        {city}{$tempcityname}{room}{$temproomname}{roomid}{$temproomid}
      };
    };
  };
};

#NOP {获取指定房间出口与给定的出口的匹配度和出口差异数统计信息，%1:房间vnum,%2:出口列表,%3:出口房间名称列表};
#FUNCTION getRoomStatistics {
  #LOCAL {tempexits} {};
  #MAP goto {%1};
  #MAP get {ROOMEXITS} {tempexits};
  #LOCAL {v} {0};
  #LOCAL {w} {0};
  #LOCAL {e} {0};
  #LOCAL {tempflag} {0};
  #NOP {处理房间出口顺序};
  #LIST {temproomexits} {clear} {};
  #FOREACH {$exitsequence[]} {x} {
    #IF {@contains{{%2}{$x}} > 0} {
      #LIST {temproomexits} {add} {$x};
    };
  };
  #LIST {temproomways} {create} {$%3[]};
  #FOREACH {*tempexits[]} {x} {
    #IF {@startWiths{{$x}{river_}} == 1} {
      #MATH {tempflag} {$tempflag + 1};
      #CONTINUE;
    };
    #NOP {解析后的路径};
    #LOCAL {y} {$x};
    #UNVARIABLE {templist};
    #LIST {templist} {create} {$x};
    #NOP {拦路NPC 开头为killnpc或者迷宫路径，取最后一个指令为方向,否则取第一个};
    #LOCAL {tempstep} {$templist[+1]};
    #IF {@startWiths{{$tempstep}{killnpc}} == 1 || @startWiths{{$tempstep}{matrix_}} == 1 || @startWiths{{$tempstep}{night_}} == 1 || @startWiths{{$tempstep}{open}} == 1 || @startWiths{{$tempstep}{pull}} == 1 || @startWiths{{$tempstep}{close}} == 1} {
      #LOCAL {y} {$templist[+&templist[]]};
    };
    #ELSE {
      #LOCAL {y} {$templist[+1]};
    };
    #NOP {出口是否存在};
    #IF {@contains{{%2}{$y}} > 0} {
      #MATH {v} {$v + 1};
    };
    #NOP {出口房间名是否存在};
    #LOCAL {vname} {@getRoomInfo{{$tempexits[$x]}{ROOMNAME}}};
    #LOCAL {vindex} {@contains{{temproomways}{$vname}}};
    #IF {$vindex > 0} {
      #MATH {w} {$w + 1};
      #LIST {temproomways} {delete} {$vindex};
    };
    #NOP {统计个方向出口房间名称的匹配};
    #LOCAL {pos} {@contains{{temproomexits}{$x}}};
    #IF {$pos > 0} {
      #IF {"$vname" == "$%3[+$pos]"} {
        #MATH {e} {$e + 1};
      };
    };
  };
  #LOCAL {vpercent} {@eval{$v * 100 / (&tempexits[] -$tempflag)}};
  #LOCAL {wpercent} {@eval{$w * 100 / (&tempexits[] -$tempflag)}};
  #LOCAL {diff} {@abs{@eval{&tempexits[] - &roomexits[] - $tempflag}}};
  #LOCAL {wpercent2} {@eval{$e * 100 /&roomexits[]}};
  #RETURN {$vpercent;$diff;$wpercent;$wpercent2};
};
#NOP {获取指定房间的信息,%1:vnum,%2房间字段};
#FUNCTION getRoomInfo {
  #MAP AT {%1} {#MAP get {%2} {result}};
};
#NOP {获取房间ID，%1:城市，%2:房间,%3出口列表,%4出口房间名列表。出口列表入参必须为变量名称，不能带$};
#FUNCTION getRoomId {
  #MAP list {%2} {ROOMAREA} {%1} {VARIABLE} {result};
  #IF {&result[] == 0} {
      #RETURN {-1};
  };
  #ELSEIF {&result[] == 1} {
    #RETURN {*result[+1]};
  };
  #ELSEIF {"%3" == ""} {
    #RETURN {*result[+1]};
  };
  #ELSE {
    #NOP {计算各房间与当前出口的匹配度并取最高的房间};
    #LOCAL {temproomid} {-1};
    #map get {ROOMVNUM} {temproomid};
    #LOCAL {matchroomid} {*result[+1]};
    #LOCAL {maxpercent} {0};
    #LOCAL {mindiff} {100};
    #LOCAL {maxwaypercent} {0};
    #LOCAL {maxseqpercent} {0};
    #FOREACH {*result[]} {e} {
      #LIST {tempdata} {create} {@getRoomStatistics{{$e}{%3}{%4}}};
      #IF {$__DEBUG__ == 1} {
        #SHOWME {<ffa>$e:出口匹配度 = $tempdata[+1],出口差异数 = $tempdata[+2],房间匹配度 = $tempdata[+3],房间顺序匹配度 = $tempdata[+4]};
      };
      #NOP {比较出口匹配度};
      #IF {$tempdata[+1] > $maxpercent} {
        #LOCAL {maxpercent} {$tempdata[+1]};
        #LOCAL {mindiff} {$tempdata[+2]};
        #LOCAL {maxwaypercent} {$tempdata[+3]};
        #LOCAL {maxseqpercent} {$tempdata[+4]};
        #LOCAL {matchroomid} {$e};
      };
      #ELSEIF {$tempdata[+1] == $maxpercent} {
        #NOP {比较出口差异数};
        #IF {$tempdata[+2] < $mindiff} {
          #LOCAL {mindiff} {$tempdata[+2]};
          #LOCAL {maxwaypercent} {$tempdata[+3]};
          #LOCAL {maxseqpercent} {$tempdata[+4]};
          #LOCAL {matchroomid} {$e};
        };
        #ELSEIF {$tempdata[+2] == $mindiff} {
          #NOP {比较出路房间名称匹配度};
          #IF {$tempdata[+3] > $maxwaypercent} {
            #LOCAL {maxwaypercent} {$tempdata[+3]};
            #LOCAL {maxseqpercent} {$tempdata[+4]};
            #LOCAL {matchroomid} {$e};
          };
          #ELSEIF {$tempdata[+3] == $maxwaypercent} {
            #NOP {比较房间顺序匹配度};
            #IF {$tempdata[+4] > $maxseqpercent} {
              #LOCAL {maxseqpercent} {$tempdata[+4]};
              #LOCAL {matchroomid} {$e};
            };
          };
        };
      };
      #IF {$maxpercent >= 100 && $mindiff == 0 && $maxwaypercent >= 100 && $maxseqpercent >= 100} {
        #BREAK;
      };
    };
    #IF {$__DEBUG__ == 1} {
      #SHOWME {<ffa>定位至 $matchroomid};
    };
    #MAP goto {$temproomid};
    #RETURN {$matchroomid};
  };
};
#NOP {获取下一个城市,p1:起点,p2:终点};
#FUNCTION getNextCity {
  #LOCAL {temproomid} {-1};
  #map get {ROOMVNUM} {temproomid};
  #MAP at {$cities[%1][cityid]} {#MAP find {$cities[%2][cityid]}};
  #LOCAL {temppath} {};
  #PATH save {forward} {temppath};
  #IF {"$temppath" == ""} {
    #RETURN {};
  };
  #LIST {citypath} {create} {$temppath};
  #RETURN {$citypath[+1]};
};
#NOP {获取两个城市间的路径,p1:起点,p2:终点};
#FUNCTION getCityPath {
  #LOCAL {temproomid} {-1};
  #map get {ROOMVNUM} {temproomid};
  #MAP at {$cities[%1][cityid]} {#MAP find {$cities[%2][cityid]}};
  #LOCAL {temppath} {};
  #PATH save {forward} {temppath};
  #IF {"$temppath" == ""} {
    #RETURN {};
  };
  #LIST {citypath} {create} {$temppath};
  #RETURN {$citypath};
};
#NOP {获取两个城市间的路径长度};
#FUNCTION getCityPathLength {
  #LOCAL {temproomid} {-1};
  #IF {"%1" == "%2"} {
    #RETURN {0};
  };
  #ELSE {
    #map get {ROOMVNUM} {temproomid};
    #MAP at {$cities[%1][cityid]} {#MAP find {$cities[%2][cityid]}};
    #LOCAL {temppath} {};
    #PATH save {forward} {temppath};
    #IF {"$temppath" == ""} {
      #RETURN {999};
    };
    #LIST {citypath} {create} {$temppath};
    #RETURN {&citypath[]};
  };
};
#NOP {获取行走路径,%1:起始房间id,%2:目标房间id};
#FUNCTION getWalkPath {
  #LOCAL {tempwalkpath} {};
  #MAP AT {%1} {#MAP find {%2}};
  #PATH save {forward} {tempwalkpath};
  #RETURN {$tempwalkpath};
};
#NOP {获取行走路径步数,%1:起始房间id,%2:目标房间id};
#FUNCTION getPathLength {
  #LOCAL {mytemppath} {@getWalkPath{{%1}{%2}}};
  #LIST {mytempsteps} {create} {$mytemppath};

  #RETURN {&mytempsteps[]};
};
#NOP {获取指定两个房间路径上的所有房间,%1-起始房间,%2-结束房间};
#FUNCTION getPathRooms {
  #VARIABLE {_temppath} {};
  #VARIABLE {_temprooms} {};
  #MAP AT {%1} {#MAP find {%2}};
  #PATH save {forward} {_temppath};
  #LIST {_tempsteps} {create} {$_temppath};
  #LIST {_hissteps} {clear} {};
  #VARIABLE {_start} {%1};
  #VARIABLE {_tempexits} {};
  #VARIABLE {_tempdir} {};
  #VARIABLE {cnt} {0};
  #WHILE {1} {
    #IF {"$_start" == "%2"} {
      #BREAK;
    };
    #VARIABLE {_tempdir} {};
    #MAP AT {$_start} {#MAP GET {ROOMEXITS} {_tempexits}};
    #FOREACH {*_tempexits[]} {_e} {
      #LOCAL {_curindex} {&_hissteps[]};
      #LOCAL {_ok} {1};
      #FOREACH {$_e} {seg} {
        #MATH {_curindex} {$_curindex + 1};
        #IF {@equal{{$seg}{$_tempsteps[+$_curindex]}} == 0} {
          #LOCAL {_ok} {0};
          #BREAK;
        };
      };
      #IF {$_ok == 1} {
        #VARIABLE {_tempdir} {$_e};
        #BREAK;
      };
    };
    #IF {"$_tempdir" == ""} {
      #BREAK;
    };
    #ELSE {
      #LIST {_hissteps} {add} {$_tempdir};
    };
    #VARIABLE {_start} {$_tempexits[$_tempdir]};
    #IF {"$_temprooms" == ""} {
      #CAT {_temprooms} {$_start};
    };
    #ELSE {
      #CAT {_temprooms} {;$_start};
    };
  };

  #RETURN {$_temprooms};
};
#NOP {获取指定npc所在房间};
#FUNCTION getNpcRoom {
  #MAP list {} {ROOMNOTE} {%1} {VARIABLE} {npclist};
};
#NOP {在指定城市查找指定名称的房间,%1:城市,%2:房间};
#FUNCTION findRooms {
  #IF {"%2" == ""} {
    #SHOWME {<ffa>缺少房间参数。};
    #LIST {result} {clear};
  };
  #ELSE {
    #LOCAL {_temprooms} {};
    #IF {"%1" == ""} {
      #MAP list {%2} {VARIABLE} {_temprooms};
    };
    #ELSE {
      #MAP list {%2} {ROOMAREA} {%1} {VARIABLE} {_temprooms};
    };
    #LIST {result} {create} {*_temprooms[]};
  };
};
#NOP {获取临近房间(含自身),%1:源房间列表,%2:深度};
#FUNCTION getNearRooms {
  #LOCAL {deep} {@eval{%2}};
  #IF {$deep == 0} {
    #LIST {result} {create} {$%1[]};
  };
  #ELSE {
    #LIST {openlist} {create} {$%1[]};
    #LIST {closelist} {clear} {};
    #LIST {pendinglist} {create};
    #LOOP 0 $deep {i} {
      #WHILE {&openlist[] > 0} {
        #LOCAL {temproomid} {$openlist[+1]};
        #MAP AT {$temproomid} {
          #LIST {tempexits} CLEAR;
          #MAP GET {ROOMEXITS} {tempexits};
          #FOREACH {$tempexits[]} {e} {
            #LIST {pendinglist} ADD {$e};
          };
        };
        #LIST {openlist} DELETE {1};
        #IF {@contains{{closelist}{$temproomid}} == 0} {
          #LIST {closelist} ADD {$temproomid};
        };
      };
      #LIST {openlist} create {$pendinglist[]};
    };
    #LIST {result} {create} {$closelist[]};
  };
};
#NOP {获取临近房间(含自身),%1:源房间列表,%2:深度};
#FUNCTION getNearRoomsEx {
  #LOCAL {deep} {@eval{%2}};
  #IF {$deep == 0} {
    #LIST {result} {create} {$%1[]};
  };
  #ELSE {
    #LIST {closelist} {create} {$%1[]};
    #NOP {遍历每个源房间按深度搜索};
    #VARIABLE {roomslot} {};
    #NOP {这里需要先将传入的列表赋值给roomslot};
    #FOREACH {$%1[]} {r} {
      #VARIABLE {roomslot[$r]} {$r};
    };
    #FOREACH {$%1[]} {r} {
      #MAP AT {$r} {#MAP LIST {distance} {@eval{1 + 2 * $deep}} {VARIABLE} {temproomlist}};
      #NOP {按距离搜索出来的list其下标是房间编号，排序后变成从1开始的顺序号，这里必须用元素的属性来保存房间编号};
      #LOOP 1 &temproomlist[] {i} {
        #VARIABLE {temproomlist[+$i][vnum]} {*temproomlist[+$i]};
      };
      #NOP {根据distance对list进行排序};
      #VARIABLE {sortedroomlist} {@sort{{temproomlist}{distance}}};
      #NOP {遍历成员将房间编号加入result};
      #LOOP 1 &sortedroomlist[] {i} {
        #LOCAL {temproomid} {$sortedroomlist[+$i][vnum]};
        #NOP {排除已添加房间};
        #IF {"$roomslot[$temproomid]" != ""} {
          #CONTINUE;
        };
        #VARIABLE {roomslot[$temproomid]} {$temproomid};
        #LIST {closelist} {add} {$temproomid};
      };
    };
    #RETURN {$closelist};
  };
};
#NOP {获取安全出口,%1:房间id,%2:要排除的出口roomid};
#FUNCTION getSafeExit {
  #VARIABLE {tempexits} {@getRoomInfo{{%1}{ROOMEXITS}}};
  #LIST {temproomlist} {clear};
  #NOP {几个特殊地点};
  #IF {"@getRoomInfo{{%1}{ROOMNAME}}" == "林间空地"} {
    #RETURN {2165};
  };
  #IF {"@getRoomInfo{{%1}{ROOMNAME}}" == "归云庄前"} {
    #RETURN {2781};
  };
  #IF {"%1" == "1485"} {
    #RETURN {1486};
  };
  #IF {"%1" == "3436"} {
    #RETURN {3437};
  };
  #FOREACH {*tempexits[]} {e} {
    #NOP {优先排除u};
    #IF {"$e" == "u"} {
      #CONTINUE;
    };
    #NOP {迷宫房间出口可能通向自己};
    #IF {%e == %1} {
      #CONTINUE;
    };
    #IF {@startWiths{{$e}{uwwp}} == 1} {
      #CONTINUE;
    };
    #IF {@startWiths{{$e}{river_}} == 1} {
      #CONTINUE;
    };
    #IF {@startWiths{{$e}{matrix_}} == 1} {
      #CONTINUE;
    };
    #IF {@startWiths{{$e}{night_}} == 1} {
      #CONTINUE;
    };
    #IF {@startWiths{{$e}{killnpc}} == 1} {
      #CONTINUE;
    };
    #IF {@startWiths{{$e}{open}} == 1} {
      #CONTINUE;
    };
    #IF {@startWiths{{$e}{pull}} == 1} {
      #CONTINUE;
    };
    #IF {@startWiths{{$e}{push}} == 1} {
      #CONTINUE;
    };
    #IF {"%2" == "$tempexits[$e]"} {
      #CONTINUE;
    };
    #NOP {排除不可访问的房间};
    #IF {"@getRoomInfo{{$tempexits[$e]}{ROOMDATA}}" != ""} {
      #CONTINUE;
    };
    #IF {"@getRoomInfo{{$e}{ROOMNAME}}" == "大戈壁"} {
      #CONTINUE;
    };
    #IF {"@getRoomInfo{{$e}{ROOMNAME}}" == "南疆沙漠"} {
      #CONTINUE;
    };
    #IF {"@getRoomInfo{{%1}{ROOMNAME}}" != "青城沙漠" && "@getRoomInfo{{$e}{ROOMNAME}}" == "青城沙漠"} {
      #CONTINUE;
    };
    #NOP {峨嵋冷杉林不要排除小竹林出口ne};
    #IF {"@getRoomInfo{{%1}{ROOMNAME}}" == "冷杉林" && "$e" == "ne"} {
      #CONTINUE;
    };
    #NOP {塘沽城中心广场排除se出口};
    #IF {"@getRoomInfo{{%1}{ROOMNAME}}" == "中心广场" && "$e" == "se"} {
      #CONTINUE;
    };
    #NOP {天山断魂崖不去山涧};
    #IF {"@getRoomInfo{{%1}{ROOMNAME}}" == "断魂崖" && "$e" == "wd"} {
      #CONTINUE;
    };
    #LIST {temproomlist} {add} {$tempexits[$e]};
  };
  #NOP {为了保证一定的变化,这里取随机的};
  #IF {&temproomlist[] == 0} {
    #RETURN {$tempexits[+@rnd{{1}{&tempexits[]}}]};
  };
  #ELSE {
    #RETURN {$temproomlist[+@rnd{{1}{&temproomlist[]}}]};
  };
};
#NOP {回溯寻找房间,%1:起点,%2:终点,%3:回溯的房间名称,%4:最多回溯的房间数};
#FUNCTION getTracebackRoom {

};
#NOP {是否名称唯一的房间,%1:城市,%2:房间};
#FUNCTION isUniqueRoom {
  #LOCAL {temprooms} {@findRooms{{%1}{%2}}};
  #IF {"$temprooms" == ""} {
    #RETURN {0};
  };
  #ELSEIF {&temprooms[] > 1} {
    #RETURN {0};
  };
  #ELSE {
    #RETURN {1};
  };
};
#NOP {是否定位意义上的唯一房间};
#FUNCTION isUniqueLocationRoom {
  
};
#NOP {处理房间(城市)间的连通%1:房间1,%2:房间2,%3:开关};
#ALIAS {setroomconnect} {
  #LOCAL {temproomid} {-1};
  #MAP GET {ROOMVNUM} {temproomid};
  #LOCAL {pathweight} {1.00};
  #IF {%3 == 0} {
    #LOCAL {pathweight} {10000.00};
  };
  #ELSE {
    #LOCAL {pathweight} {1.00};
  };
  #LOCAL {temproomname1} {};
  #MAP GOTO {%1};
  #MAP GET {ROOMNAME} {temproomname1};
  #LOCAL {temproomname2} {};
  #MAP GOTO {%2};
  #MAP GET {ROOMNAME} {temproomname2};
  #MAP EXIT {$temproomname1} {WEIGHT} {$pathweight};
  #MAP GOTO {%1};
  #MAP EXIT {$temproomname2} {WEIGHT} {$pathweight};
  
  #MAP GOTO {$temproomid};
};

#NOP {解析地址,%1:地址描述,%1:是否为简短描述};
#ALIAS parseaddress {
  #LOCAL {address} {%1};
  #VARIABLE {tempcity} {};
  #VARIABLE {temproom} {};

  #IF {@startWiths{{$address}{中原神州}} > 0} {
    #REGEXP {$address} {^中原神州%*} {
      #VARIABLE {tempcity} {扬州城};
      #VARIABLE {temproom} {&1};
    };
  };
  #ELSEIF {@startWiths{{$address}{中原}} > 0} {
    #REGEXP {$address} {^中原%*} {
      #VARIABLE {tempcity} {成都城};
      #VARIABLE {temproom} {&1};
    };
  };
  #ELSE {
    #IF {"%2" == ""} {
      #REGEXP {$address} {^扬州城%*} {
        #VARIABLE {tempcity} {扬州城};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^扬州城%*} {
        #VARIABLE {tempcity} {扬州城};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^扬州城%*} {
        #VARIABLE {tempcity} {扬州城};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^长乐帮%*} {
        #VARIABLE {tempcity} {长乐帮};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^襄阳城%*} {
        #VARIABLE {tempcity} {襄阳城};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^襄阳郊外%*} {
        #VARIABLE {tempcity} {襄阳城};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^成都郊外%*} {
        #VARIABLE {tempcity} {成都城};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^成都城%*} {
        #VARIABLE {tempcity} {成都城};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^铁掌山%*} {
        #VARIABLE {tempcity} {铁掌山};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^南阳城%*} {
        #VARIABLE {tempcity} {南阳城};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^萧府%*} {
        #VARIABLE {tempcity} {黄河流域};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^黄河流域%*} {
        #VARIABLE {tempcity} {黄河流域};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^长安城%*} {
        #VARIABLE {tempcity} {长安城};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^泰山%*} {
        #VARIABLE {tempcity} {泰山};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^兰州城%*} {
        #VARIABLE {tempcity} {兰州城};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^峨嵋山%*} {
        #VARIABLE {tempcity} {峨嵋山};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^无量山%*} {
        #VARIABLE {tempcity} {无量山};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^玉虚观%*} {
        #VARIABLE {tempcity} {大理城};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^大理城%*} {
        #VARIABLE {tempcity} {大理城};
        #VARIABLE {temproom} {&1};
      };    
      #REGEXP {$address} {^大理城东%*} {
        #VARIABLE {tempcity} {大理城东};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^大理城南%*} {
        #VARIABLE {tempcity} {大理城南};
        #VARIABLE {temproom} {&1};
      };    
      #REGEXP {$address} {^大理城西%*} {
        #VARIABLE {tempcity} {大理城西};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^大理城北%*} {
        #VARIABLE {tempcity} {大理城北};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^大理皇宫%*} {
        #VARIABLE {tempcity} {大理城};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^大理王府%*} {
        #VARIABLE {tempcity} {大理城};
        #VARIABLE {temproom} {&1};
      };    
      #REGEXP {$address} {^桃源县%*} {
        #VARIABLE {tempcity} {桃源县};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^天龙寺%*} {
        #VARIABLE {tempcity} {天龙寺};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^丝绸之路%*} {
        #VARIABLE {tempcity} {星宿海};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^星宿海%*} {
        #VARIABLE {tempcity} {星宿海};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^天山%*} {
        #VARIABLE {tempcity} {天山};
        #VARIABLE {temproom} {&1};
      };     
      #REGEXP {$address} {^逍遥派%*} {
        #VARIABLE {tempcity} {逍遥派};
        #VARIABLE {temproom} {&1};
      };     
      #REGEXP {$address} {^回疆%*} {
        #VARIABLE {tempcity} {回疆};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^大草原%*} {
        #VARIABLE {tempcity} {回疆};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^明教%*} {
        #VARIABLE {tempcity} {明教};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^昆仑山%*} {
        #VARIABLE {tempcity} {昆仑山};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^昆仑翠谷%*} {
        #VARIABLE {tempcity} {昆仑山};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^大雪山%*} {
        #VARIABLE {tempcity} {大雪山};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^苏州城%*} {
        #VARIABLE {tempcity} {苏州城};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^佛山镇%*} {
        #VARIABLE {tempcity} {佛山镇};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^福州城%*} {
        #VARIABLE {tempcity} {福州城};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^杭州城%*} {
        #VARIABLE {tempcity} {杭州城};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^梅庄%*} {
        #VARIABLE {tempcity} {梅庄};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^嘉兴城%*} {
        #VARIABLE {tempcity} {嘉兴城};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^姑苏慕容%*} {
        #VARIABLE {tempcity} {姑苏慕容};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^归云庄%*} {
        #VARIABLE {tempcity} {归云庄};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^牛家村%*} {
        #VARIABLE {tempcity} {牛家村};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^莆田少林%*} {
        #VARIABLE {tempcity} {莆田少林};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^宁波城%*} {
        #VARIABLE {tempcity} {宁波城};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^丐帮%*} {
        #VARIABLE {tempcity} {丐帮};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^燕子坞%*} {
        #VARIABLE {tempcity} {燕子坞};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^曼佗罗山庄%*} {
        #VARIABLE {tempcity} {曼佗罗山庄};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^沧州城%*} {
        #VARIABLE {tempcity} {沧州城};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^神龙岛%*} {
        #VARIABLE {tempcity} {神龙岛};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^平定州%*} {
        #VARIABLE {tempcity} {平定州};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^黑木崖%*} {
        #VARIABLE {tempcity} {黑木崖};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^恒山%*} {
        #VARIABLE {tempcity} {恒山};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^塘沽城%*} {
        #VARIABLE {tempcity} {塘沽城};
        #VARIABLE {temproom} {&1};
      };   
      #REGEXP {$address} {^华山%*} {
        #VARIABLE {tempcity} {华山};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^华山村%*} {
        #VARIABLE {tempcity} {华山村};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^终南山%*} {
        #VARIABLE {tempcity} {终南山};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^全真教%*} {
        #VARIABLE {tempcity} {全真教};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^武当山%*} {
        #VARIABLE {tempcity} {武当山};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^武当后山%*} {
        #VARIABLE {tempcity} {武当山};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^嵩山%*} {
        #VARIABLE {tempcity} {嵩山};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^嵩山少林%*} {
        #VARIABLE {tempcity} {嵩山少林};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^少林寺%*} {
        #VARIABLE {tempcity} {嵩山少林};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^绝情谷%*} {
        #VARIABLE {tempcity} {绝情谷};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^桃花岛%*} {
        #VARIABLE {tempcity} {桃花岛};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^蒙古%*} {
        #VARIABLE {tempcity} {蒙古};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^柳宗镇%*} {
        #VARIABLE {tempcity} {襄阳城};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^伊犁城%*} {
        #VARIABLE {tempcity} {星宿海};
        #VARIABLE {temproom} {&1};
      }; 
      #REGEXP {$address} {^苗疆%*} {
        #VARIABLE {tempcity} {苗疆};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^灵蛇岛%*} {
        #VARIABLE {tempcity} {灵蛇岛};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^蝴蝶谷%*} {
        #VARIABLE {tempcity} {蝴蝶谷};
        #VARIABLE {temproom} {&1};
      };
      #IF {"$tempcity"=="大理城东" && "$temproom" == "大街"} {
        #VARIABLE {tempcity} {大理城};
        #VARIABLE {temproom} {东大街};
      };
      #IF {"$tempcity"=="大理城西" && "$temproom" == "大街"} {
        #VARIABLE {tempcity} {大理城};
        #VARIABLE {temproom} {西大街};
      };
      #IF {"$tempcity"=="大理城南" && "$temproom" == "大街"} {
        #VARIABLE {tempcity} {大理城};
        #VARIABLE {temproom} {南大街};
      };
      #IF {"$tempcity"=="大理城北" && "$temproom" == "大街"} {
        #VARIABLE {tempcity} {大理城};
        #VARIABLE {temproom} {北大街};
      };
      #IF {"$tempcity"=="大理城东" && "$temproom" == "门"} {
        #VARIABLE {tempcity} {大理城};
        #VARIABLE {temproom} {东门};
      };
      #IF {"$tempcity"=="大理城西" && "$temproom" == "门"} {
        #VARIABLE {tempcity} {大理城};
        #VARIABLE {temproom} {西门};
      };
      #IF {"$tempcity"=="大理城南" && "$temproom" == "门"} {
        #VARIABLE {tempcity} {大理城};
        #VARIABLE {temproom} {南门};
      };
      #IF {"$tempcity"=="大理城北" && "$temproom" == "门"} {
        #VARIABLE {tempcity} {大理城};
        #VARIABLE {temproom} {北门};
      };
      #IF {"$tempcity" == "大理城东" || "$tempcity" == "大理城西" || "$tempcity" == "大理城南" || "$tempcity" == "大理城北"} {
        #VARIABLE {tempcity} {大理城};
      };
    };
    #ELSE {
      #REGEXP {$address} {^扬州%*} {
        #VARIABLE {tempcity} {扬州城};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^长乐帮%*} {
        #VARIABLE {tempcity} {长乐帮};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^襄阳%*} {
        #VARIABLE {tempcity} {襄阳城};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^襄阳郊外%*} {
        #VARIABLE {tempcity} {襄阳城};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^成都%*} {
        #VARIABLE {tempcity} {成都城};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^成都郊外%*} {
        #VARIABLE {tempcity} {成都城};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^铁掌山%*} {
        #VARIABLE {tempcity} {铁掌山};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^南阳%*} {
        #VARIABLE {tempcity} {南阳城};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^萧府%*} {
        #VARIABLE {tempcity} {黄河流域};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^黄河流域%*} {
        #VARIABLE {tempcity} {黄河流域};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^长安%*} {
        #VARIABLE {tempcity} {长安城};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^泰山%*} {
        #VARIABLE {tempcity} {泰山};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^兰州%*} {
        #VARIABLE {tempcity} {兰州城};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^峨嵋山%*} {
        #VARIABLE {tempcity} {峨嵋山};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^无量山%*} {
        #VARIABLE {tempcity} {无量山};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^玉虚观%*} {
        #VARIABLE {tempcity} {大理城};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^大理城%*} {
        #VARIABLE {tempcity} {大理城};
        #VARIABLE {temproom} {&1};
      };    
      #REGEXP {$address} {^大理城东%*} {
        #VARIABLE {tempcity} {大理城东};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^大理城南%*} {
        #VARIABLE {tempcity} {大理城南};
        #VARIABLE {temproom} {&1};
      };    
      #REGEXP {$address} {^大理城西%*} {
        #VARIABLE {tempcity} {大理城西};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^大理城北%*} {
        #VARIABLE {tempcity} {大理城北};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^大理皇宫%*} {
        #VARIABLE {tempcity} {大理城};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^大理王府%*} {
        #VARIABLE {tempcity} {大理城};
        #VARIABLE {temproom} {&1};
      };    
      #REGEXP {$address} {^桃源县%*} {
        #VARIABLE {tempcity} {桃源县};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^天龙寺%*} {
        #VARIABLE {tempcity} {天龙寺};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^丝绸之路%*} {
        #VARIABLE {tempcity} {星宿海};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^星宿海%*} {
        #VARIABLE {tempcity} {星宿海};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^天山%*} {
        #VARIABLE {tempcity} {天山};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^逍遥派%*} {
        #VARIABLE {tempcity} {逍遥派};
        #VARIABLE {temproom} {&1};
      };      
      #REGEXP {$address} {^回疆%*} {
        #VARIABLE {tempcity} {回疆};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^大草原%*} {
        #VARIABLE {tempcity} {回疆};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^明教%*} {
        #VARIABLE {tempcity} {明教};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^昆仑山%*} {
        #VARIABLE {tempcity} {昆仑山};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^昆仑翠谷%*} {
        #VARIABLE {tempcity} {昆仑山};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^大雪山%*} {
        #VARIABLE {tempcity} {大雪山};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^苏州%*} {
        #VARIABLE {tempcity} {苏州城};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^佛山%*} {
        #VARIABLE {tempcity} {佛山镇};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^福州%*} {
        #VARIABLE {tempcity} {福州城};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^杭州%*} {
        #VARIABLE {tempcity} {杭州城};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^梅庄%*} {
        #VARIABLE {tempcity} {梅庄};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^嘉兴%*} {
        #VARIABLE {tempcity} {嘉兴城};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^姑苏慕容%*} {
        #VARIABLE {tempcity} {姑苏慕容};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^归云庄%*} {
        #VARIABLE {tempcity} {归云庄};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^牛家村%*} {
        #VARIABLE {tempcity} {牛家村};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^莆田少林%*} {
        #VARIABLE {tempcity} {莆田少林};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^宁波%*} {
        #VARIABLE {tempcity} {宁波城};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^丐帮分舵%*} {
        #VARIABLE {tempcity} {丐帮};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^燕子坞%*} {
        #VARIABLE {tempcity} {燕子坞};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^曼佗罗山庄%*} {
        #VARIABLE {tempcity} {曼佗罗山庄};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^沧州%*} {
        #VARIABLE {tempcity} {沧州城};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^神龙岛%*} {
        #VARIABLE {tempcity} {神龙岛};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^平定州%*} {
        #VARIABLE {tempcity} {平定州};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^黑木崖%*} {
        #VARIABLE {tempcity} {黑木崖};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^恒山%*} {
        #VARIABLE {tempcity} {恒山};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^塘沽%*} {
        #VARIABLE {tempcity} {塘沽城};
        #VARIABLE {temproom} {&1};
      };   
      #REGEXP {$address} {^华山%*} {
        #VARIABLE {tempcity} {华山};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^华山村%*} {
        #VARIABLE {tempcity} {华山村};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^终南山%*} {
        #VARIABLE {tempcity} {终南山};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^全真教%*} {
        #VARIABLE {tempcity} {全真教};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^武当山%*} {
        #VARIABLE {tempcity} {武当山};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^武当后山%*} {
        #VARIABLE {tempcity} {武当山};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^嵩山%*} {
        #VARIABLE {tempcity} {嵩山};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^嵩山少林%*} {
        #VARIABLE {tempcity} {嵩山少林};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^少林寺%*} {
        #VARIABLE {tempcity} {嵩山少林};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^绝情谷%*} {
        #VARIABLE {tempcity} {绝情谷};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^桃花岛%*} {
        #VARIABLE {tempcity} {桃花岛};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^蒙古%*} {
        #VARIABLE {tempcity} {蒙古};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^柳宗镇%*} {
        #VARIABLE {tempcity} {襄阳城};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^伊犁%*} {
        #VARIABLE {tempcity} {星宿海};
        #VARIABLE {temproom} {&1};
      }; 
      #REGEXP {$address} {^苗疆%*} {
        #VARIABLE {tempcity} {苗疆};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^灵蛇岛%*} {
        #VARIABLE {tempcity} {灵蛇岛};
        #VARIABLE {temproom} {&1};
      };
      #REGEXP {$address} {^蝴蝶谷%*} {
        #VARIABLE {tempcity} {蝴蝶谷};
        #VARIABLE {temproom} {&1};
      };
      #IF {"$tempcity"=="大理城东" && "$temproom" == "大街"} {
        #VARIABLE {tempcity} {大理城};
        #VARIABLE {temproom} {东大街};
      };
      #IF {"$tempcity"=="大理城西" && "$temproom" == "大街"} {
        #VARIABLE {tempcity} {大理城};
        #VARIABLE {temproom} {西大街};
      };
      #IF {"$tempcity"=="大理城南" && "$temproom" == "大街"} {
        #VARIABLE {tempcity} {大理城};
        #VARIABLE {temproom} {南大街};
      };
      #IF {"$tempcity"=="大理城北" && "$temproom" == "大街"} {
        #VARIABLE {tempcity} {大理城};
        #VARIABLE {temproom} {北大街};
      };
      #IF {"$tempcity"=="大理城东" && "$temproom" == "门"} {
        #VARIABLE {tempcity} {大理城};
        #VARIABLE {temproom} {东门};
      };
      #IF {"$tempcity"=="大理城西" && "$temproom" == "门"} {
        #VARIABLE {tempcity} {大理城};
        #VARIABLE {temproom} {西门};
      };
      #IF {"$tempcity"=="大理城南" && "$temproom" == "门"} {
        #VARIABLE {tempcity} {大理城};
        #VARIABLE {temproom} {南门};
      };
      #IF {"$tempcity"=="大理城北" && "$temproom" == "门"} {
        #VARIABLE {tempcity} {大理城};
        #VARIABLE {temproom} {北门};
      };
      #IF {"$tempcity" == "大理城东" || "$tempcity" == "大理城西" || "$tempcity" == "大理城南" || "$tempcity" == "大理城北"} {
        #VARIABLE {tempcity} {大理城};
      };
    };
  };
};
#NOP {测试路径,%1:起始房间,%2:目标房间,如未提供%2,则使用当前房间为起始房间,%1为目标房间};
#ALIAS {printpath} {
  #IF {"%2" == ""} {
    #SHOWME {<aff>@getWalkPath{{$roomid}{%1}}};
  };
  #ELSE {
    #SHOWME {<aff>@getWalkPath{{%1}{%2}}};
  };
} {5};
#SHOWME {<fac>@padRight{{地图}{12}}<fac> <cfa>模块加载完毕<cfa>};