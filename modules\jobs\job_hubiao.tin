#NOP {福州护镖任务模块};
#ALIAS {jobgo_hubiao} {
  checkteam {
    gotonpc {林震南} {
      startfull {jobask_husong}
    }
  };
};
#NOP {队伍集合接收任务};
#ALIAS {jobmuster_hubiao} {
  #IF {$conf[team][leader] == 1} {
    teammuster_leader {$conf[team][partner]} {jobask_qqll};
  };
  #ELSE {
    teammuster_member {$conf[team][partner]} {wwp};
  };
};
#ALIAS {jobask_hubiao} {
  #VARIABLE {joblocation} {};
  #VARIABLE {jobnpc_owner} {};
  #LIST {hubiaopath} {create} {};
  #CLASS jobrequestclass KILL;
  #CLASS jobrequestclass OPEN;
  #ACTION {^你向林震南打听有关『护镖』的消息。} {
    #VARIABLE {idle} {0};
    #CLASS jobresponseclass KILL;
    #CLASS jobresponseclass OPEN;     
    #ACTION {^林震南说道：「你来晚了已经有人接下这支镖了} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
    };
    #ACTION {^林震南说道：「一直护镖很辛苦的，我看这位} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
    };
    #ACTION {^林震南说道：「请护送这一笔镖银到%*的%*手中} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      #VARIABLE {joblocation} {%%%1};
      #VARIABLE {jobnpc_owner} {%%%2};
      parsejoblocation {$joblocation} {jobdo_hubiao} {
        joblog {未能解析地址【$joblocation】。};
        jobfangqi_hubiao
      } {} {1}
      dohalt {jobdo_hubiao};
    };
    #CLASS jobresponseclass CLOSE;     
  };
  #CLASS jobrequestclass CLOSE;
  time;
  cond;
  ask lin zhennan about 护镖;
};
#ALIAS {jobdo_hubiao} {
  #CLASS jobdoclass KILL;
  #CLASS jobdoclass OPEN;
  #CLASS jobdoclass CLOSE;
  #LIST {hubiaopath} {create} {@getWalkPath{{$rooomid}{$jobroomlist[+1]}}};
};
#ALIAS {jobdriver_hubiao} {
};
#ALIAS {jobcheckowner_hubiao} {
};
#ALIAS {jobfight_hubiao} {
};
#ALIAS {jobfinish_hubiao} {
};
#ALIAS {jobfangqi_hubiao} {
};
#ALIAS {jobclear_hubiao} {
  #VARIABLE {jobnpc_owner} {};
  #VARIABLE {joblocation} {};
  #VARIABLE {jobrober_count} {0};
  #VARIABLE {jobstart_ts} {0};
};