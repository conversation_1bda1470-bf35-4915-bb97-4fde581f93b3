#NOP {少林教和尚任务模块};
#NOP {少林教和尚位置};
#VARIABLE {env[monkroomid]} {0}
#ALIAS {jobgo_tm} {
  sl_guilty {
    gotodo {嵩山少林} {2507} {startfull {jobask_tm_finddashi}}
  };
};
#ALIAS {jobask_tm_finddashi} {
  #LIST {jobroomlist} {create} {$dashiwheres};
  #CLASS jobrequestclass KILL;
  #CLASS jobrequestclass OPEN;
  #ACTION {^这里没有 xuancan dashi} {
    jobnextroom {follow xuancan dashi} {jobgo_tm};
  };
  #ACTION {^你决定跟随玄惭大师一起行动} {
    #CLASS jobrequestclass KILL;
    jobask_tm;
  };
  #CLASS jobrequestclass CLOSE;
  follow none;
  #IF {$env[guilty] == 0} {
    jobnextroom {follow xuancan dashi} {faint};
  };
  #ELSE {
    sl_guilty {gotodo {嵩山少林} {罗汉堂} {jobask_tm_finddashi}};
  };
};
#ALIAS {jobask_tm} {
  #CLASS jobrequestclass KILL;
  #CLASS jobrequestclass OPEN;
  #ACTION {^这里没有这个人。} {
    #CLASS jobrequestclass KILL;
    jobgo_tm;
  };
  #ACTION {^你向玄惭大师打听有关『罗汉堂值勤』的消息。} {
    #CLASS jobresponseclass KILL;
    #CLASS jobresponseclass OPEN;
    #ACTION {^玄惭大师说道：「你刚才不是已经问过了吗} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {jobdo_tm};
    };
    #ACTION {^玄惭大师说道：「现在暂时没有适合你的工作} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {jobask_tm};
    };
    #ACTION {^玄惭大师说道：「你刚训练武僧结束，还是先休息一会吧} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {jobprepare};
    };
    #ACTION {^玄惭大师说道：「%*你累犯数戒，身带重罪，我如何能准许你在罗汉堂效力} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {sl_guilty {jobgo_tm}};
    };
    #ACTION {^玄惭说道：好吧，你就在罗汉堂里训练武僧吧，若有外敌入侵，你们负担着护寺重任} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      follow none;
      dohalt {jobdo_tm};
    };
    #CLASS jobresponseclass CLOSE;
  };
  #CLASS jobrequestclass CLOSE;
  ask xuancan dashi about 罗汉堂值勤;
};
#ALIAS {jobdo_tm} {
  #IF {$env[monkroomid] != 0} {
    gotoroom {$env[monkroomid]} {jobdo_tm_checkmonk};
  };
  #ELSE {
    jobdo_tm_findmonk;
  };
};
#ALIAS {jobdo_tm_findmonk} {
  #LIST {jobroomlist} {create} {$monkwheres};
  jobnextroom {jobdo_tm_checkmonk} {startfull {jobdo_tm_findmonk} {1}};
};
#ALIAS {jobdo_tm_checkmonk} {
  #VARIABLE {teachflag} {0};
  #VARIABLE {motoulocation} {};
  #CLASS jobdoclass KILL;
  #CLASS jobdoclass OPEN;
  #ACTION {^你双手抱拳，对圆%*和尚作了个揖道：这位大师请了} {
    ask monk about 武技;
    echo {checkmotou};
  };
  #ACTION {^你要对谁做这个动作} {
    #CLASS jobdoclass KILL;
    jobnextroom {jobdo_tm_checkmonk} {startfull {jobdo_tm_findmonk} {1}}
  };
  #ACTION {^你还是先去跟玄惭大师打声招呼吧。} {
    #VARIABLE {teachflag} {5};
  };
  #ACTION {^你问我想学什么了吗} {
    #VARIABLE {teachflag} {1};
  };
  #ACTION {^圆%*和尚说道：「小僧在学%*，已经练到%*级，请你指点} {
    #VARIABLE {teachflag} {0};
  };
  #ACTION {^圆%*和尚说道：「我正由%*教着呢} {
    #VARIABLE {teachflag} {2};
  };
  #ACTION {^你的修为还不如我呢，还想教我} {
    #VARIABLE {teachflag} {3};
  };
  #ACTION {^圆%*和尚「啪」的一声倒在地上，挣扎着抽动了几下就死了} {
    #CLASS jobdoclass KILL;
    jobnextroom {jobdo_tm_checkmonk} {startfull {jobdo_tm_findmonk} {1}};
  };
  #ACTION {^圆%*和尚说道：「%*在你旁边，你无法专心指点} {
    say 麻烦让一让;
  };
  #ACTION {^你尽心竭力，对%*的道理} {
    #IF {$env[monkroomid] != $roomid} {
      #VARIABLE {env[monkroomid]} {$roomid};
      set env_monkroomid $env[monkroomid];
    };
  };
  #ACTION {^圆%*和尚神情振奋，一声大叫向%*奔去} {
    #VARIABLE {teachflag} {4};
    #VARIABLE {motoulocation} {%%2};
  };
  #ACTION {^{设定环境变量：action \= \"checkmotou\"|你设定checkmotou为反馈信息}} {
    #VARIABLE {echots} {0};
    #VARIABLE {idle} {0};
    #DELAY {1} {
      dohalt {
        #SWITCH {$teachflag} {
          #CASE {1} {
            ask monk about 武技;
            echo {checkmotou};
          };
          #CASE {2} {
            #CLASS jobdoclass KILL;
            jobnextroom {jobdo_tm_checkmonk} {startfull {jobdo_tm_findmonk} {1}}
          };
          #CASE {3} {kill monk};
          #CASE {4} {
            #CLASS jobdoclass KILL;
            jobfight_tm_motou {$motoulocation};
          };
          #CASE {5} {
            #CLASS jobdoclass KILL;
            jobprepare
          };
          #DEFAULT {
            yun jing;
            teach monk;
            echo {checkmotou};
          };
        };
      };
    };
  };
  #CLASS jobdoclass CLOSE;
  hi monk;
};
#NOP {位置};
#ALIAS {jobfight_tm_motou} {
  #CLASS jobdoclass KILL;
  #CLASS jobdoclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkmotou\"|你设定checkmotou为反馈信息}} {
    #VARIABLE {echots} {0};
    #IF {"$roomthings[邪道魔头]" == ""} {
      jobnextroom {id here;echo {checkmotou}} {
        #CLASS jobdoclass KILL;
        jobprepare;
      };
    };
    #ELSE {
      startfight;
      kill motou;
    };
  };
  #ACTION {^邪道魔头「啪」的一声倒在地上，挣扎着抽动了几下就死了} {
    dohalt {
      stopfight;
      get gold from corpse;
      get muou from corpse;
      i;
      echo {checkmuou};
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkmuou\"|你设定checkmuou为反馈信息}} {
    #VARIABLE {echots} {0};
    #CLASS jobdoclass KILL;
    #IF {@carryqty{muou} == 0} {
      jobdo_tm;
    };
    #ELSE {
      gotonpc {玄慈大师} {
        give muou to xuanci dashi;
        runwait {jobdo_tm};
      };
    };
  };
  #ACTION {^邪道魔头道：他奶奶的，老子还有事，下回再来烧寺！} {
    #CLASS jobdoclass KILL;
    dohalt {
      stopfight;
      jobdo_tm;
    };
  };
  #CLASS jobdoclass CLOSE;
  #IF {"$motouwheres[%1]"} {
    log {教和尚地点【%1】不明} {map}
  };
  #LIST {jobroomlist} {create} {$motouwheres[%1]};
  jobnextroom {id here;echo {checkmotou}} {
    #CLASS jobdoclass KILL;
    jobprepare;
  };
};
#ALIAS {jobclear_tm} {
  #VARIABLE {joblocation} {};
  #LIST {jobroomlist} {clear};
  #VARIABLE {jobfight_ts} {0};
  #VARIABLE {jobstart_ts} {0};
};