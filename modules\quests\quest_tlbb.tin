#NOP {越女剑法,%1:后续指令};
#ALIAS {goquest_tlbb} {
  #VARIABLE {questmodule} {天龙八部};
  #SWITCH {$questlist[$questmodule][laststep]} {
    #CASE {0} {
      gotonpc {段正淳} {tlbb_chuanwen_askduan {%1}}
    };
    #CASE {1} {
      gotonpc {段正淳} {tlbb_saveduan_askdzc {%1}}
    };
    #CASE {2} {
      #IF {"$hp[shen]" == "戾气"} {
        gozshen {100} {goquest_tlbb {%1}};
      };
      #ELSE {
        gotonpc {段正淳} {tlbb_tls_askdzc {%1}}
      };
    };
    #CASE {3} {
      gotonpc {梁长老} {tlbb_jxz_ask {%1}};
    };
    #CASE {4} {
      gotonpc {吴长老} {tlbb_jiuyuan_dgz {%1}};
    };
    #DEFAULT {
      questfail {$questmodule};
      %1;
    };
  };
};
#NOP {开启流程杀人};
#ALIAS {tlbb_chuanwen_askduan} {
  #VARIABLE {questmodule} {天龙八部};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你向段正淳打听有关『段誉』的消息。} {
    #CLASS questresponseclass OPEN;
    #ACTION {^段正淳说道：「不过，%*已经在帮我了} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questupdate {$questmodule};
      dohalt {%1}
    };
    #ACTION {^段正淳说道：「{小儿不爱学武|前段时间有人说在无量山附近看到小儿}} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      dohalt {
        gotoroom {无量剑宗} {tlbb_chuanwen_kill {%1}};
      };
    };
    #ACTION {^段正淳说道：「前段时间有人说在无量山附近看到小儿，不知道现在身在何处。」} {
      #NOP {直接去山洞};
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      dohalt {
        gotoroom {无量剑宗} {tlbb_chuanwen_kill {%1}};
      };
    };
    #ACTION {^段正淳说道：「{今天很忙了|誉儿这几天就可能回来了}} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questupdate {$questmodule} {1};
      dohalt {%1};
    };
    #ACTION {^段正淳说道：「小儿日前被四大恶人的南海鳄神掳走了，} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questupdate {$questmodule} {1};
      dohalt {%1};
    };
  };
  #CLASS questclass CLOSE;
  ask duan zhengchun about 段誉
};
#NOP {杀郁光标};
#ALIAS {tlbb_chuanwen_kill} {
  #VARIABLE {okflag} {0};
  #VARIABLE {startts} {0};
  #VARIABLE {questmodule} {天龙八部};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checknpc\"|你设定checknpc为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {"$roomthings[吴光胜]" != "" && "$roomthings[郁光标]" != ""} {
      kill yu guangbiao
    };
    #ELSE {
      #DELAY {6} {
        id here;
        echo {checknpc};
      };
    };
  };
  #ACTION {^这里没有这个人} {
    echo {checknpc}
  };
  #ACTION {^郁光标「啪」的一声倒在地上，挣扎着抽动了几下就死了} {
    kill wu guangsheng;
  };
  #ACTION {^吴光胜「啪」的一声倒在地上，挣扎着抽动了几下就死了} {
    echo {checkjianying}
  };
  #ACTION {^有时更是男女对使，互相击刺。玉壁上所显现的剑法之精，据说极其高明，相传是仙人使剑。} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkjianying\"|你设定checkjianying为反馈信息}} {
    #IF {$okflag == 1} {
      #CLASS questclass KILL;
      dohalt {
        pray pearl;
        gotodo {无量山} {树丛后} {
          look;
          runwait {
            gotodo {无量山} {内室} {tlbb_findduan_askduan {%1}}
          };
        };
      };
    };
    #ELSE {
      id here;
      echo {checknpc};
    };
  };
  #CLASS questclass CLOSE;
  id here;
  echo {checknpc};
};
#NOP {问段誉顺便解一下北冥神功};
#NOP {你一转身大吃一惊，却发现身旁却见一个男子站在玉像前，痴痴的呆看着，那表情竟然由爱生敬，由敬成痴。};
#ALIAS {tlbb_findduan_askduan} {
  #VARIABLE {questmodule} {天龙八部};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你向青年男子打听有关『段誉』的消息} {
    #VARIABLE {idle} {0};
    #CLASS questresponseclass OPEN;
    #ACTION {^段誉说道：「在下便是段誉。」} {
      #CLASS questresponseclass KILL;
      dohalt {
        ask duan yu about 回家
      };
    };
    #CLASS questresponseclass CLOSE;
  };
  #ACTION {^你向段誉打听有关『回家』的消息} {
    #VARIABLE {idle} {0};
    #CLASS questresponseclass OPEN;
    #ACTION {^段誉说道：「我也想回去，只是} {
      #NOP {这种情况下是没有触发段誉发愣的过程,出去再进来,尝试一定次时间就退出重来};
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      dohalt {
        #IF {@elapsed{$startts} > 180} {
          questdelay {$questmodule} {0} {1200};
          doquit;
        };
        #ELSE {
          out;
          loc {walk};
        };
      };
    };
    #ACTION {^段誉对着你挥了挥手} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      dohalt {
        tlbb_findduan_beiming {%1}
      };
    };
    #CLASS questresponseclass CLOSE;
  };
  #CLASS questclass CLOSE;
  dohalt {
    ask nanzi about 段誉
  }
};
#NOP {顺便解一下北冥神功};
#ALIAS {tlbb_findduan_beiming} {
  #VARIABLE {questmodule} {天龙八部};
  #CLASS questclass OPEN;
  #ACTION {^你已经完全参透了北冥神功绝学} {
    #NOP {已解开};
    #CLASS questclass KILL;
    questsuccess {北冥神功};
    dohalt {
      tlbb_findduan_ketou {%1}
    };
  };
  #ACTION {^你来的也太勤快了些吧} {
    #NOP {时间太短};
    #CLASS questclass KILL;
    questdelay {北冥神功} {0} {7200};
    dohalt {
      tlbb_findduan_ketou {%1}
    };
  };
  #ACTION {^你只觉多瞧一眼也是亵渎了神仙姊姊，急忙掩卷不看。然而你却发现画卷已不知所踵} {
    #NOP {失败了,24小时};
    #CLASS questclass KILL;
    questfail {北冥神功};
    dohalt {
      tlbb_findduan_ketou {%1}
    };
  };
  #ACTION {^你俨然已学会画卷中的武功绝学，以后就要靠你自己练习了！} {
    #NOP {成功};
    #CLASS questclass KILL;
    questsuccess {北冥神功};
    dohalt {
      tlbb_findduan_ketou {%1}
    };
  };
  #CLASS questclass CLOSE;
  pray pearl;
  look picture;
  yanjiu picture
};
#NOP {磕头};
#ALIAS {tlbb_findduan_ketou} {
  #VARIABLE {questmodule} {天龙八部};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你口中数着，恭恭敬敬的向玉像磕起头来。} {
    #DELAY {0.2} {
      ketou yuxiang
    };
  };
  #ACTION {^你看见蒲团上的薄草早已破裂，不由伸手进去，里面什么也没有，好象已经被取走了} {
    gotonpc {段正淳}
  };
  #ACTION {^你上前告诉段正淳，说在无量山发现段誉，大可放心！} {
    #CLASS questclass KILL;
    questupdate {$questmodule} {1};
    #DELAY {2} {
      %1
    };
  };
  #CLASS questclass CLOSE;
  look yuxiang;
  ketou yuxiang;
  look left;
  look right;
};
#NOP {营救段誉开始};
#ALIAS {tlbb_saveduan_askdzc} {
  #VARIABLE {questmodule} {天龙八部};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你向段正淳打听有关『段誉』的消息} {
    #VARIABLE {idle} {0};
    #CLASS questresponseclass OPEN;
    #ACTION {^段正淳说道：「多谢%*搭救小儿，只是这万劫谷之后，身体一直不调，才被送往天龙寺求救去了。」} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      #NOP {到第三步了};
      questupdate {$questmodule} {2};
      dohalt {
        goquest_tlbb {%1}
      };
    };
    #ACTION {^段正淳说道：「{今天很忙了|誉儿这几天就可能回来了}} {
      #NOP {时间未满24h};
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questdelay {$questmodule} {0} {7200};
      dohalt {%1};
    };
    #ACTION {^段正淳说道：「小儿日前被四大恶人的南海鳄神掳走了，} {
      #CLASS questresponseclass KILL;
      dohalt {
        ask duan zhengchun about 营救段誉
      };
    };
    #CLASS questresponseclass CLOSE;
  };
  #ACTION {^你向段正淳打听有关『营救段誉』的消息} {
    #VARIABLE {wsteps} {0};
    #VARIABLE {nsteps} {0};
    #CLASS questresponseclass OPEN;
    #ACTION {^段正淳说道：「以%*目前的经验就算目前有情况，也帮不上什么忙，还是抓紧练功吧！」} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questdelay {$questmodule} {0} {7200};
      dohalt {%1}
    };
    #ACTION {^段正淳说道：「%*已经在帮我了} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questdelay {$questmodule} {0} {1200};
      dohalt {%1}
    };
    #ACTION {^段正淳说道：「{今天很忙了|誉儿被那四大恶人擒走，这件事还需从长计议}} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questdelay {$questmodule} {0} {7200};
      dohalt {%1}
    };
    #ACTION {^段正淳说道：「我看%*面带阴邪之气，恐怕是四大恶人一伙的奸细} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      dohalt {
        gozshen {10000} {gotonpc {段正淳} {tlbb_saveduan_askdzc {%1}}};
      };
    };
    #ACTION {^再向西行%*里，再向北行%*里，然后会到一座高山，再往后就要见机行事了} {
      #VARIABLE {wsteps} {@ctd{%%%1}};
      #VARIABLE {nsteps} {@ctd{%%%2}};
    };
    #ACTION {^段正淳说道：好了，准备一下，为了不惊动对方，切记要在晚上行动，路上小心。」} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      dohalt {
        gotodo {无量山} {山中小溪} {tlbb_saveduan_finddyq {$wsteps} {$nsteps} {%1}}
      };
    };
    #ACTION {^刚有探报，小儿确实就在无量山附近的一座石屋内，不得已只怕要走一次了。} {
      #NOP {问过了，干脆重启};
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questdelay {$questmodule} {0} {7200};
      dohalt {%1};
    };
    #ACTION {^刚有探报，小儿确实就在无量山附近的一座石屋内，不过，有%*相助，料无大碍。} {
      #NOP {别人正在营救,等吧};
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questdelay {$questmodule} {0} {7200};
      dohalt {%1};
    };
    #ACTION {^小儿自万劫谷回来，总是气血不定，内力怪异，才被送往天龙求救去了。} {
      
    };
    #ACTION {^竟然惹出鸠摩智这等高手，但愿小儿吉人天相啊。} {
      
    };
    #ACTION {^段正淳说道：「{今天很忙了|誉儿这几天就可能回来了}} {
      #NOP {时间未满24h};
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questdelay {$questmodule} {0} {7200};
      dohalt {%1};
    };
    #ACTION {^段正淳说道：「小儿日前被四大恶人的南海鳄神掳走了，} {
      #CLASS questresponseclass KILL;
      dohalt {
        ask duan zhengchun about 营救段誉
      };
    };
    #CLASS questresponseclass CLOSE;
  };
  #CLASS questclass CLOSE;
  ask duan zhengchun about 段誉
};
#NOP {寻找段延庆,%1:西向步数,%2:北向步数,%3:后续指令};
#ALIAS {tlbb_saveduan_finddyq} {
  #VARIABLE {okflag} {0};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #VARIABLE {tempdo} {
    #%1 w;
    #%2 n;
    echo {checkgaoshan};
  };
  #ACTION {^{设定环境变量：action \= \"checktime\"|你设定checktime为反馈信息}} {
    #VARIABLE {idle} {0};

    #IF {"$room" == "无量山峰"} {
      #VARIABLE {idle} {0};
      wd;
    };
    #ELSEIF {$env[gametime] > 6} {
      #DELAY {6} {
        time;
        echo {checktime};
      };
    };
    #ELSE {
      #VARIABLE {okflag} {0};
      look;
      echo {confirmtime};
    };
  };
  #ACTION {^天黑了,这里黑黝黝一片，伸手不见五指,你四处乱走着,心里充满恐惧} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"confirmtime\"|你设定confirmtime为反馈信息}} {
    #VARIABLE {idle} {0};
    #DELAY {1} {
      #IF {$env[gametime] >= 5 || $okflag == 1} {
        #VARIABLE {okflag} {0};
        $tempdo
      };
      #ELSE {
        look;
        time;
        echo {confirmtime};
      };
    }
  };
  #ACTION {^你筋疲力尽地走出这段山路，沮丧地发现自己怎么也找不到四大恶人的行踪。} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^你{熟悉地绕过森林|走到了一个高山上}} {
    #VARIABLE {okflag} {2};
  };
  #ACTION {^{设定环境变量：action \= \"checkgaoshan\"|你设定checkgaoshan为反馈信息}} {
    #VARIABLE {idle} {0};
    #DELAY {0.5} {
      #IF {$okflag == 2 || "$room" == "无量山峰"} {
        #VARIABLE {idle} {0};
        wd;
      };
      #ELSEIF {$okflag == 1} {
        #CLASS questclass KILL;
        questfail {$questmodule};
        loc {%3};
      };
      #ELSE {
        $tempdo;
      };
    };
  };
  #ACTION {^你悄立江边，思涌如潮，心中思绪万千。突然眼角瞥处，见数十丈外一块大岩石（yan)上似乎有些古怪。} {
    #VARIABLE {idle} {0};
    look yan
  };
  #ACTION {^一块巨岩，不知道可不可以跳上去？} {
    #VARIABLE {idle} {0};
    jump yan
  };
  #ACTION {^你一提气，纵身跳到了岩石上。} {
    #VARIABLE {idle} {0};
    dohalt {
      id here;
      echo {checkdyq};
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkdyq\"|你设定checkdyq为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {"$roomthings[青袍客]" != ""} {
      ask qingpao ke about name;
    };
    #ELSEIF {"$roomthings[段延庆]" != ""} {
      ask duan yanqing about name;
    };
    #ELSE {
      #DELAY {6} {
        id here;
        echo {checkdyq};
      };
    };
  };
  #ACTION {^段延庆说道：「老夫便是四大恶人中的老大：段延庆！」} {
    #VARIABLE {idle} {0};
    dohalt {
      pfm_wuxing;
      pfm_buff_normal;
      ask duan about 段誉
    };
  };
  #ACTION {^段延庆说道：「想救段誉？送死来了} {
    #IF {$hp[exp] > 8000000 || @getSkillLevel{hamagong} > 300} {
      closewimpy;
    };
    #ELSE {
      openwimpy;
    };
    kill duan;
  };
  #ACTION {^段延庆说道：「你问这个干什么} {
    #NOP {问他名字};
    dohalt {
      ask duan yanqing about name;
    };
  };
  #ACTION {^你大声喝道：哪里跑！你紧紧追了过去。} {
    #CLASS questclass KILL;
    #VARIABLE {okflag} {1};
    openwimpy;
    loc {
      dohalt {doheal {startfull {gotodo {无量山} {空地} {tlbb_saveduan_killdyq {%3}}}}}
    }
  };
  #ACTION {^看来，营救的事情只能由其他人完成，你还是暂时回避一下。} {
    #CLASS questclass KILL;
    #VARIABLE {okflag} {0};
    questfail {$questmodule};
    dohalt {
      jump down;
      eu;
      loc {%3}
    }
  };
  #LINE ONESHOT #ACTION {^你马上要昏迷了，不能做任何事情。} {
    #CLASS questclass KILL;
    questfail {$questmodule};
  };
  #CLASS questclass CLOSE;
  #5 n;
  time;
  echo {checktime};
};
#NOP {杀段延庆,%1:后续指令};
#ALIAS {tlbb_saveduan_killdyq} {
  #VARIABLE {okflag} {0};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #LINE ONESHOT #ACTION {^你马上要昏迷了，不能做任何事情。} {
    #CLASS questclass KILL;
    questfail {$questmodule};
  };
  #ACTION {^段延庆「啪」的一声倒在地上，挣扎着抽动了几下就死了} {
    #VARIABLE {idle} {0};
    #CLASS questclass KILL;
    dohalt {
      doheal {
        startfull {
          n;
          push yan;
          enter;
          tlbb_saveduan_saveduan {%1}
        };
      };
    };
  };
  #CLASS questclass CLOSE;
  kill duan yanqing
};
#NOP {救段誉};
#ALIAS {tlbb_saveduan_saveduan} {
  #VARIABLE {okflag} {0};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^段誉说道：「在下便是段誉。」} {
    dohalt {
      ask duan about 救你
    };
  };
  #ACTION {^你向段誉打听有关『救你』的消息} {
    #CLASS questresponseclass OPEN;
    #ACTION {^你于%*经过千辛万苦终于段誉从万劫谷解救出来} {
      questupdate {$questmodule} {2};
    };
    #ACTION {^赶紧离开吧。”众人点头称是，于是范、巴、华、段誉跳下地道离开了了} {
      #NOP {顺便解一下凌波微步};
      #CLASS questresponseclass KILL;
      loc {
        dohalt {
          pray pearl;
          pfm_wuxing;
          #5 fan bo juan;
          echo {checkfan};
        };
      };
    };
    #CLASS questresponseclass CLOSE;
  };
  #ACTION {^你翻到最后，不由得大为狂喜，这部分并没有被撕烂，题着“凌波微步”四字。} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^你翻到最后发现帛卷撕的乱七八糟，什么都看不清，根本无法从里面学到东西。} {
    #VARIABLE {okflag} {2};
  };
  #ACTION {^{设定环境变量：action \= \"checkfan\"|你设定checkfan为反馈信息}} {
    #DELAY {1} {
      #IF {$okflag == 0} {
        #5 fan bo juan;
        echo {checkfan};
      };
      #ELSE {
        #CLASS questclass KILL;
        #IF {$okflag == 1} {
          questsuccess {凌波微步};
        };
        #ELSE {
          questfail {凌波微步};
        };
        dohalt {%1}
      };
    };
  };
  #CLASS questclass CLOSE;
  dohalt {
    ask nanzi about name
  };
};
#NOP {天龙营救问段正淳};
#ALIAS {tlbb_tls_askdzc} {
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你向段正淳打听有关『营救』的消息。} {
    #VARIABLE {idle} {0};
    #CLASS questresponseclass OPEN;
    #ACTION {^段正淳说道：「%*求救去了} {
      #NOP {正常开始};
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      dohalt {
        gotonpc {本因大师} {tlbb_tls_askby {%1}};
      };
    };
    #ACTION {^段正淳说道：「竟然惹出鸠摩智这等高手，但愿小儿吉人天相啊} {
      #NOP {已经走完天龙寺流程，直接去找鸠摩智};
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      dohalt {
        gotonpc {鸠摩智} {tlbb_tls_jiumozhi {%1}};
      };
    };
    #ACTION {^小儿向来只爱佛法已经，平易近人，援救之说何来之说} {
      #NOP {异常状态};
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questdelay {$questmodule} {0} {86400};
      %1;
    };
  };
  #CLASS questclass CLOSE;
  ask duan zhengchun about 营救;
};
#NOP {天龙营救问本因};
#ALIAS {tlbb_tls_askby} {
  #VARIABLE {askresult} {0};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^这里没有这个人。} {
    #VARIABLE {askresult} {255};
  };
  #ACTION {^你向本因大师打听有关『天龙有难』的消息。} {
    #VARIABLE {askresult} {0};
    #CLASS questresponseclass OPEN;
    #ACTION {^本因大师说道：「%*好意心领了，只是......} {
      #NOP {负神};
      #VARIABLE {askresult} {1};
    };
    #ACTION {^本因大师说道：「我佛慈悲%*化解这场天龙危机，还将我大理小王子得以解救} {
      #NOP {已完成};
      #VARIABLE {askresult} {2};
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkresult\"|你设定checkresult为反馈信息}} {
    #CLASS questresponseclass KILL;
    #VARIABLE {idle} {0};
    dohalt {
      #SWITCH {$askresult} {
        #CASE {1} {
          #CLASS questclass KILL;
          gozshen {20000} {gotonpc {本因大师} {tlbb_tls_askby {%1}}};
        };
        #CASE {2} {
          #CLASS questclass KILL;
          questupdate {$questmodule} {3};
          %1;
        };
        #CASE {255} {
          #DELAY {2} {
            ask benyin dashi about 天龙有难;
            echo {checkresult};
          };
        };
        #DEFAULT {
          #VARIABLE {askresult} {0};
          ask benyin dashi about 鸠摩智;
          echo {checkresult};
        }
      };
    };
  };
  #ACTION {^你向本因大师打听有关『鸠摩智』的消息。} {
    #VARIABLE {askresult} {0};
    #VARIABLE {idle} {0};
    #CLASS questresponseclass OPEN;
    #ACTION {^本因大师说道：「感谢%*如此仗义。今天很忙了%*先行休息去吧！} {
      #NOP {时间不够与天龙武功公用CD};
      #VARIABLE {askresult} {1};
    };
    #ACTION {^本因大师说道：「以%*目前的经验就算目前有情况，也帮不上什么忙，还是抓紧练功吧！} {
      #NOP {经验不够};
      #VARIABLE {askresult} {2};
    };
    #ACTION {^本因大师说道：「不过%*已经在帮我了%*还是抓紧练功吧} {
      #NOP {有人在解};
      #VARIABLE {askresult} {3};
    };
    #ACTION {^{设定环境变量：action \= \"checkresult\"|你设定checkresult为反馈信息}} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      dohalt {
        #SWITCH {$askresult} {
          #CASE {1} {
            questdelay {$questmodule} {0} {7200};
            %1; 
          };
          #CASE {2} {
            questdelay {$questmodule} {} {7200};
            %1; 
          };
          #CASE {3} {
            questdelay {$questmodule} {0} {3600};
            %1; 
          };
          #DEFAULT {
            #NOP {去白石路杀人};
            gotoroom {1242} {tlbb_tls_killroad {%1}};
          }
        };
      };
    };
  };
  #CLASS questclass CLOSE;
  ask benyin dashi about 天龙有难;
  echo {checkresult};
};
#NOP {天龙营救白石路杀人};
#ALIAS {tlbb_tls_killroad} {
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^有%*座下高徒%*来保护天龙寺，也不犯你出手了} {
    #NOP {有人在做，基本不可能出现};
    #CLASS questclass KILL;
    questdelay {$questmodule} {0} {3600};
    runwait {%1};
  };
  #ACTION {^你还是耐心地等待吧} {
    #VARIABLE {idle} {0};
    #NOP {等};
  };
  #ACTION {^你料想这场战斗定是非同小可，而这白石路，更是通往内堂的必经之路} {
    #VARIABLE {idle} {0};
    #NOP {等};
  };
  #ACTION {^%*突然警惕地看了你一眼急声道：“小心、有人、结阵} {
    kill dls dizi;
    startfight;
  };
  #ACTION {^%*「啪」的一声倒在地上，挣扎着抽动了几下就死了} {
    kill dls dizi;
  };
  #ACTION {^这里没有这个人。} {
    #CLASS questclass KILL;
    #NOP {去见本因大师，触发鸠摩智抓段誉情节};
    stopfight;
    gotonpc {本因大师} {tlbb_tls_storyline {%1}};
  };
  #CLASS questclass CLOSE;
  dohalt {
    wwp;
    pfm_buff_normal;
  };
};
#NOP {回去本因大师触发事件};
#ALIAS {tlbb_tls_storyline} {
  #VARIABLE {checkts} {@now{}};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你要对谁做这个动作？} {
    #VARIABLE {idle} {0};
    #IF {@elapsed{$checkts} >= 1200} {
      #CLASS questclass KILL;
      questfail {$questmodule};
      %1;
    };
    #DELAY {2} {
      #SEND {@@};
      hi benyin dashi;
    }
  };
  #ACTION {^段誉当即收招，只是你却发现他真气竟然不能随意收发，只将手指一抬向怀顶指去} {
    #VARIABLE {idle} {0};
  };
  #ACTION {^鸠摩智长笑说道：“烧了死图谱，反得活图谱。慕容先生地下有人相伴，可不觉寂寞了} {
    id here;
    echo {checkjiumozhi};
  };
  #ACTION {^{设定环境变量：action \= \"checkjiumozhi\"|你设定checkjiumozhi为反馈信息}} {
    #IF {"$roomthings[鸠摩智]" != ""} {
      startfight;
      kill jiumo zhi;
    };
    #ELSE {
      #CLASS questclass KILL;
      doheal {gotonpc {鸠摩智} {tlbb_tls_jiumozhi {%1}}};
    };
  };
  #ACTION {^鸠摩智「啪」的一声倒在地上，挣扎着抽动了几下就死了} {
    #CLASS questclass KILL;
    doheal {gotonpc {鸠摩智} {tlbb_tls_jiumozhi {%1}}};
  };
  #CLASS questclass CLOSE;
  #SEND {@@};
  hi benyin dashi;
};
#NOP {找鸠摩智就段誉};
#ALIAS {tlbb_tls_jiumozhi} {
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^这里没有这个人} {
    #VARIABLE {idle} {0};
    #DELAY {6} {ask jiumo zhi about 段誉};
  };
  #ACTION {^你向鸠摩智打听有关『段誉』的消息。} {
    #VARIABLE {idle} {0};
    #CLASS questresponseclass OPEN;
    #ACTION {^鸠摩智说道：「不错，大理段公子正在我这里逗留几天，不过现在早不在这里了} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      #NOP {已经完成，基本不会发生};
      questupdate {$questmodule} {3};
      dohalt {%1};
    };
    #ACTION {^鸠摩智说道：「不错，大理段公子正在我这里，你要带走还是拿点本事出来吧} {
      #NOP {负神};
      #CLASS questresponseclass KILL;
      kill jiumo zhi;
      startfight;
    };
  };
  #ACTION {^鸠摩智「啪」的一声倒在地上，挣扎着抽动了几下就死了} {
    #CLASS questclass KILL;
    stopfight;
    dohalt {
      execute {
        push wall;
        n
      };
      tlbb_tls_duanyu {%1};
    };
  };
  #CLASS questclass CLOSE;
  ask jiumo zhi about 段誉;
};
#NOP {问段誉};
#ALIAS {tlbb_tls_duanyu} {
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你向青年男子打听有关『段誉』的消息} {
    dohalt {ask duan yu about 救你};
  };
  #ACTION {^段誉似乎不懂你的意思。} {
    #CLASS questclass KILL;
    #NOP {时间不够};
    questfail {$questmodule};
    dohalt {
      push wall;
      s;
      loc {%1};
    }
  };
  #ACTION {^你于%*经过千辛万苦终于段誉从天龙寺解救出来} {
    #CLASS questclass KILL;
    questupdate {$questmodule} {3};
    dohalt {
      push wall;
      s;
      loc {%1};
    };
  };
  #CLASS questclass CLOSE;
  dohalt {
    ask qingnian nanzi about 段誉;
  };
};
#NOP {萧峰身世篇,问梁->陈->宋->梁->白->吴};
#ALIAS {tlbb_jxz_ask} {
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你向梁长老打听有关『萧峰』的消息。} {
    #VARIABLE {idle} {0};
    #CLASS questresponseclass OPEN;
    #ACTION {^梁长老沉默了一会了，道：“当时在竹林那时，丐帮其他长老都在场，你可以找其他人问问吧} {
      #CLASS questresponseclass KILL;
      dohalt {gotonpc {陈长老} {ask chen zhanglao about 萧峰}};
    };
    #ACTION {^梁长老担心道：“这萧峰才略过人，英雄了得，当年谁不佩服？现在变成契丹人} {
      #CLASS questresponseclass KILL;
      dohalt {gotonpc {白世镜} {ask bai shijing about 萧峰}};
    };
    #ACTION {^梁长老顿了一顿道：“这件事说起来牵连太多，传了出去，丐帮在江湖上再也抬不起头来} {
      #CLASS questresponseclass KILL;
      #NOP {都已经问过了,直接找吴长老};
      dohalt {gotonpc {吴长老} {ask wu zhanglao about 萧峰}};
    };
    #ACTION {^梁长老说道：「萧峰是契丹狗种，还是堂堂汉人，此时还未分明} {
      #CLASS questresponseclass KILL;
      #NOP {找吴长老确认};
      dohalt {gotonpc {吴长老} {ask wu zhanglao about 萧峰}};
    };
    #ACTION {^梁长老说道：「那萧峰要是还是我们帮主该多好} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      #NOP {已经完成了};
      questupdate {$questmodule} {4};
      dohalt {%1};
    };
  };
  #ACTION {^你向陈长老打听有关『萧峰』的消息。} {
    #VARIABLE {idle} {0};
    #CLASS questresponseclass OPEN;
    #ACTION {^陈长老一脸的沉重：“竹林一场惊变，是历年来丐帮最大的一次内乱，事情经过一波三折} {
      #CLASS questresponseclass KILL;
      dohalt {gotonpc {宋长老} {ask song zhanglao about 萧峰}};
    };
    #ACTION {^陈长老说道：「萧峰是契丹狗种，还是堂堂汉人，此时还未分明} {
      #CLASS questresponseclass KILL;
      #NOP {找吴长老确认};
      dohalt {gotonpc {吴长老} {ask wu zhanglao about 萧峰}};
    };
    #ACTION {^陈长老说道：「那萧峰要是还是我们帮主该多好} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      #NOP {已经完成了};
      questupdate {$questmodule} {4};
      dohalt {%1};
    };
  };
  #ACTION {^你向宋长老打听有关『萧峰』的消息。} {
    #VARIABLE {idle} {0};
    #CLASS questresponseclass OPEN;
    #ACTION {^宋长老叹了口气，道：“这萧峰，或者是个装腔作势的大奸雄，或者是个直肠直肚的好汉子} {
      #CLASS questresponseclass KILL;
      dohalt {gotonpc {梁长老} {ask liang zhanglao about 萧峰}};
    };
    #ACTION {^宋长老说道：「萧峰是契丹狗种，还是堂堂汉人，此时还未分明} {
      #CLASS questresponseclass KILL;
      #NOP {找吴长老确认};
      dohalt {gotonpc {吴长老} {ask wu zhanglao about 萧峰}};
    };
    #ACTION {^宋长老说道：「那萧峰要是还是我们帮主该多好} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      #NOP {已经完成了};
      questupdate {$questmodule} {4};
      dohalt {%1};
    };
  };
  #ACTION {^你向白世镜打听有关『萧峰』的消息。} {
    #VARIABLE {idle} {0};
    #CLASS questresponseclass OPEN;
    #ACTION {^白世镜顿了一顿道：“可恨那厮为了掩盖事实，竟然杀死养父、害死师傅、烧毁单家庄，可谓是人面兽心，果然是契丹的种子} {
      #CLASS questresponseclass KILL;
      dohalt {gotonpc {吴长老} {ask wu zhanglao about 萧峰}};
    };
    #ACTION {^白世镜说道：「萧峰是契丹狗种，还是堂堂汉人，此时还未分明} {
      #CLASS questresponseclass KILL;
      #NOP {找吴长老确认};
      dohalt {gotonpc {吴长老} {ask wu zhanglao about 萧峰}};
    };
    #ACTION {^白世镜说道：「那萧峰要是还是我们帮主该多好} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      #NOP {已经完成了};
      questupdate {$questmodule} {4};
      %1;
    };
  };
  #ACTION {^你向吴长老打听有关『萧峰』的消息。} {
    #VARIABLE {idle} {0};
    #CLASS questresponseclass OPEN;
    #ACTION {^吴长老说道：「在下这就去一趟聚闲庄%*一起去吧。} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      #NOP {已经开始了，直接去聚贤庄};
      dohalt {
        gotodo {南阳城} {聚贤庄大门} {tlbb_jxz_save {%1}};
      };
    };
    #ACTION {^吴长老说道：「不过，已经有%*这样的高手去了} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      #NOP {有人在解};
      questdelay {$questmodule} {0} {3600};
      dohalt {%1};
    };
    #ACTION {^吴长老说道：「不过今天很忙了%*先行休息去吧} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      #NOP {时间不够};
      questdelay {$questmodule} {0} {7200};
      dohalt {%1};
    };
    #ACTION {^吴长老说道：「不过，以%*目前的经验就算目前有情况} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      #NOP {经验不够};
      questdelay {$questmodule} {} {7200};
      dohalt {%1};
    };
  };
  #CLASS questclass CLOSE;
  ask liang zhanglao about 萧峰;
};
#NOP {萧峰身世篇，聚贤庄就萧峰};
#ALIAS {tlbb_jxz_save} {
  #VARIABLE {checkcount} {0};
  #VARIABLE {okflag} {0};
  #VARIABLE {checkts} {@now{}};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^{管家|家丁}哼了一声道：“萧峰这厮乃契丹狗种} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkstory\"|你设定checkstory为反馈信息}} {
    #IF {$okflag == 1} {
      startfight;
      kill guan jia;
      kill jia ding;
    };
    #ELSEIF {$checkcount <= 10} {
      #MATH {checkcount} {$checkcount + 1};
      #DELAY {1} {echo {checkstory}};
    };
    #ELSE {
      #CLASS questclass KILL;
      questfail {$questmodule};
      %1;
    };
  };
  #ACTION {^{管家|家丁}「啪」的一声倒在地上，挣扎着抽动了几下就死了} {
    id here;
    echo {checknpc};
  };
  #ACTION {^{设定环境变量：action \= \"checknpc\"|你设定checknpc为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {"$roomthings[管家]" != ""} {
      kill guan jia;
    };
    #ELSEIF {"$roomthings[家丁]" != ""} {
      kill jia ding;
    };
    #ELSE {
      stopfight;
      #VARIABLE {okflag} {0};
      dohalt {
        n;
        echo {checkflag};
      };
    };
  };
  #ACTION {^你走进大厅内，果然一片混乱} {
    #VARIABLE {okflag} {0};
  };
  #ACTION {^你发现聚贤庄内似乎传来激烈的打斗之声，还是走为上策} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^{管家|家丁}冷笑一声道：这位%*请回，这里不欢迎你} {
    #VARIABLE {okflag} {2};
  };
  #ACTION {^突然从里面传来一阵冷笑道：这位%*请回，这里不欢迎你} {
    #VARIABLE {okflag} {3};
  };
  #ACTION {^{设定环境变量：action \= \"checkflag\"|你设定checkflag为反馈信息}} {
    #VARIABLE {idle} {0};
    #SWITCH {$okflag} {
      #CASE {1} {
        #DELAY {2} {
          #IF {@elapsed{$checkts} >= 1200} {
            #CLASS questclass KILL;
            questfail {$questmodule};
            %1;
          };
          #ELSE {
            n;
            echo {checkflag};
          };
        };
      };
      #CASE {2} {
        id here;
        echo {checknpc};
      };
      #CASE {3} {
        #CLASS questclass KILL;
        questfail {$questmodule};
        %1;
      };
      #DEFAULT {@@};
    };
  };
  #ACTION {^萧峰对着你冷笑一声} {
    #VARIABLE {idle} {0};
  };
  #ACTION {^你暗叫一声“好武功”，萧峰只如此一掌，便破了渡难的成名绝技} {
    #VARIABLE {idle} {0};
  };
  #ACTION {^玄难似乎渐渐不是对手，群雄又是全部一拥而上，场面更加混乱，战斗越来越是激烈} {
    #VARIABLE {idle} {0};
  };
  #ACTION {^你暗道一声“不妙”，需要你赶快去解救萧峰} {
    dohalt {jiejiu xiao feng};
  };
  #ACTION {^你赶快去解救萧峰} {
    dohalt {jiejiu xiao feng};
  };
  #ACTION {^你怕有追兵追赶，连续走过几个地方，才来到一个断崖边停住} {
    #VARIABLE {idle} {0};
    dohalt {ask xiao feng about 真相};
  };
  #ACTION {^萧峰说道：「我受伤未能远行，还请这位%*去一次颂摩崖，那里据说有一块刻字记载当年事迹。} {
    dohalt {
      gotodo {星宿海} {颂摩崖} {tlbb_jxz_smy {%1}};
    };
  };
  #CLASS questclass CLOSE;
  closewimpy;
  pfm_buff_normal;
  echo {checkstory};
};
#NOP {萧峰身世篇，颂摩崖};
#ALIAS {tlbb_jxz_smy} {
  #VARIABLE {checkts} {@now{}};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你没有想到，这块峭壁上竟然还有如此大的空间可以落脚，估计是当年中原高手埋伏的地点之一} {
    look cliff;
  };
  #ACTION {^你突然发现这个落脚之处过于狭小，已经有} {
    #VARIABLE {idle} {0};
    #IF {@elapsed{$checkts} < 60} {
      #DELAY {2} {
        tiao cliff;
      }
    };
    #ELSE {
      #CLASS questclass KILL;
      questfail {$questmodule};
      %1;
    };
  };
  #ACTION {^你暗暗想道：只怕这萧峰真的是契丹人了} {
    #VARIABLE {idle} {0};
  };
  #ACTION {^你没有料道，萧峰竟然恢复如此之迅速，果然是功夫了得} {
    #VARIABLE {idle} {0};
  };
  #ACTION {^你于%*终于帮助萧峰确定身世之谜} {
    questupdate {$questmodule} {4};
  };
  #ACTION {^突然之间，你脑子闪过两个念头。一个因为这萧峰已确是契丹人} {
    dohalt {ask xiao feng about 归隐};
  };
  #ACTION {^你于%*选择劝说萧峰归隐江湖} {
    #CLASS questclass KILL;
    dohalt {
      tiao cliff;
      loc {%1};
    };
  };
  #CLASS questclass CLOSE;
  tiao cliff;
};
#NOP {大辽救援，打狗阵};
#ALIAS {tlbb_jiuyuan_dgz} {
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你向吴长老打听有关『救援』的消息。} {
    #VARIABLE {idle} {0};
    #CLASS questresponseclass OPEN;
    #ACTION {^吴长老说道：「我也听说这事情，已经有%*前往大辽营救去了} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      #NOP {有人在解};
      questdelay {$questmodule} {0} {3600};
      dohalt {%1};
    };
    #ACTION {^吴长老说道：「今天很忙了%*先行休息去吧} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      #NOP {时间不够};
      questdelay {$questmodule} {0} {7200};
      dohalt {%1};
    };
    #ACTION {^吴长老说道：「以%*目前的经验就算目前有情况} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      #NOP {经验不够};
      questdelay {$questmodule} {} {7200};
      dohalt {%1};
    };
    #ACTION {^吴长老说道：「%*却是了得。吴某佩服。还请%*主持大局} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      #NOP {已经过阵了，去找玄慈};
      dohalt {gotonpc {玄慈大师} {tlbb_jiuyuan_slz {%1}}};
    };
    #ACTION {^你随吴长老来到一个小屋之中} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      tlbb_jiuyuan_zhenfight {%1};
    };
  };
  ask wu zhanglao about 救援;
};
#NOP {大辽救援，少林阵};
#ALIAS {tlbb_jiuyuan_slz} {
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你向玄慈大师打听有关『救援』的消息。} {
    #VARIABLE {idle} {0};
    #CLASS questresponseclass OPEN;
    #ACTION {^玄慈大师说道：「我也听说这事情，已经有%*前往大辽营救去了} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      #NOP {有人在解};
      questdelay {$questmodule} {0} {3600};
      dohalt {%1};
    };
    #ACTION {^玄慈大师说道：「今天很忙了%*先行休息去吧} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      #NOP {时间不够};
      questdelay {$questmodule} {0} {7200};
      dohalt {%1};
    };
    #ACTION {^玄慈大师说道：「以%*目前的经验就算目前有情况} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      #NOP {经验不够};
      questdelay {$questmodule} {} {7200};
      dohalt {%1};
    };
    #ACTION {^玄慈大师说道：「%*却是了得。老衲佩服。还请%*主持大局} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      #NOP {已经过阵了，去颂摩崖。如没有切口直接失败};
      #IF {"$env[qiekou]" == ""} {
        questfail {$questmodule};
        dohalt {%1};
      };
      #ELSE {
        dohalt {gotoroom {颂摩崖} {tlbb_jiuyuan_jietou {$env[qiekou]} {%1}}};
      };
      
    };
    #ACTION {^你随玄慈主持来到一个小屋之中} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      tlbb_jiuyuan_zhenfight {%1};
    };
  };
  ask xuanci dashi about 救援;
};
#NOP {大辽救援，过阵战斗};
#ALIAS {tlbb_jiuyuan_zhenfight} {
  #VARIABLE {faintflag} {0};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你决定闯阵} {
    #VARIABLE {idle} {0};
  };
  #ACTION {^稍等一会，阵法重新启动！} {
    #VARIABLE {idle} {0};
    #DELAY {6} {
      start;
    };
  };
  #ACTION {^( 你{看起来已经力不从心了|气喘嘘嘘|似乎十分疲惫|摇头晃脑、歪歪斜斜}} {
    yun qi
  };
  #ACTION {^( 你{受伤不轻|你气息粗重|受了相当重的伤}} {
    yun qi;
  };
  #ACTION {^( 你{已经一副头重脚轻的模样|已经陷入半昏迷状态|已经伤痕累累|伤重之下|受伤过重}} {
    yun qi;
  };
  #ACTION {^{丐帮|护法}弟子鼓足真气，纵声长啸} {
    #VARIABLE {idle} {0};
  };
  #LINE ONESHOT #ACTION {^{丐帮|护法}弟子微笑地对着%*道：“下次努力啊} {
    #CLASS questclass KILL;
    tlbb_jiuyuan_zhenfight {%1};
  };
  #ACTION {^你「啪」的一声倒在地上，挣扎着抽动了几下就死了。} {
    #CLASS questclass KILL;
    questfail {$questmodule};
  } {1};
  #LINE ONESHOT #ACTION {^{丐帮|护法}弟子一言不发，慢慢地消失在角落里} {
    #DELAY {6} {
      #IF {$faintflag == 0} {
        #CLASS questclass KILL;
        tlbb_jiuyuan_zhenfight {%1};
      };
    };
  };
  #ACTION {^你只觉得头昏脑胀，眼前一黑，接着什么也不知道了} {
    #VARIABLE {idle} {0};
    #VARIABLE {faintflag} {1};
    #LINE ONESHOT #ACTION {^一股暖流发自丹田流向全身，慢慢地你又恢复了知觉} {
      #CLASS questclass KILL;
      tlbb_jiuyuan_zhenfight {%1};
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkhp\"|你设定checkhp为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {@eval{$hp[qi_max]-$hp[qi]} > @eval{$hp[qi_max]/4}} {
      yun qi
    }
  };
  #ACTION {^{设定环境变量：action \= \"checkstart\"|你设定checkstart为反馈信息}} {
    doheal {
      startfull {
        pfm_buff_normal;
        start;
      };
    };
  };
  #ACTION {^丐帮弟子扬声道：恭喜你闯过} {
    #CLASS questclass KILL;
    dohalt {
      leave;
      loc {gotonpc {玄慈大师} {tlbb_jiuyuan_slz {%1}}};
    };
  };
  #ACTION {^护法弟子扬声道：恭喜你闯过} {
    leave;
  };
  #ACTION {^玄慈大师在你的耳边悄声说道：注意一定是半夜时刻，另外，可能需要暗号接应，暗号是【%*】} {
    #CLASS questclass KILL;
    set env_qiekou %%1;
    #VARIABLE {env[qiekou]} {%%1};
    dohalt {
      gotoroom {颂摩崖} {tlbb_jiuyuan_jietou {%%1} {%1}}
    };
  };
  #CLASS questclass CLOSE;
  stopfight;
  openwimpy;
  dohalt {
    full;
    yun qi;
    yun jing;
    yun jingli;
    hp;
    echo {checkstart};
  };
};
#NOP {大辽救援，过阵战斗,%1:暗号,%2:后续指令};
#ALIAS {tlbb_jiuyuan_jietou} {
  #VARIABLE {okflag} {0};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你吸了口气，道：“在下%*，还请阁下现身！”声音传出去很远。} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^丐帮弟子向你一拱手，道：“天王盖地虎！”} {
    dohalt {answer %1};
  };
  #ACTION {^{设定环境变量：action \= \"checkhan\"|你设定checkhan为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {$env[gametime] >= 2} {
      #DELAY {12} {
        time;
        echo {checkhan};
      };
    };
    #ELSE {
      dohalt {
        #IF {$okflag == 0} {
          #DELAY {2} {
            han;
            echo {checkhan};
          };
        };
      }
    };
  };
  #ACTION {^丐帮弟子微笑着道：不用担心了，已经有%*前往大辽营救去了} {
    #CLASS questclass KILL;
    #NOP {有人正在救，等会再来};
    questdelay {$questmodule} {0} {3600};
    %1;
  };
  #ACTION {^丐帮弟子说道：「%*最好先去粮仓所在，可以点火（dianhuo），引起混乱，之后伺机营救萧大侠。萧大侠据说在辽营左堂附近。」} {
    loc {
      dohalt {
        pfm_buff_normal;
        gotoroom {3704} {tlbb_jiuyuan_kill {%2}}
      };
    };
  };
  #CLASS questclass CLOSE;
  startfull {
    echo {checkhan};
  };
};
#ALIAS {tlbb_jiuyuan_kill} {
  #LIST {temppath} {create} {n;n;s;w};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^%*这点本事也来劫营} {
    #CLASS questclass KILL;
    #NOP {失败了};
    questfail {$questmodule};
    yun qi;
    hp;
    loc {%1};
  };
  #ACTION {^辽兵「啪」的一声倒在地上，挣扎着抽动了几下就死了} {
    kill liao bing;
  };
  #ACTION {^这里没有这个人。} {
    #VARIABLE {idle} {0};
    #NOP {等待perform cd};
    #DELAY {6} {
      #IF {&temppath[] == 0} {
        #CLASS questclass KILL;
        tlbb_jiuyuan_guard {%1};
      };
      #ELSE {
        #IF {&temppath[] == 2} {
          dianhuo liang cao;
        };
        dohalt {
          pfm_buff_normal;
          $temppath[+1];
          #LIST {temppath} {delete} {1};
          kill liao bing;
        }
      };
    };
  };
  #CLASS questclass CLOSE;
  startfight;
  kill liao bing;
};
#NOP {大辽救援，护送萧峰};
#ALIAS {tlbb_jiuyuan_guard} {
  #LIST {temppath} {create} {e;s};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^萧峰说道：「没想到，又是%*舍命相救。」} {
    dohalt {ask xiao feng about 太祖拳};
  };
  #ACTION {^你向萧峰打听有关『太祖拳』的消息} {
    dohalt {ask xiao feng about 救援};
  };
  #ACTION {^萧峰说道：「好，我们这就赶快一起闯出去。」} {
    push wall;
    dohalt {
      execute {
        s;
        kill liao bing 1;
        kill liao bing 2
      };
    };
  };
  #ACTION {^辽兵「啪」的一声倒在地上，挣扎着抽动了几下就死了} {
    kill liao bing;
  };
  #ACTION {^这里没有这个人。} {
    #VARIABLE {idle} {0};
    #NOP {等待perform cd};
    #DELAY {6} {
      #IF {&temppath[] == 0} {
        loc {
          gotoroom {3700};
        };
      };
      #ELSE {
        pfm_buff_normal;
        $temppath[+1];
        #LIST {temppath} {delete} {1};
        kill liao bing;
      };
    };
  };
  #ACTION {^%*这点本事也来劫营} {
    #CLASS questclass KILL;
    #NOP {失败了};
    questfail {$questmodule};
    yun qi;
    hp;
    loc {%1};
  };
  #ACTION {^你于%*营救萧峰失败} {
    #CLASS questclass KILL;
    #NOP {失败了};
    questfail {$questmodule};
    yun qi;
    hp;
    loc {%1};
  };
  #ACTION {^你们边打边退，一路奔波，连续走过好几个地方，才来到一个一片树林边} {
    #SEND {@@};
  };
  #ACTION {^你于%*终于将萧峰从大辽解救出来} {
    #CLASS questclass KILL;
    questupdate {$questmodule} {5};
    dohalt {
      loc {%1}
    };
  };
  #CLASS questclass CLOSE;
  push anshi;
  w;
};
#NOP {大辽救援，完毕};
#ALIAS {tlbb_jiuyuan_finish} {
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你们边打边退，一路奔波，连续走过好几个地方，才来到一个一片树林边} {
    #SEND {@@};
  };
  #ACTION {^你于%*终于将萧峰从大辽解救出来} {
    #CLASS questclass KILL;
    questupdate {$questmodule} {5};
    dohalt {
      loc {%1}
    };
  };
  #CLASS questclass CLOSE;
  s;
};