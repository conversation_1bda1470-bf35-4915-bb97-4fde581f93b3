#NOP {洪七公做菜任务};
#ALIAS {jobgo_zuocai} {
  #IF {@carryqty{silver} < 100} {
    #LOCAL {localbank} {@getLocalBank{}};
    gotodo {$localbank[city]} {$localbank[room]} {balanceex {} {200} {jobgo_zuocai}}
  };
  #ELSE {
    gotoroom {1865} {startfull {jobask_zuocai}}
  };
};
#NOP {接收长乐帮任务};
#ALIAS {jobask_zuocai} {
  gotonpc {洪七公} {jobask_zuocai_ask}
};
#ALIAS {jobask_zuocai_ask} {
  #NOP {询问结果,0:成功,1:放弃,2:busy,3:空挡,4:继续任务,5:其他选项};
  #VARIABLE {askresult} {0};
  #VARIABLE {joblist_zuocai} {};
  #VARIABLE {jobzones_zuocai} {};
  #VARIABLE {jobstart_ts} {@now{}};
  #VARIABLE {askdo} {
    ask bei haishi about job;
    time;
    set action jobask
  };
  #CLASS jobrequestclass OPEN;
  #ACTION {^你向洪七公打听有关『玉笛谁家听落梅』的消息。} {
    #CLASS jobresponseclass OPEN;
    #ACTION {^洪七公说道：「嗯？我不是告诉你了吗，快去取原料啊，不想做就算了！」} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {jobfangqi_zuocai_ask}
    };
    #ACTION {^洪七公说道：「我现在不饿，你还是先去休息一会吧。」} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {on_zuocai_wait};
    };
    #ACTION {^洪七公说道：「这位%*的潜能已经这么多了，还是先去用完再来吧} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {cun_pot {jobgo_zuocai}};
    };
    #ACTION {^洪七公在你的耳边悄声说道：听说%*的%*手中有%*，你帮我去找来吧！} {
      #VARIABLE {joblist_zuocai[%%%3]} {
        {location} {%%%1}
        {npc} {%%%2}
        {city} {}
        {roomlist} {}
      };
    };
    #ACTION {^洪七公说道：「{我这里已经有些原料|唉！我这里什么原料都没有}} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      #NOP {先解析全部地址，如有不可到达的直接放弃};
      #VARIABLE {jobstart_ts} {@now{}};
      dohalt {jobget_zuocai {1}}
    };
    #CLASS jobresponseclass CLOSE;
  };
  #CLASS jobrequestclass CLOSE;
  jobclear_zuocai;
  execute {
    time;
    cond;
    ask hong qigong about 玉笛谁家听落梅
  };
};
#NOP {解析做菜地址，%1:地址索引};
#ALIAS {jobget_zuocai} {
  #IF {%1 > &joblist_zuocai[]} {
    jobdo_zuocai
  };
  #ELSE {
    parsejoblocation {$joblist_zuocai[+%1][location]} {jobset_zuocai {%1}} {jobfangqi_zuocai}
  };
};
#NOP {保存做菜地址，%1:地址索引};
#ALIAS {jobset_zuocai} {
  #VARIABLE {joblist_zuocai[+%1][city]} {$jobcity};
  #VARIABLE {joblist_zuocai[+%1][roomlist]} {$jobroomlist};
  #LOCAL {zone} {mainland};
  #IF {@contains{{common[mainland]}{$jobcity}} > 0} {
    #LOCAL {zone} {mainland};
  };
  #ELSEIF {@contains{{common[northland]}{$jobcity}} > 0} {
    #LOCAL {zone} {northland};
  };
  #ELSEIF {@contains{{common[westland]}{$jobcity}} > 0} {
    #LOCAL {zone} {westland};
  };
  #ELSEIF {@contains{{common[southland]}{$jobcity}} > 0} {
    #LOCAL {zone} {southland};
  };
  #VARIABLE {jobzones_zuocai[$zone][*joblist_zuocai[+%1]]} {*joblist_zuocai[+%1]};
  jobget_zuocai {@eval{%1 + 1}}
};
#NOP {寻找做菜NPC};
#ALIAS {jobdo_zuocai} {
  #VARIABLE {jobnpc_zone} {};
  #VARIABLE {jobnpc_zuocai} {};
  #VARIABLE {jobitem_zuocai} {};
  #NOP {根据坐在区域就近选择区域进行寻找};
  #LOCAL {nextzone} {@getLocalZone{}};
  #IF {"$jobzones_zuocai[$nextzone]" != ""} {
    #VARIABLE {jobnpc_zone} {$nextzone};
  };
  #ELSEIF {"$jobzones_zuocai[mainland]" != ""} {
    #VARIABLE {jobnpc_zone} {mainland};
  };
  #ELSEIF {"$jobzones_zuocai[northland]" != ""} {
    #VARIABLE {jobnpc_zone} {northland};
  };
  #ELSEIF {"$jobzones_zuocai[westland]" != ""} {
    #VARIABLE {jobnpc_zone} {westland};
  };
  #ELSEIF {"$jobzones_zuocai[southland]" != ""} {
    #VARIABLE {jobnpc_zone} {southland};
  };
  #ELSE {
    #VARIABLE {jobnpc_zone} {mainland};
  };
  #IF {&jobzones_zuocai[$jobnpc_zone][] > 0} {
    #VARIABLE {jobitem_zuocai} {$jobzones_zuocai[$jobnpc_zone][+1]};
    #VARIABLE {joblocation} {$joblist_zuocai[$jobitem_zuocai][location]};
    #VARIABLE {jobnpc_zuocai} {$joblist_zuocai[$jobitem_zuocai][npc]};
    #VARIABLE {jobroomlist} {$joblist_zuocai[$jobitem_zuocai][roomlist]};
    time;
    wwp;
    pfm_buff_normal;
    follow none;
    jobnextroom {checkowner {%1}};
  };
  #ELSE {
    jobfinish_zuocai
  };
};
#NOP {检查做菜的NPC};
#ALIAS {checkowner} {
  #VARIABLE {checkcount} {0};
  #VARIABLE {jobnpc_zuocai_id} {};
  #CLASS jobcheckclass OPEN;
  #ACTION {$jobnpc_zuocai(%*)} {
    #VARIABLE {jobnpc_zuocai_id} {@lower{%%1}};
  };
  #ACTION {^{设定环境变量：action \= \"checkowner\"|你设定checkowner为反馈信息}} {
    #VARIABLE {echots} {0};
    #VARIABLE {idle} {0};
    #IF {"$jobnpc_zuocai_id" == ""} {
      #CLASS jobcheckclass KILL;
      #DELAY {0.5} {
        jobnextroom {checkowner}
      };
    };
    #ELSE {
      #CLASS jobcheckclass KILL;
      follow $jobnpc_zuocai_id;
      jobfight_zuocai
    };
  };
  #CLASS jobcheckclass CLOSE;
  look;
  echo {checkowner};
};
#NOP {做菜任务索要物品};
#ALIAS {jobfight_zuocai} {
  #VARIABLE {askflag} {0};
  #VARIABLE {silveramount} {0};
  #CLASS jobfightclass KILL;
  #CLASS jobfightclass OPEN;
  #ACTION {^$jobnpc_zuocai说道：「嘿嘿，你总要表示表示吧？就%*两银子吧} {
    #VARIABLE {askflag} {1};
    #VARIABLE {silveramount} {@ctd{%%1}};
    #SHOWME {<faa>给钱};
  };
  #ACTION {^$jobnpc_zuocai说道：「%*拿去吧} {
    #VARIABLE {askflag} {2};
    #SHOWME {<faa>给了};
  };
  #ACTION {^{设定环境变量：action \= \"checkresponse\"|你设定checkresponse为反馈信息}} {
    #VARIABLE {echots} {0};
    #CLASS jobfightclass KILL;
    #IF {$askflag == 0} {
      dohalt {jobfight_zuocai}
    };
    #ELSE {
      #UNVARIABLE {joblist_zuocai[$jobitem_zuocai]};
      #UNVARIABLE {jobzones_zuocai[$jobnpc_zone][$jobitem_zuocai]};
      dohalt {
        #IF {$askflag == 1} {
          give $silveramount silver to $jobnpc_zuocai_id
        };
        loc {dohalt {jobdo_zuocai}}
      }
    };
  };
  #CLASS jobfightclass CLOSE;
  dohalt {
    ask $jobnpc_zuocai_id about $jobitem_zuocai;
    echo {checkresponse}
  }
};
#NOP {完成洪七公做菜任务};
#ALIAS {jobfinish_zuocai} {
  gotonpc {洪七公} {jobfinish_zuocai_ask}
};
#ALIAS {jobfinish_zuocai_ask} {
  #NOP {询问结果,0:完成,1:busy,2:没有任务,3:失败};
  #VARIABLE {askresult} {0};
  #CLASS jobrequestclass OPEN;
  #ACTION {^你向洪七公打听有关『finish』的消息。} {
    #VARIABLE {idle} {0};
    #CLASS jobresponseclass OPEN;
    #ACTION {^洪七公说道：「我有叫你去做什么吗？你完成什么啊？」} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {jobprepare};
    };
    #ACTION {^洪七公说道：「你真的完成了？？」} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {jobfangqi_zuocai};
    };
    #ACTION {^洪七公指点了你一些武学上的迷津，你获得了%*点潜能} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      joblog {成功完成，获得 0 点经验 %%%1 点潜能，耗时@elapsed{$jobstart_ts}秒。};
      jobclear_zuocai;
      dohalt {jobprepare};
    };
    #CLASS jobresponseclass CLOSE;
  };
  #CLASS jobrequestclass CLOSE;
  zuo cai;
  ask hong qigong about finish;
};
#NOP {放弃做菜任务};
#ALIAS {jobfangqi_zuocai} {
  gotonpc {洪七公} {jobfangqi_zuocai_ask}
};
#ALIAS {jobfangqi_zuocai_ask} {
  #VARIABLE {askresult} {0};
  #CLASS jobrequestclass OPEN;
  #ACTION {^你向洪七公打听有关『fangqi』的消息。} {
    #CLASS jobresponseclass OPEN;
    #ACTION {^洪七公说道：「既然做不了，也就不勉强你了。」} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {jobprepare};
    };
    #ACTION {^洪七公说道：「我有叫你去做什么吗？你放弃什么啊？」} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {jobprepare};
    };
    #CLASS jobresponseclass CLOSE;
  };
  #CLASS jobrequestclass CLOSE;
  ask hong qigong about fangqi;
};
#ALIAS {jobclear_zuocai} {
  #VARIABLE {joblist_zuocai} {};
  #VARIABLE {jobzones_zuocai} {};
  #VARIABLE {jobnpc_zone} {};
  #VARIABLE {jobnpc_zuocai} {};
  #VARIABLE {jobitem_zuocai} {};
  #LIST {jobroomlist} {clear};
  #VARIABLE {jobstart_ts} {0};
};