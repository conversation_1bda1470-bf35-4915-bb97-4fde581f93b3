#NOP {无相劫指,%1:后续指令};
#ALIAS {goquest_wxjz} {
  #VARIABLE {questmodule} {无相劫指};
  gotonpc {方生大师} {wxjz_askfang {%1}}
};
#NOP {找方生};
#ALIAS {wxjz_askfang} {
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你向方生大师打听有关『少林武技』的消息。} {
    dohalt {ask fangsheng dashi about 因果}
  };
  #ACTION {^你向方生大师打听有关『因果』的消息。} {
    dohalt {ask fangsheng dashi about 后山}
  };
  #ACTION {^你向方生大师打听有关『后山』的消息。} {
    dohalt {ask fangsheng dashi about 挠钩}
  };
  #ACTION {^你向方生大师打听有关『挠钩』的消息。} {
    dohalt {ask fangsheng dashi about 套索}
  };
  #ACTION {^你向方生大师打听有关『套索』的消息。} {
    echo {checkid}
  };
  #ACTION {^{设定环境变量：action \= \"checkid\"|你设定checkid为反馈信息}} {
    #CLASS questclass KILL;
    #IF {@carryqty{nao gou} == 0 || @carryqty{tao suo} == 0} {
      questdelay {$questmodule} {0} {1800}
      dohalt {%1}
    };
    #ELSE {
      gotonpc {无相禅师} {wxjz_askwuxiang {%1}}
    };
  };
  #CLASS questclass CLOSE;
  ask fangsheng dashi about 少林武技
};
#ALIAS {wxjz_askwuxiang} {
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你向无相禅师打听有关『少林武技』的消息。} {
    dohalt {ask wuxiang chanshi about 无相劫指}
  };
  #ACTION {^你向无相禅师打听有关『无相劫指』的消息。} {
    dohalt {bo 木屑}
  };
  #ACTION {^你双手笼袖，运气鼓劲，地上的碎木屑突然飞舞跳跃起来，便似有人以一根无形的细棒挑动搅拨一般} {
    questsuccess {$questmodule};
    %1;
  };
  #CLASS questclass CLOSE;
  ask wuxiang chanshi about 少林武技
};