#NOP {扩展接口};
#NOP {当做雪山任务前，%1:后续指令};
#ALIAS {on_xueshan_before_go} {
  #NOP {检查CD};
  #IF {@eval{$hp[busy][雪山]} > 60} {
    #NOP {去做一轮其他的};
    #LOCAL {nextjob} {@getNextJob{雪山}};
    #IF {"$nextjob" == "" || @eval{$hp[busy][$nextjob]} >= 60} {
      #LOCAL {nextjob} {长乐帮};
      #IF {"$hp[shen]" == "正气"} {
        #LOCAL {nextjob} {华山}
      };
    };
    jobgo {$nextjob} {雪山}
  };
  #ELSEIF {"$hp[shen]" == "正气"} {
    #NOP {华山村玄坛庙给钱降神};
    reduceshen_buy {10000} {on_xueshan_before_go {%1}};
  };
  #ELSEIF {$hp[shen_num] < 10000} {
    #NOP {有药吃药，吃完去接任务};
    #IF {@carryqty{xieqi wan} > 0} {
      fu xieqi wan;
      i;
      hp;
      %1
    };
    #ELSE {
      #NOP {没药补全};
      replenishmedicine_cd {xieqi wan} {on_xueshan_before_go {%1}}
    };
  };
  #ELSE {
    %1
  };
};
#NOP {扩展接口};
#NOP {当接雪山任务前，%1:后续指令};
#ALIAS {on_xueshan_before_ask} {
  #CLASS jobcheckclass KILL;
  #CLASS jobcheckclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkshen\"|你设定checkshen为反馈信息}} {
    #CLASS jobcheckclass KILL;
    #IF {$hp[shen_num] >= 10000} {
      %1
    };
    #ELSEIF {@carryqty{xieqi wan} > 0} {
      #DELAY {1} {on_xueshan_before_ask {%1}}
    };
    #ELSE {
      replenishmedicine_cd {xieqi wan} {jobgo_xueshan}
    };
  };
  #CLASS jobcheckclass CLOSE;
  #IF {$hp[shen_num] < 10000} {
    fu xieqi wan;
    hp;
    i;
    echo {checkshen}
  };
  #ELSE {
    #CLASS jobcheckclass KILL;
    %1
  };
};
#NOP {当等待雪山任务时};
#ALIAS {on_xueshan_wait} {
  waitlian;
  #IF {@eval{$hp[busy][雪山]} > 20 || @eval{$hp[busy][公共]} > 20} {
    gotodo {大雪山} {入幽口} {startfull {jobask_xueshan} {2}};
  };
  #ELSE {
    gotodo {大雪山} {入幽口} {startfull {jobask_xueshan} {1}};
  };
};
#NOP {当雪山保镖搞定后，%1:后续指令};
#ALIAS {on_xueshan_down} {
  %1
};
#NOP {当雪山任务完成，%1:后续指令};
#ALIAS {on_xueshan_finish} {
  #NOP {雪山武当混做先去狄云那里降个神，因为正气丹最少+7000，肯定要吃两颗，这里留4000负神};
  #IF {@contains{{conf[joblist]}{武当}} > 0} {
    reduceshen_fu {4000} {
      fu zhengqi dan;
      #DELAY {1} {
        %1
      }
    };
  };
  #ELSE {
    %1
  };
};
#NOP {当做武当任务前，%1:后续指令};
#ALIAS {on_wudang_before_go} {
  #NOP {当做雪山任务且上一个任务不是雪山时可以直接去做雪山，注意必须判定雪山的任务busy时间，不然和on_xueshan_before_go会形成无限嵌套};
  #IF {@contains{{conf[joblist]}{雪山}} > 0 && "$hp[lastjob]" != "雪山" && @eval{$hp[busy][雪山]} < 60} {
    jobgo {雪山};
  };
  #ELSEIF {@eval{$hp[busy][武当]} >= 60} {
    #NOP {去做一轮其他的};
    #LOCAL {nextjob} {@getNextJob{武当}};
    #IF {"$nextjob" == "" || @eval{$hp[busy][$nextjob]} >= 60} {
      #LOCAL {nextjob} {长乐帮};
      #IF {"$hp[shen]" == "正气"} {
        #LOCAL {nextjob} {华山}
      };
    };
    jobgo {$nextjob} {武当}
  };
  #ELSE {
    #IF {"$hp[shen]" == "戾气" || $hp[shen_num] < 10000} {
      #IF {@carryqty{zhengqi dan} < 2} {
        fu zhengqi dan;
        replenishmedicine_cd {zhengqi dan} {on_wudang_before_go {%1}}
      };
      #ELSE {
        fu zhengqi dan;
        i;
        hp;
        %1;
      };
    };
    #ELSE {
      %1
    };
  };
};
#NOP {当接武当任务前，%1:后续指令};
#ALIAS {on_wudang_before_ask} {
  #CLASS jobcheckclass KILL;
  #CLASS jobcheckclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkshen\"|你设定checkshen为反馈信息}} {
    #CLASS jobcheckclass KILL;
    #IF {$hp[shen_num] >= 10000} {
      %1
    };
    #ELSEIF {@carryqty{zhengqi dan} > 0} {
      #DELAY {1} {on_wudang_before_ask {%1}}
    };
    #ELSE {
      replenishmedicine_cd {zhengqi dan} {jobgo_wudang}
    };
  };
  #CLASS jobcheckclass CLOSE;
  #IF {$hp[shen_num] < 10000} {
    fu zhengqi dan;
    hp;
    i;
    echo {checkshen}
  };
  #ELSE {
    #CLASS jobcheckclass KILL;
    %1
  };
};
#NOP {当等待武当任务时};
#ALIAS {on_wudang_wait} {
  #IF {@eval{$hp[busy][武当]} >= 60} {
    #IF {"@getNextJob{}" == "武当" && "$hp[lastjob]" != "华山"} {
      jobgo {华山}
    };
    #ELSE {
      jobgo {送信2}
    };
  };
  #ELSE {
    #NOP {等着吧};
    waitlian;
    #IF {@eval{$hp[busy][武当]} > 20 || @eval{$hp[busy][公共]} > 20} {
      gotodo {武当山} {天乙真庆宫} {startfull {jobask_wudang} {2}};
    };
    #ELSE {
      gotodo {武当山} {天乙真庆宫} {startfull {jobask_wudang} {1}};
    };
  };
};
#NOP {当武当NPC搞定后，%1:后续指令};
#ALIAS {on_wudang_down} {
  %1
};
#NOP {当武当任务完成，%1:后续指令};
#ALIAS {on_wudang_finish} {
  %1
};
#NOP {当做华山任务前，%1:后续指令};
#ALIAS {on_huashan_before_go} {
  %1
};
#NOP {当接华山任务前，%1:后续指令};
#ALIAS {on_huashan_before_ask} {
  #IF {$hp[shen_num] >= 10000} {
    %1
  };
  #ELSE {
    #IF {@carryqty{zhengqi dan} > 0} {
      fu zhengqi dan;
    };
    %1
  };
};
#NOP {当做送信任务前，%1:后续指令};
#ALIAS {on_songxin_before_go} {
  #NOP {当做雪山任务且上一个任务不是雪山时可以直接去做雪山，注意必须判定雪山的任务busy时间，不然和on_xueshan_before_go会形成无限嵌套};
  #IF {@contains{{conf[joblist]}{雪山}} > 0 && "$hp[lastjob]" != "雪山" && @eval{$hp[busy][雪山]} < 60} {
    jobgo {雪山};
  };
  #ELSE {
    %1
  };
};
#NOP {当接送信任务前，%1:后续指令};
#ALIAS {on_songxin_before_ask} {
  %1
};
#NOP {当送信任务等待时};
#ALIAS {on_songxin_wait} {
  #NOP {如果是混作，补一下货};
  #IF {@contains{{conf[joblist]}{武当}} > 0 && @contains{{conf[joblist]}{雪山}} > 0} {
    #IF {@isNeedReplenish{zhengqi dan} == 1 || @isNeedReplenish{xieqi wan} == 1} {
      replenishmedicine_dl {zhengqi dan} {replenishmedicine_dl {xieqi wan} {jobgo_songxin}}
    };
    #ELSE {
      waitlian;
      #IF {@eval{$hp[busy][送信]} > 20 || @eval{$hp[busy][公共]} > 20} {
        gotodo {大理城} {马房} {startfull {jobask_songxin} {2}};
      };
      #ELSE {
        gotodo {大理城} {马房} {startfull {jobask_songxin} {1}};
      };
    };
  };
  #ELSE {
    waitlian;
    #IF {@eval{$hp[busy][送信]} > 20 || @eval{$hp[busy][公共]} > 20} {
      gotodo {大理城} {马房} {startfull {jobask_songxin} {2}};
    };
    #ELSE {
      gotodo {大理城} {马房} {startfull {jobask_songxin} {1}};
    };
  };
};
#NOP {当送信任务完成，%1:后续指令};
#ALIAS {on_songxin_finish} {
  %1
};