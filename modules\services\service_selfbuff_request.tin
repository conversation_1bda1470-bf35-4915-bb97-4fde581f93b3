#NOP {找我加强};
#ALIAS {selfbuff_call} {
  #IF {"$hp[master][name]" != "任我行"} {
    %1
  };
  #ELSEIF {$hp[exp] < 110000000} {
    %1
  };
  #ELSEIF {@eval{$kungfu[spec][yuanshi-jianfa][lv]} < 220} {
    %1
  };
  #ELSEIF {"$kungfu[base][force][jifa]" != "xixing-dafa"} {
    %1
  };
  #ELSEIF {"$env[powerup]" == "YES"} {
    %1
  };
  #ELSEIF {$kungfu[base][sword][effectlv] > @eval{$kungfu[base][force][lv]*10}} {
    #IF {"$env[powerup]" != "YES"} {
      #VARIABLE {env[powerup]} {YES};
      set env_powerup;
    };
    #ELSE {
      %1
    };
  };
  #ELSEIF {@carryqty{changjian} == 0} {
    buybaseweapon {changjian} {selfbuff_call {%1}}
  };
  #ELSE {
    gotoroom {2523} {selfbuff_call_start {%1}}
  };
};
#ALIAS {selfbuff_call_start} {
  #CLASS serviceclass KILL;
  #CLASS serviceclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkstatus\"|你设定checkstatus为反馈信息}} {
    #IF {"$id[weapon]" != "changjian"} {
      #CLASS serviceclass KILL;
      #DELAY {1} {
        selfbuff_call_start {%1}
      }
    };
    #ELSE {
      fight tong ren;
      perform yuanshi-jianfa.jianzhong;
      jifa;
      yun qi;
      echo {checklevel}
    };
  };
  #ACTION {^{设定环境变量：action \= \"checklevel\"|你设定checklevel为反馈信息}} {
    #VARIABLE {idle} {0};
    resonate {checklevel};
    #VARIABLE {targetlv} {$kungfu[base][force][effectlv]};
    #IF {$hp[max_lv] < 1000} {
      #MATH {targetlv} {$targetlv * 30};
    };
    #ELSEIF {$hp[max_lv] < 1500} {
      #MATH {targetlv} {$targetlv * 20};
    };
    #ELSE {
      #MATH {targetlv} {$targetlv * 15};
    };
    #IF {$kungfu[base][sword][effectlv] < $targetlv} {
      #DELAY {1} {
        ensure {
          fight tong ren;
          perform yuanshi-jianfa.jianzhong;
          jifa;
        } {checklevel}
      };
    };
    #ELSE {
      #CLASS serviceclass KILL;
      set 原始见终 威猛;
      #VARIABLE {env[powerup]} {YES};
      set env_powerup;
      dohalt {%1}
    };
  };
  #CLASS serviceclass CLOSE;
  closewimpy;
  yun qi;
  jiali 10;
  jifa sword yuanshi-jianfa;
  jifa parry yuanshi-jianfa;
  set 原始见终 迅捷;
  wwp {changjian};
  echo {checkstatus}
};