#NOP {状态模块};
#CLASS statusmodule KILL;
#CLASS statusmodule OPEN;
#ACTION {^「书剑· 情怀」共有%*位玩家在%*处站点连线中} {
	#VARIABLE {env[playernumber]} {@ctd{%1}};
};
#ACTION {^┃称    谓：【 %1 】}  {#VARIABLE hp[title] %1};
#ACTION {^┃头    衔：%*%!s┃膂  力：「%*/%*」%!s根  骨：「%*/%*」} 
{
	#VARIABLE hp[rank] %1; 
	#VARIABLE hp[str] %2; 
	#VARIABLE hp[con] %4;
	#VARIABLE hp[str_xt] %3; 
	#VARIABLE hp[con_xt] %5;
}; 
#ACTION {^┃姓    名：%*(%*)%!s┃身  法：「%*/%*」%!s悟  性：「%*/%*」}
{
	#VARIABLE hp[name] %1; 
	#FORMAT {hp[id]} {%l} {%2}; 
	#VARIABLE hp[dex] %3; 
	#VARIABLE hp[dex_xt] %4;
	#VARIABLE hp[int] %5;
	#VA<PERSON><PERSON><PERSON> hp[int_xt] %6;
};
#ACTION {^┃外    号：%1%s┗} {#variable hp[nickname] %1};
#ACTION {^┃年    龄：%1岁又%2个月 } {
	#variable hp[age] @ctd{%1};
	#variable hp[month] @ctd{%2}
};
#ACTION {^┃性    别：%1性%!s攻：%* 躲：%* 架：%*┃} {
	#IF {"%1" == "男"} {
		#variable hp[sex] {m};
	};
	#ELSEIF {"%1" == "女"} {
		#variable hp[sex] {f};
	};
	#ELSE {
		#variable hp[sex] {n};
	};
	#VARIABLE {hp[attack]} {@trim{%2}};
	#VARIABLE {hp[dodge]} {@trim{%3}};
	#VARIABLE {hp[parry]} {@trim{%4}};
};
#ACTION {┃钱庄存款：%1锭黄金 %s师    承：【%2】}
{
	#VARIABLE hp[balance] @ctd{%1};
	#VARIABLE hp[party] %2;
	#IF {"$hp[party]" != "普通百姓"} {
		#VARIABLE {hp[master][name]} {自学};
	};
	#ELSE {
		#VARIABLE {hp[master]} {};
	};
};
#ACTION {┃钱庄存款：很少 %s师    承：【%2】}
{
	#VARIABLE hp[balance] {0};
	#VARIABLE hp[party] %2;
	#IF {"$hp[party]" != "普通百姓"} {
		#VARIABLE {hp[master][name]} {自学};
	};
	#ELSE {
		#VARIABLE {hp[master]} {};
	};
};
#ACTION {┃钱庄存款：%1锭黄金 %s师    承：【%2】【%3】}
{	
	#VARIABLE hp[balance] @ctd{%1};
	#VARIABLE hp[party] %2; 	
	#VARIABLE hp[master][name] {%3};
} {4};
#ACTION {┃钱庄存款：很少 %s师    承：【%2】【%3】}
{	
	#VARIABLE hp[balance] {0};
	#VARIABLE hp[party] %2; 	
	#VARIABLE hp[master][name] {%3};
} {4};
#ACTION {┃情 怀 币：%*枚} {
	#VARIABLE hp[tongbao] {@ctd{%1}};
};
#ACTION {┃情 怀 币：无} {
	#VARIABLE hp[tongbao] {0};
};
#ACTION {·精血·%s%1%s/%s%2%s(%3%)%s ·精力·%s%4%s/%s%5(%6)}  
{
	#VARIABLE hp[jing] %1;
	#VARIABLE hp[jing_max] %2;
	#VARIABLE hp[jing_per] %3;
	#VARIABLE hp[jingli] %4;
	#VARIABLE hp[jingli_max] %5;
	#VARIABLE hp[jingli_limit] %6;
	#IF {$hp[jing_per] < 100} {
		#VARIABLE hp[jing_wound] @eval{$hp[jing_max] * 100 / $hp[jing_per] - $hp[jing_max]};
	};
	#ELSE {
		#VARIABLE hp[jing_wound] {0}
	};
};
	
#ACTION {·气血·%s%1%s/%s%2%s(%s%3%)%s·内力·%s%4%s/%s%5(\+%6)}  
{
	#VARIABLE hp[qi] %1;
	#VARIABLE hp[qi_max] %2;
	#VARIABLE hp[qi_per] %3;
	#VARIABLE hp[qi_ratio] @eval{%1*100/(%2*100/%3)};
	#VARIABLE hp[neili] %4;
	#VARIABLE hp[neili_max] %5;
	#VARIABLE hp[neili_jiali] %6;
	#IF {$hp[qi_per] < 100} {
		#VARIABLE hp[qi_wound] @eval{$hp[qi_max] * 100 / $hp[qi_per] - $hp[qi_max]};
	};
	#ELSE {
		#VARIABLE hp[qi_wound] {0}
	};
};
#ACTION {^┃{夫君|娇妻}：%*(%*)} {
	#VARIABLE {hp[spouse]} {@lower{%3}};
};
#ACTION {^┃婚姻：未婚} {
	#VARIABLE {hp[spouse]} {};
};
#ACTION {·{正气|戾气}· %2%s·内力上限·%s%3 /%s%4}
{
	#VARIABLE hp[shen] %1;
	#VARIABLE hp[shen_num] %2;
	#REPLACE hp[shen_num] {,}{};
	#VARIABLE hp[neili_limit] %3;
	#VARIABLE hp[neili_limit_max] %4; 
};
#ACTION {·食物·%s%1\%%s·潜能·%s%2%s/%s%3} 
{
	#VARIABLE hp[food] %1;
	#VARIABLE hp[pot] %2;
	#VARIABLE hp[pot_max] %3;
	#VARIABLE {hp[max_lv]} {@eval{$hp[pot_max] - 100}};
};
#ACTION {·饮水· %s%1\%%s·经验·%s%2\ (%3\%\)} 
{
	#VARIABLE hp[water] %1;
	#VARIABLE hp[exp] %2;
	#REPLACE hp[exp] {,}{};
	#VARIABLE hp[exp_per] %3;
};
#ACTION {^经验值没有变动} {
	#VARIABLE hp[expgain] {0};
};
#ACTION {^经验值增加了%*点} {
	#VARIABLE hp[expgain] @ctd{%1}
};
#ACTION {^经验值减少了%*点} {
	#VARIABLE hp[expgain] -@ctd{%1}
};
#ACTION {^每小时进帐：%1点经验。} {
	#VARIABLE hp[expspeed] @ctd{%1}
};
#ACTION {^每小时亏损：%1点经验。} {
	#VARIABLE hp[expspeed] -@ctd{%1}
};
#ACTION {^你身上带着%*件东西(负重%*)：$} 
{
	#VARIABLE id[weight] %2;
	#REPLACE id[weight] {%} {};
	#VARIABLE id[weapon] {};
	#VARIABLE id[things] {};
	#CLASS idclass open;
	#ACTION {^>} {
		#CLASS idclass kill;
		defineweaponmap
	};
	#ACTION {^  %u盒大还丹(盒)(Da huandan)} {
		#VARIABLE {id[things][@lower{da huandan}]} {{name}{大还丹(盒)}{unit}{盒}{qty}{@ctd{%%1}}};
	} {1};
	#ACTION {^  %u{锭|两|文|颗|棵|条|张|封|只|支|双|件|把|根|柄|本|对|个|根|块|张|枚|份|茧|卷|捆|艘|盒|方|枝|朵|面|片}%*(%*)} {
		#IF {"$id[things][@lower{%%4}]" == ""} {
			#VARIABLE {id[things][@lower{%%4}]} {{name}{%%3}{unit}{%%2}{qty}{@ctd{%%1}}};
		};
		#ELSE {
			#VARIABLE {id[things][@lower{%%4}][name]} {$id[things][@lower{%%4}][name];%%3};
		};
	};
	#NOP {装备的物品};
	#ACTION {^□%u(%*)} {
		#VARIABLE {id[things][@lower{%%2}]} {{name}{%%1}{unit}{}{qty}{1}};
		#IF {"%%1"=="铁桨"} {
			#VARIABLE {id[weapon]} {tie jiang};
		};
		#ELSEIF {"%%1"=="小树枝"} {
			#VARIABLE {id[weapon]} {xiao shuzhi};
		};
		#ELSE {
			#REGEXP {%%1} {%u{剑|刀|锤|棍|杖|鞭|钩|棒|斧|笔|箫|匕|匕首|针|枪|琴}} {
				#VARIABLE {id[weapon]} {@lower{%%2}};
			};
		};
	};
	#CLASS idclass close;
};
#ACTION {^您目前还剩%*枚情怀币} {
	#VARIABLE {hp[tongbao]} {@ctd{%1}};
};
#ACTION {^您在敝银行共存有%u点潜能。} {
	#VARIABLE {hp[balance_pot]} {@ctd{%1}};
};
#ACTION {^等级和经验简明对照表} {
	#VARIABLE {lvmaps} {};
	#CLASS lvcapclass OPEN;
	#ACTION {^%d%!s%d%%!s|%!s%d%!s%d%%!s|%!s%d%!s%d%!s|%!s%d%!s%d%!s|%*} {
		#VARIABLE {lvmaps[%%1]} {%%2};
		#VARIABLE {lvmaps[%%3]} {%%4};
		#VARIABLE {lvmaps[%%5]} {%%6};
		#VARIABLE {lvmaps[%%7]} {%%8};
	};
	#ACTION {^您本次在线} {
		#CLASS lvcapclass KILL;
	};
	#CLASS lvcapclass CLOSE;
};
#NOP {现在是书剑%u日%u时%u。};
#ACTION {^现在是书剑%u日%u时%u。} {
	#VARIABLE {env[onlinehours]} {1};
	#VARIABLE {env[vip]} {0};
  #SWITCH {"%2"} {
    #CASE {"子"} {#VARIABLE {env[gametime]} {23}};
    #CASE {"丑"} {#VARIABLE {env[gametime]} {1}};
    #CASE {"寅"} {#VARIABLE {env[gametime]} {3}};
    #CASE {"卯"} {#VARIABLE {env[gametime]} {5}};
    #CASE {"辰"} {#VARIABLE {env[gametime]} {7}};
    #CASE {"巳"} {#VARIABLE {env[gametime]} {9}};
    #CASE {"午"} {#VARIABLE {env[gametime]} {11}};
    #CASE {"未"} {#VARIABLE {env[gametime]} {13}};
    #CASE {"申"} {#VARIABLE {env[gametime]} {15}};
    #CASE {"酉"} {#VARIABLE {env[gametime]} {17}};
    #CASE {"戌"} {#VARIABLE {env[gametime]} {19}};
    #CASE {"亥"} {#VARIABLE {env[gametime]} {21}};
  };
  #LOCAL {ketime} {%3};
  #IF {"$ketime" != "正"} {
    #REPLACE {ketime} {刻} {};
    #MATH {env[gametime]} {$env[gametime] + @ctd{$ketime}*0.5};
  };
  #IF {$env[gametime] > 24} {
    #MATH {env[gametime]} {$env[gametime - 24]};
  };
	#VARIABLE {env[sjtime]} {%1日%2时%3};
  night_process
};
#ACTION {^您参与游戏的主机北京时间是 %* %* %+2d:%+2d:%+2d} {
	#FORMAT env[realtime] {%T};
};
#ACTION {^逍遥游生效时：%*} {
	#VARIABLE {env[vip]} {1};
};
#ACTION {^你最近玩了%*天%*小时} {
	#VARIABLE {env[onlinehours]} {@eval{@ctd{%1}*24 + @ctd{%2}}};
} {1};
#ACTION {^你最近玩了%*小时} {
	#VARIABLE {env[onlinehours]} {@ctd{%1}};
};
#NOP {是否打开调试信息};
#VARIABLE {__DEBUG__} {0};
#ALIAS {debug} {
  #IF {"%1" == "on"} {
		#SHOWME {<ffa>调试信息已打开};
    #VARIABLE {__DEBUG__} {1};
  };
  #ELSE {
		#SHOWME {<ffa>调试信息已关闭};
    #VARIABLE {__DEBUG__} {0};
  };
};
#ACTION {^在鬼谷算术的作用下，你感觉天地之间没有你不能理解的事。} {
  #VARIABLE {env[guigu]} {1};
};
#ACTION {^鬼谷算术状态：%*本周还可以使用零秒。} {
	#VARIABLE {env[guigu]} {0};
};
#ACTION {^在这个房间中, 生物及物品的(英文)名称如下：} {
	#VARIABLE {roomthings} {};
	#CLASS idhereclass OPEN;
	#ACTION {%%u = %%*} {
		#LOCAL {ids} {%%2};
		#REPLACE {ids} {, } {;};
		#LIST {roomthings[%%1]} {create} {$ids};
	} {1};
	#ACTION {^>} {
		#CLASS idhereclass kill;
	} {1};
	#CLASS idhereclass CLOSE;
};
#ACTION {^你身上%!*特殊状态} {
	#UNVARIABLE {hp[busy]};
	#UNVARIABLE {hp[condition]};
	#VARIABLE {hp[busy][公共]} {0};
	#CLASS condclass OPEN;
	#ACTION {^│{封招|闭气|气息不匀|内息紊乱}%!s%*分} {
		#VARIABLE {hp[condition][%%1]} {@eval{@ctd{%%2}*60}};
	};
	#ACTION {^│{封招|闭气|气息不匀|内息紊乱}%!s%*秒} {
		#VARIABLE {hp[condition][%%1]} {@ctd{%%2}};
	};
	#ACTION {^│七伤拳内伤%!s%*分} {
		#VARIABLE {hp[condition][七伤拳]} {@eval{@ctd{%%1}*60}};
	};
	#ACTION {^│七伤拳内伤%!s%*秒} {
		#VARIABLE {hp[condition][七伤拳]} {@ctd{%%1}};
	};
	#ACTION {^│混元掌内伤%!s%*分} {
		#VARIABLE {hp[condition][混元掌]} {@eval{@ctd{%%1}*60}};
	};
	#ACTION {^│混元掌内伤%!s%*秒} {
		#VARIABLE {hp[condition][混元掌]} {@ctd{%%1}};
	};
	#ACTION {^│一指禅内劲%!s%*分} {
		#VARIABLE {hp[condition][一指禅]} {@eval{@ctd{%%1}*60}};
	};
	#ACTION {^│一指禅内劲%!s%*秒} {
		#VARIABLE {hp[condition][一指禅]} {@ctd{%%1}};
	};
	#ACTION {^│%!s蓝砂手之刑%*分} {
		#VARIABLE {hp[condition][蓝砂手]} {600};
	};
	#ACTION {^│%*毒%!s%*分} {
		#VARIABLE {hp[condition][%%1]} {@eval{@ctd{%%2}*60}};
	};
	#ACTION {^│%*毒%!s%*秒} {
		#VARIABLE {hp[condition][%%1]} {@ctd{%%2}};
	};
	#ACTION {^│双倍经验%!s%*分} {
		#VARIABLE {hp[condition][双倍经验]} {@eval{@ctd{%%1}*60}};
	};
	#ACTION {^│双倍经验%!s%*秒} {
		#VARIABLE {hp[condition][双倍经验]} {@ctd{%%1}};
	};
	#ACTION {^│%*任务%!s%*分} {
		#VARIABLE {hp[busy][%%1]} {@eval{@ctd{%%2}*60}};
	};
	#ACTION {^│%*任务%!s%*秒} {
		#VARIABLE {hp[busy][%%1]} {@ctd{%%2}};
	};
	#ACTION {^│雪山强抢美女%!s%*分} {
		#VARIABLE {hp[busy][雪山]} {@eval{@ctd{%%1}*60}};
	};
	#ACTION {^│雪山强抢美女%!s%*秒} {
		#VARIABLE {hp[busy][雪山]} {@ctd{%%1}};
	};
	#ACTION {^│任务繁忙状态%!s%*秒} {
		#VARIABLE {hp[busy][公共]} {@ctd{%%1}};
	};
	#ACTION {^│任务繁忙状态%!s%*分} {
		#VARIABLE {hp[busy][公共]} {@eval{@ctd{%%1}*60}};
	};
	#ACTION {^当前你} {
		#CLASS condclass KILL;
	};
	#CLASS condclass CLOSE;
};
#NOP {任务记录};
#ACTION {^你完成的任务次数如下} {
	#CLASS jobtimesclass OPEN;
	#ACTION {^│%*任务%!s│%!s%d%!s次%!s│} {
		#SWITCH {"%%1"} {
			#CASE {"桃花岛守墓杀贼"} {#VARIABLE {hp[jobtimes][守墓]} {%%2}};
			#CASE {"华山岳不群惩恶扬善"} {#VARIABLE {hp[jobtimes][华山]} {%%2}};
			#CASE {"大理王府送信"} {#VARIABLE {hp[jobtimes][送信]} {%%2}};
			#CASE {"武当宋远桥杀恶贼"} {#VARIABLE {hp[jobtimes][武当]} {%%2}};
			#CASE {"宝象抢美女"} {#VARIABLE {hp[jobtimes][雪山]} {%%2}};
			#CASE {"嵩山派五岳合并"} {#VARIABLE {hp[jobtimes][嵩山]} {%%2}};
			#CASE {"天地会"} {#VARIABLE {hp[jobtimes][天地会]} {%%2}};
			#CASE {"官府六扇门追捕"} {#VARIABLE {hp[jobtimes][官府]} {%%2}};
			#CASE {"罗汉堂值勤教习"} {#VARIABLE {hp[jobtimes][教和尚]} {%%2}};
			#CASE {"少林救援"} {#VARIABLE {hp[jobtimes][护送]} {%%2}};
			#CASE {"明教巡逻"} {#VARIABLE {hp[jobtimes][巡逻]} {%%2}};
			#CASE {"丐帮吴长老杀人"} {#VARIABLE {hp[jobtimes][丐帮]} {%%2}};
			#CASE {"星宿击杀叛徒"} {#VARIABLE {hp[jobtimes][叛徒]} {%%2}};
			#CASE {"颂摩崖"} {#VARIABLE {hp[jobtimes][颂摩崖]} {%%2}};
			#CASE {"守卫襄阳"} {#VARIABLE {hp[jobtimes][守卫襄阳]} {%%2}};
		};
	};
	#ACTION {^你最近刚完成了%*任务。} {
		#CLASS jobtimesclass KILL;
		#SWITCH {"%%1"} {
			#CASE {"桃花守墓"} {#VARIABLE {hp[lastjob]} {守墓}};
			#CASE {"惩恶扬善"} {#VARIABLE {hp[lastjob]} {华山}};
			#CASE {"大理送信"} {#VARIABLE {hp[lastjob]} {送信}};
			#CASE {"武当锄奸"} {#VARIABLE {hp[lastjob]} {武当}};
			#CASE {"长乐帮"} {#VARIABLE {hp[lastjob]} {长乐帮}};
			#CASE {"强抢美女"} {#VARIABLE {hp[lastjob]} {雪山}};
			#CASE {"嵩山并派"} {#VARIABLE {hp[lastjob]} {嵩山}};
			#CASE {"天地会"} {#VARIABLE {hp[lastjob]} {天地会}};
			#CASE {"七窍玲珑"} {#VARIABLE {hp[lastjob]} {七窍玲珑}};
			#CASE {"官府捕快"} {#VARIABLE {hp[lastjob]} {官府}};
			#CASE {"训练武僧"} {#VARIABLE {hp[lastjob]} {教和尚}};
			#CASE {"恒山救援"} {#VARIABLE {hp[lastjob]} {护送}};
			#CASE {"明教巡逻"} {#VARIABLE {hp[lastjob]} {巡逻}};
			#CASE {"丐帮"} {#VARIABLE {hp[lastjob]} {丐帮}};
			#CASE {"星宿叛徒"} {#VARIABLE {hp[lastjob]} {叛徒}};
			#CASE {"抗敌颂摩崖"} {#VARIABLE {hp[lastjob]} {颂摩崖}};
			#CASE {"守卫襄阳"} {#VARIABLE {hp[lastjob]} {守卫襄阳}};			
		};
		#VARIABLE {lastjob} {$hp[lastjob]};
	};
	#CLASS jobtimesclass CLOSE;
};
#ACTION {^你目前设定的环境变量有} {
	#VARIABLE {env[wimpy]} {0};
	#VARIABLE {env[积蓄]} {0};
};
#ACTION {^wimpy                100} {
	#VARIABLE {env[wimpy]} {1};
};
#ACTION {^积蓄                 \"YES\"} {
	#VARIABLE {env[积蓄]} {1};
};
#ACTION {^您已经连续玩了%*。} {
	#VARIABLE {ol_days} {0};
	#VARIABLE {ol_hours} {0};
	#VARIABLE {ol_minutes} {0};
	#VARIABLE {ol_seconds} {};
	#VARIABLE {oldesc} {%1};
	#REGEXP {$oldesc} {%*天%*} {
		#VARIABLE {ol_days} {@ctd{&1}};
		#VARIABLE {oldesc} {&2};
	};
	#REGEXP {$oldesc} {%*小时%*} {
		#VARIABLE {ol_hours} {@ctd{&1}};
		#VARIABLE {oldesc} {&2};
	};
	#REGEXP {$oldesc} {%*分%*} {
		#VARIABLE {ol_minutes} {@ctd{&1}};
		#VARIABLE {oldesc} {&2};
	};
	#REGEXP {$oldesc} {%*秒} {
		#VARIABLE {ol_seconds} {@ctd{&1}};
	};
	#VARIABLE {env[logints]} {@eval{@now{} - $$ol_days*86400 - $ol_hours*3600 - $ol_minutes*60 - $ol_seconds}};
};
#ACTION {^{一个月又过去了|时间过得真快}} {
	#VARIABLE {env[pray]} {0};
	#VARIABLE {env[month]} {@now{}};
	set env_month $env[month];
};
#ACTION {^你{觉得自己的运气|运气已经很好}} {
	#VARIABLE {env[pray]} {1};
};
#ACTION {^【谣言】某人：听说韦小宝从韦兰铁匠那里得到了很多武器} {
	#VARIABLE {env[wxb]} {1};
};
#ACTION {「書劍」紫檀站将在 %* 分钟后重新启动} {
	#VARIABLE {env[countdown]} {@ctd{%1}};
};
#ACTION {^【扬州知府通告】近日有恶贼行凶作乱，请各位江湖豪杰速来官府领取铁捕文书，协助将彼等缉拿归案。} {
	#VARIABLE {env[wanted]} {@now{}};
};
#ACTION {^【谣言】某人：%*{死于|死了}%*} {
	#IF {"$hp[id]" == "xcjhmi"} {
		deathlog {%0}
	};
};
#ACTION {^{$buff_desc}} {
	#VARIABLE {env[buff]} {1};
};
#ACTION {^{$buff_over}} {
	#VARIABLE {env[buff]} {0};
};
#NOP {身上物品携带数量,%1:物品id};
#FUNCTION carryqty {
	#IF {"%1" == ""} {
		#RETURN {0};
	};
	#IF {"$id[things][%1]" == ""} {
		#RETURN {0};
	};
	#ELSE {
		#RETURN {$id[things][%1][qty]};
	};
};
#NOP {获取距离过月的时间};
#NOP {书剑中过月时间为19岁以前(含19)为2小时,此后每加一岁增加20分钟};
#FUNCTION getNextMonthTimespan {
	#LOCAL {duration} {7200};
	#IF {$hp[age] > 19} {
		#LOCAL {duration} {@eval{$duration + @eval{$hp[age]-19}*1200}};
	};
	#RETURN {$duration};
};
#NOP {获取指定的busy类型中最大的，%1:以分号隔开的busy类型};
#FUNCTION getConditionTick {
	#LIST {busylist} {create} {%1};
	#IF {&busylist[] == 0} {
		#RETURN {0};
	};
	#VARIABLE {maxtick} {0};
	#FOREACH {$busylist[]} {b} {
		#LOCAL {currenttick} {@eval{$hp[busy][$b]}};
		#IF {$currenttick > $maxtick} {
			#VARIABLE {maxtick} {$currenttick};
		};
	};
	#RETURN {$maxtick};
};
#NOP {获取指定的condition类型中最大的，%1:以分号隔开的busy类型};
#FUNCTION getNegtiveTick {
	#LIST {busylist} {create} {%1};
	#IF {&busylist[] == 0} {
		#RETURN {0};
	};
	#VARIABLE {maxtick} {0};
	#FOREACH {$busylist[]} {b} {
		#LOCAL {currenttick} {@eval{$hp[condition][$b]}};
		#IF {$currenttick > $maxtick} {
			#VARIABLE {maxtick} {$currenttick};
		};
	};
	#RETURN {$maxtick};
};
#ALIAS {checkcond} {
	#VARIABLE {condduration} {0};
	#VARIABLE {condtimestamp} {@now{}};
	#CLASS myclass KILL;
	#CLASS myclass OPEN;
	#ACTION {^{设定环境变量：action \= \"checkcond\"|你设定checkcond为反馈信息}} {
		#VARIABLE {idle} {0};
		#IF {"$hp[busy][武当]" == ""} {
			#CLASS myclass KILL;
			#SHOWME {<faa>完毕};
			joblog {$condduration -> 0，耗时@elapsed{$condtimestamp}} {武当};
		};
		#IF {$condduration == 0} {
			#VARIABLE {condduration} {$hp[busy][武当]};
			#VARIABLE {condtimestamp} {@now{}};
		};
		#ELSEIF {$hp[busy][武当] != $condduration} {
			joblog {$condduration -> $hp[busy][武当]，耗时@elapsed{$condtimestamp}} {武当};
			#VARIABLE {condduration} {$hp[busy][武当]};
			#VARIABLE {condtimestamp} {@now{}};
		};
		#DELAY {1} {
			cond;
			echo {checkcond}
		};
	};
	#CLASS myclass CLOSE;
	cond;
	echo {checkcond}
};
#ALIAS {checkheartbeat} {
	#VARIABLE {condduration} {0};
	#VARIABLE {condtimestamp} {@now{}};
	#CLASS myclass KILL;
	#CLASS myclass OPEN;
	#ACTION {^{设定环境变量：action \= \"checkhp\"|你设定checkhp为反馈信息}} {
		#VARIABLE {idle} {0};
		#IF {$hp[jingli] >= $hp[jingli_max]} {
			#CLASS myclass KILL;
			#SHOWME {<faa>完毕};
			joblog {$condduration -> $hp[jingli_max]，耗时@elapsed{$condtimestamp}} {武当};
		};
		#IF {$condduration == 0} {
			#VARIABLE {condduration} {$hp[jingli]};
			#VARIABLE {condtimestamp} {@now{}};
		};
		#ELSEIF {$hp[jingli] != $condduration} {
			#SHOWME {<ffa>有变化};
			joblog {$condduration -> $hp[jingli]，耗时@elapsed{$condtimestamp}} {武当};
			#VARIABLE {condduration} {$hp[jingli]};
			#VARIABLE {condtimestamp} {@now{}};
		};
		#DELAY {1} {
			hp;
			cond;
			echo {checkhp}
		};
	};
	#CLASS myclass CLOSE;
	lian sword;
	hp;
	cond;
	echo {checkhp}
};
#CLASS statusmodule CLOSE;
#SHOWME {<fac>@padRight{{状态}{12}}<fac> <cfa>模块加载完毕<cfa>};