#NOP {长乐帮任务模块};
#ALIAS {jobgo_clb} {
  gotodo {长乐帮} {卧室} {
    startfull {jobask_clb}
  };
};
#NOP {接收长乐帮任务};
#ALIAS {jobask_clb} {
  checkrequest {gotodo {长乐帮} {小厅} {jobask_clb_ask}};
};
#ALIAS {jobask_clb_ask} {
  #NOP {询问结果,0:成功,1:放弃,2:busy,3:空挡,4:继续任务,5:其他选项};
  #VARIABLE {askresult} {0};
  #VARIABLE {joblocation} {};
  #VARIABLE {jobnpc_dept} {};
  #VARIABLE {jobnpc_member} {};
  #VARIABLE {jobnpc_skill} {};
  #VARIABLE {jobnpc_desc} {};
  #VARIABLE {askdo} {
    ask bei haishi about job;
    time;
    set action jobask
  };
  #CLASS jobrequestclass OPEN;
  #ACTION {^你向贝海石打听有关『job』的消息。} {
    #CLASS jobresponseclass OPEN;
    #ACTION {^贝海石在你的耳边悄声说道：我接到飞鸽传书，%*属下帮众%*在%*处遇到袭击，你赶快前去救援！} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      #VARIABLE {jobnpc_dept} {%%%1};
      #VARIABLE {jobnpc_member} {%%%2};
      #VARIABLE {joblocation} {%%%3};
      dohalt {parsejoblocation {$joblocation} {jobdo_clb}};
    };
    #ACTION {^贝海石说道：「你不是已经进展到一定地步了，还是继续努力吧！」} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {jobfangqi_clb};
    };
    #ACTION {^贝海石说道：「你不是已经知道了，杀害我帮帮众的仇人%*} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {jobfangqi_clb};
    };
    #ACTION {^贝海石说道：「我不是告诉你了吗} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {jobfangqi_clb};
    };
    #ACTION {^贝海石说道：「{我帮现在比较空闲|暂时没有任务需要做}} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {
        waitlian;
        gotodo {长乐帮} {卧室} {startfull {jobask_clb} {@getWaitfullType{@getConditionTick{{公共;长乐帮}}}}};
      }
    };
    #ACTION {^贝海石说道：「你刚做完长乐帮任务，还是先去休息一会吧。」} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {jobprepare};
    };
    #ACTION {^贝海石说道：「现在我这里没有给你的任务，你还是先处理好你其他事情再说吧} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {doquit};
    };
    #CLASS jobresponseclass CLOSE;
  };
  #CLASS jobrequestclass CLOSE;
  jobclear_clb;
  execute {
    time;
    cond;
    ask bei haishi about job
  };
};
#NOP {长乐帮找帮众};
#ALIAS {jobdo_clb} {
  #VARIABLE {jobstart_ts} {@now{}};
  joblog {开始寻找位于【$joblocation】的$jobnpc_dept属下帮众$jobnpc_member。};
  wwp;
  jobnextroom {checkmember}
};
#NOP {检查帮众,由于达到房间有一个确认过程,npc死亡可能在检查触发之前,这里加上尸体的触发条件};
#ALIAS {checkmember} {
  #VARIABLE {checkcount} {0};
  #VARIABLE {jobnpc_member_id} {};
  #CLASS jobcheckclass OPEN;
  #ACTION {长乐帮$jobnpc_dept属下帮众 $jobnpc_member(%*)} {
    #VARIABLE {jobnpc_member_id} {@lower{%%1}};
  };
  #ACTION {^  $jobnpc_member的尸体(Corpse)} {
    #VARIABLE {jobnpc_member_id} {corpse};
  };
  #ACTION {^{设定环境变量：action \= \"checkmember\"|你设定checkmember为反馈信息}} {
    #VARIABLE {idle} {0};
    #VARIABLE {echots} {0};
    #MATH {checkcount} {$checkcount + 1};
    #IF {"$jobnpc_member_id" == ""} {
      #CLASS jobcheckclass KILL;
      jobnextroom {checkmember}
    };
    #ELSEIF {"$jobnpc_member_id" == "corpse"} {
      #CLASS jobcheckclass KILL;
      jobfight_clb
    };
    #ELSEIF {$checkcount < 3} {
      #DELAY {1} {
        look;
        echo {checkmember};
      }
    };
    #ELSE {
      #CLASS jobcheckclass KILL;
      jobfight_clb
    };
  };
  #CLASS jobcheckclass CLOSE;
  look;
  echo {checkmember};
};
#FUNCTION isReceiveClb2 {
  #NOP {未能渡江前仅访问本大陆和主大陆};
  #IF {"$jobcity" == "绝情谷" || "$jobcity" == "桃源县" || "$jobcity" == "桃花岛"} {
    #RETURN {0};
  };
  #IF {"$jobcity" == "神龙岛" && "$city" != "神龙岛"} {
    #RETURN {0};
  };
  #IF {"$jobcity" == "燕子坞" && "$city" != "燕子坞" && ("$hp[party]" != "姑苏慕容" || "$hp[party]" == "普通百姓")} {
    #RETURN {0};
  };
  #IF {"$jobcity" == "曼佗罗山庄" && "$city" != "曼佗罗山庄" && ("$hp[party]" != "姑苏慕容" || "$hp[party]" == "普通百姓")} {
    #RETURN {0};
  };
  #IF {"$jobcity" == "福州城" && ("$jobroom" == "礁石" || "$jobroom" == "沙滩")} {
    #NOP {这里是随机房间还是别去了，基本浪费时间};
    #RETURN {0};
  };
  #IF {$hp[neili_max] < 3500 || $kungfu[base][dodge][effectlv] < 300 || $hp[jingli_max] < 1500} {
    #LOCAL {localzone} {@getCityZone{$city}};
    #LOCAL {jobzone} {@getCityZone{$jobcity}};
    #IF {"$jobzone" == "mainland" || "$localzone" == "$jobzone"} {
      #RETURN {1};
    };
    #ELSE {
      #RETURN {0};
    };
  };
  #ELSE {
    #RETURN {1};
  };
};
#NOP {长乐帮任务查看};
#ALIAS {jobfight_clb} {
  #VARIABLE {corpseindex} {0};
  #VARIABLE {jobnpc_killer_party} {};
  #VARIABLE {jobnpc_killer_kungfu} {};
  #VARIABLE {jobnpc_player} {};
  #VARIABLE {jobnpc_player_id} {};
  #VARIABLE {jobnpc_player_findcount} {0};
  #CLASS jobfightclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkcubu\"|你设定checkcubu为反馈信息}} {
    #VARIABLE {idle} {0};
    #VARIABLE {echots} {0};
    #IF {@carryqty{cu bu} == 0} {
      #IF {$corpseindex >= 3} {
        #CLASS jobfightclass KILL;
        jobfangqi_clb
      };
      #ELSE {
        #VARIABLE {corpseindex} {@eval{$corpseindex + 1}};
        dohalt {
          get cu bu from corpse $corpseindex;
          i;
          #DELAY {0.5} {echo {checkcubu}}
        }
      };
    };
    #ELSE {
      chakan cu bu;
      chakan corpse;
      echo {checkcorpse};
    };
  };
  #ACTION {^你仔细地查看$jobnpc_member的尸体%!*应该是%*所为} {
    #VARIABLE {jobnpc_killer_party} {%%1};
    #VARIABLE {jobnpc_killer_kungfu} {未知};
  };
  #ACTION {^你暗下寻思询问%!*的%*(%*)或许能得到提示，传闻%!*曾在%*附近出现} {
    #VARIABLE {jobnpc_player} {%%1};
    #VARIABLE {jobnpc_player_id} {@lower{%%2}};
    #VARIABLE {jobnpc_player_location} {@lower{%%3}};
  };
  #ACTION {^{设定环境变量：action \= \"checkcorpse\"|你设定checkcorpse为反馈信息}} {
    #CLASS jobfightclass KILL;
    #NOP {如果有请求的话不继续};
    #IF {"$jobnpc_player" != "" && "$currentjob" == "长乐帮2" && "$caller" == ""} {
      parsejoblocation {$jobnpc_player_location} {jobfindplayer_clb} {jobfindplayer_search}
    };
    #ELSE {
      jobfinish_clb
    };
  };
  #CLASS jobfightclass CLOSE;
  echo {checkcubu};
};
#ALIAS {jobfindplayer_clb} {
  #VARIABLE {jobkiller_clb_id} {};
  #VARIABLE {jobkiller_clb_name} {};
  #VARIABLE {jobkiller_clb_party} {};
  #CLASS jobdoclass KILL;
  #CLASS jobdoclass OPEN;
  #ACTION {^你身旁有这个人么} {
    jobnextroom {time;xunwen $jobnpc_player_id} {jobfindplayer_search}
  };
  #ACTION {^看起来$jobnpc_player暂时无法回答你的问题} {
    #DELAY {0.5} {xunwen $jobnpc_player_id}
  };
  #ACTION {^$jobnpc_player在你的耳边悄声说道：适才%*弟子%*从我身边经过，好像前往了%*。} {
    #CLASS jobdoclass KILL;
    #VARIABLE {jobkiller_clb_party} {%%1};
    #VARIABLE {jobkiller_clb_name} {%%2};
    #VARIABLE {joblocation} {%%3};
    joblog {寻找位于【$joblocation】的凶手【$jobkiller_clb_name】。};
    createpfm {@getFightPerform{}} {1};
    #IF {"$hp[party]" == "武当派"} {
      #LOCAL {lhj} {@getLiuhejin{%%1}};
      #VARIABLE {env[lhj]} {$lhj};
      #IF {"$lhj" == ""} {
        unset 六合劲
      };
      #ELSE {
        set 六合劲 $lhj
      };
    };
    dohalt {
      parsejoblocation {$joblocation} {
        jobnextroom {clb_checkkiller} {jobfinish_clb}
      } {jobfinish_clb}
    }
  };
  #ACTION {^$jobnpc_player在你的耳边悄声说道：刚才是} {
    #CLASS jobdoclass KILL;
    dohalt {jobfinish_clb}
  };
  #CLASS jobdoclass CLOSE;
  #IF {@isReceiveClb2{} == 0} {
    #CLASS jobdoclass KILL;
    jobfinish_clb;
  };
  #ELSE {
    joblog {向位于【$jobnpc_player_location】的【$jobnpc_player($jobnpc_player_id)】打听消息。};
    jobnextroom {time;xunwen $jobnpc_player_id} {jobfindplayer_search}
  };
};
#ALIAS {jobfindplayer_search} {
  #VARIABLE {checkcount} {0};
  #VARIABLE {jobnpc_player_location} {};
  #CLASS jobfightclass KILL;
  #CLASS jobfightclass OPEN;
  #ACTION {^$jobnpc_player(%*)回答你：当前位于:%* %*} {
    #IF {"@lower{%%1}" == "$jobnpc_player_id"} {
      #VARIABLE {jobnpc_player_location} {%%2%%3};
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkresponse\"|你设定checkresponse为反馈信息}} {
    #VARIABLE {echots} {0};
    #VARIABLE {idle} {0};
    laugh;
    #MATH {checkcount} {$checkcount + 1};
    #IF {"$jobnpc_player_location" == ""} {
      #IF {$checkcount > 3} {
        #CLASS jobfightclass KILL;
        jobfinish_clb
      };
      #ELSE {
        #DELAY {1} {echo {checkresponse}};
      };
    };
    #ELSE {
      #MATH {jobnpc_player_findcount} {$jobnpc_player_findcount + 1};
      parsejoblocation {$jobnpc_player_location} {jobfindplayer_clb} {jobfindplayer_search}
    };
  };
  #CLASS jobfightclass CLOSE;
  #IF {$jobnpc_player_findcount > 0} {
    #CLASS jobfightclass KILL;
    jobfinish_clb
  };
  #ELSE {
    #VARIABLE {jobnpc_player_location} {};
    tell $jobnpc_player_id where are you;
    echo {checkresponse}
  };
};
#ALIAS {clb_checkkiller} {
  #VARIABLE {jobfight_ts} {@now{}};
  #VARIABLE {nofight_flag} {0};
  #CLASS jobdoclass KILL;
  #CLASS jobdoclass OPEN;
  #ACTION {^  $jobkiller_clb_party弟子 $jobkiller_clb_name(%*)} {
    #VARIABLE {jobkiller_clb_id} {@lower{%%1}};
  };
  #ACTION {^这里不准战斗} {
    #VARIABLE {nofight_flag} {1};
  };
  #ACTION {这里没有这个人} {
    #VARIABLE {nofight_flag} {2};
  };
  #ACTION {^{设定环境变量：action \= \"checkkiller\"|你设定checkkiller为反馈信息}} {
    #VARIABLE {echots} {0};
    #IF {"$jobkiller_clb_id" != ""} {
      #VARIABLE {jobfight_ts} {@now{}};
      #VARIABLE {nofight_flag} {0};
      follow $jobkiller_clb_id;
      kill $jobkiller_clb_id;
      createpfm;
      startfight {} {$jobkiller_clb_name};
      echo {checkengage}
    };
    #ELSE {
      #CLASS jobdoclass KILL;
      jobnextroom {clb_checkkiller} {jobfinish_clb}
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkengage\"|你设定checkengage为反馈信息}} {
    #VARIABLE {echots} {0};
    #VARIABLE {idle} {0};
    #IF {@elapsed{$jobfight_ts} > 10} {
      #CLASS jobdoclass KILL;
      jobfinish_clb
    };
    #ELSEIF {$nofight_flag == 1} {
      #VARIABLE {nofight_flag} {0};
      #VARIABLE {escapepath} {@getEscapeExit{}};
      $escapepath;
      #DELAY {1} {
        id here;
        echo {checkkiller}
      }
    };
    #ELSEIF {$nofight_flag == 2} {
      #CLASS jobdoclass KILL;
      jobfinish_clb
    };
  };
  #ACTION {^$jobkiller_clb_name「啪」的一声倒在地上，挣扎着抽动了几下就死了} {
    #CLASS jobdoclass KILL;
    joblog {顺利杀死凶手【$jobkiller_clb_name】，耗时@elapsed{$jobfight_ts}秒。};
    dohalt {
      get silver from corpse;
      get gold from corpse;
      jobfinish_clb
    }
  };
  #CLASS jobdoclass CLOSE;
  look;
  echo {checkkiller};
};
#NOP {完成长乐帮任务};
#ALIAS {jobfinish_clb} {
  gotodo {长乐帮} {小厅} {jobfinish_clb_ask};
};
#ALIAS {jobfinish_clb_ask} {
  #NOP {询问结果,0:完成,1:busy,2:没有任务,3:失败};
  #VARIABLE {askresult} {0};
  #CLASS jobrequestclass OPEN;
  #ACTION {^你向贝海石打听有关『finish』的消息。} {
    #VARIABLE {idle} {0};
    #CLASS jobresponseclass OPEN;
    #ACTION {^贝海石说道：「你根本就没有领任务，完成什么啊？」} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {jobprepare};
    };
    #ACTION {^贝海石说道：「你真的完成任务了么？」} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {jobfangqi_clb};
    };
    #ACTION {^恭喜你！你成功的完成了长乐帮任务！你被奖励了：} {
      #CLASS rewardclass KILL;
      #CLASS rewardclass OPEN;
      #ACTION {^%*点经验} {
        #VARIABLE {jobreward_exp} {@ctd{%%%%1}};
      };
      #ACTION {^%*点潜能} {
        #VARIABLE {jobreward_pot} {@ctd{%%%%1}};
        #CLASS rewardclass KILL;
        #CLASS jobresponseclass KILL;
        #CLASS jobrequestclass KILL;
        joblog {成功完成，获得$jobreward_exp点经验$jobreward_pot点潜能，耗时@elapsed{$jobstart_ts}秒。};
        jobclear_clb;
        dohalt {jobprepare};
      };
      #CLASS rewardclass CLOSE;
    };
    #CLASS jobresponseclass CLOSE;
  };
  #CLASS jobrequestclass CLOSE;
  ask bei haishi about finish;
};
#NOP {放弃长乐帮任务};
#ALIAS {jobfangqi_clb} {
  gotodo {长乐帮} {小厅} {jobfangqi_clb_ask};
};
#ALIAS {jobfangqi_clb_ask} {
  #NOP {询问结果,0:成功,1:busy,2:完成第一步,3:没领任务};
  #VARIABLE {askresult} {0};
  #CLASS jobrequestclass OPEN;
  #ACTION {^你向贝海石打听有关『放弃』的消息。} {
    #CLASS jobresponseclass OPEN;
    #ACTION {^贝海石对你失望极了：“你没救了。} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {jobprepare};
    };
    #ACTION {^贝海石说道：「你不是已经进展到一定地步了，还是继续努力吧！」} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {jobfinish_clb};
    };
    #ACTION {^贝海石说道：「你根本就没有领任务，完成什么啊？」} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {jobprepare};
    };
    #CLASS jobresponseclass CLOSE;
  };
  #CLASS jobrequestclass CLOSE;
  ask bei haishi about 放弃;
};
#ALIAS {jobclear_clb} {
  #VARIABLE {jobnpc_dept} {};
  #VARIABLE {jobnpc_member} {};
  #VARIABLE {jobnpc_member_id} {};
  #VARIABLE {joblocation} {};
  #LIST {jobroomlist} {clear};
  #VARIABLE {jobfight_ts} {0};
  #VARIABLE {jobnpc_count} {0};
  #VARIABLE {jobfight_ts} {0};
  #VARIABLE {jobnpc_count} {0};
  #VARIABLE {jobstart_ts} {0};
};