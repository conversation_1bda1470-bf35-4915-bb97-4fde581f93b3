#NOP {君临天下,%1:后续指令};
#ALIAS {goquest_jltx} {
  #VARIABLE {questmodule} {君临天下};
  gotodo {铁掌山} {蝴蝶泉} {jltx_mo {%1}};
};
#NOP {拿大燕国世系表};
#ALIAS {jltx_mo} {
  #VARIABLE {missingflag} {0};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你来晚了，世系表已经给人取走了} {
    #VARIABLE {missingflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkyuxi\"|你设定checkyuxi为反馈信息}} {
    #VARIABLE {idle} {0};
    #DELAY {1} {
      #IF {@carryqty{shixi biao} == 1} {
        #CLASS questclass KILL;
        gotonpc {慕容博} {jltx_askbo {%1}};
      };
      #ELSEIF {$missingflag == 1} {
        #CLASS questclass KILL;
        questdelay {$questmodule} {0} {3600};
        %1;
      };
      #ELSE {
        mo 水底;
        i;
        echo {checkyuxi};
      };
    };
  };
  #CLASS questclass CLOSE;
  mo 水底;
  i;
  echo {checkyuxi};
};
#ALIAS {jltx_askbo} {
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^慕容博说道：「%*不是已经掌握君临天下的秘密了么} {
    #CLASS questclass KILL;
    questsuccess {$questmodule};
    dohalt {%1};
  };
  #ACTION {^慕容博说道：「我看你暂时领会不到君临天下的秘诀} {
    #CLASS questclass KILL;
    questfail {$questmodule};
    dohalt {%1};
  };
  #ACTION {^慕容博说道：「你一天问那么多次} {
    #CLASS questclass KILL;
    questdelay {$questmodule} {0} {7200};
    dohalt {%1};
  };
  #ACTION {^慕容博说道：「大燕国自先祖慕容皝} {
    dohalt {
      pray pearl;
      give shixi biao to murong bo;
    };
  };
  #ACTION {^慕容博说道：「%*难道不知君临天下} {
    dohalt {
      pray pearl;
      give shixi biao to murong bo;
    };
  };
  #ACTION {^你对如何使用君临天下似乎有些了解了} {
    questsuccess {$questmodule};
    dohalt {%1};
  };
  #ACTION {^很可惜，你本次尝试君临天下解谜失败} {
    questfail {$questmodule};
    dohalt {%1};
  };
  #CLASS questclass CLOSE;
  ask murong bo about 君临天下;
};