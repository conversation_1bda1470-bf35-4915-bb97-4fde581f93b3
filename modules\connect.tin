#CONFIG charset GBK1TOUTF8;
#CONFIG {COLOR PATCH}	{ON};
#CONFIG {repeat enter} {ON};
#CONFIG {COMMAND ECHO} {OFF};
#CONFIG {PACKET PATCH} {0.8};
#CONFIG	{VERBOSE} {OFF};
#CONFIG {hybernate} {ON};
#CONFIG	{BUFFER SIZE} {10000};
#CONFIG	{HISTORY SIZE} {100};
#CONFIG {LOG} {HTML};
#EVENT {SEND OUTPUT} {#FORMAT ots %t {%H:%M:%S};#ECHO {<acf>INPUT[$ots]: <fac>%p<fac>} {%0}};

#ALIAS {import} {#READ {./shujian/modules/%1.tin}};

#NOP {重连};
#EVENT {SESSION DISCONNECTED}
{
	#SHOWME {<faa>SESSION DISCONNECTED};
	#FORMAT ts %t {%Y-%m-%d %T};
	#gts #DELAY {60} {#READ ./shujian/chars/conf/$user_profile.tin};
};

#NOP {超时};
#EVENT {SESSION TIMED OUT}
{
	#SHOWME {<faa>SESSION TIMED OUT};
	#gts #DELAY {60} {#READ ./shujian/chars/conf/$user_profile.tin};
};

#EVENT {RECEIVED OUTPUT} 
{
    #UNDELAY {KeepAlive};
    #DELAY KeepAlive {#zap} 60;
};

#NOP 加载通用模块
#ALIAS {loadmodules} {
	#MAP READ ./shujian/modules/sjmap.tin;
	import {procedure};
	import {map};
	import {common};
	import {ui};	
	import {river};
	import {matrix};
	import {night};
	import {walk};
	import {kungfu};
	import {fight};
	import {quest};
	import {guide};
	import {instances};
	#NOP 书剑情怀站没有保姆服务，所以屏蔽保姆模块;
	#NOP import {service};
	import {festival};
	import {status};
	import {job};
	import {beauty}
};

#ACTION {Best view with 800 * 600 or better, Fixedsys font.} {#DELAY {0.1} {#SEND {n}}};
#ACTION {您的英文名字(ID)是：} {#DELAY {0.1} {#SEND {$user_char}}};

#nop 创建新角色
#ACTION {使用 $user_char 这个名字将会在此创造一个新的人物} {#DELAY {0.1} {#SEND {y}}};
#ACTION {请设定您的密码：} {#DELAY {0.1} {#SEND {$user_password}}};
#ACTION {请再输入一次您的密码，以确认您没记错：} {#DELAY {0.1} {#SEND {$user_password}}};
#ACTION {您的中文名字：} {#DELAY {0.1} {#SEND {$user_name}}};
#ACTION {请输入您的选择(0-4)：} {0};
#ACTION {您同意这一组天赋吗？} {#DELAY {0.1} {#SEND {y}}};
#ACTION {您的电子邮件地址：} {#DELAY {0.1} {#SEND {<EMAIL>}}};
#ACTION {您要扮演男性(m)的角色或女性(f)的角色？}  {#DELAY {0.1} {#SEND {$user_gender}}};
#ACTION {请您输入这个人物的识别密码(passwd)：} {#DELAY {0.1} {#SEND {$user_password}}};
#ACTION {人物目前正在游戏当中} {#DELAY {0.1} {#SEND {y}}};

#ACTION {{您上次连线地址是|欢迎您注册成}} {
	#VARIABLE hp[id] {$user_char}; 
	#VARIABLE hp[name] {$user_name}; 
	#VARIABLE {env[loginmode]} {0};
	#IF {"$user_nick" != ""} {
		nick $user_nick;
	};
	#IF {$"user_killmsg" != ""} {
		#SEND {set kill_msg $user_killmsg};
	};
	loadmodules;
	initenv;
	#DELAY {1} {preparestart}
};
#ACTION {^重新连线完毕。} {
	#VARIABLE hp[id] {$user_char}; 
	#VARIABLE hp[name] {$user_name}; 
	#VARIABLE {env[loginmode]} {1};
	loadmodules;
	preparestart
};

#NOP {进行连接};
#session $user_session 61.184.21.150 $user_port;