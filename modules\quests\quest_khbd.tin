#NOP {葵花宝典,%1:后续指令};
#ALIAS {goquest_khbd} {
	#VARIABLE {questmodule} {葵花宝典};
	gotonpc {东方不败} {khbd_askdongfang {%1}};
};
#ALIAS {khbd_askdongfang} {
	#VARIABLE {okflag} {0};
	#CLASS questclass KILL;
	#CLASS questclass OPEN;
	#ACTION {^这里没有这个人} {
		#CLASS questclass KILL;
		questdelay {$questmodule} {0} {600};
		#DELAY {1} {%1};
	};
	#ACTION {^你向东方不败打听有关『葵花宝典』的消息。} {
		#CLASS questresponseclass KILL;
		#CLASS questresponseclass OPEN;
		#ACTION {^东方不败说道：「你没看见我正忙着么} {
			#CLASS questresponseclass KILL;
      #DELAY {6} {ask dongfang about 葵花宝典};
    };
		#ACTION {^东方不败说道：「{咦？「葵花宝典」不是在你身上么|好吧，这本「葵花宝典」你拿回去好好研究研究|你要的「葵花宝典」已经给别人拿走了}} {
			#CLASS questresponseclass KILL;
			#CLASS questclass KILL;
			questsuccess {$questmodule};
      dohalt {%1};
    };
		#ACTION {^东方不败说道：「我现在很忙，没兴趣和你多罗嗦} {
			#CLASS questresponseclass KILL;
			#CLASS questclass KILL;
			questdelay {$questmodule} {} {10800};
      dohalt {%1};
    };
		#ACTION {^东方不败说道：「就凭你这%*} {
			#CLASS questresponseclass KILL;
			pray pearl;
			kill dongfang bubai;
			startfight;
			autopfm;
    };
		#CLASS questresponseclass CLOSE;
	};
	#ACTION {^东方不败「啪」的一声倒在地上，挣扎着抽动了几下就死了} {
		stopfight;
		echo {checkkhbd}
	};
	#ACTION {^东方不败说道：「哈哈，你我也算是有缘之人，这本「葵花宝典」就交给你吧} {
		#VARIABLE {okflag} {1};
	};
	#ACTION {^东方不败怒叫道：「即使我死了，你等也休想得到「葵花宝典」} {
		#VARIABLE {okflag} {0};
	};
	#ACTION {^{设定环境变量：action \= \"checkkhbd\"|你设定checkkhbd为反馈信息}} {
		#CLASS questclass KILL;
		#IF {$okflag == 0} {
			questfail {$questmodule};
		};
		#ELSE {
			questsuccess {$questmodule};
		};
		dohalt {%1};
	};
	#CLASS questclass CLOSE;
	pfm_buff_normal;
	ask dongfang about 葵花宝典;
}