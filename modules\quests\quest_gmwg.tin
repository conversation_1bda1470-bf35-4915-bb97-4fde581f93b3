#NOP {古墓武功,%1:后续指令。九阴和石刻一起解};
#NOP {标识:0:不解密，不存在,1:古墓九阴,2:古墓石刻,3:都解};
#VARIABLE {gmwgflag} {0};
#ALIAS {goquest_gmwg} {
  #VARIABLE {gmwgflag} {0};
  #IF {"$questlist[古墓九阴][done]" != "YES"} {
    #MATH {gmwgflag} {$gmwgflag + 1};
    #VARIABLE {questmodule} {古墓九阴};
  };
  #IF {"$questlist[古墓石刻][done]" != "YES" && @getSkillLevel{yunu-xinjing} >= 550 && @getSkillLevel{xuantie-jianfa} >= 550 && @getSkillLevel{yunu-jianfa} >= 550} {
    #MATH {gmwgflag} {$gmwgflag + 2};
    #VARIABLE {questmodule} {古墓石刻};
  };
  #NOP {石刻需要每次都去问杨过};
  #IF {$gmwgflag >= 2 && $env[askyang] == 0} {
    gotonpc {杨过} {gmwg_askyang {%1}};
  };
  #ELSE {
    gotoroom {3542} {gmwg_start {%1}};
  };
};
#NOP {问杨过石刻};
#ALIAS {gmwg_askyang} {
  #VARIABLE {askok} {1};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^杨过说道：「你过%*后再来问吧} {
    #VARIABLE {askok} {0};
  };
  #ACTION {^杨过说道：「我古墓武技尽悉刻于石室之内} {
    #VARIABLE {askok} {1};
  };
  #ACTION {^杨过说道：「我不是已经告诉%*你了} {
    #VARIABLE {askok} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkask\"|你设定checkask为反馈信息}} {
    #CLASS questclass KILL;
    dohalt {
      #IF {$askok == 1} {
        #VARIABLE {env[askyang]} {1};
        gotoroom {3542} {gmwg_start {%1}};
      };
      #ELSE {
        #IF {$gmwgflag == 1 || $gmwgflag == 3} {
          questdelay {古墓九阴} {0} {10800};
        };
        #IF {$gmwgflag == 2 || $gmwgflag == 3} {
          questdelay {古墓石刻} {0} {10800};
        };
        %1
      };
    };
  };
  #CLASS questclass CLOSE;
  ask yang guo about 古墓石刻;
  echo {checkask};
};
#ALIAS {gmwg_start} {
  #VARIABLE {gmbusy} {0};
  #VARIABLE {gmjyok} {0};
  #VARIABLE {gmskok} {0};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你看着室顶，满满的都是密密麻麻的小字。} {
    dohalt {
      pray pearl;
      yun xinjing;
      look zi;
      echo {checklook};
    };
  };
  #ACTION {^不过上面写的艰深难懂%*{增加的历练还不足以理解其奥义|或许需要再过一点时间}} {
    #VARIABLE {gmbusy} {1};
  };
  #ACTION {^你陡然一瞥间，看到几个小字“九阴真经内功要诀”} {
    #VARIABLE {gmjyok} {1};
  };
  #ACTION {^你看到在密密麻麻的小字旁边，另外刻了一些图形招式} {
    #VARIABLE {gmskok} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checklook\"|你设定checklook为反馈信息}} {
    #CLASS questclass KILL;
    #IF {$gmbusy == 1} {
      #IF {@eval{$gmwgflag & 1} == 1} {
        questdelay {古墓九阴} {} {10800};
      };
      #IF {@eval{$gmwgflag & 2} == 2} {
        questdelay {古墓石刻} {} {10800};
      };
    };
    #ELSE {
      #IF {@eval{$gmwgflag & 1} == 1} {
        #IF {$gmjyok == 1} {
          questsuccess {古墓九阴};
        };
        #ELSE {
          questfail {古墓九阴};
        };
      };
      #IF {@eval{$gmwgflag & 2} == 2} {
        #IF {$gmskok == 1} {
          questsuccess {古墓石刻};
        };
        #ELSE {
          questfail {古墓石刻};
        };
      };
    };
    dohalt {%1};
  };
  #CLASS questclass CLOSE;
  look ceiling
};