#NOP {蛤蟆功,%1:后续指令};
#ALIAS {goquest_kmq} {
  #IF {@getSkillLevel{qimen-bagua} < 121} {
    thdprepare {learnbagua {goquest_hubo {%1}}}
  };
  #ELSE {
    #VARIABLE {questmodule} {空明拳};
    gotodo {桃花岛} {岩洞} {kmq_jiebai {%1}}
  };
};
#ALIAS {kmq_jiebai} {
  #VARIABLE {askts} {@now{}};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^这里没有这个人} {
    #IF {@elapsed{$askts} > 60} {
      #CLASS questclass KILL;
      dohalt {%1}
    };
    #ELSE {
      #DELAY {3} {
        out;
        enter;
        pray pearl;
        ask zhou about 结拜
      };
    };
  };
  #ACTION {^你向周伯通打听有关『结拜』的消息。} {
    #CLASS questresponseclass KILL;
    #CLASS questresponseclass OPEN;
    #ACTION {我现在没有空，要不你先陪我玩玩？} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questfail {$questmodule};
      dohalt {%1}
    };
    #ACTION {周伯通说道：「我现在可没空，我忙着玩呢} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questfail {$questmodule};
      dohalt {%1}
    };
    #ACTION {^看起来周伯通%*之内不准备理你} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questdelay {$questmodule} {} {10800} 
      dohalt {%1}
    };
    #ACTION {^{周伯通与你并肩而跪，朗声说道：“老顽童周伯通，今日与$hp[name]|周伯通说道：「咱俩不是结拜过了吗}} {
      #CLASS questresponseclass KILL;
      questsuccess {$questmodule};
      dohalt {goquest_hubo {%1}}
    };
    #CLASS questresponseclass CLOSE;
  };
  #CLASS questclass CLOSE;
  pray pearl;
  ask zhou about 结拜
};
