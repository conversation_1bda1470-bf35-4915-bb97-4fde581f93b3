#NOP {雪山飞狐分四部,1:两页刀法篇,2:复仇篇,3:解药篇,4:宝藏篇};
#NOP {雪山飞狐,%1:后续指令};
#ALIAS {goquest_xsfh} {
  #VARIABLE {questmodule} {雪山飞狐};
  #SWITCH {"$questlist[$questmodule][laststep]"} {
    #CASE {"0"} {
      gotonpc {胡斐} {xsfh_daofa_askhu {%1}}
    };
    #CASE {"1"} {
      gotonpc {胡斐} {xsfh_fuchou_askhu {%1}}
    };
    #CASE {"2"} {
      gotonpc {苗人凤} {xsfh_jieyao_askmiao {%1}}
    };
    #CASE {"3"} {
      #NOP {宝藏篇先拿绳子};
      #IF {@carryqty{sheng zi} == 0} {
        getshengzi {gotonpc {苗人凤} {xsfh_baozang_checkmiao {%1}}} {
          questupdate {$questmodule};
          %1
        }
      };
      #ELSEIF {@carryqty{sheng zi} == 0} {
        buyfire {gotonpc {苗人凤} {xsfh_baozang_checkmiao {%1}}}
      };
      #ELSE {
        gotonpc {苗人凤} {xsfh_baozang_checkmiao {%1}}
      };
    };
    #DEFAULT {%1};
  };
};
#NOP {两页刀法起始,%1:后续指令};
#ALIAS {xsfh_daofa_askhu} {
  #VARIABLE {questmodule} {雪山飞狐};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你向胡斐打听有关『胡一刀』的消息。} {
    #CLASS questresponseclass OPEN;
    #ACTION {^胡斐说道：「不错不错，当年的关东大侠胡一刀正是我父亲} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      #NOP {复仇篇已完成};
      questupdate {$questmodule} {2};
      dohalt {
        %1
      };
    };
    #ACTION {^胡斐说道：「爹爹的死我这就去找苗人凤算账去} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      #NOP {两页刀法篇已完成};
      questupdate {$questmodule} {1};
      dohalt {
        %1
      };
    };
    #ACTION {^胡斐说道：「可惜爹爹的仇，还未报！」} {
      #CLASS questresponseclass KILL;
      dohalt {
        ask hu fei about 苗人凤
      };
    };
    #CLASS questresponseclass CLOSE;
  };
  #ACTION {^你向胡斐打听有关『苗人凤』的消息。} {
    #CLASS questresponseclass OPEN;
    #ACTION {^胡斐说道：「苗大侠最近很好} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      #NOP {解药篇已完成};
      questupdate {$questmodule} {3};
      dohalt {
        %1
      };
    };
    #ACTION {^胡斐说道：「果真一代大侠，可惜之前我一直冤枉了他。} {
      #NOP {复仇篇已完成};
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questupdate {$questmodule} {2};
      dohalt {
        %1
      };
    };
    #ACTION {^胡斐说道：「我不明白苗大侠与我爹爹如此交好，为何使出如此毒手，而他平常为人却有光明磊落！} {
      #CLASS questresponseclass KILL;
      dohalt {
        ask hu fei about 家仇
      };
    };
    #CLASS questresponseclass CLOSE;
  };
  #ACTION {^你向胡斐打听有关『家仇』的消息。} {
    #CLASS questresponseclass OPEN;
    #ACTION {^胡斐说道：「你连我都不是对手，如何帮我复仇？} {
      #NOP {两页刀法篇已经完成};
      #CLASS questresponseclass KILL;
      questupdate {$questmodule} {1};
      dohalt {%1};
    };   
    #ACTION {^胡斐说道：「还要多谢大侠能够撮合在下与苗姑娘的婚姻。} {
      #NOP {解药篇已完成};
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questupdate {$questmodule} {3};
      dohalt {%1};
    };
    #ACTION {^胡斐说道：「确实没有想到事情如此复杂。恨我自己，苗大侠如此正气，我竟然怀疑他，这毒如此凶猛，只怕......} {
      #NOP {复仇篇已完成};
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questupdate {$questmodule} {2};
      dohalt {%1};
    };
    #ACTION {^胡斐说道：「胡苗两家今天能够再次重归于好，也多亏大侠帮忙。苗大侠如此正气，我竟然怀疑他，还好毒也解了，没能铸成大错。} {
      #NOP {解药篇已完成};
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questupdate {$questmodule} {3};
      dohalt {%1};
    };
    #ACTION {^胡斐说道：「只要这位%*能够帮忙，在下不胜感激。其实我也不能确认这事情原委。} {
      #NOP {没有回答};
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questupdate {$questmodule} {1};
      dohalt {%1};
    };
    #ACTION {^胡斐说道：「{当年我爹爹和号称“打遍天下无敌手”的苗人凤在此比武|我真的不明白，想不通这其中的变化，只可惜我爹妈死得不明不白|我一直怀疑苗人凤就是杀害我爹的凶手}} {
      #NOP {接收任务};
      #CLASS questresponseclass KILL;
      dohalt {
        ask hu fei about 胡家刀法
      };
    };
    #ACTION {^胡斐说道：「{不过，今天已经很麻烦|不过，以你当前的经验恐怕还是难以有所帮助}} {
      #NOP {时间不够或者经验不足};
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questdelay {$questmodule} {} {18000};
      dohalt {%1};
    };
    #CLASS questresponseclass CLOSE;
  };
  #ACTION {^你向胡斐打听有关『胡家刀法』的消息。} {
    #CLASS questresponseclass OPEN;
    #ACTION {^胡斐说道：「{可惜我武功未能圆通|苗人凤听说武功号称打遍天下，自是厉害，可惜我胡家刀法秘籍竟有残缺|我听平四叔说，这两页好像被一个江湖郎中夺去}} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      #NOP {去找阎基};
      dohalt {
        gotodo {苗疆} {后山} {xsfh_daofa_askyanji {%1}}
      };
    };
    #ACTION {^胡斐说道：「有些累了，这刀法还是以后切磋吧。} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      #NOP {等会};
      questupdate {$questmodule};
      dohalt {%1};
    };
    #CLASS questresponseclass CLOSE;
  };
  #CLASS questclass CLOSE;
  ask hu fei about 胡一刀
};
#NOP {两页刀法篇找阎基拿两页刀法,%1:后续指令};
#ALIAS {xsfh_daofa_askyanji} {
  #VARIABLE {questmodule} {雪山飞狐};
  #VARIABLE {tempflag} {0};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你向阎基打听有关『胡一刀』的消息。} {
    #CLASS questresponseclass OPEN;
    #ACTION {^阎基%*说道：「是我错了，害死了胡大侠，不过罪魁祸首是天田归农！} {
      #NOP {解药篇开始了};
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questupdate {$questmodule} {2};
      dohalt {
        %1
      };
    };
    #ACTION {^阎基%*说道：「20年前的比武，其实我也不是很明白，但是胡大侠死在苗人凤手上，确是事实。} {
      #NOP {复仇篇完成了};
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questupdate {$questmodule} {2};
      dohalt {
        %1
      };
    };
    #ACTION {^阎基%*说道：「%*可惜小人苗人凤竟然使用毒剑，这段公案迟早有人要去还的。} {
      #CLASS questresponseclass KILL;
      dohalt {
        ask yan ji about 苗人凤
      };
    };
    #CLASS questresponseclass CLOSE;
  };
  #ACTION {^你向阎基打听有关『苗人凤』的消息。} {
    #ACTION {^阎基%*说道：「我，我，苗大侠最近可好？...都是“田归农”指使小人干的......这天杀的卑鄙小人！} {
      #NOP {解药篇开始了};
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questupdate {$questmodule} {2};
      dohalt {
        %1
      };
    };
    #ACTION {^阎基%*说道：「苗大侠，很久没有听过他的踪迹了，20年前有过一面而已。} {
      #CLASS questresponseclass KILL;
      dohalt {
        ask yan ji about 下毒
      };
    };
  };
  #ACTION {^你向阎基打听有关『下毒』的消息。} {
    #CLASS questresponseclass OPEN;
    #ACTION {^阎基%*说道：「是我错了，害死了胡大侠，不过罪魁祸首是田归农指使我下毒的！} {
      #NOP {复仇篇结束了};
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questupdate {$questmodule} {2};
      dohalt {
        %1
      };
    };
    #ACTION {^阎基%*说道：「但是胡大侠当年豪气冲天，可惜小人苗人凤竟然使用毒剑，这段公案迟早有人要去还的。} {
      #NOP {复仇篇结束了};
      #CLASS questresponseclass KILL;
      dohalt {
        ask yan ji about 胡斐
      };
    };
    #CLASS questresponseclass CLOSE;
  };
  #ACTION {^你向阎基打听有关『胡斐』的消息。} {
    #CLASS questresponseclass OPEN;
    #ACTION {^阎基%*说道：「虽然干了一场荒唐事，天可怜见胡大侠还剩下胡斐这个孩子。我真是后悔啊......} {
      #NOP {两页刀法篇结束了};
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questupdate {$questmodule} {1};
      dohalt {
        %1
      };
    };
    #ACTION {^阎基%*说道：「当年冤死的胡大侠的孩子好像叫胡斐，我已经有些忘记了。} {
      #CLASS questresponseclass KILL;
      dohalt {
        ask yan ji about 两页刀法
      };
    };
    #CLASS questresponseclass CLOSE;
  };
  #ACTION {^你向阎基打听有关『两页刀法』的消息。} {
    #CLASS questresponseclass OPEN;
    #ACTION {^阎基%*说道：「虽然干了一场荒唐事，天可怜见胡大侠还剩下胡斐这个孩子。我真是后悔啊......} {
      #NOP {两页刀法结束了};
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questupdate {$questmodule} {1};
      dohalt {
        %1
      };
    };
    #ACTION {^阎基%*说道：「我很少用刀的。} {
      #NOP {未开启任务};
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questupdate {$questmodule} {0};
      dohalt {
        %1
      };
    };
    #ACTION {^阎基%*说道：「你，你是怎么知道的？嘿嘿，看来我只能让真相永远消失了。} {
      #CLASS questresponseclass KILL;
      kill yan ji;
      openwimpy;
    };
    #CLASS questresponseclass CLOSE;
  };
  #ACTION {^阎基交给你两页纸，上面记载着胡家刀法。} {
    #CLASS questresponseclass KILL;
    #CLASS questclass KILL;
    dohalt {
      gotodo {黄河流域} {墓地} {xsfh_daofa_givehu {%1}}
    };
  };
  #CLASS questclass CLOSE;
  ask yan ji about 胡一刀
};
#NOP {两页刀法给胡斐,%1:后续指令};
#ALIAS {xsfh_daofa_givehu} {
  #VARIABLE {questmodule} {雪山飞狐};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你身上没有这样东西。} {
    #NOP {意外情况};
    #CLASS questclass KILL;
    questupdate {$questmodule} {0}; 
    runwait {%1}
  };
  #ACTION {^你于%*解开雪山飞狐两页刀法篇} {
    #NOP {两页刀法篇完成后需要间隔500k经验和半天时间才可能家仇较量};
    #NOP {每次失败大约需要间隔三个小时和150k经验};
    #CLASS questclass KILL;
    questupdate {$questmodule} {1};
    questdelay {$questmodule} {} {86400};
    dohalt {
      %1;
    };
  };
  #CLASS questclass CLOSE;
  give liangye daofa to hu fei
};
#NOP {复仇篇问家仇和较量,%1:后续指令};
#ALIAS {xsfh_fuchou_askhu} {
  #VARIABLE {questmodule} {雪山飞狐};
  #VARIABLE {winflag} {0};
  #VARIABLE {okflag} {0};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你向胡斐打听有关『家仇』的消息。} {
    #CLASS questresponseclass OPEN;
    #ACTION {^胡斐说道：「确实没有想到事情如此复杂。恨我自己，苗大侠如此正气，我竟然怀疑他} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questupdate {$questmodule} {2};
      dohalt {
        %1
      };
    };
    #ACTION {^胡斐说道：「你连我都不是对手，如何帮我复仇？} {
      #NOP {较量未赢过或者未失败三次};
      #CLASS questresponseclass KILL;
      dohalt {
        pfm_wuxing;
        pfm_buff_normal;
        ask hu fei about 较量
      };
    };   
    #ACTION {^胡斐说道：「不过，今天已经很麻烦了} {
      #NOP {经验或时间间隔不足};
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questfail {$questmodule};
      dohalt {%1}
    };
    #ACTION {^胡斐说道：「不过，以你当前的经验} {
      #NOP {经验或时间间隔不足};
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questfail {$questmodule};
      dohalt {%1}
    };
    #ACTION {^回答命令answer yes 或者answer no} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      answer yes;
      dohalt {
        wwp;
        guard_call {兰州城} {前厅} {heiyi ren} {
          gotodo {兰州城} {苗家庄门口} {
            xsfh_fuchou_kill {%1}
          };
        };
      };
    };
    #ACTION {^胡斐说道：「只要这位%*能够帮忙} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      answer yes;
      dohalt {
        wwp;
        guard_call {兰州城} {前厅} {heiyi ren} {
          gotodo {兰州城} {苗家庄门口} {
            xsfh_fuchou_kill {%1}
          };
        };
      };
    };
    #CLASS questresponseclass CLOSE;
  };
  #ACTION {^你向胡斐打听有关『较量』的消息。} {
    #CLASS questresponseclass OPEN;
    #ACTION {^胡斐说道：「刚切磋完} {
      #NOP {经验或时间间隔不足或者有人刚切磋完};
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questupdate {$questmodule};
      dohalt {%1}
    };
    #ACTION {^胡斐说道：「{今天先切磋到这里吧|你武功没什么大的变化}} {
      #NOP {经验或时间间隔不足或者有人刚切磋完};
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questfail {$questmodule};
      dohalt {%1}
    };
    #ACTION {^胡斐说道：「你还是先治好你的病再来切磋吧！」} {
      #NOP {经验或时间间隔不足或者有人刚切磋完};
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questdelay {$questmodule} {0} {1200};
      dohalt {%1}
    };
    #ACTION {^胡斐向你一招手道：“且随我来”，胡斐和你急急忙忙地离开坟地} {
      #DELAY {1} {
        ok;
        startfight;
      };
    };
    #ACTION {^经过一番苦斗，你发现自己的武功大有激进} {
      #VARIABLE {winflag} {1};
    };
    #ACTION {^突然一道神光笼罩着你，你的精气神竟然全部恢复了！} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      stopfight;
      #IF {$winflag == 1} {
        #NOP {直接复仇};
        dohalt {
          gotodo {扬州城} {小吃店} {
            startfull {
              gotonpc {胡斐} {xsfh_fuchou_askhu {%1}};
            };
          };
        };
      };
      #ELSE {
        questfail {$questmodule};
        dohalt {%1}
      };
    };
    #CLASS questresponseclass CLOSE;
  };
  #CLASS questclass CLOSE;
  ask hu fei about 家仇;
};
#NOP {复仇篇去苗家庄,%1:后续指令};
#ALIAS {xsfh_fuchou_kill} {
  #VARIABLE {okflag} {0};
  #VARIABLE {checkts} {@now{}};
  #VARIABLE {questmodule} {雪山飞狐};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^只听门内一个深沉浑厚的声音传来} {
    #NOP {未开始复仇篇};
    #VARIABLE {okflag} {2};
  };
  #ACTION {^你一提内息，使出「一苇渡江」轻功，一翻身，越过高墙，这一手轻功当真落地无声，确实了得。} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkengage\"|你设定checkengage为反馈信息}} {
    #IF {$okflag == 0} {
      #IF {@elapsed{$checkts} > 5} {
        #CLASS questclass KILL;
        guard_cancel;
        questdelay {$questmodule} {} {10800};
        %1
      };
      #ELSE {
        #DELAY {1} {echo {checkengage}}
      };
    };
    #ELSEIF {$okflag == 2} {
      #CLASS questclass KILL;
      guard_cancel;
      questdelay {$questmodule} {} {10800};
      %1
    };
  };
  #ACTION {^%*哼了一声：“什么人也敢管本大爷的事！不过，苗老贼果然厉害，兄弟们走！} {
    #NOP {失败了};
    #CLASS questclass KILL;
    questfail {$questmodule};
    loc {%1}
  };
  #ACTION {^那%*“啊”的一声，手臂酸麻，三节棍已然脱手} {
    #NOP {成功};
    startfight {1};
    kill heiyi ren
  };
  #ACTION {^黑衣人「啪」的一声倒在地上，挣扎着抽动了几下就死了。} {
    kill heiyi ren
  };
  #ACTION {^这里没有这个人。} {
    #CLASS questclass KILL;
    guard_over;
    stopfight;
    loc {
      dohalt {
        wwp;
        gotonpc {苗人凤} {xsfh_fuchou_askmiao {%1}}
      };
    };
  };
  #CLASS questclass CLOSE;
  pfm_wuxing;
  pfm_buff_normal;
  echo {checkengage}
};
#NOP {复仇篇问苗人凤,%1:后续指令};
#ALIAS {xsfh_fuchou_askmiao} {
  #VARIABLE {questmodule} {雪山飞狐};
  #VARIABLE {okflag} {0};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^苗人凤果然心思敏捷，你沉默了一下，脑子里一边盘想着如何开口询问这20年前的故事......} {
    #VARIABLE {okflag} {0};
    dohalt {
      ask miao renfeng about 胡一刀
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkresponse\"|你设定checkresponse为反馈信息}} {
    #DELAY {5} {
      #IF {$okflag == 0} {
        ask miao renfeng about 胡一刀
      };
    };
  };
  #ACTION {^你向苗人凤打听有关『胡一刀』的消息。} {
    #CLASS questresponseclass OPEN;
    #ACTION {^苗人凤说道：「{胡一刀，我的好兄弟|关东大侠胡一刀正是我的至交好友}} {
      #CLASS questresponseclass KILL;
      dohalt {
        ask miao renfeng about 真相
      };
    };
    #CLASS questresponseclass CLOSE;
  };
  #ACTION {^你向苗人凤打听有关『真相』的消息。} {
    #CLASS questresponseclass OPEN;
    #ACTION {^苗人凤说道：「20年前的比武，现在已经完全明了。只是可恨胡兄竟然被阴险小人所暗算。} {
      #NOP {复仇篇完成了};
      #CLASS questresponseclass KILL;
      questupdate {$questmodule} {2};
      dohalt {%1}
    };
    #ACTION {^苗人凤说道：「当年我确实和胡一刀比过武，胡兄也确实毒发身亡，但怎么可能是我忍心下此毒手？这20年来，我也在一直查找凶手。} {
      #NOP {去找阎基问真相};
      #CLASS questresponseclass KILL;
      dohalt {gotonpc {阎基} {xsfh_fuchou_askyan {%1}}}
    };
    #ACTION {^你听到这里，感觉和阎基所说不同，不由问道：“那后来呢？难不成胡一刀不是自杀？} {
      #VARIABLE {idle} {0};
    };
    #ACTION {^你听完解说一阵沉默，不由被胡与苗的豪情所感动，只怕其中真有别情！你想了一会儿，只有阎基这个线索了！} {
      #NOP {去找阎基问真相};
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      #VARIABLE {idle} {0};
      gotonpc {阎基} {xsfh_fuchou_askyan {%1}}
    };
    #CLASS questresponseclass CLOSE;
  };
  #CLASS questclass CLOSE;
  echo {checkresponse};
};
#NOP {复仇篇问阎基真相};
#ALIAS {xsfh_fuchou_askyan} {
  #VARIABLE {questmodule} {雪山飞狐};
  #VARIABLE {tempflag} {0};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^这里没有这个人。} {
    #NOP {阎基莫名跑了，回去从新找苗人凤};
    #CLASS questclass KILL;
    runwait {
      gotonpc {苗人凤} {xsfh_fuchou_askmiao {%1}}
    };
  };
  #ACTION {^你向阎基打听有关『真相』的消息。} {
    #CLASS questresponseclass OPEN;
    #ACTION {^你暗想：这当务之急，恐怕是先告诉胡斐这事情的经过，只是胡家这所谓大仇也最终不了了之，的确出乎意料之外。} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      dohalt {
        gotonpc {胡斐} {xsfh_fuchou_meethu {%1}}
      }
    };
    #CLASS questresponseclass CLOSE;
  };
  #ACTION {^突然，草丛中钻出一只毒蛇。} {
    kill snake
  };
  #ACTION {^毒蛇「啪」的一声倒在地上，挣扎着抽动了几下就死了。} {
    dohalt {
      ask yan ji about 真相
    };
  };
  #ACTION {^你看了看四周，发现这里根本没有人行的痕迹，或许是路走错了} {
    #NOP {阎基莫名跑了，回去从新找苗人凤};
    #CLASS questclass KILL;
    runwait {
      gotonpc {苗人凤} {xsfh_fuchou_askmiao {%1}}
    };
  };
  #CLASS questclass CLOSE;
  pfm_wuxing;
  pfm_buff_normal;
  ask yan ji about 真相
};
#NOP {复仇篇告知胡斐真相};
#ALIAS {xsfh_fuchou_meethu} {
  #VARIABLE {questmodule} {雪山飞狐};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你点头道：我马上去苗家庄，你心情不定，还是先歇息为妙} {
  };
  #ACTION {^你于%*解开雪山飞狐复仇篇} {
    #NOP {继续解药篇};
    #CLASS questclass KILL;
    #SHOWME {$questmodule};
    questupdate {$questmodule} {2};
    dohalt {
      gotodo {扬州城} {小吃店} {
        startfull {
          gotonpc {苗人凤} {xsfh_jieyao_askmiao {%1}}
        };
      };
    };
  };
  #CLASS questclass CLOSE;
};
#NOP {解药篇问苗人凤,%1:后续指令};
#ALIAS {xsfh_jieyao_askmiao} {
  #VARIABLE {questmodule} {雪山飞狐};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你向苗人凤打听有关『解药』的消息。} {
    #CLASS questresponseclass OPEN;
    #ACTION {^苗人凤说道：「需要治疗可以寻找程姑娘，确实高手。我也多谢她了} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questupdate {$questmodule} {3};
      dohalt {%1}
    };
    #ACTION {^苗人凤说道：「如此说来，治疗我这毒，怕是只有程灵素了} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      dohalt {
        gotonpc {程灵素} {xsfh_jieyao_askcheng {%1}}
      }
    };
    #ACTION {^你当即说道：“咱们好歹也得将他请到} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      dohalt {
        gotonpc {程灵素} {xsfh_jieyao_askcheng {%1}}
      }
    };
    #CLASS questresponseclass CLOSE;
  };
  #CLASS questclass CLOSE;
  dohalt {
    ask miao renfeng about 解药
  }
};
#NOP {解药篇问程灵素,%1:后续指令};
#ALIAS {xsfh_jieyao_askcheng} {
  #VARIABLE {questmodule} {雪山飞狐};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #NOP {程灵素说道：「除了七星海棠，还需要五毒教一种蓝色药花，我们一起找找看。」};
  #ACTION {^你向程灵素打听有关『解药』的消息。} {
    #CLASS questresponseclass OPEN;
    #ACTION {^程灵素说道：「我想也应该有效地治好苗大侠的病} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questupdate {$questmodule} {3};
      dohalt {%1}
    };
    #ACTION {^程灵素说道：「%*可惜被阎基和石万嗔抢走了。你赶快去找回来吧} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      dohalt {
        gotonpc {阎基} {xsfh_jieyao_askyanji {%1}}
      };
    };
    #ACTION {^程灵素说道：「快把七星海棠给我} {
      #NOP {这里是得到过七星海棠，但是流程异常，退出清空临时变量};
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questdelay {$questmodule} {0} {10800};
      dohalt {
        doquit
      };
    };
    #ACTION {^程灵素说道：「除了七星海棠，还需要五毒教一种蓝色药花} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      dohalt {
        gotodo {苗疆} {果园} {
          doheal {
            startfull {
              xsfh_jieyao_findlanhua {%1}
            };
          };
        };
      };
    };
    #CLASS questresponseclass CLOSE;
  };
  #CLASS questclass CLOSE;
  ask cheng lingsu about 解药
};
#NOP {解药篇问阎基};
#ALIAS {xsfh_jieyao_askyanji} {
  #VARIABLE {questmodule} {雪山飞狐};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkyanji\"|你设定checkyanji为反馈信息}} {
    #IF {"$roomthings[阎基]" != ""} {
      ask yan ji about 七星海棠;
    };
    #ELSE {
      #NOP {20分钟再过来};
      #CLASS questclass KILL;
      questupdate {$questmodule};
      runwait {%1};
    };
  };
  #ACTION {^你向阎基打听有关『七星海棠』的消息。} {
    #CLASS questresponseclass OPEN;
    #ACTION {^阎基%*说道：「是程丫头让你来得？哼，能不能拿到要看你的实力了} {
      #CLASS questresponseclass KILL;
    };
    #ACTION {^阎基%*说道：「不在我这里了} {
      #NOP {这里未知原因后续的流程异常了，退出清空临时变量};
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questdelay {$questmodule} {0} {10800};
      dohalt {
        doquit
      };
    };
    #CLASS questresponseclass CLOSE;
  };
  #ACTION {^突然从后山窜出一个人来，叫道：别怕，老阎，我来帮你！这小子，纯粹找死} {
    openwimpy;
    startfight;
    kill shi wanchen
  };
  #ACTION {^石万嗔「啪」的一声倒在地上，挣扎着抽动了几下就死了。} {
    kill yan ji
  };
  #ACTION {^阎基交给你一株七星海棠} {
    stopfight;
    cond;
    echo {checkcondition};
  };
  #ACTION {^{设定环境变量：action \= \"checkcondition\"|你设定checkcondition为反馈信息}} {
    #CLASS questclass KILL;
    dohalt {
      #IF {"$hp[condition][星宿掌]" != ""} {
        antipoison {星宿掌} {
          doheal {gotonpc {程灵素} {xsfh_jieyao_givehaitang {%1}}}
        };
      };
      #ELSE {
        doheal {gotonpc {程灵素} {xsfh_jieyao_givehaitang {%1}}}
      };
    }
  };
  #CLASS questclass CLOSE;
  closewimpy;
  pfm_wuxing;
  pfm_buff_normal;
  id here;
  echo {checkyanji};
};
#NOP {解药篇给程灵素七星海棠};
#ALIAS {xsfh_jieyao_givehaitang} {
  #VARIABLE {questmodule} {雪山飞狐};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你身上没有这样东西。} {
    #NOP {出错了，等会再来};
    #CLASS questclass KILL;
    questupdate {$questmodule};
    runwait {%1}
  };
  #ACTION {^你给程灵素一份七星海棠} {
    #CLASS questclass KILL;
    runwait {
      gotodo {苗疆} {果园} {
        startfull {
          xsfh_jieyao_findlanhua {%1}
        };
      };
    };
  };
  #CLASS questclass CLOSE;
  give qixing haitang to cheng lingsu
};
#NOP {解药篇找兰花};
#ALIAS {xsfh_jieyao_findlanhua} {
  #VARIABLE {questmodule} {雪山飞狐};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你找了半天，什么也没找到} {
    dohalt {
      yun jingli;
      search yao hua
    };
  };
  #ACTION {^你终于发现一颗蓝色药花。你伸手去摘了下来} {
    i
  };
  #ACTION {^突然，草丛中钻出一只{大狼狗|毒蛇}} {
    #IF {"%%1" == "大狼狗"} {
      kill lang gou
    };
    #ELSE {
      kill snake
    };
    startfight;
  };
  #ACTION {^{大狼狗|毒蛇}「啪」的一声倒在地上，挣扎着抽动了几下就死了} {
    stopfight;
    #IF {@carryqty{lanse yaohua} < 2} {
      dohalt {
        yun qi;
        yun jing;
        yun jingli;
        search yao hua
      };
    };
    #ELSE {
      #CLASS questclass KILL;
      gotonpc {程灵素} {xsfh_jieyao_give {%1}}
    };
  };
  #CLASS questclass CLOSE;
  openwimpy;
  search yao hua
};
#NOP {解药篇给程灵素蓝色药花};
#ALIAS {xsfh_jieyao_give} {
  #VARIABLE {questmodule} {雪山飞狐};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你身上没有这样东西。} {
    #NOP {出错了，等会再来};
    #CLASS questclass KILL;
    questupdate {$questmodule};
    runwait {%1}
  };
  #ACTION {^你给程灵素一棵蓝色药花} {
    #VARIABLE {idle} {-30};
  };
  #ACTION {^程灵素说道：「等着急了吧。这是药方，你可以使用内屋那个药炉制作} {
    #CLASS questclass KILL;
    #NOP {检查一下葫芦};
    dohalt {
      #IF {@carryqty{qingshui hulu} == 0 && @carryqty{qing hulu} == 0} {
        gethulu {gotodo {苗疆} {药王局} {xsfh_jieyao_make {%1}}} {}
      };
      #ELSE {
        gotodo {苗疆} {药王局} {xsfh_jieyao_make {%1}}
      };
    };
  };
  #CLASS questclass CLOSE;
  give yaohua to cheng lingsu
};
#NOP {解药篇制作解药};
#ALIAS {xsfh_jieyao_make} {
  #VARIABLE {questmodule} {雪山飞狐};
  #VARIABLE {busyflag} {0};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你突然手尖一阵巨痛，手指被火燎了一下} {
    #NOP {有人在制作解药,略等};
    #VARIABLE {busyflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkopen\"|你设定checkopen为反馈信息}} {
    #IF {$busyflag == 1} {
      #CLASS questclass KILL;
      #DELAY {5} {
        xsfh_jieyao_make {%1}
      };
    };
    #ELSE {
      dohalt {
        execute {
          add qixing haitang in lu;
          add ren shen in lu;
          add lanse yaohua in lu;
          add zang honghua in lu;
          add jinyin hua in lu;
          pour hulu to lu;
          close lu;
          dianhuo
        };
      };
    };
  };
  #ACTION {^{柴火已经点起来了|你点燃一堆柴火}} {
    #VARIABLE {idle} {-30};
    dohalt {burn lu}
  };
  #ACTION {^火势渐渐大了起来，一股浓重的草药味道扑鼻而来。应该熬药了} {
    #VARIABLE {idle} {-30};
    dohalt {aoyao}
  };
  #ACTION {^药快熬好了，赶紧灭火吧} {
    #VARIABLE {idle} {-30};
    dohalt {miehuo}
  };
  #ACTION {^你把丹炉内的柴火渐渐地熄灭了} {
    #VARIABLE {idle} {-30};
    dohalt {
      open lu;
      qu jie yao from lu
    }
  };
  #ACTION {^你从丹炉中拿出一颗解药} {
    #VARIABLE {idle} {-30};
    #CLASS questclass KILL;
    dohalt {
      drop hulu;
      gotonpc {苗人凤} {xsfh_jieyao_givemiao {%1}}
    }
  };
  #CLASS questclass CLOSE;
  open lu;
  echo {checkopen};
};
#NOP {解药篇给苗人凤};
#ALIAS {xsfh_jieyao_givemiao} {
  #VARIABLE {questmodule} {雪山飞狐};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你上前准备将解药送给了苗人凤服下，且发现药物遗失了} {
    #CLASS questclass KILL;
    %1
  };
  #ACTION {^你上前将内服药送给了苗人凤服下，并帮他将外敷药附在眼睛上} {
    #VARIABLE {idle} {0};
  };
  #ACTION {你于%*解开雪山飞狐解药篇} {
    #NOP {直接开始宝藏};
    questupdate {$questmodule} {3};
    dohalt {
      xsfh_baozang_checkmiao { %1};
    };
  };
  #CLASS questclass CLOSE;
};
#NOP {检查苗人凤较量是否OK,%1:后续指令,%2:重复检测标识};
#ALIAS {xsfh_baozang_checkmiao} {
  #VARIABLE {winflag} {0};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^%!s你曾与苗人凤进行过%*次较量，并获得%*次胜利。} {
    #VARIABLE {winflag} {1};
  };
  #ACTION {^%!s你曾与苗人凤进行过%*次较量，可惜一次也没有胜出。} {
    #IF {@ctd{%%1} >= 3} {
      #VARIABLE {winflag} {1};
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkstory\"|你设定checkstory为反馈信息}} {
    #CLASS questclass KILL;
    #IF {$winflag == 0} {
      #NOP {如果不满足，重复检测时直接结束，否则进行较量};
      #IF {"%2" != ""} {
        %1;
      };
      #ELSE {
        xsfh_baozang_fight {%1};
      };
    };
    #ELSE {
      xsfh_baozang_map {%1};
    };
  };
  #CLASS questclass CLOSE;
  dohalt {
    story;
    echo {checkstory};
  };
};
#NOP {较量};
#ALIAS {xsfh_baozang_fight} {
  #VARIABLE {questmodule} {雪山飞狐};
  #VARIABLE {checkcount} {0};
  #VARIABLE {winflag} {0};
  #VARIABLE {askresult} {0};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你向苗人凤打听有关『较量』的消息。} {
    #CLASS questresponseclass OPEN;
    #ACTION {^苗人凤说道：「{在下现在没有什么心思切磋武功了|你还是先治好你的病再来切磋吧}} {
      #NOP {经验或时间间隔不足或者有人刚切磋完};
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questupdate {$questmodule};
      dohalt {%1}
    };
    #ACTION {^苗人凤说道：「{今天先切磋到这里吧|你武功没什么大的变化}} {
      #NOP {经验或时间间隔不足};
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questfail {$questmodule};
      dohalt {%1}
    };
    #ACTION {^苗人凤向你一招手} {
      #DELAY {1} {
        ok;
        startfight;
      };
    };
    #ACTION {^经过一番苦斗，你发现自己的武功大有激进} {
      #VARIABLE {winflag} {1};
    };
    #ACTION {^突然一道神光笼罩着你，你的精气神竟然全部恢复了！} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      stopfight;
      #IF {$winflag == 1} {
        #NOP {直接宝藏};
        dohalt {
          gotodo {兰州城} {城中心} {
            startfull {
              gotonpc {苗人凤} {xsfh_baozang_map {%1}};
            };
          };
        };
      };
      #ELSE {
        questfail {$questmodule};
        #NOP {这里直接再检查一下};
        xsfh_baozang_checkmiao {%1} {1};
      };
    };
    #CLASS questresponseclass CLOSE;
  };
  #CLASS questclass CLOSE;
  pfm_wuxing;
  pfm_buff_normal;
  ask miao renfeng about 较量
};
#NOP {宝藏图};
#ALIAS {xsfh_baozang_map} {
  #VARIABLE {questmodule} {雪山飞狐};
  #VARIABLE {checkcount} {0};
  #VARIABLE {winflag} {0};
  #VARIABLE {askresult} {0};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你向苗人凤打听有关『闯王宝藏』的消息。} {
    #CLASS questresponseclass OPEN;
    #ACTION {^苗人凤说道：「你连我都赢不了，如何独力寻找宝藏。我这也算是为了你好。」} {
      #NOP {失败三次后询问时3/4几率有这个回复，继续问};
      #VARIABLE {askresult} {0};
    };
    #ACTION {^苗人凤说道：「宝藏图不在我这里，真是老了糊涂了} {
      #VARIABLE {askresult} {2};
    };
    #ACTION {^苗人凤交给你一张宝藏图。} {
      #VARIABLE {askresult} {1};
    };
    #ACTION {^苗人凤说道：「李闯王当年确实威震朝野，可惜时不逢时} {
      #VARIABLE {askresult} {1};
    };
    #CLASS questresponseclass CLOSE;
  };
  #ACTION {^{设定环境变量：action \= \"checkbaozang\"|你设定checkbaozang为反馈信息}} {
    #CLASS questresponseclass KILL;
    #MATH {checkcount} {$checkcount + 1};
    dohalt {
      #IF {$askresult == 1} {
        #CLASS questclass KILL;
        #IF {@carryqty{baozang tu} > 0} {
          gotonpc {胡斐} {xsfh_baozang_baodao {%1}};
        };
        #ELSE {
          questdelay {$questmodule} {0} {7200};
        %1;
        };
      };
      #ELSEIF {$askresult == 2} {
        #CLASS questclass KILL;
        questdelay {$questmodule} {0} {3600};
        %1;
      };
      #ELSEIF {$askresult == 3} {
        #CLASS questclass KILL;
        xsfh_baozang_fight {%1};
      };
      #ELSE {
        ask miao renfeng about 闯王宝藏;
        i;
        echo {checkbaozang};
      };
    }
  };
  #CLASS questclass CLOSE;
  ask miao renfeng about 闯王宝藏;
  i;
  echo {checkbaozang};
};
#NOP {拿冷月宝刀,%2:是否冷泉再入};
#ALIAS {xsfh_baozang_baodao} {
  #VARIABLE {questmodule} {雪山飞狐};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你看准了墓后三尺之处，运劲于指，伸手挖土} {
    #VARIABLE {idle} {0};
    #DELAY {1} {
      yun jingli;
      hp;
      wa di
    }
  };
  #ACTION {^你挖了半天，只弄了一手泥} {
    #NOP {冷月宝刀不在};
    #CLASS questclass KILL;
    questupdate {$questmodule};
    dohalt {
      %1
    };
  };
  #ACTION {^你抓住刀柄轻轻把单刀从土中抽出，刀刃抽出寸许，毫无生锈} {
    dohalt {
      #IF {"$env[baozang]" == ""} {
        yanjiu lengyue;
      };
      #ELSE {
        #VARIABLE {bzcity} {};
        #VARIABLE {bzroom} {};
        #SWITCH {"$env[baozang]"} {
          #CASE {"五佛寺"} {
            #VARIABLE {bzcity} {兰州城};
            #VARIABLE {bzroom} {五佛寺};
          };
          #CASE {"古长城"} {
            #VARIABLE {bzcity} {兰州城};
            #VARIABLE {bzroom} {古长城};
            #SHOWME {$bzcity $bzroom};
          };
          #CASE {"兰州土门子"} {
            #VARIABLE {bzcity} {兰州城};
            #VARIABLE {bzroom} {土门子};
          };
          #CASE {"青城"} {
            #VARIABLE {bzcity} {兰州城};
            #VARIABLE {bzroom} {青城};
          };
          #CASE {"昆仑小溪"} {
            #VARIABLE {bzcity} {昆仑山};
            #VARIABLE {bzroom} {山溪边};
          };
          #CASE {"草海"} {
            #VARIABLE {bzcity} {回疆};
            #VARIABLE {bzroom} {1581};
          };
        };
        #CLASS questclass KILL;
        guard_call {$bzcity} {$bzroom} {gao shou} {
          gotodo {$bzcity} {$bzroom} {xsfh_baozang_search {%1} {%2}}
        };
      };
    };
  };
  #ACTION {^你将冷月宝刀翻来覆去地研究着} {
    dohalt {
      yanjiu lengyue
    };
  };
  #ACTION {^你感觉全身气血翻腾，看来刚才的研究已经大伤你的精神} {
    #CLASS questclass KILL;
    #NOP {上次解谜失败,时间间隔不足};
    questdelay {$questmodule} {0} {10800};
    dohalt {
      drop lengyue;
      drop tu;
      %1
    };
  };
  #ACTION {^你感觉全身气血翻腾，看来以你当前的功力无法继续研究} {
    #CLASS questclass KILL;
    #NOP {上次解谜失败,经验隔时间不足};
    questdelay {$questmodule} {0} {50000};
    dohalt {
      drop lengyue;
      drop tu;
      %1
    };
  };
  #ACTION {^你已经发现刀身上的脉路，你可以尝试对照藏宝图！} {
    dohalt {
      guanzhu lengyue
    };
  };
  #ACTION {^你仔细观察冷月宝刀，发现脉路更加清晰} {
    dohalt {
      guanzhu lengyue
    };
  };
  #ACTION {^你已经灌注了内力，刀身上的脉路更加清晰} {
    duizhao lengyue
  };
  #ACTION {^地图{已经什么都看不出来了|渐渐变得模糊}} {
    #NOP {退出一下就行了};
    #CLASS questclass KILL;
    dohalt {
      doquit
    };
  };
  #ACTION {^你突然发现两者结合最终的标志竟然落在一个你曾经熟悉的地方——%*。} {
    set env_baozang %%1;
    #VARIABLE {env[baozang]} {%%1};
    dohalt {
      #VARIABLE {bzcity} {};
      #VARIABLE {bzroom} {};
      #SWITCH {"%%1"} {
        #CASE {"五佛寺"} {
          #VARIABLE {bzcity} {兰州城};
          #VARIABLE {bzroom} {五佛寺};
        };
        #CASE {"古长城"} {
          #VARIABLE {bzcity} {兰州城};
          #VARIABLE {bzroom} {古长城};
        };
        #CASE {"兰州土门子"} {
          #VARIABLE {bzcity} {兰州城};
          #VARIABLE {bzroom} {土门子};
        };
        #CASE {"青城"} {
          #VARIABLE {bzcity} {兰州城};
          #VARIABLE {bzroom} {青城};
        };
        #CASE {"昆仑小溪"} {
          #VARIABLE {bzcity} {昆仑山};
          #VARIABLE {bzroom} {山溪边};
        };
        #CASE {"草海"} {
          #VARIABLE {bzcity} {回疆};
          #VARIABLE {bzroom} {1581};
        };
      };
      #CLASS questclass KILL;
      guard_call {$bzcity} {$bzroom} {gao shou} {
        gotodo {$bzcity} {$bzroom} {xsfh_baozang_search {%1} {%2}}
      };
    };
  };
  #CLASS questclass CLOSE;
  wa di;
};
#NOP {雪山飞狐宝藏找入口,%1:后续指令,%2:是否冷泉再入};
#ALIAS {xsfh_baozang_search} {
  #VARIABLE {okflag} {0};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你马上要昏迷了，不能做任何事情。} {
    #CLASS questclass KILL;
    questfail {$questmodule};
  };
  #ACTION {^突然从角落里跳出一个人，%*对着你嚷道} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^跟着又出来一个人，竟然是{黑风寨寨主|黑风寨二当家|黑风寨三当家}%*，嚷道：“我们老大的话} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^%*「啪」的一声倒在地上，挣扎着抽动了几下就死了} {
    kill gao shou
  };
  #ACTION {^这里没有这个人} {
    #VARIABLE {okflag} {0};
    stopfight;
    dohalt {
      doheal {startfull {
        pfm_wuxing;
        pfm_buff_normal;
        echo {checkkiller};
      }};
    };
  };
  #ACTION {^你意外地发现一个可以通向地下的通道} {
    #VARIABLE {okflag} {2};
    guard_over;
  };
  #ACTION {^你已经发现找到藏宝地址了} {
    #VARIABLE {okflag} {3};
    guard_over;
  };
  #ACTION {^{设定环境变量：action \= \"checkkiller\"|你设定checkkiller为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {$okflag == 1} {
      startfight {1};
      kill gao shou;
    };
    #ELSEIF {$okflag == 2} {
      #VARIABLE {okflag} {1};
      dohalt {
        d;
        drop lengyue;
        echo {checkok};
      };
    };
    #ELSEIF {$okflag == 3} {
      #IF {@contains{{roomexits}{d}} == 0} {
        #CLASS questclass KILL;
        questdelay {$questmodule} {0} {3600};
        dohalt {doquit};
      };
      #ELSE {
        #VARIABLE {okflag} {1};
        dohalt {
          d;
          drop lengyue;
          echo {checkok};
        };
      };
    };
    #ELSE {
      dohalt {
        search;
        look;
        #DELAY {1} {
          echo {checkkiller};
        };
      };
    };
  };
  #ACTION {^突然你听到一个声音道：“这里不欢迎你} {
    #VARIABLE {okflag} {0};
  };
  #ACTION {^突然你听到一个声音道：“宝藏已经被人抢先一步了} {
    #VARIABLE {okflag} {0};
  };
  #ACTION {^{设定环境变量：action \= \"checkok\"|你设定checkok为反馈信息}} {
    #CLASS questclass KILL;
    #IF {$okflag == 0} {
      dohalt {
        questdelay {$questmodule} {0} {1200};
        loc {doquit};
      };
    };
    #ELSEIF {"%2" == ""} {
      dohalt {
        loc {gotodo {星宿海} {兵器库} {xsfh_baozang_bingqiku {%1}}};
      };
    };
    #ELSE {
      dohalt {
        loc {gotodo {星宿海} {书房} {xsfh_baozang_yupei {%1}}};
      };
    };
  };
  #CLASS questclass CLOSE;
  startfull {
    pfm_wuxing;
    pfm_buff_normal;
    echo {checkkiller};
  };
};
#NOP {兵器库};
#ALIAS {xsfh_baozang_bingqiku} {
  #VARIABLE {findcount} {0};
  #VARIABLE {checkcount} {0};
  #LIST {matrixstep} {clear};
  #VARIABLE {stepindex} {0};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你发现} {
    #MATH {findcount} {$findcount + 1};
  };
  #ACTION {^{设定环境变量：action \= \"checkfind\"|你设定checkfind为反馈信息}} {
    #VARIABLE {idle} {0};
    #MATH {checkcount} {$checkcount + 1};
    dohalt {
      #IF {$checkcount >= 20} {
        #NOP {如果搜索很多次没有收获，说明这个误触发进来的，直接设置完成};
        #CLASS questclass KILL;
        questupdate {雪山飞狐} {4};
        %1;
      };
      #ELSEIF {$findcount < 2} {
        search;
        echo {checkfind};
      };
      #ELSE {
        #CLASS questclass KILL;
        dohalt {
          loc {gotodo {星宿海} {金库} {xsfh_baozang_jinku {%1}}};
        };
      };
    };
  };
  #CLASS questclass CLOSE;
  echo {checkfind};
};
#NOP {金库};
#ALIAS {xsfh_baozang_jinku} {
  #LIST {matrixstep} {clear};
  #VARIABLE {stepindex} {0};
  #VARIABLE {checkcount} {0};
  #VARIABLE {okflag} {0};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你四处转了一圈，果然找到很多玉器黄金，还有一些值钱的名迹，看来这次不虚此行啊。} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkfind\"|你设定checkfind为反馈信息}} {
    #VARIABLE {idle} {0};
    #MATH {checkcount} {$checkcount + 1};
    #DELAY {1} {
      #IF {$checkcount >= 10 || $okflag == 1} {
        i;
        echo {checkweight};
      };
      #ELSE {
        echo {checkfind};
      };
    }
  };
  #ACTION {^{设定环境变量：action \= \"checkweight\"|你设定checkweight为反馈信息}} {
    #VARIABLE {idle} {0};
    #MATH {checkcount} {$checkcount + 1};
    #DELAY {1} {
      #IF {$id[weight] > 95} {
        drop 5 gold;
        i;
        echo {checkweight};
      };
      #ELSE {
        #CLASS questclass KILL;
        dohalt {
          loc {gotodo {星宿海} {密室} {xsfh_baozang_shuku {%1}}};
        };
      };
    };
  };
  #CLASS questclass CLOSE;
  i;
  echo {checkfind};
};
#NOP {书库};
#ALIAS {xsfh_baozang_shuku} {
  #LIST {matrixstep} {clear};
  #VARIABLE {stepindex} {0};
  #VARIABLE {itemname} {};
  #VARIABLE {findcount} {0};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你发现一颗%*丹} {
    #VARIABLE {itemname} {dan};
  };
  #ACTION {^你发现一颗%*丸} {
    #VARIABLE {itemname} {wan};
  };
  #ACTION {^你找了半天，什么也没找到。} {
    #VARIABLE {idle} {0};
    dohalt {
      search
    }
  };
  #ACTION {^你展开纸团，仔细看了看似乎是武功秘籍之类的介绍} {
    #VARIABLE {itemname} {zhituan};
  };
  #ACTION {^{设定环境变量：action \= \"checkfind\"|你设定checkfind为反馈信息}} {
    dohalt {
      #IF {"$itemname" == "zhituan"} {
        pray pearl;
        yanjiu zhituan
      };
      #ELSE {
        #IF {"$itemname" != ""} {
          fu $itemname;
        };
        search;
        echo {checkfind};
      };
    }
  };
  #ACTION {^你还是认真看一下提示卡的内容，不要过于冲动。} {
    #VARIABLE {idle} {0};
    dohalt {
      look ka;
      yanjiu zhituan
    };
  };
  #ACTION {^你仔细研究纸团的内容，地点应该就在刚才经过的书房里。} {
    #VARIABLE {idle} {0};
    #CLASS questclass KILL;
    questsuccess {冷泉神功};
    questupdate {雪山飞狐} {4};
    dohalt {
      drop sheng zi;
      drop ka;
      out;
      w;
      u;
      loc {
        runwait {
          gotonpc {顾炎武} {xsfh_baozang_sell {%1}};
        };
      }
    };
  };
  #ACTION {^你按照纸团上的这些线线运行全身经脉，发现全无用处，更别说提高武功了。} {
    #VARIABLE {idle} {0};
    #CLASS questclass KILL;
    questfail {冷泉神功};
    questupdate {雪山飞狐} {4};
    dohalt {
      drop ka;
      out;
      w;
      u;
      loc {
        runwait {
          gotonpc {顾炎武} {xsfh_baozang_sell {%1}};
        };
      }
    };
  };
  #CLASS questclass CLOSE;
  search;
  echo {checkfind};
};
#NOP {书库};
#ALIAS {xsfh_baozang_yupei} {
  #VARIABLE {okflag} {0};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你按照纸团提示的方位，并没有发现秘籍，但却发现一块} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkfind\"|你设定checkfind为反馈信息}} {
    #IF {$okflag == 1} {
      #CLASS questclass KILL;
      dohalt {gotonpc {苗人凤} {lqsg_giveyupei {%1}}}
    };
    #ELSE {
      questfail {$questmodule};
      dohalt {%1}
    };
  };
  #CLASS questclass CLOSE;
  search;
  echo {checkfind};
};
#NOP {卖垃圾,%1};
#ALIAS {xsfh_baozang_sell} {
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkthings\"|你设定checkthings为反馈信息}} {
    #VARIABLE {idle} {0};
    #DELAY {1} {
      #IF {@carryqty{gold} >= 10} {
        #CLASS questclass KILL;
        gotodo {扬州城} {天阁斋} {balanceex {2} {} {gotonpc {顾炎武} {xsfh_baozang_sell {%1}}}};
      };
      #ELSEIF {@carryqty{xiang shi} > 0} {
        give xiang shi to gu;
      };
      #ELSEIF {@carryqty{tu juan} > 0} {
        give tu juan to gu;
      };
      #ELSE {
        #CLASS questclass KILL;
        checkreward {checkreward {%1}};
      };
    };
  };
  #ACTION {^顾炎武说道：在下极其喜爱这%*，不知这位%*能(yes)不能(no)忍痛割爱呢？} {
    no
  };
  #ACTION {^说着顾炎武不由你分说%*就拿进内屋里去了。} {
    i;
    echo {checkthings};
  };
  #CLASS questclass CLOSE;
  i;
  echo {checkthings};
};
#NOP {获取装水的葫芦,用于雪山飞狐制作解药,%1:成功指令,%2:失败指令};
#ALIAS {gethulu} {
  gotodo {华山} {饭厅} {
    gethulustart {qingshui hulu} {%1} {%2}
  }
};
#NOP {开始拿葫芦,%1:葫芦id,%2:成功指令,%3:失败指令};
#ALIAS {gethulustart} {
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkhulu\"|你设定checkhulu为反馈信息}} {
    #CLASS questclass KILL;
    runwait {
      #IF {@carryqty{%1} == 0} {
        #IF {"%1" == "qingshui hulu"} {
          gotodo {星宿海} {厨房} {
            gethulustart {qing hulu} {%2} {%3}
          }
        };
        #ELSE {
          gethulu {%2} {%3};
        };
      };
      #ELSE {
        %2
      };
    }
  };
  #CLASS questclass CLOSE;
  get %1;
  i;
  echo {checkhulu};
};
#NOP {去华山拿绳子,%1:成功指令,%2:失败指令};
#ALIAS {getshengzi} {
  gotodo {华山} {寝室} {getshengzistart {%1} {%2}}
};
#ALIAS {getshengzistart} {
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkshengzi\"|你设定checkshengzi为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {@carryqty{sheng zi} > 0} {
      #CLASS questclass KILL;
      runwait {%1};
    };
    #ELSE {
      #DELAY {6} {
        get sheng zi;
        i;
        echo {checkshengzi};
      }
    };
  };
  #CLASS questclass CLOSE;
  get sheng zi;
  i;
  echo {checkshengzi};
};