#CONFIG	{VERBOSE} {OFF};
#CLASS commonmodule KILL;
#CLASS commonmodule OPEN;
#NOP {==============================================信息屏蔽==============================================开始};
#GAG {^%*从%*走了过来。}
#GAG {^%*往%*离开。}
#GAG {^%*长长地舒了一口气。}
#GAG {^%*深深吸了几口气，脸色看起来好多了。}
#GAG {^%*正在专心练习%*。};
#GAG {干渴难熬！};
#GAG {长袖飘飘};
#NOP {==============================================信息屏蔽==============================================结束};

#NOP {==============================================通用触发内容==============================================开始};
#NOP {打坐完成};
#VARIABLE {dazuo_over} {你将内息走满一个周天，只感到全身通泰|你将真气逼入体内|你的真气已尽数归聚于丹田|你运功完毕，站了起来|你呼翕九阳，抱一含元|你吸气入丹田，真气运转渐缓|你真气在体内运行了一个周天，缓缓收气于丹田，慢慢睁开了眼睛。|你将北冥真气在体内运行十二周天|你将周身内息贯通经脉|你分开双手，黑气慢慢沉下|你将内息在体内运行十二周天|你将真气在体内沿脉络运行了一圈|你双眼微闭，缓缓将天地精华之气吸入体内,见天地恢复清明，收功站了起来。|你将内息游走全身，但觉全身舒畅，内力充沛无比|你感觉毒素越转越快，就快要脱离你的控制了！|你将内息又运了一个小周天，缓缓导入丹田，双臂一震，站了起来。|过了片刻，你感觉自己已经将玄天无极神功气聚丹田，深吸口气站了起来。|你将纯阳神通功运行完毕，除却白气笼罩，双眼精光四射，缓缓站了起来。|你只觉神元归一，全身精力弥漫，无以复加，忍不住长啸一声，徐徐站了起来。|你将寒冰真气按周天之势搬运了一周，感觉精神充沛多了。|你慢慢收气，归入丹田，睁开眼睛，轻轻的吐了一口气。|你将内息走了个小周天，流回丹田，收功站了起来。|你只觉真力运转顺畅，周身气力充沛，气沉丹田，站起身来|你一个周天行将下来|你已将「冷泉内劲」游走全身经脉诸穴|你感到自己和天地融为一体};
#NOP {打坐中止};
#VARIABLE {dazuo_halt} {你心神一动，将内息压回丹田|你面色一沉，迅速收气|你眉头微皱，缓缓睁开双眼|你眉头一皱，急速运气|你长出一口气，将内息急速退了回去|你双眼一睁|你双掌一分|你微一簇眉，将内息压回丹田|你双眼缓缓闭合|你周身微微颤动，长出口气|你感到烦躁难耐|你内息一转，迅速收气|你忽然强运一口真气，双眼一睁|你双眼一睁，眼中射出一道精光|你把正在运行的真气强行压回丹田|你匆匆将内息退了回去|你把正在运行的真气强行压回丹田|你双眼一睁，极速压下内息站了起来|你猛吸几口大气|你猛的睁开双眼|你突然睁开双眼，口嘘冷雾，将运转全身的「冷泉神功」散去|你感到呼吸紊乱，全身燥热};
#NOP {打坐描述};
#VARIABLE {dazuo_desc} {你随意坐下，双手平放在双膝|你盘膝而坐，右手高举|你随意一站，双手缓缓抬起|你盘膝而坐，运起八荒六合唯我独尊功|你盘膝而坐，双目紧闭|你盘腿坐下，双目微闭|你盘膝坐下，双手合十置于头顶|你盘膝坐下，暗运内力|你手捏剑诀，将寒冰真气提起在体内慢慢转动|你气运丹田，将体内毒素慢慢逼出|你凝神静气，盘坐下来|你随意坐下，双手平放在双膝|你五心向天|你盘膝而坐，运使九阳，气向下沉|你盘腿坐下，双目微闭|你手捏绣花针，盘膝坐下|你盘膝坐下，垂目合什|你盘膝而坐，形神合一|你席地而坐，五心向天|你收敛心神闭目打坐，手搭气诀|你盘膝坐下，闭目合什|你盘膝而坐，双手垂于胸前成火焰状|随意一站，双手缓缓抬起|你慢慢盘膝而坐，双手摆于胸前|你盘膝而坐，双目紧闭|你盘膝坐下，默运天魔大法|你运起纯阳神通功，片刻之间|你随意坐下，双手平放在双膝|你运起玄天无极神功，气聚丹田|五心向天，排除一切杂念|你盘膝而坐，双目紧闭|你轻轻的吸一口气，闭上眼睛|你屏息静气，坐了下来|你坐下来运气用功|你双眼微闭，被一股九阴真气围绕著|你盘膝而坐，形神合一，暗运「冷泉神功」|你收敛心神闭目打坐};
#NOP {疗伤描述};
#VARIABLE {heal_desc} {你盘膝坐下|你凝神静气|你神情肃然|你全身放松|你运起寒冰真气|你连催|你盘膝而坐|你双手合什，盘膝而坐|你连催四道冷泉内劲};
#NOP {增强型buff启动描述};
#VARIABLE {buff_desc} {你屏气凝神，口中默念|你运起北冥神功口中默念|你仔细的思考奇门八卦之学};
#NOP {增强型buff结束描述};
#VARIABLE {buff_over} {你的玉女心经「心经」字诀运功完毕|你渐渐地散去了聚于气海|你的思路浑浊，只好散去了「奇门」心法};
#NOP {武器描述};
#VARIABLE {weapondes} {这是一根|这是一柄|一枚蓝莹莹的细针|这是一只由上好的翠玉|这是一普普通通的钢斧|这是一支用于引火的火折|这对判官笔看起来极其普通|这是一只上好铁打制而成的铁笔。|魏太子丕，造百辟匕首三|这是一把练习剑法用的竹剑。|一柄精铁制的流星锤|这是一寻常的钢刀|这种刀背厚刃薄|这把剑犹如一条盘蛇|这只法轮由玄铁所制|这是一把薄薄的软刀|这只法轮由玄铁所制|一堆色泽暗红，上铸有飞龙图案的飞镖|这把剑};
#NOP {==============================================通用触发内容==============================================结束};

#NOP {==============================================通用基础数据==============================================开始};
#NOP {功夫等级描述};
#LIST {common[leveldesc]} {create} {
  不堪一击;毫不足虑;不足挂齿;初学乍练;勉勉强强;初窥门径;初出茅庐;略知一二;普普通通;平平淡淡;
  平淡无奇;粗通皮毛;半生不熟;马马虎虎;略有小成;已有小成;鹤立鸡群;驾轻就熟;青出于蓝;融会贯通;
  心领神会;炉火纯青;了然于胸;略有大成;已有大成;豁然贯通;出类拔萃;无可匹敌;技冠群雄;神乎其技;
  出神入化;非同凡响;傲视群雄;登峰造极;无与伦比;所向披靡;一代宗师;精深奥妙;神功盖世;举世无双;
  惊世骇俗;撼天动地;震古铄今;超凡入圣;威镇寰宇;空前绝后;天人合一;深藏不露;深不可测;返璞归真
};
#NOP {武器伤害描述};
#LIST {damagedes} {create} {极其可怕;无与伦比;斩妖伏魔;倚天屠龙;开天劈地;举世无匹;绝世罕见;天下无双;开元创世;毁天灭地};

#NOP {实力比对描述};
#LIST {comparedes} {create} {超级菜鸟肉脚;普通菜鸟肉脚;小卒仔;普通角色;不分上下，伯仲之间;普通硬角;一般高手;武林高手;深不可测;空前绝后;天神般无所伦比};

#NOP {super武器名字，应用于回收遗产时};
#LIST {superlist} {create} {xuanyuan;shentong;wuji;bagua;joeywus;taibai;qiankun;taiji;tiandi;fengyun;haoqi;lianyu;sanqing;kunlun};

#NOP {备选用于练功和打坐的房间，为防止聚集，使用此脚本的角色练功和打坐时会随机分布在以下房间};
#LIST {practicerooms} {create} {924;926;928;930;931;909;907;906;900;898;897;975;977;978;985;989;990;991;993;994;995};

#NOP {不能进行学习的技能};
#LIST {nolearnskills} {create} {literate;trade;medicine;jinmai-xue;xuantie-jianfa};

#NOP {打造用的玉};
#LIST {common[jade]} {create} {you yu;du yu;nan yu;mi yu;fenglei yu;longling yu;ban yu;liu yu;zhi yu;xiangni yu;lvyu sui;fengling yu};

#NOP {一般值钱物品};
#LIST {common[valuable]} {create} {yitian canpian;tulong canpian;mizong longyangsan;qiqiaolinglong yu;ebook;yuehua shi;shensheng zhufu};

#NOP {要丢的垃圾};
#LIST {common[garbage]} {create} {weilan's hammer;qingtong;mian hua;mianhua zhongzi;yao cao};

#NOP {钱购买的药品};
#LIST {common[normaldrugs]} {create} {neixi wan;chantui yao;huoxue dan;chuanbei wan;zhengqi dan;xieqi wan};

#NOP {通宝购买的药品};
#LIST {common[raredrugs]} {create} {dahuan dan;da huandan};

#NOP {正神任务};
#LIST {common[zsjobs]} {create} {武当;天地会;华山;华山2};

#NOP {负神任务};
#LIST {common[fsjobs]} {create} {雪山;嵩山};

#NOP {物品ID对应};
#VARIABLE {common[items]} {
  {田七鲨胆散} {tianqi}
  {精英之书} {ebook}
  {龙鳞玉} {longling yu}
  {风雷玉} {fenglei yu}
  {凤瓴玉} {fengling yu}
  {绿玉髓} {lvyu sui}
  {香凝玉} {xiangni yu}
};
#NOP {房间需要处理的危险成员，即在叫杀任务NPC前需要把下述的npc处理到};
#LIST {common[dangernpcs]} {create} {
  飘然子;陆乘风;护法使者;凌震天;黄令天;毒蛇;菜花蛇;野狼;马贼;玉蜂;大灰狼;灰衣帮众;平寇将军;折冲将军;车骑将军;征东将军;
  烈火旗教众;洪水旗教众;锐金旗教众;厚土旗教众;范瑶;出尘子;值勤兵;星宿派弟子;慧真尊者;黑色毒蛇;野猪;竹叶青;獒犬;藏獒;老虎;恶犬
};
#NOP {守卫襄阳和颂摩崖武士技能处理优先级};
#LIST {common[dangersmyskills]} {create} {玄阴剑法;灵蛇杖法;天山杖法;天羽奇剑;圣火令法;独孤九剑;玉女素心剑;七弦无形剑;回风拂柳剑;如意刀法};

#NOP {危险的武当NPC技能};
#LIST {common[dangerwdskills]} {create} {玄阴剑法;独孤九剑;灵蛇杖法;灵蛇拳法;银索金铃;打狗棒法;降龙十八掌;抽髓掌;三阴蜈蚣爪;原始剑法;古拙掌法;七弦无形剑;天山折梅手;圣火令法;七伤拳};

#NOP {需要关注的拦路NPC};
#NOP {passexp:经验阈值，低于阈值放弃};
#NOP {performexp:使用技能阈值，低于阈值使用perform，0永远使用};
#VARIABLE {common[blocker]} {
  {ding chunqiu} {
    {passexp} {2500000}
    {performexp} {0}
  }
  {huang yaoshi} {
    {passexp} {2500000}
    {performexp} {0}
  }
  {murong bo} {
    {passexp} {2500000}
    {performexp} {0}
  }
  {he taichong} {
    {passexp} {2500000}
    {performexp} {0}
  }
  {fan yao} {
    {passexp} {1000000}
    {performexp} {10000000}
  }
  {yang xiao} {
    {passexp} {1000000}
    {performexp} {10000000}
  }
  {yu lianzhou} {
    {passexp} {800000}
    {performexp} {10000000}
  }
  {yin liting} {
    {passexp} {800000}
    {performexp} {10000000}
  }
  {zhang songxi} {
    {passexp} {800000}
    {performexp} {10000000}
  }
  {liu chuxuan} {
    {passexp} {800000}
    {performexp} {10000000}
  }
  {ding mian} {
    {passexp} {800000}
    {performexp} {10000000}
  }
  {benyin dashi} {
    {passexp} {800000}
    {performexp} {10000000}
  }
  {xuansheng dashi} {
    {passexp} {800000}
    {performexp} {10000000}
  }
  {dadian dashi} {
    {passexp} {800000}
    {performexp} {10000000}
  }
  {rong ziju} {
    {passexp} {600000}
    {performexp} {6000000}
  }
  {xin shuangqing} {
    {passexp} {600000}
    {performexp} {6000000}
  }
  {zuo zimu} {
    {passexp} {600000}
    {performexp} {6000000}
  }
  {huizhen zunzhe} {
    {passexp} {400000}
    {performexp} {10000000}
  }
  {caihua zi} {
    {passexp} {160000}
    {performexp} {200000}
  }
  {dali guanbing} {
    {passexp} {150000}
    {performexp} {200000}
  }
  {huanggong shiwei} {
    {passexp} {150000}
    {performexp} {200000}
  }
  {lama} {
    {passexp} {150000}
    {performexp} {200000}
  }
  {wu jiang} {
    {passexp} {150000}
    {performexp} {200000}
  }
  {guan bing} {
    {passexp} {150000}
    {performexp} {200000}
  }
  {ya yi} {
    {passexp} {150000}
    {performexp} {200000}
  }
};
#NOP {==============================================通用基础数据==============================================结束};

#NOP {==============================================区域相关资料==============================================开始};
#NOP {==============================================受限区域资料==============================================开始};
#NOP {受限区域资料，用户配置conf[limitedzone]配置的区域忽略访问，未配置的区域判定是否满足区域条件};
#NOP {exp:经验条件，低于此阈值的不访问};
#NOP {skill:技能条件，不满足列表中技能等级要求的不访问};
#NOP {weight:负重，仅应用于绝情谷底，负重大于30浮不上来};
#NOP {range:访问受限的房间编号列表};
#VARIABLE {common[limitedzone]} {
  {武当山} {
    {weight} {0}
    {range} {3378;3379;3401;3402;3403;3404;3405;3406;3407;3408;3409;3410;3411;3412;3413;3414;3415;3416;3384;3550;3551;3552;3553}
  }
  {苗疆} {
    {exp} {3500000}
    {skill} {      
    }
    {range} {738;739;740;741;742;743;744;745;746;747;748;749;750;751;752;753;754;755}
  }
  {桃源县} {
    {exp} {1000000}
    {range} {3249;3250;3251;3252;3253;3254;3921;3922;3923;3924;3925;3926;3255;3256;3257;3258;3259;3260;3261;3262;3263;3264;3265;3266;3267;3268}
  }
  {桃花岛} {
    {skill} {
      {qimen-bagua} {121}
    }
  }
  {绝情谷} {
    {weight} {30}
    {range} {3342;3343;3344;3345;3346;3347;3320;3321;3322;3323;3324;3325;3326}
  }
  {黑木崖} {
    {exp} {1000000}
    {range} {2123;2124;2125;2126;2127;2128;2129;2130;2131;2132;2133;2134;2135;2136;2137;2138;2139;2140;2141;2142;2143;2144;2145;2146;2147}
  }
  {襄阳城} {
    {skill} {
      {force} {0}
    }
    {range} {654;655;656;657;3639;658;659;3469;3912;3913;3914;3915;3916;3917;3918;3919;3920}
  }
};
#NOP {==============================================受限区域资料==============================================结束};

#NOP {==============================================天地会受限区域资料==============================================开始};
#NOP {部分不适合天地会任务的区域};
#VARIABLE {common[tdhlimitedzone]} {
  {桃源县} {
    {range} {3249;3250;3251;3252;3253;3254;3921;3922;3923;3924;3925;3926}
  }
  {绝情谷} {
    {weight} {30}
    {range} {3342;3343;3344;3345;3346;3347;3320;3321;3322;3323;3324;3325;3326}
  }
  {襄阳城} {
    {range} {654;655;656;657;3639;658;659;3469;3912;3913;3914;3915;3916;3917;3918;3919;3920}
  }
};
#NOP {天地会用于左右横跳的特别定义的区域房间，原则上如果区域因迷宫过河分隔后应分为多个区域};
#VARIABLE {common[tdhwanderroom]} {
  {扬州城} {
    {default} {410;411;413;433;436;438;454;473;479;485;486;493;494;500;501;507;510}
  }
  {襄阳城} {
    {default} {616;562;563;564;596;595;593;594;567}
  }
  {苏州城} {
    {default} {314;315;316;317;318;319;321;322;342;343;344;346;347;348;349;350;351;386;387;388;389}
  }
  {杭州城} {
    {default} {205;206;207;230;231;232;233;235;236;237;242;243;244;245}
  }
  {宁波城} {
    {default} {134;135;139;140;141;142;143;144;145}
  }
  {牛家村} {
    {default} {225;226;227;211;213}
  }
  {莆田少林} {
    {default} {2996;2998;2999;3000;3001;3002;3003;3004;3005;3006;3007;3008}
  }
  {福州城} {
    {default} {81;82;83;84;88;89;97;98;99;100;101;1853;1854;1855;1856}
    {partition} {
      {p1} {
        {rooms} {75;73;74;72;71;70;69;68;67;66;65;3281}
        {places} {65;71;74;3281}
      }
    }
  }
  {佛山镇} {
    {default} {16;17;19;20;46;47;29;30}
  }
  {燕子坞} {
    {default} {1870;1871;1872;1875;1878;1887;1888;1907}
  }
  {曼佗罗山庄} {
    {default} {1929;1931;1914;1915;1922;1919}
  }
  {姑苏慕容} {
    {default} {1957;1958;1967;1964}
    {partition} {
      {p1} {
        {rooms} {290;291;292;293;294}
        {places} {290;291;292;293;294}
      }
    }
  }
  {黄河流域} {
    {default} {784;785;787;788;800;801;802}
  }
  {泰山} {
    {default} {830;831;832;833;834;835;836;837;838;839;840;841;842;843;844;845;846}
  }
  {长乐帮} {
    {default} {533;534;535}
  }
  {苗疆} {
    {default} {720;721;722;723;724;728;729;730}
  }
  {铁掌山} {
    {default} {679;680;681;669;670;693;694;695}
  }
  {成都城} {
    {default} {1041;1042;1050;1051;1846;1023;1751}
  }
  {峨嵋山} {
    {default} {1342;1343;1344;1345;1346;1347;1348;1349;1332;1333;1335;1338}
  }
  {大理城} {
    {default} {1096;1097;1098;1099;1100;1101;1107;1108;1088;1089;1090;1091;1092;1136;1137;1138;1139;1140;1141;1142;1143}
  }
  {天龙寺} {
    {default} {1237;1238;1239;1240;1241}
  }
  {长安城} {
    {default} {887;888;907;908;909;930;931;932}
  }
  {华山} {
    {default} {2234;2235;2236;2237;2238;2239;2240;2241;2242;2243;2244;2245;2246}
  }
  {华山村} {
    {default} {2217;2221;2222;2233}
  }
  {终南山} {
    {default} {2298;2299;2286;2290;2298}
  }
  {南阳城} {
    {default} {772;773;774;777;778}
  }
  {嵩山少林} {
    {default} {2515;2516;2517;2518;2519;2520;2521;2522;2540;2541;2542;2543;2544;2545;2546;2547;2564;2568;2570;2576;2583;2589;2615}
  }
  {嵩山} {
    {default} {2679;2680;2682;2683;2684;2685;2687;2689;2702}
  }
  {塘沽城} {
    {default} {2015;2018;2019;2020;2021;2029;2030;2031;2033}
  }
  {沧州城} {
    {default} {1976;1977;1980;1989;1990;1991;1992;1993}
  }
  {恒山} {
    {default} {2079;2080;2081;2082;2083;2084;2085;2086;2087;2088;2089;2090;2091}
  }
  {平定州} {
    {default} {2051;2060;2062;2064}
  }
  {星宿海} {
    {default} {1437;1438;1439;1440;1441;1442;1443;1445;1446;1447}
  }
  {明教} {
    {default} {1591;1592;1593;1594;1662;1663;1664;1665;1659;1684}
  }
  {逍遥派} {
    {default} {1533;1534;1535;1536;1538}
  }
  {大雪山} {
    {default} {1773;1774;1775;1765;1766;1767;1768;1770}
    {partition} {
      {p1} {
        {rooms} {1803;1804;1805;1806;1807;1808;1809;1810;1811;1812;1813;1814;1815;1816;1817;1818;1819;1820;1821;1822;1823;1824;1825;1826;1827;1828;1829;1830;1831;1832;1833;1834;1836;1837;1838;1841;1842;1843;1844;1845}
        {places} {1805;1807;1821}
      }
    }
  }
  {武当山} {
    {default} {2370;2371;2372;2374;2375;2376;2377;2378;2379;2381;2382;2383;2384;2385;2386;2387;2388}
  }
  {昆仑山} {
    {default} {1689;1690;1691;1692;1706;1711;1712;1713;1714;1715;1718}
  }
  {神龙岛} {
    {default} {2165;2166;2167;2168;2169;2170}
    {partition} {
      {p1} {
        {rooms} {2036;2037}
        {places} {2036;2037}
      }
    }
  }
  {全真教} {
    {default} {2311;2313;2315;2350;2322}
  }
  {嘉兴城} {
    {default} {267;268;269;270;271;272;273;274;275;276;277;278;279;283}
  }
  {回疆} {
    {default} {1541;1542;1545}
  }
  {桃源县} {
    {partition} {
      {p1} {
        {rooms} {1203;1204;1205;1206}
        {places} {1203;1204;1205;120}
      }
      {p2} {
        {rooms} {3255;3256;3257;3258;3259;3260;3261;3262;3263;3264;3265;3266;3267;3268}
        {places} {3255;3256;3260;3261;3263;3264}
      }
    }
  }
  {兰州城} {
    {default} {1000;1001;1002;1003;1005;1006;1007;1008;1009;1010}
    {partition} {
      {p1} {
        {rooms} {1404;1405;1406;1407;1408;1409;1410;1411;1412;1413;1414;1415;1416;1417;1418;1419;1420;1421;1422;1423;1424;1425;1426}
        {places} {1412;1413;1414;1415;1419;1420;1421;1422}
      }
    }
  }
  {黑木崖} {
    {default} {2100;2099;2067}
    {partition} {
      {p1} {
        {rooms} {2123;2124;2125;2126;2127;2128;2129;2130;2131;2132;2133;2134;2135;2136;2137;2138;2139;2140;2141;2142;2143;2144;2145;2146;2147}
        {places} {2126;2127}
      }
    }
  }
  {绝情谷} {
    {default} {2733;2734;2735;2736;2737;2738}
    {partition} {
      {p1} {
        {rooms} {620;621;622;623}
        {places} {620;621;622;623}
      }
      {p2} {
        {rooms} {2741;2742;2743;2744;3224;3225;3226;3227;3228;3229;3230;3231;3232;3233;3234;3235;3236;3237}
        {places} {2741;2742;2743}
      }
    }
  }
  {天山} {
    {default} {1486;1487;1488;1489;1490;1491;1492;1493;1494}
    {partition} {
      {p1} {
        {rooms} {1496;1498;1499;1500;1501;1502;1503;1504;1505;1506;1507;1508;1509;1510;1511;1512;1513;1514;1515;1516;1517;1518;1519;1520;1521;1522;1523}
        {places} {1503;1506;1509;1511;1514;1516;1518;1520;1521;1522}
      }
    }
  }
  {蝴蝶谷} {
    {default} {3436;3437;3438;3439;3440;3447;3448}
    {partition} {
      {p1} {
        {rooms} {515}
        {places} {515}
      }
      {p2} {
        {rooms} {3444;3458;3450;3453;3454;3455;3456;3457;3465;3459;3460;3461;3462;3463;3464}
        {places} {3458;3457;3454;3464}
      }
    }
  }
};
#NOP {==============================================天地会受限区域资料==============================================结束};

#NOP {==============================================区域划分资料==============================================开始};
#NOP {书剑地图Block划分};
#NOP {主大陆};
#LIST {common[mainland]} {create} {长安城;长乐帮;成都城;大理城;大雪山;峨嵋山;黄河流域;华山村;华山;南阳城;嵩山少林;嵩山;泰山;天龙寺;铁掌山;武当山;襄阳城;扬州城;终南山;蒙古;全真教;苗疆;兰州城;桃源县;蝴蝶谷};
#NOP {黄河北};
#LIST {common[northland]} {create} {恒山;平定州;黑木崖;沧州城;塘沽城;神龙岛};
#NOP {西域};
#LIST {common[westland]} {create} {星宿海;回疆;昆仑山;逍遥派;天山;明教};
#NOP {江南};
#LIST {common[southland]} {create} {苏州城;杭州城;嘉兴城;牛家村;莆田少林;福州城;佛山镇;燕子坞;姑苏慕容;曼佗罗山庄;丐帮;归云庄;宁波城;桃花岛};
#NOP {==============================================区域划分资料==============================================结束};

#NOP {==============================================区域相关资料==============================================开始};
#NOP {坐船的房间};
#LIST {riverrooms} {create} {长江北岸;长江南岸;澜沧江边;西夏渡口;陕晋渡口;大渡口;岸边;码头;小岛边;听雨居;海滨;百丈涧;仙愁门;海滩;陆府正厅;水潭岸边;谷底;高山脚下;山顶;石梁尽头;渡口;日月坪;崖顶};
#NOP {渡船房间};
#LIST {boatrooms} {create} {长江渡船;黄河渡船;渡船;小木筏;小帆船;竹篓;小舟};
#NOP {各地区默认钱庄};
#VARIABLE {common[bank]} {
  {长安城} {
    {zone} {mainland}
    {room} {威信钱庄}
  }
  {成都城} {
    {zone} {mainland}
    {room} {墨玉斋}
  }
  {大理城} {
    {zone} {mainland}
    {room} {大理钱庄}
  }
  {襄阳城} {
    {zone} {mainland}
    {room} {宝龙斋}
  }
  {扬州城} {
    {zone} {mainland}
    {room} {天阁斋}
  }
  {沧州城} {
    {zone} {northland}
    {room} {天音阁}
  }
  {塘沽城} {
    {zone} {northland}
    {room} {钱庄}
  }
  {明教} {
    {zone} {westland}
    {room} {勒马斋}
  }
  {星宿海} {
    {zone} {westland}
    {room} {万宝斋}
  }
  {福州城} {
    {zone} {southland}
    {room} {通宝斋}
  }
  {杭州城} {
    {zone} {southland}
    {room} {金华斋}
  }
  {苏州城} {
    {zone} {southland}
    {room} {聚宝斋}
  }
};
#NOP {==============================================区域相关资料==============================================结束};

#NOP {==============================================官府地点资料==============================================开始};
#NOP {官府任务NPC地点都是特定房间，这里定义了各地点描述对应的地图房间编号};
#NOP {找到几率很低的房间排除少林寺塔林(2653)};
#NOP {缺峨嵋山小屋};
#VARIABLE {common[guanfuplaces]} {
  {武当山玄岳门} {{city}{武当山}{room}{2372}}
  {武当山小径} {{city}{武当山}{room}{2424}}
  {襄阳朱雀门} {{city}{襄阳城}{room} {645}}
  {襄阳汉水东岸} {{city}{襄阳城}{room}{761}}
  {嘉兴南湖} {{city}{嘉兴城}{room}{280}}
  {大雪山猴子洞} {{city}{大雪山}{room}{1800}}
  {苏州灵岩寺} {{city}{苏州城}{room}{2763}}
  {苏州留园} {{city}{苏州城}{room}{306}}
  {嘉兴铁枪庙} {{city}{嘉兴城}{room}{278}}
  {杭州龙井} {{city}{杭州城}{room}{199}}
  {杭州黄龙洞} {{city}{杭州城}{room}{243}}
  {杭州玉皇山} {{city}{杭州城}{room}{150}}
  {杭州鸿昌客栈} {{city}{杭州城}{room}{190}}
  {杭州长廊} {{city}{杭州城}{room}{246}}
  {杭州玉泉} {{city}{杭州城}{room}{235}}
  {丐帮分舵杏子林} {{city}{丐帮}{room}{3614}}
  {苗疆小镇} {{city}{苗疆}{room}{734}}
  {佛山英雄会馆} {{city}{佛山镇}{room}{45}}
  {峨嵋山观音桥} {{city}{峨嵋山}{room}{1343}}
  {峨嵋山八十四盘} {{city}{峨嵋山}{room}{1378}}
  {铁掌山山门} {{city}{铁掌山}{room}{670}}
  {铁掌山荒草路} {{city}{铁掌山}{room}{710}}
  {大雪山遮雨廊} {{city}{大雪山}{room}{1808}}
  {大雪山雪谷} {{city}{大雪山}{room}{1808}}
  {峨嵋山草棚} {{city}{峨嵋山}{room}{1331}}
  {峨嵋山古德林} {{city}{峨嵋山}{room}{3622}}
  {明教碧水寒潭} {{city}{明教}{room}{2904}}
  {明教山亭} {{city}{明教}{room}{1625}}
  {福州海港} {{city}{福州城}{room}{103}}
  {福州向阳老宅} {{city}{福州城}{room}{102}}
  {华山村菜地} {{city}{华山村}{room}{2284}}
  {少林寺松树林} {{city}{嵩山少林}{room}{2562}}
  {襄阳檀溪} {{city}{襄阳城}{room} {627}}
  {华山猢狲愁} {{city}{华山}{room} {2242}}
  {华山玉女峰} {{city}{华山}{room} {2247}}
  {苏州石湖} {{city}{苏州城}{room}{300}}
  {苏州玄妙观} {{city}{苏州城}{room}{344}}
  {苏州紫金庵} {{city}{苏州城}{room}{367}}
  {恒山翠屏山道} {{city}{恒山}{room}{2077}}
  {恒山果老岭} {{city}{恒山}{room}{2087}}
  {少林寺山路} {{city}{嵩山少林}{room}{2624}}
  {大雪山雪积古道} {{city}{大雪山}{room}{1799}}
  {佛山林间道} {{city}{佛山镇}{room}{34}}
  {佛山小路} {{city}{佛山镇}{room}{62}}
  {峨嵋山九老洞} {{city}{峨嵋山}{room}{1364}}
  {恒山北岳庙} {{city}{恒山}{room}{2088}}
  {扬州山冈} {{city}{扬州城}{room}{486}}
  {福州城中心} {{city}{福州城}{room}{95}}
  {黄河流域黄河岸边} {{city}{黄河流域}{room}{791}}
  {沧州东街} {{city}{沧州城}{room}{1996}}
  {塘沽中心广场} {{city}{塘沽城}{room}{2016}}
  {莆田少林小路} {{city}{莆田少林}{room}{125}}
  {青石大道} {{city}{天山}{room}{1513}}
  {山径} {{city}{天山}{room}{1501}}
  {黑木崖长廊} {{city}{黑木崖}{room}{2128}}
  {黑木崖桃花川} {{city}{黑木崖}{room}{2107}}
  {无量山大松林} {{city}{无量山}{room}{3607}}
  {星宿海大沙漠} {{city}{星宿海}{room}{2906}}
  {兰州沙漠} {{city}{兰州城}{room}{1401}}
};
#NOP {==============================================官府地点资料==============================================结束};

#NOP {==============================================少林教和尚任务资料==============================================开始};
#VARIABLE {dashiwheres} {2508;2509;2527;2524;2523;2511;2526;2525;2528;2510;2511;2512;2513;2515;2516;2517;2518;2519;2520;2521;2522;2523;2514;3695;3696;3697;3698;3699};
#VARIABLE {monkwheres} {2514;3695;3696;3697;3698;3699}
#VARIABLE {motouwheres} {
  {法堂} {2606}
  {石板路} {2603}
  {戒坛} {2604}
  {林中小路} {2591;2599}
  {方丈室} {2596}
  {白衣殿} {2614}
  {地藏殿} {2613}
  {菩提院} {3283}
  {佛塔三层} {2668}
  {钟楼七层} {2483}
  {鼓楼七层} {2472}
  {青云坪} {3689}
  {侧廊} {2529;2557}
  {寺前广场} {2458;2459}
  {石坊} {2456}
  {佛心井} {2453}
  {达摩洞} {2641}
  {罗汉堂} {2508}
  {般若堂} {2535}
  {山门殿} {2460}
  {后殿} {2586}
  {证道院} {2503}
  {平台} {2567}
  {药王院} {2564}
  {须弥殿} {2584}
  {六祖殿} {2570}
  {紧那罗王殿} {2568}
  {立雪亭} {2589}
  {千佛殿} {2615}
  {初祖庵} {2620}
  {心禅坪} {2610}
  {藏经阁一楼} {3117}
};
#NOP {==============================================少林教和尚任务资料==============================================结束};

#NOP {==============================================少林教护送任务资料==============================================结束};
#VARIABLE {fangzhengwheres} {
  {罗汉堂} {2508}
  {般若堂} {2535}
  {山门殿} {2460}
  {钟楼} {2483}
  {鼓楼} {2472}
  {后殿} {2586}
  {证道院} {2503}
  {平台} {2567}
  {菩提院} {3283}
  {药王院} {2564}
  {须弥殿} {2584}
  {六祖殿} {2570}
  {紧那罗王殿} {2568}
  {立雪亭} {2589}
  {千佛殿} {2615}
  {初祖庵} {2620}
  {白衣殿} {2614}
  {地藏殿} {2613}
  {心禅坪} {2610}
};
#NOP {==============================================少林护送任务资料==============================================结束};

#NOP {==============================================迷宫任务资料==============================================结束};
#VARIABLE {common[wanderwheres]} {
  {星宿海大沙漠} {
    {roomname} {大沙漠}
    {roomid} {2906}
    {range} {10}
  }
  {回疆针叶林} {
    {roomname} {针叶林}
    {roomid} {3329}
    {range} {10}
  }
  {绝情谷竹林} {
    {roomname} {竹林}
    {roomid} {3468}
    {range} {20}
  }
  {襄阳城树林} {
    {roomname} {树林}
    {roomid} {3469}
    {range} {20}
  }
  {明教紫杉林} {
    {roomname} {紫杉林}
    {roomid} {1676}
    {range} {10}
  }
};
#NOP {==============================================迷宫任务资料==============================================结束};

#NOP {==============================================特定条件下无法到达或者不能去的房间==============================================结束};
#NOP {蝴蝶谷花圃到不了，会直接穿过};
#LIST {unreachrooms[明教]} {create} {3449};
#NOP {武当派弟子小径无法到达，后山小院也不去了};
#LIST {unreachrooms[武当派]} {create} {2432;3905;3906;3907;2434};
#NOP {会玄铁剑的无法去襄阳树林};
#LIST {unreachrooms[xuantie-jianfa]} {create} {3469;3912;3913;3914;3915;3916;3917;3918;3919;3920};
#NOP {==============================================部分门派无法到达的房间==============================================结束};

#NOP {==============================================门派休息房间资料==============================================开始};
#NOP {门派休息房间,ops指师傅所在房间无法打坐时可以打坐的房间的偏移路径};
#NOP {在新增门派向导配置时，里面的师傅资料必须在此处配置其休息房间，否则学习时无法睡觉};
#VARIABLE {common[bedroom]} {
  {陆冠英} {
    {m}{{city}{归云庄}{room}{客房}{roomid}{2797}}
    {f}{{city}{归云庄}{room}{客房}{roomid}{2797}}
  }
  {陆乘风} {
    {m}{{city}{归云庄}{room}{客房}{roomid}{2797}}
    {f}{{city}{归云庄}{room}{客房}{roomid}{2797}}
  }
  {裘千丈} {
    {m}{{city}{铁掌山}{room}{男休息室}{roomid}{3515}}
    {f}{{city}{铁掌山}{room}{女休息室}{roomid}{689}}
  }
  {张浩天} {
    {m}{{city}{铁掌山}{room}{男休息室}{roomid}{3515}}
    {f}{{city}{铁掌山}{room}{女休息室}{roomid}{689}}
  }
  {裘千仞} {
    {m}{{city}{铁掌山}{room}{男休息室}{roomid}{3515}}
    {f}{{city}{铁掌山}{room}{女休息室}{roomid}{689}}
  }
  {岳不群} {
    {ops} {n}
    {m}{{city}{华山}{room}{男休息室}{roomid}{2271}}
    {f}{{city}{华山}{room}{女休息室}{roomid}{2266}}
  }
  {宁中则} {
    {ops} {n}
    {m}{{city}{华山}{room}{男休息室}{roomid}{2271}}
    {f}{{city}{华山}{room}{女休息室}{roomid}{2266}}
  }
  {风清扬} {
    {ops} {n}
    {m}{{city}{华山}{room}{男休息室}{roomid}{2271}}
    {f}{{city}{华山}{room}{女休息室}{roomid}{2266}}
  }
  {阿紫} {
    {m}{{city}{星宿海}{room}{逍遥洞}{roomid}{1483}}
    {f}{{city}{星宿海}{room}{逍遥洞}{roomid}{1483}}
  }
  {狮吼子} {
    {m}{{city}{星宿海}{room}{逍遥洞}{roomid}{1483}}
    {f}{{city}{星宿海}{room}{逍遥洞}{roomid}{1483}}
  }
  {天狼子} {
    {m}{{city}{星宿海}{room}{逍遥洞}{roomid}{1483}}
    {f}{{city}{星宿海}{room}{逍遥洞}{roomid}{1483}}
  }
  {摘星子} {
    {m}{{city}{星宿海}{room}{逍遥洞}{roomid}{1483}}
    {f}{{city}{星宿海}{room}{逍遥洞}{roomid}{1483}}
  }
  {丁春秋} {
    {m}{{city}{星宿海}{room}{逍遥洞}{roomid}{1483}}
    {f}{{city}{星宿海}{room}{逍遥洞}{roomid}{1483}}
  }
  {黄药师} {
    {m}{{city}{桃花岛}{room}{客房}{roomid}{2848}}
    {f}{{city}{桃花岛}{room}{客房}{roomid}{2848}}
  }
  {自学} {
    {m}{{city}{终南山}{room}{休息室}{roomid}{3163}}
    {f}{{city}{终南山}{room}{休息室}{roomid}{3163}}
  }
  {杨过} {
    {m}{{city}{绝情谷}{room}{卧室}{roomid}{3325}}
    {f}{{city}{绝情谷}{room}{卧室}{roomid}{3325}}
  }
  {小龙女} {
    {m}{{city}{绝情谷}{room}{卧室}{roomid}{3325}}
    {f}{{city}{绝情谷}{room}{卧室}{roomid}{3325}}
  }
  {谷虚道长} {
    {m}{{city}{武当山}{room}{男休息室}{roomid}{2427}}
    {f}{{city}{绝情谷}{room}{女休息室}{roomid}{2429}}
  }
  {俞岱岩} {
    {m}{{city}{武当山}{room}{男休息室}{roomid}{2427}}
    {f}{{city}{绝情谷}{room}{女休息室}{roomid}{2429}}
  }
  {宋远桥} {
    {ops} {out}
    {m}{{city}{武当山}{room}{男休息室}{roomid}{2427}}
    {f}{{city}{绝情谷}{room}{女休息室}{roomid}{2429}}
  }
  {张三丰} {
    {m}{{city}{武当山}{room}{男休息室}{roomid}{2427}}
    {f}{{city}{绝情谷}{room}{女休息室}{roomid}{2429}}
  }
  {高则成} {
    {m}{{city}{昆仑山}{room}{休息室}{roomid}{3555}}
    {f}{{city}{昆仑山}{room}{休息室}{roomid}{3555}}
  }
  {西华子} {
    {m}{{city}{昆仑山}{room}{休息室}{roomid}{3555}}
    {f}{{city}{昆仑山}{room}{休息室}{roomid}{3555}}
  }
  {玉灵子} {
    {m}{{city}{昆仑山}{room}{休息室}{roomid}{3555}}
    {f}{{city}{昆仑山}{room}{休息室}{roomid}{3555}}
  }
  {何太冲} {
    {m}{{city}{昆仑山}{room}{休息室}{roomid}{3555}}
    {f}{{city}{昆仑山}{room}{休息室}{roomid}{3555}}
  }
  {班淑娴} {
    {m}{{city}{昆仑山}{room}{休息室}{roomid}{3555}}
    {f}{{city}{昆仑山}{room}{休息室}{roomid}{3555}}
  }
  {何足道} {
    {m}{{city}{昆仑山}{room}{休息室}{roomid}{3555}}
    {f}{{city}{昆仑山}{room}{休息室}{roomid}{3555}}
  }
  {尹志平} {
    {m}{{city}{全真教}{room}{休息室}{roomid}{2337}}
    {f}{{city}{全真教}{room}{休息室}{roomid}{2337}}
  }
  {程瑶迦} {
    {m}{{city}{全真教}{room}{休息室}{roomid}{2337}}
    {f}{{city}{全真教}{room}{休息室}{roomid}{2337}}
  }
  {丘处机} {
    {m}{{city}{全真教}{room}{休息室}{roomid}{2337}}
    {f}{{city}{全真教}{room}{休息室}{roomid}{2337}}
  }
  {孙不二} {
    {m}{{city}{全真教}{room}{休息室}{roomid}{2337}}
    {f}{{city}{全真教}{room}{休息室}{roomid}{2337}}
  }
  {包不同} {
    {m}{{city}{姑苏慕容}{room}{厢房}{roomid}{1963}}
    {f}{{city}{姑苏慕容}{room}{厢房}{roomid}{1963}}
  }
  {公冶乾} {
    {m}{{city}{燕子坞}{room}{厢房}{roomid}{1877}}
    {f}{{city}{燕子坞}{room}{厢房}{roomid}{1877}}
  }
  {慕容复} {
    {m}{{city}{燕子坞}{room}{厢房}{roomid}{1877}}
    {f}{{city}{燕子坞}{room}{厢房}{roomid}{1877}}
  }
  {清法比丘} {
    {m}{{city}{嵩山少林}{room}{僧舍}{roomid}{2531}}
    {f}{{city}{嵩山少林}{room}{僧舍}{roomid}{2531}}
  }
  {清为比丘} {
    {m}{{city}{嵩山少林}{room}{僧舍}{roomid}{2531}}
    {f}{{city}{嵩山少林}{room}{僧舍}{roomid}{2531}}
  }
  {道正禅师} {
    {m}{{city}{嵩山少林}{room}{僧舍}{roomid}{2531}}
    {f}{{city}{嵩山少林}{room}{僧舍}{roomid}{2531}}
  }
  {慧修尊者} {
    {m}{{city}{嵩山少林}{room}{僧舍}{roomid}{2531}}
    {f}{{city}{嵩山少林}{room}{僧舍}{roomid}{2531}}
  }
  {澄知} {
    {m}{{city}{嵩山少林}{room}{僧舍}{roomid}{2531}}
    {f}{{city}{嵩山少林}{room}{僧舍}{roomid}{2531}}
  }
  {玄苦大师} {
    {m}{{city}{嵩山少林}{room}{僧舍}{roomid}{2531}}
    {f}{{city}{嵩山少林}{room}{僧舍}{roomid}{2531}}
  }
  {玄难大师} {
    {m}{{city}{嵩山少林}{room}{僧舍}{roomid}{2531}}
    {f}{{city}{嵩山少林}{room}{僧舍}{roomid}{2531}}
  }
  {玄慈大师} {
    {m}{{city}{嵩山少林}{room}{僧舍}{roomid}{2531}}
    {f}{{city}{嵩山少林}{room}{僧舍}{roomid}{2531}}
  }
  {渡难} {
    {m}{{city}{嵩山少林}{room}{僧舍}{roomid}{2531}}
    {f}{{city}{嵩山少林}{room}{僧舍}{roomid}{2531}}
  }
  {渡厄} {
    {m}{{city}{嵩山少林}{room}{僧舍}{roomid}{2531}}
    {f}{{city}{嵩山少林}{room}{僧舍}{roomid}{2531}}
  }
  {渡劫} {
    {m}{{city}{嵩山少林}{room}{僧舍}{roomid}{2531}}
    {f}{{city}{嵩山少林}{room}{僧舍}{roomid}{2531}}
  }
  {无名老僧} {
    {m}{{city}{嵩山少林}{room}{僧舍}{roomid}{2531}}
    {f}{{city}{嵩山少林}{room}{僧舍}{roomid}{2531}}
  }
  {殷无福} {
    {m}{{city}{明教}{room}{休息室}{roomid}{1654}}
    {f}{{city}{明教}{room}{休息室}{roomid}{1654}}
  }
  {周颠} {
    {m}{{city}{明教}{room}{休息室}{roomid}{1654}}
    {f}{{city}{明教}{room}{休息室}{roomid}{1654}}
  }
  {冷谦} {
    {m}{{city}{明教}{room}{休息室}{roomid}{1654}}
    {f}{{city}{明教}{room}{休息室}{roomid}{1654}}
  }
  {殷天正} {
    {m}{{city}{明教}{room}{休息室}{roomid}{1654}}
    {f}{{city}{明教}{room}{休息室}{roomid}{1654}}
  }
  {杨逍} {
    {m}{{city}{明教}{room}{休息室}{roomid}{1654}}
    {f}{{city}{明教}{room}{休息室}{roomid}{1654}}
  }
  {范瑶} {
    {m}{{city}{明教}{room}{休息室}{roomid}{1654}}
    {f}{{city}{明教}{room}{休息室}{roomid}{1654}}
  }
  {张无忌} {
    {m}{{city}{明教}{room}{休息室}{roomid}{1654}}
    {f}{{city}{明教}{room}{休息室}{roomid}{1654}}
  }
  {卢大哥} {
    {m}{{city}{平定州}{room}{客店二楼}{roomid}{2056}}
    {f}{{city}{平定州}{room}{客店二楼}{roomid}{2056}}
  }
  {丁坚} {
    {m}{{city}{梅庄}{room}{厢房}{roomid}{3804}}
    {f}{{city}{梅庄}{room}{厢房}{roomid}{3804}}
  }
  {施令威} {
    {m}{{city}{梅庄}{room}{厢房}{roomid}{3804}}
    {f}{{city}{梅庄}{room}{厢房}{roomid}{3804}}
  }
  {丹青生} {
    {m}{{city}{梅庄}{room}{厢房}{roomid}{3804}}
    {f}{{city}{梅庄}{room}{厢房}{roomid}{3804}}
  }
  {黑白子} {
    {m}{{city}{梅庄}{room}{厢房}{roomid}{3804}}
    {f}{{city}{梅庄}{room}{厢房}{roomid}{3804}}
  }
  {秃笔翁} {
    {m}{{city}{梅庄}{room}{厢房}{roomid}{3804}}
    {f}{{city}{梅庄}{room}{厢房}{roomid}{3804}}
  }
  {黄钟公} {
    {m}{{city}{梅庄}{room}{厢房}{roomid}{3804}}
    {f}{{city}{梅庄}{room}{厢房}{roomid}{3804}}
  }
  {鲍大楚} {
    {m}{{city}{黑木崖}{room}{厢房}{roomid}{2125}}
    {f}{{city}{黑木崖}{room}{休息室}{roomid}{2142}}
  }
  {向问天} {
    {m}{{city}{黑木崖}{room}{厢房}{roomid}{2125}}
    {f}{{city}{黑木崖}{room}{休息室}{roomid}{2142}}
  }
  {任我行} {
    {m}{{city}{黑木崖}{room}{厢房}{roomid}{2125}}
    {f}{{city}{黑木崖}{room}{休息室}{roomid}{2142}}
  }
  {摩诃巴思} {
    {m}{{city}{大雪山}{room}{清心舍}{roomid}{1831}}
    {f}{{city}{大雪山}{room}{绝尘院}{roomid}{1829}}
  }
  {呼巴音} {
    {m}{{city}{大雪山}{room}{清心舍}{roomid}{1831}}
    {f}{{city}{大雪山}{room}{绝尘院}{roomid}{1829}}
  }
  {灵智上人} {
    {m}{{city}{大雪山}{room}{清心舍}{roomid}{1831}}
    {f}{{city}{大雪山}{room}{绝尘院}{roomid}{1829}}
  }
  {善勇} {
    {m}{{city}{大雪山}{room}{清心舍}{roomid}{1831}}
    {f}{{city}{大雪山}{room}{绝尘院}{roomid}{1829}}
  }
  {胜谛} {
    {m}{{city}{大雪山}{room}{清心舍}{roomid}{1831}}
    {f}{{city}{大雪山}{room}{绝尘院}{roomid}{1829}}
  }
  {鸠摩智} {
    {m}{{city}{大雪山}{room}{清心舍}{roomid}{1831}}
    {f}{{city}{大雪山}{room}{绝尘院}{roomid}{1829}}
  }
  {金轮法王} {
    {m}{{city}{星宿海}{room}{逍遥洞}{roomid}{1483}}
    {f}{{city}{星宿海}{room}{逍遥洞}{roomid}{1483}}
  }
  {血刀老祖} {
    {m}{{city}{大雪山}{room}{清心舍}{roomid}{1831}}
    {f}{{city}{大雪山}{room}{绝尘院}{roomid}{1829}}
  }
};
#NOP {==============================================门派休息房间资料==============================================结束};
#NOP {==============================================区域相关资料==============================================结束};

#NOP {==============================================武器相关资料==============================================开始};
#NOP {基础武器表，运行时填充};
#VARIABLE {common[weaponmapping]} {};

#NOP {扬州铁匠铺兵器};
#VARIABLE {common[baseweapon]} {
  {changjian}{sword}
  {blade} {blade}
  {liuxing chui} {hammer}
  {changbian} {whip}
  {tiegun} {club}
  {gangzhang} {staff}
  {hook} {hook}
  {zhubang} {stick}
  {gang fu} {axe}
  {ansha bishou} {dagger}
};

#NOP {襄阳铁匠铺兵器};
#VARIABLE {common[normalweapon]} {
  {youlong bian} {whip}
  {zhu bang} {stick} 
  {bishou} {dagger}
  {kulou chui} {hammer}
  {tiegun} {club}
  {dafu} {axe}
  {falun} {hammer}
  {hongying qiang} {spear}
  {hanyu gou} {hook}
  {xue sui} {blade}
  {panguan bi} {brush}
  {yinshe sword} {sword}
  {lanyu duzhen} {sword}
  {tianshe zhang} {staff}
  {zhen} {throwing}
}

#NOP {宝物武器配置};
#VARIABLE {common[uniqueweapon]} {
  {yitian jian} {sword}
  {tulong dao} {blade}
  {xuantie jian} {sword}
  {songwen jian} {sword}
};

#NOP {携带药物数量,大理成都药铺};
#VARIABLE {common[medicine]} {
  {chantui yao} {2}
  {huoxue dan} {2}
  {chuanbei wan} {2}
};
#NOP {==============================================武器相关资料==============================================结束};

#NOP {==============================================技能相关资料==============================================开始};
#NOP {空手基础技能};
#LIST {common[fist]} {create} {cuff;strike;finger;hand;claw;leg};

#NOP {对神有要求的功夫，应用于获取可练习的武功方法};
#VARIABLE {common[shenrequires]} {
  {xiantian-gong} {20000}
  {yinyun-ziqi} {20000}
  {huashan-qigong} {20000}
  {zixia-gong} {20000}
  {qiantian-yiyang} {20000}
  {taiji-shengong} {20000}
  {emei-jiuyang} {20000}
};
#NOP {能否增加悟性的内功极其指令，应用于角色配置conf[wuxingforce]时启用的指令};
#VARIABLE {common[wuxingforce]} {
  {yunu-xinjing} {xinjing}
  {beiming-shengong} {beiming}
  {bihai-chaosheng} {qimen}
  {linji-zhuang} {zhixin}
};
#NOP {学习或练习依赖内功激发的武功，应用于练习时自动切换内功};
#VARIABLE {common[dependencies]} {
  {xuanyin-jian} {jiuyin-zhengong}
  {dafumo-quan} {jiuyin-zhengong}
  {yuxiao-jian} {bihai-chaosheng}
};

#NOP {排除特定技能的可激发的基础技能，应用于练习时选择的基础技能。主要用于解决类似冰蚕毒掌可激发force的类似问题};
#VARIABLE {common[baseexcludes]} {
  {bingcan-duzhang} {force}
  {shenghuo-lingfa} {cuff}
  {pixie-jian} {dodge}
  {yuenu-jian} {dodge}
};

#NOP {特定只能激发parry的技能};
#VARIABLE {common[parryskills]} {
  {douzhuan-xingyi} {parry}
  {qiankun-danuoyi} {parry}
};

#NOP {武功练习所需耗费，防止部分武功导致waitlian时内力没了};
#VARIABLE {common[practicecost]} {
  {hujia-daofa} {60}
  {miaojia-jianfa} {60}
  {hamagong} {150}
  {jiuyin-zhengong} {150}
  {yuanshi-jianfa} {100}
  {guzhuo-zhangfa} {100}
};
#NOP {==============================================技能相关资料==============================================结束};

#NOP {==============================================运行时环境变量==============================================开始};
#NOP {任务内力打坐阈值};
#VARIABLE {threshold_neili} {0}

#NOP {最佳的打坐点数};
#VARIABLE {dazuo_point} {0};

#NOP {最佳的吐纳点数};
#VARIABLE {tuna_point} {0};

#VARIABLE {heal_point} {0};

#NOP {正在忙碌禁止判定idle，在发呆阈值设置较小时坐船、疗伤或者打坐时可能会触发发呆机制};
#NOP {在上述动作时该变量会置为1，操作结束时复位，具体参考walk.tin里面的idleticker};
#VARIABLE {workingflag} {0};

#NOP {是否使用ado合并指令快速行走,当前仅紫檀站有效,启用ado时下面的max_walk_steps可以设置的大一点};
#NOP {书剑情怀站暂时不能用ado，所以默认0，不要修改这个设置}
#VARIABLE {ado} {0};

#NOP {是否服务器反馈指令,而不是通过设置action环境变量};
#NOP {情怀站没有srvecho这个命令，所以默认0，不要修改这个设置}
#VARIABLE {srvecho} {0};

#NOP {登录时间};
#VARIABLE {env[logintime]} {0};

#NOP {少林大阵标识，仅在165K一下经验有效，用于解决无人值守拜师过阵流程};
#VARIABLE {env[shaolin]} {};

#NOP {送信是否加入官府};
#VARIABLE {env[guanfu]} {0};

#NOP {送信是否加入皇宫};
#VARIABLE {env[huanggong]} {0};

#NOP {加入五毒教标识，空:未加入五毒教，RSP:已吃到真药未还蜘蛛，YES:已吃到真药且已还蜘蛛，具体参考common.tin的joinwdj};
#VARIABLE {env[wudujiao]} {};

#NOP {加入五毒教的时间戳，已经吃了真药的情况下不会等待雪珠，过了20分钟后会重新尝试拿雪珠，如果没吃真药会一直等待拿到雪珠再拿真药};
#VARIABLE {env[wdjrsp]} {0};

#NOP {过月时间戳，没啥实际用途};
#VARIABLE {env[month]} {0};

#NOP {吃珍珠的标识};
#VARIABLE {env[pray]} {0};

#NOP {开始鬼谷的标识};
#VARIABLE {env[guigu]} {0};

#NOP {登入模式,0:正常,1:重连};
#VARIABLE {env[loginmode]} {0};

#NOP {重新连接请求，可通过输入reboot指令设置该值为1，角色完成任务后会重新加载所有模块};
#VARIABLE {env[reboot]} {0};

#NOP {断线重连释放内存，可通过输入restart指令设置该值为1，角色完成任务后会重新加载所有模块};
#VARIABLE {env[restart]} {0};

#NOP {衣服没了，会去襄阳成衣铺买衣服};
#VARIABLE {env[naked]} {0};

#NOP {站点周五凌晨重启倒数计时，一分钟时所有在线角色会去杂货铺保存物品等待重启};
#VARIABLE {env[countdown]} {0};

#NOP {主动退出登录时需要进行的操作，主要应用于读medicine拿本草纲目和本草集注};
#VARIABLE {env[logindo]} {};

#NOP {官府任务刷新标识};
#VARIABLE {env[wanted]} {0};

#NOP {悟性buff状态};
#VARIABLE {env[buff]} {0};

#NOP {wimpy标识，应用于closewimpy和openwimpy，尽量减少环境变量的保存};
#VARIABLE {env[wimpy]} {0};

#NOP {积蓄标识，应用于closesaving和opensaving，尽量减少环境变量的保存};
#VARIABLE {env[积蓄]} {0};

#NOP {少林和尚有罪标识};
#VARIABLE {env[guilty]} {0}

#NOP {仓库没有tianqi了，不再尝试保持身上的tianqi数量};
#VARIABLE {env[oot]} {0}

#NOP {银行没潜能了};
#VARIABLE {env[oop]} {0}

#NOP {天龙八部大辽救援切口};
#VARIABLE {env[qiekou]} {}

#NOP {powerup状态};
#VARIABLE {env[powerup]} {};

#NOP {buff时间戳};
#VARIABLE {env[buffts]} {0};

#NOP {辟邪剑密码};
#VARIABLE {env[pxjpwd]} {};

#NOP {独孤九剑提示};
#VARIABLE {env[dgjj]} {};

#NOP {九剑无招问风};
#VARIABLE {env[askfeng]} {0};

#NOP {吸星大法提示};
#VARIABLE {env[xxdf]} {};

#NOP {是否进过梅庄，本次登录期间如解过xxdf则只需带着令旗进们要拿钥匙即可};
#VARIABLE {env[meizhuang]} {};

#NOP {九阳神功提示};
#VARIABLE {env[jysg]} {};

#NOP {无相劫指提示};
#VARIABLE {env[wxjz]} {};

#NOP {倚天屠龙功};
#VARIABLE {env[yttl]} {};

#NOP {古墓石刻问杨过};
#VARIABLE {env[askyang]} {0};

#NOP {少林护送时间戳，大约20分钟冷却};
#VARIABLE {env[husong]} {0};

#NOP {打坐用时};
#VARIABLE {env[dzduration]} {10};

#NOP {是否充值会员};
#VARIABLE {env[vip]} {0};

#NOP {古墓寒冰床ts};
#VARIABLE {env[gmbed]} {@now{}};

#NOP {大还丹使用次数};
#VARIABLE {env[dhdbox]} {10};

#NOP {统计用数据}
#VARIABLE {env[statistic]} {};

#NOP {武器获得时间};
#VARIABLE {env[weapongs]} {};

#NOP {当前主武器};
#VARIABLE {env[currentpfm]} {};

#NOP {平一指杀人标志};
#VARIABLE {env[pingkilled]} {0};
#NOP {平一指最后询问时间};
#VARIABLE {env[pingkillts]} {0};

#NOP {玄铁剑树林标识};
#VARIABLE {env[yangpass]} {0};
#NOP {武器使用时间};

#NOP {鬼谷开启时间};
#VARIABLE {env[guiguts]} {0};

#NOP {查老先生询问时间戳};
#VARIABLE {env[zhasirts]} {0};

#NOP {拿九阴书的时间戳};
#VARIABLE {env[jybookts]} {0};

#NOP {echo反馈的内容};
#VARIABLE {env[echo]} {};

#NOP {杂货铺最后盘点的时间，初始化是0，我这里改成2038年屏蔽掉盘点};
#VARIABLE {env[pdts]} {2147483647};
#NOP {==============================================运行时环境变量==============================================结束};

#NOP {==============================================系统指令==============================================开始};
#NOP {重新加载模块,%1:模块路径，手动修改了模块内容后可以通过该指令重新刷新模块};
#ALIAS {reload} {
  #IF {"%1" == ""} {
    #SHOWME {<faa>missing module name};
  };
  #ELSE {
    import {%1};
    #SWITCH {"%1"} {
      #CASE {"common"} {
        i;
        checkenv;
        checkalias;
      };
      #CASE {"quest"} {
        checkenv;
        checkalias;
      };
      #CASE {"kungfu"} {
        prepareskills
      };
    };
  };
};
#NOP {重新加载地图，修改地图后通过该指令重新加载};
#ALIAS {loadmap} {
  #MAP READ ./shujian/modules/sjmap.tin;
  reload map;
  setuppersonalmap
};
#NOP {记录日志,%1:要记录的日志,%2:日志模板,%3:颜色};
#ALIAS {log} {
  #FORMAT ts %t {%Y-%m-%d %T};
  #LOCAL {logcolor} {<fff>};
  #IF {"%3" != ""} {
    #LOCAL {logcolor} {<%3>};
  };
  #LINE LOG {./shujian/log/%2_$hp[id].html} {$logcolor$ts %1}
};
#NOP {任务日志,%1:日志内容,%2:指定任务,%3:指定颜色};
#ALIAS {joblog} {
  #VARIABLE {prefix} {【@padRight{{$currentjob}{8}}】};
  #IF {"%2" != ""} {
    #VARIABLE {prefix} {【@padRight{{%2}{8}}】};
  };
  #FORMAT ds %t {%Y%m%d};
  log {$prefix: %1} {jobtimes_$ds} {%3}
};
#NOP {谣言日志，%1:日志内容};
#ALIAS {deathlog} {
  #FORMAT prefix %t {%Y%m};
  #FORMAT ts %t {%Y-%m-%d %T};
  #LOCAL {content} {<fbf>$ts %1};
  #LINE LOG {./shujian/log/death_$prefix.html} {$content}
};
#NOP {治疗记录,%1:日志内容};
#ALIAS {curelog} {
  #VARIABLE {prefix} {【门诊】};
  #FORMAT ds %t {%Y%m%d};
  log {$prefix: %1} {cure_$ds}
};
#NOP {%1:解谜名称,%2:日志内容};
#ALIAS {questlog} {
  #VARIABLE {prefix} {【%1】};
  #FORMAT ds %t {%Y%m};
  log {$prefix: %2} {quest_$ds}
};
#NOP {服务日志,%1:日志内容};
#ALIAS {servicelog} {
  #FORMAT ds %t {%Y%m};
  log {%1} {service_$ds}
};
#NOP {缓冲区快照,%1名称};
#ALIAS {logbuff} {
  #FORMAT ts %t {%Y%m%d%H%M%S};
  #IF {"%1" == ""} {
    dologbuff {log} {$ts}
  };
  #ELSE {
    dologbuff {%1} {$ts}
  };
};
#NOP {%1:名称,%2:时间戳};
#ALIAS {dologbuff} {
  #BUFFER WRITE ./shujian/log/%1_%2_$hp[id].html
};
#ALIAS snapshot {
  #format date %t %F;
}
#NOP {切换回话,%1:会话id,不填则依次切换，应用于单窗口多会话情况};
#ALIAS {tss} {
  #IF {"%1" == ""} {
    #SESSION +;
  };
  #ELSE {
    #SESSION %1;
  };
};
#NOP {创建会话(适用于linux),%1:名称,%2:角色文件,角色文件路径为./shujian/chars/conf/xxx.tin};
#ALIAS {css} {
  #IF {"%1" == ""} {
    #SHOWME {<faa>missing session name};
  };
  #ELSEIF {"%2" == ""} {
    #SHOWME {<faa>missing profile};
  };
  #ELSE {
    #SESSION %1 123.57.************ ./shujian/chars/conf/%2.tin;
  };
};

#NOP {通过telegram发送通知消息,%1:消息内容，仅适用于linux};
#ALIAS {notify} {
  #SYSTEM {shujian/tgsend %1};
};

#NOP {显示反馈信息,%1:反馈显示的信息,%2:重复执行的时间间隔};
#ALIAS {echo} {
  #IF {$interrupt == 1} {
    #VARIABLE {interrupt} {0};
  };
  #ELSE {
    #IF {"%2" != ""} {
      #VARIABLE {env[echo][%1][timestamp]} {@eval{@nowms{} + %2*1000}};
    };
    #IF {$srvecho == 1} {
      #SEND {srvback %1};
    };
    #ELSE {
      #SEND {set action %1};
    };
  };
};
#NOP {响应echo的反馈信息,%1:内容};
#ALIAS {resonate} {
  #IF {"%1" != ""} {
    #UNVARIABLE {env[echo][%1]};
  };
};
#NOP {有保障的执行指令并触发反馈信息,%1:要执行的指令,%2:要触发的反馈信息,%3:重试的时间,默认为2s};
#ALIAS {ensure} {
  #IF {"%2" != ""} {
    %1;
    #VARIABLE {env[echo][%2][cmds]} {%1};
    #LOCAL {ds} {%3};
    #IF {"$ds" == ""} {
      #LOCAL {ds} {2};
    };
    echo {%2} {$ds}
  };
};
#LIST {cmdqueue} {clear};
#VARIABLE {env[error]} {0};
#VARIABLE {env[errcount]} {0};
#NOP {添加指令至待执行序列,%1:要添加的指令,必须为mud服务器识别的指令};
#ALIAS {addcmd} {
  #LIST {cmdqueue} {add} {%1};
  #IF {&cmdqueue[] == 0} {
    #IF {$env[error] == 0} {
      #VARIABLE {env[error]} {1};
    };
  };
};
#NOP {执行指令,%1:要执行的指令，以;分隔，不要在参数中添加脚本表达式，如为空则执行默认的待执行序列};
#ALIAS {execute} {
  #IF {"%1" != ""} {
    #LIST {cmdqueue} {create} {%1};
    execute {};
  };
  #ELSEIF {&cmdqueue[] > 0} {
    #IF {$ado == 0} {
      #VARIABLE {executelist} {$cmdqueue};
      #LIST {cmdqueue} {clear};
      #LIST {executelist} {collapse} {;};
      $executelist;
    };
    #ELSE {
      #NOP {获取拆分点};
      #LOCAL {sp} {999};
      #LOCAL {p} {@contains{{cmdqueue}{wwp}}};
      #IF {$p > 0 && $p < $sp} {
        #LOCAL {sp} {$p};
      };
      #LOCAL {p} {@contains{{cmdqueue}{uwwp}}};
      #IF {$p > 0 && $p < $sp} {
        #LOCAL {sp} {$p};
      };
      #LOCAL {p} {@contains{{cmdqueue}{wcwp}}};
      #IF {$p > 0 && $p < $sp} {
        #LOCAL {sp} {$p};
      };
      #IF {$sp == 999} {
        executeado {cmdqueue};
        #LIST {cmdqueue} {clear};
      };
      #ELSEIF {$sp == 1} {
        #LOCAL {spcmd} {$cmdqueue[+1]};
        #LIST {cmdqueue} {delete} {1};
        $spcmd;
        execute;
      };
      #ELSE {
        #NOP {拆分执行前面};
        #LIST {executelist} {clear};
        #LOOP {1} {$sp - 1} {p} {
          #LIST {executelist} {add} {$cmdqueue[+1]};
          #LIST {cmdqueue} {delete} {1};
        };
        executeado {executelist};
        #NOP {执行特别指令};
        #LOCAL {spcmd} {$cmdqueue[+1]};
        #LIST {cmdqueue} {delete} {1};
        #VARIABLE {executelist} {$cmdqueue};
        #LIST {cmdqueue} {clear};
        $spcmd;
        #NOP {执行后面};
        #VARIABLE {cmdqueue} {$executelist};
        execute;
      };
    };
  };
};
#NOP {合并执行,%1:列表变量};
#ALIAS {executeado} {
  #LIST {adolist} {clear} {};
  #LOOP {1} {&%1[]} {i} {
    #REGEXP {$%1[+$i]} {^#%+1..2d%+1..2s%*} {
      #LOOP {1} {&1} {j} {
        #LIST {adolist} {add} {&3};
      };
    } {
      #LIST {adolist} {add} {$%1[+$i]};
    }
  };
  #IF {&adolist[] > 0} {
    #LIST {adolist} {collapse} {|};
    #SEND {ado $adolist};
  };
};
#NOP {执行指令并设置为目标指令，可有效降低发呆异常};
#ALIAS {executecmd} {
  #VARIABLE {aimdo} {%1};
  %1;
};

#NOP {打开wimpy,%1:强制打开};
#ALIAS {openwimpy} {
  #IF {$env[wimpy] == 0 || "%1" != ""} {
    set wimpy 100;
    #VARIABLE {env[wimpy]} {1};
  };
};

#NOP {关闭wimpy};
#ALIAS {closewimpy} {
  #IF {$env[wimpy] == 1} {
    unset wimpy;
    #VARIABLE {env[wimpy]} {0};
  };
};

#NOP {打开积蓄};
#ALIAS {opensaving} {
  #IF {$env[积蓄] == 0} {
    set 积蓄;
    #VARIABLE {env[积蓄]} {1};
  };
};

#NOP {关闭积蓄};
#ALIAS {closesaving} {
  #IF {$env[积蓄] == 1} {
    unset 积蓄;
    #VARIABLE {env[积蓄]} {0};
  };
};
#NOP {==============================================系统指令==============================================结束};

#NOP {==============================================通用工具指令==============================================开始};
#NOP {清除类};
#ALIAS {clk} {
  #CLASS %1 KILL;
};

#NOP {重连系统,需要等任务完成,%1:需要重新加载的模块};
#ALIAS {reboot} {
  #VARIABLE {env[reboot]} {1};
  #VARIABLE {env[invalidmodule]} {%1};
  #SHOWME {<faa>当前任务完成后将断线重连};
};

#NOP {重启系统，断线自动重连};
#ALIAS {restart} {
  #VARIABLE {env[restart]} {1};
  #SHOWME {<faa>当前任务完成后将断线重连};
};

#NOP {初始化环境变量};
#ALIAS {initenv} {
  #VARIABLE {env[guanfu]} {};
  #VARIABLE {env[huanggong]} {};
  #VARIABLE {env[wudujiao]} {};
  #VARIABLE {env[guigu]} {0};
  unset env_guigu;
  unset env_guanfu;
  unset env_huanggong;
  unset env_wudujiao;
  unset env_baozang;
  unset env_monkroomid;
  unset env_qiekou;
  unset env_powerup;
  unset env_dgjj;
  unset env_xxdf;
  unset env_meizhuang;
  unset env_wxjz;
  unset env_yttl;
  unset env_dazuopoint;
  unset env_tunapoint;
  unset env_healpoint;
  set env_logintime @now{}
};

#NOP {清除通用触发类};
#ALIAS {commonclear} {
  #CLASS envclass KILL;
  #CLASS commonclass KILL;
  #CLASS commonresponseclass KILL;
  #CLASS haltclass KILL;
  #CLASS prepareclass KILL;
  #CLASS weaponclass KILL;
  #CLASS joinclass KILL;
  #CLASS joinresponseclass KILL;
  #CLASS stockclass KILL;
  #CLASS dzclass KILL;
};

#NOP {设置下次登录动作,%1:动作,%2:参数1,%3:参数2,%3:参数3};
#ALIAS {setlogindo} {
  #send {alias logindo %1|%2|%3|%4};
};

#NOP {设置定时操作,%1:时间(多少秒),%2:要执行的指令};
#ALIAS {settimeout} {
  #VARIABLE {routine} {
    {timestamp} {@eval{@now{} + %1}}
    {action} {%2}
  };
  #UNTICKER {routineticker};
  #TICKER {routineticker} {
    #IF {@now{} > $routine[timestamp]} {
      doroutine;
    };
  } {10};
};

#NOP {设置经验操作,%1:增加多少经验,%2:要执行的指令};
#ALIAS {setexpout} {
  #VARIABLE {routine} {
    {exp} {@eval{$hp[exp] + %1}}
    {action} {%2}
  };
  #UNTICKER {routineticker};
  #TICKER {routineticker} {
    #IF {$hp[exp] > $routine[exp]} {
      doroutine;
    };
  } {10};
};

#NOP {技能等级大于指定值时触发操作,%1:技能名称,%2:等级,%3:要执行的指令};
#ALIAS {setskillout} {
};

#NOP {执行定时器操作,%1:后续指令};
#ALIAS {doroutine} {
  #UNTICKER {routineticker};
  #LOCAL {tempaction} {$routine[action]};
  #VARIABLE {routine} {}; 
  $tempaction
};

#NOP {自杀重生};
#ALIAS {reborn} {
  #IF {"$conf[newbie][party]" != ""} {
    suicide -f;
    $user_password
  };
};

#NOP {设置环境变量,%1:变量名称,%2:变量值};
#ALIAS {setenv} {
  #LOCAL {value} {YES};
  #IF {"%2" != ""} {
    #LOCAL {value} {%2};
  };
  #IF {"$env[%1]" != "$value"} {
    #VARIABLE {env[%1]} {$value};
    set %1 %2;
  };
};

#NOP {取消环境变量,%1:变量名称};
#ALIAS {unsetenv} {
  #IF {"$env[%1]" != ""} {
    #VARIABLE {env[%1]} {};
    unset %1;
  };
};

#NOP {检查环境状态，断线重连需要保持的游戏状态均可以通过这里加载};
#ALIAS {checkenv} {
  #CLASS envclass KILL;
  #CLASS envclass OPEN;
  #ACTION {^env_%*%!s\"{YES|RSP}\"} {
    #VARIABLE {env[%%1]} {%%2};
  } {1};
  #ACTION {^env_month%!s%u} {
    #VARIABLE {env[month]} {@trim{%%1}};
  };
  #ACTION {^env_pdts%!s%u} {
    #VARIABLE {env[pdts]} {@trim{%%1}};
  };
  #ACTION {^env_festival%!s%u} {
    #VARIABLE {env[festival]} {@trim{%%1}};
  };
  #ACTION {^env_baozang%!s\"%u\"} {
    #VARIABLE {env[baozang]} {@trim{%%1}};
  };
  #ACTION {^env_logintime%!s%u} {
    #VARIABLE {env[logintime]} {@trim{%%1}};
  };
  #ACTION {^env_monkroomid%!s%u} {
    #VARIABLE {env[monkroomid]} {@trim{%%1}};
  };
  #ACTION {^env_qiekou%!s\"%u\"} {
    #VARIABLE {env[qiekou]} {@trim{%%1}};
  };
  #ACTION {^玄铁剑法%!s\"%u\"} {
    #VARIABLE {env[玄铁剑法]} {%%1};
  };
  #ACTION {^env_point%!s\"%u\"} {
    #REGEXP {%%1} {dps%d} {#VARIABLE {dazuo_point} {&1}};
    #REGEXP {%%1} {tps%d} {#VARIABLE {tuna_point} {&1}};
    #REGEXP {%%1} {hps%d} {#VARIABLE {heal_point} {&1}};
  };
  #ACTION {^env_dazuopoint%!s%u} {
    #VARIABLE {dazuo_point} {@trim{%%1}};
  };
  #ACTION {^env_tunapoint%!s%u} {
    #VARIABLE {tuna_point} {@trim{%%1}};
  };
  #ACTION {^env_healpoint%!s%u} {
    #VARIABLE {heal_point} {@trim{%%1}};
  };
  #ACTION {^{设定环境变量：action \= \"checkenv\"|你设定checkenv为反馈信息}} {
    #CLASS envclass KILL;
  };
  #CLASS envclass CLOSE;
  set;
  echo {checkenv};
};

#NOP {检查系统alias状态，主要用于加载解谜状态，系统最多允许50个内置alias};
#ALIAS {checkalias} {
  #CLASS aliasclass KILL;
  #CLASS aliasclass OPEN;
  #ACTION {^quest_%*%!s= %*} {
    #IF {"%%2" == "YES"} {
      #VARIABLE {questlist[%%1][done]} {YES};
    };
    #ELSEIF {"%%2" == "FAIL"} {
      #VARIABLE {questlist[%%1][done]} {FAIL};
    };
    #ELSE {
      #REGEXP {%%2} {%*_%*_%*_%*} {
        #VARIABLE {questlist[%%1][lastexp]} {&1};
        #VARIABLE {questlist[%%1][lasttime]} {&2};
        #VARIABLE {questlist[%%1][laststep]} {&3};
        #VARIABLE {questlist[%%1][fail]} {&4};
      };
    };
  };
  #ACTION {^logindo%!s= %*} {
    #VARIABLE {env[logindo]} {%%1};
  };
  #ACTION {^{设定环境变量：action \= \"checkalias\"|你设定checkalias为反馈信息}} {
    #CLASS aliasclass KILL;
  };
  #CLASS aliasclass CLOSE;
  alias;
  echo {checkalias};
};

#NOP {受限区域是否允许去,必要条件,%1:区域};
#FUNCTION isZoneAllow {
  #IF {@contains{{conf[limitedzone]}{%1}} > 0} {
    #RETURN {0};
  };
  #IF {"$common[limitedzone][%1][exp]" == ""} {
    #RETURN {1};
  };
  #IF {$hp[exp] >= $common[limitedzone][%1][exp]} {
    #RETURN {1};
  };
  #RETURN {0};
};
#NOP {受限区域是否能去,充分条件,%1:区域};
#FUNCTION isZoneCan {
  #IF {@contains{{conf[limitedzone]}{%1}} > 0} {
    #RETURN {0};
  };
  #IF {@isZoneAllow{%1} == 0} {
    #RETURN {0};
  };
  #LOCAL {tempzone} {$common[limitedzone][%1]};
  #IF {"$tempzone" == ""} {
    #RETURN {1};
  };
  #IF {"%1" == "苗疆" && "$env[wudujiao]" != ""} {
    #RETURN {1};
  };
  #IF {"$tempzone[weight]" != "" && $id[weight] > $tempzone[weight]} {
    #RETURN {0};
  };
  #IF {"$tempzone[skill]" != ""} {
    #LOCAL {skok} {0};
    #FOREACH {*tempzone[skill][]} {sk} {
      #IF {@getSkillLevel{$sk} >= $tempzone[skill][$sk]} {
        #IF {("$sk" == "hamagong" || "$sk" == "yinyun-ziqi") && "$kungfu[base][force][jifa]" != "$sk"} {
          #CONTINUE;
        };
        #LOCAL {skok} {1};
        #BREAK;
      };
    };
    #IF {$skok == 0} {
      #RETURN {0};
    };
  };
  #RETURN {1};
};
#NOP {获取城市坐在大陆,%1-城市};
#FUNCTION getCityZone {
  #IF {@contains{{common[northland]}{%1}} > 0} {
    #RETURN {northland};
  };
  #ELSEIF {@contains{{common[westland]}{%1}} > 0} {
    #RETURN {westland};
  };
  #ELSEIF {@contains{{common[southland]}{%1}} > 0} {
    #RETURN {southland};
  };
  #ELSE {
    #RETURN {mainland};
  };
};
#NOP {获取本地区域};
#FUNCTION getLocalZone {
  #RETURN {@getCityZone{$city}};
};
#NOP {获取本地最近银行};
#FUNCTION getLocalBank {
  #LOCAL {zone} {@getLocalZone{}};
  #LOCAL {banks} {@filter{{common[bank]}{zone}{"&" == "$zone"}}};
  #IF {&banks[] == 0} {
    #RETURN {
      {city} {扬州城}
      {room} {天阁斋}
    };
  };
  #NOP {寻找本区域内最近的};
  #LOOP 1 &banks[] {i} {
    #LOCAL {banks[+$i][distance]} {@getCityPathLength{{$city}{*banks[+$i]}}};
  };
  #LOCAL {sortbanks} {@sort{{banks}{distance}}};
  #RETURN {
    {city} {$sortbanks[+1][sidx]}
    {room} {$sortbanks[+1][room]}
  };
};
#NOP {需要存钱时就近原则取钱庄，地图任务中心为襄阳城，这里默认选择襄阳城为最终目的地};
#FUNCTION getNearestBank {
  #NOP {寻找自当地至目的城市间路径上的最近的一个银行};
  #LOCAL {cities} {@getCityPath{{$city}{襄阳城}}};
  #IF {&cities[] == 0} {
    #RETURN {
      {city} {襄阳城}
      {room} {宝龙斋}
    };
  };
  #LOOP 1 &cities[] {i} {
    #IF {"$common[bank][$cities[+$i]]" != ""} {
      #RETURN {
        {city} {$cities[+$i]}
        {room} {$common[bank][$cities[+$i]][room]}
      };
    };
  };
  #RETURN {@getLocalBank{}};
};
#NOP {准备基本信息};
#ALIAS {preparestart} {
  #VARIABLE {scriptpause} {0};
  #CLASS prepareclass KILL;
  #CLASS prepareclass open;
  #ACTION {^{设定环境变量：action \= \"checkprepare\"|你设定checkprepare为反馈信息}} {
    #CLASS prepareclass kill;
    #VARIABLE {threshold_neili} {@eval{$hp[neili_max]*5/3}};
    #LOCAL {per} {$conf[neilithreshold]};
    #IF {$per >= 80 && $per <= 180} {
      #VARIABLE {threshold_neili} {@eval{$hp[neili_max] * $per / 100}};
    };
    #VARIABLE {fullthreshold} {$threshold_neili};
    setuppersonalmap;
    #IF {"$city" == "极乐世界"} {
      waitentry
    };
    #ELSEIF {$dazuo_point == 0 || $tuna_point == 0 || $heal_point == 0} {
      prepareskills {
        dohalt {doheal {checkdazuopoint {checkhealpoint {startaction}}}};
      };
    };
    #ELSE {
      prepareskills {
        dohalt {doheal {startaction}};
      };
    };
  };
  #CLASS prepareclass close;
  #VARIABLE {kungfu} {};
  #VARIABLE {hp} {};
  #VARIABLE {id} {};
  checkenv;
  checkalias;
  set 积蓄;
  set brief;
  set look;
  worker;
  loc;
  i;
  hp;
  score;
  exp;
  cond;
  jobtimes;
  #DELAY {2} {echo {checkprepare}};
};
#ALIAS {waitentry} {
  #CLASS commmclass KILL;
  #CLASS commmclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkdown\"|你设定checkdown为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {"$city" == "极乐世界"} {
      #DELAY {2} {
        goingdown;
        look;
        echo {checkdown}
      };
    };
    #ELSE {
      #CLASS commmclass KILL;
      loc {preparestart}
    };
  };
  #CLASS commmclass CLOSE;
  #SHOWME {<faa>狗日的教授胡搞瞎搞};
  goingdown;
  look;
  echo {checkdown}
};
#NOP {载入后的起始操作};
#ALIAS {startaction} {
  #IF {"$city" == "武馆" && $hp[exp] > 3000} {
    settimeout {180} {logbuff};
    loc {leavewg};
  };
  #ELSEIF {"$env[logindo]" != ""} {
    #LOCAL {tparam} {$env[logindo]};
    #VARIABLE {env[logindo]} {};
    alias logindo;
    #REGEXP {$tparam} {%*|%*|%*|%*} {
      loadstock {&1 {&2} {&3} {&4}};
    } {startaction};    
  };
  #ELSE {
    #SWITCH {"$conf[role]"} {
      #CASE {"job"} {startjob};
      #DEFAULT {#SHOWME {<faa>没事干}};
    };
  };
};
#NOP {退出,一般物品的保存};
#ALIAS {doquit} {
  savestock {
    quit
  }
};
#NOP {去拿珍珠,先去当铺,没有进行兑换,%1:后续指令};
#ALIAS {getpearl} {
  gotodo {扬州城} {杂货铺} {qupearl_start {%1}}
};
#NOP {去珍珠,%1:后续指令};
#ALIAS {qupearl_start} {
  #CLASS commonclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkpearl\"|你设定checkpearl为反馈信息}} {
    #CLASS commonclass KILL;
    #IF {@carryqty{pearl} > 0} {
      dohalt {%1}
    };
    #ELSE {
      runwait {
        gotodo {扬州城} {当铺} {duihuanpearl_start {%1}}
      };
    };
  };
  #CLASS commonclass CLOSE;
  qu pearl;
  i;
  echo {checkpearl};
};
#NOP {兑换珍珠,%1:后续指令};
#ALIAS {duihuanpearl_start} {
  #CLASS commonclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkpearl\"|你设定checkpearl为反馈信息}} {
    #CLASS commonclass KILL;
    #DELAY {1} {
      #IF {@carryqty{pearl} > 0} {
        dohalt {
          %1
        };
      };
      #ELSE {
        duihuan pearl;
        i;
        echo {checkpearl};
      };
    };
  };
  #CLASS commonclass CLOSE;
  duihuan pearl;
  i;
  echo {checkpearl};
};
#NOP {吃珍珠};
#ALIAS {dopray} {
  #IF {@carryqty{pearl} > 0} {
    #VARIABLE {env[pray]} {1};
    pray pearl;
    %1
  };
  #ELSE {
    getpearl {dopray {%1}}
  };
};
#FUNCTION getPointString {
  #VARIABLE {result} {dps};
  #CAT {result} {$dazuo_point};
  #CAT {result} {tps};
  #CAT {result} {$tuna_point};
  #CAT {result} {hps};
  #CAT {result} {$heal_point};
};
#NOP {获取最佳打坐点数,%1:后续指令,%2:强制执行};
#ALIAS {getdazuopoint} {
  #LOCAL {localcity} {扬州城};
  #IF {"$city" != ""} {
    #LOCAL {localcity} {$city};
  };
  gotodo {$localcity} {$cities[$localcity][cityroom]} {checkdazuopoint {%1} {%2}};
};
#NOP {获取最佳打坐点数,%1:后续指令,%2:强制执行};
#ALIAS {checkdazuopoint} {
  #VARIABLE {checkresult} {0};
  #VARIABLE {dazuothreshold} {@eval{$hp[qi_max]*100/$hp[qi_per]/5}};
  #VARIABLE {orginalqi} {0};
  #VARIABLE {orginaljing} {0};
  #VARIABLE {checkcount} {0};
  #CLASS prepareclass KILL;
  #CLASS prepareclass OPEN;
  #ACTION {^{这里不准战斗|卧室不能打坐|你无法静下心来修炼|这里可不是让你}} {
    #CLASS prepareclass KILL;
    getdazuopoint {%1} {%2}
  };
  #ACTION {你现在精不够} {
    #VARIABLE {checkresult} {1};
  };
  #ACTION {^你至少需要%u点的气来打坐！} {
    #VARIABLE {checkresult} {0};
    #VARIABLE {dazuothreshold} {@ctd{%%1}};
    #DELAY {1} {
      dazuo @eval{$hp[qi] - 10};
      echo {checkdazuopoint};
    }
  };
  #ACTION {^你必须先用 enable 选择你要用的特殊内功} {
    #CLASS prepareclass KILL;
    jifa all;
    #VARIABLE {spforce} {};
    #DELAY {1} {
      #FOREACH {*kungfu[spec][]} {ssk} {
        #IF {"$kungfu[spec][$ssk][jifa][force]" != ""} {
          #VARIABLE {spforce} {$ssk};
          #BREAK;
        };
      };
      #IF {"$spforce" != ""} {
        checkdazuopoint {%1} {%2}
      };
      #ELSE {
        %1
      };
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkready\"|你设定checkready为反馈信息}} {
    #VARIABLE {orginalqi} {$hp[qi]};
    #VARIABLE {orginaljing} {$hp[jing]};
    #IF {($hp[exp] < 150000 || "$kungfu[force]" == "") && "%2" == ""} {
      #CLASS prepareclass KILL;
      loc {startaction}
    };
    #ELSE {
      dazuo 0;
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkdazuopoint\"|你设定checkdazuopoint为反馈信息}} {
    #VARIABLE {idle} {0};
    #MATH {checkcount} {$checkcount + 1};
    #DELAY {1} {
      #IF {$checkresult == 1} {
        #IF {$hp[jing_per] < 80} {
          #IF {@carryqty{huoxue dan} > 0} {
            execute {
              fu huoxue dan;
              hp;
              i;
              dazuo 0
            };
          };
          #ELSE {
            #CLASS prepareclass KILL;
            buymedicine {huoxue dan} {2} {checkdazuopoint {%1} {%2}}
          };
        };
        #ELSE {
          execute {
            yun jing;
            hp;
            dazuo 0
          };
        };
      };
      #ELSEIF {$hp[qi] < $orginalqi} {
        #LOCAL {qireduce} {@eval{$orginalqi - $hp[qi]}};
        #LOCAL {qitimes} {@eval{$dazuothreshold/$qireduce + 1}};
        #VARIABLE {dazuo_point} {@eval{$qireduce*$qitimes}};
        set env_point @getPointString{};
        #VARIABLE {checkcount} {0};
        dohalt {
          #VARIABLE {orginaljing} {$hp[jing]};
          #VARIABLE {checkresult} {0};
          tuna @eval{$hp[jing] - 100};
          echo {checktunapoint};
        }
      };
      #ELSEIF {$checkcount > 10} {
        #CLASS prepareclass KILL;
        checkdazuopoint {%1} {%2};
      };
      #ELSE {
        hp;
        echo {checkdazuopoint};
      };
    }
  };
  #ACTION {^你现在身体状况太差了，无法集中精神} {
    #VARIABLE {checkresult} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checktunapoint\"|你设定checktunapoint为反馈信息}} {
    #VARIABLE {idle} {0};
    #MATH {checkcount} {$checkcount + 1};
    #DELAY {1} {
      #IF {$checkresult == 1} {
        #CLASS prepareclass KILL;
        #VARIABLE {checkresult} {0};
        #IF {$hp[qi_per] < 50 || @getSkillLevel{medicine} == 0} {
          #IF {@carryqty{dahuan dan} > 0} {
            fu dahuan dan;
            checkdazuopoint {%1} {%2};
          };
          #ELSE {
            tbbuy {dahuan dan} {1} {checkdazuopoint {%1} {%2}};
          };
        };
        #ELSE {
          doheal {checkdazuopoint {%1}};
        };
      };
      #ELSEIF {$checkcount < 10} {
        #LOCAL {jingreduce} {@eval{$orginaljing - $hp[jing]}};
        #IF {$jingreduce > 0} {
          #LOCAL {jingtimes} {@eval{$hp[jing]/4/$jingreduce + 1}};
          #VARIABLE {tuna_point} {@eval{$jingtimes * $jingreduce}};
          #IF {$tuna_point > @eval{$hp[jing_max]/4}} {
            #VARIABLE {tuna_point} {@eval{$hp[jing_max]/4}};
          };
          set env_point @getPointString{};
          #CLASS prepareclass KILL;
          dohalt {
            yun jing;
            %1
          };
        };
        #ELSE {
          hp;
          echo {checktunapoint};
        };
      };
      #ELSE {
        dohalt {
          checkdazuopoint {%1} {%2};
        };
      };
    };
  };
  #CLASS prepareclass CLOSE;
  dohalt {
    execute {
      yun jing;
      yun qi;
      hp;
    };
    echo {checkready};
  };
};
#NOP {获取heal速度，%1:后续指令};
#ALIAS {checkhealpoint} {
  #IF {@getSkillLevel{medicine} < 32} {
    %1
  };
  #ELSE {
    gotodo {南阳城} {断崖} {checkhealpoint_start {%1}}
  };
};
#NOP {开始测试heal速度,%1:后续指令};
#ALIAS {checkhealpoint_start} {
  #VARIABLE {checkcount} {0};
  #VARIABLE {startflag} {0};
  #VARIABLE {overflag} {0};
  #VARIABLE {pacount} {0};
  #VARIABLE {startts} {0};
  #VARIABLE {startper} {0};
  #VARIABLE {startqi} {0};
  #CLASS prepareclass KILL;
  #CLASS prepareclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkwound\"|你设定checkwound为反馈信息}} {
    #IF {$startper == 0} {
      #VARIABLE {startper} {$hp[qi_per]};
    };
    #IF {$hp[qi_per] >= 90 && $pacount < 40} {
      #MATH {pacount} {$pacount + 5};
      #DELAY {0.5} {
        execute {#5 pa ya;hp};
        echo {checkwound};
      }
    };
    #ELSE {
      yun qi;
      yun heal;
      echo {checkstart};
    };
  };
  #ACTION {^{$heal_desc}} {
    #VARIABLE {startts} {@now{}};
    #VARIABLE {startqi} {$hp[qi_max]};
    #VARIABLE {startflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkstart\"|你设定checkstart为反馈信息}} {
    #MATH {checkcount} {$checkcount + 1};
    #VARIABLE {idle} {0};
    #IF {$checkcount > 20} {
      #CLASS prepareclass KILL;
      checkhealpoint_start {%1};
    };
    #ELSEIF {$startflag == 0} {
      #DELAY {1} {echo {checkstart}};
    };
    #ELSE {
      hp;
      echo {checkqi};
    };
  };
  #ACTION {^良久，你感觉通过自己的内息运行} {
    #VARIABLE {overflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkqi\"|你设定checkqi为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {$hp[qi_per] >= $startper || $overflag == 1} {
      #CLASS prepareclass KILL;
      #VARIABLE {heal_point} {@eval{($hp[qi_max] - $startqi) / @elapsed{$startts}}};
      set env_point @getPointString{};
      dohalt {%1};
    };
    #ELSE {
      #DELAY {0.5} {
        hp;
        echo {checkqi};
      }
    };
  };
  #CLASS prepareclass CLOSE;
  #IF {@getSkillLevel{medicine} < 32} {
    %1
  };
  #ELSEIF {$hp[neili] < $hp[neili_max]} {
    startfull {hp;echo {checkwound}};
  };
  #ELSE {
    hp;
    echo {checkwound};
  };
};
#NOP {载入杂货铺存货,只加载药品和通用武器,%1:后续指令};
#ALIAS {loadstock} {
  gotodo {扬州城} {杂货铺} {loadstock_start {%1}}
};
#ALIAS {loadstock_start} {
  #VARIABLE {favourite} {};
  #VARIABLE {pweapon} {none};
  #VARIABLE {sweapon} {none};
  #VARIABLE {cweapon} {none};
  #IF {"$conf[weapon][primary]" != ""} {
    #VARIABLE {pweapon} {$conf[weapon][primary]}
  };
  #IF {"$conf[weapon][secondary]" != ""} {
    #VARIABLE {sweapon} {$conf[weapon][secondary]}
  };
  #IF {"$conf[weapon][chop]" != ""} {
    #VARIABLE {cweapon} {$conf[weapon][chop]}
  };
  #CLASS stockclass KILL;
  #CLASS stockclass OPEN;
  #ACTION {^┃       ID             货  物               价  格 } {
    #VARIABLE {favourite} {};
    #CLASS dlistclass OPEN;
    #ACTION {^┃%!s{sheng zi|chuanbei wan|huoxue dan|chantui yao|xiong huang|$pweapon|$sweapon|$cweapon|snowsword|youlong bian|zhu bang|bishou|kulou chui|tiegun|dafu|falun|hongying qiang|hanyu gou|lanyu duzhen|xue sui|panguan bi|yinshe sword|yu xiao|tianshe zhang|dahuan dan|da huandan}} {  
      #VARIABLE {favourite} {%%%1};
    };
    #ACTION {^┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛} {
      #CLASS dlistclass KILL;
    };
    #CLASS dlistclass CLOSE;
  };
  #ACTION {^你没有保存任何物品} {
    #VARIABLE {favourite} {};
  };
  #ACTION {^{设定环境变量：action \= \"loadstock\"|你设定loadstock为反馈信息}} {
    #CLASS dlistclass KILL;
    #DELAY {1} {
      dohalt {
        #IF {"$favourite" != ""} {
          execute {
            qu $favourite;
            i;
            dlist;
          };
          echo {loadstock};
        };
        #ELSE {
          #CLASS stockclass KILL;
          %1
        };
      };
    };
  };
  #CLASS stockclass CLOSE;
  dlist;
  echo {loadstock};
};
#NOP {保存药品、通用武器和珍珠到杂货铺,%1:后续指令,%2:额外要保存的物品};
#ALIAS {savestock} {
  gotodo {扬州城} {杂货铺} {savestock_start {%1} {%2}}
};
#ALIAS {savestock_start} {
  #LIST {favouritethings} {create} {*common[normalweapon][]};
  #LIST {favouritethings} {add} {pearl};
  #LIST {favouritethings} {add} {chuanbei wan};
  #LIST {favouritethings} {add} {huoxue dan};
  #LIST {favouritethings} {add} {chantui yao};
  #LIST {favouritethings} {add} {sheng zi};
  #LIST {favouritethings} {add} {dahuan dan};
  #LIST {favouritethings} {add} {tianqi};
  #IF {@carryqty{%2} > 0} {
    #LIST {favouritethings} {add} {%2};
  };
  #CLASS stockclass KILL;
  #CLASS stockclass OPEN;
  #ACTION {^{设定环境变量：action \= \"savestock\"|你设定savestock为反馈信息}} {
    #LOCAL {tempthing} {};
    #FOREACH {*id[things][]} {t} {
      #NOP {暴雨梨花针单独处理};
      #IF {"$t" == "zhen"} {
        #CONTINUE;
      };
      #IF {@contains{{favouritethings}{$t}} > 0} {
        #LOCAL {tempthing} {$t};
        #BREAK;
      };
    };
    dohalt {
      #IF {"$tempthing" != ""} {
        execute {
          cun $tempthing;
          i;
        };
        echo {savestock};
      };
      #ELSE {
        #CLASS stockclass KILL;
        %1
      };
    }
  };
  #CLASS stockclass CLOSE;
  uwwp;
  echo {savestock};
};
#NOP {保存临时装备，后续指令};
#ALIAS {saveequipment} {
  gotodo {扬州城} {杂货铺} {saveequipment_start {%1}}
};
#NOP {保存临时装备};
#ALIAS {saveequipment_start} {
  #CLASS stockclass KILL;
  #CLASS stockclass OPEN;
  #LIST {equiplist} {create} {armor;mantle;coat;boot;belt;cap;glove};
  #ACTION {^{设定环境变量：action \= \"checksave\"|你设定checksave为反馈信息}} {
    #IF {&equiplist[] > 0} {
      cun $equiplist[+1];
      #LIST {equiplist} {DELETE} {1};
      dohalt {echo {checksave}};
    };
    #ELSE {
      #CLASS stockclass KILL;
      %1
    };
  };
  #CLASS stockclass CLOSE;
  remove all;
  echo {checksave}
};
#NOP {从仓库中取物品,%1:物品,%2:后续指令,%3:失败指令};
#ALIAS {qusomething} {
  gotodo {扬州城} {杂货铺} {qusomething_start {%1} {%2} {%3}}
};
#ALIAS {qusomething_start} {
  #CLASS stockclass KILL;
  #CLASS stockclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkthing\"|你设定checkthing为反馈信息}} {
    #CLASS stockclass KILL;
    #IF {@carryqty{%1} == 0 && "%3" != ""} {
      dohalt {%3}
    };
    #ELSE {
      dohalt {%2}
    };
  };
  #CLASS stockclass CLOSE;
  qu %1;
  i;
  echo {checkthing}
};
#NOP {向仓库中存物品,%1:物品,%2:后续指令};
#ALIAS {cunsomething} {
  gotodo {扬州城} {杂货铺} {cunsomething_start {%1} {%2} {%3}}
};
#ALIAS {cunsomething_start} {
  #CLASS stockclass KILL;
  #CLASS stockclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkthing\"|你设定checkthing为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {@carryqty{%1} > 0} {
      #DELAY {1} {
        cun %1;
        i;
        echo {checkthing}
      };
    };
    #ELSE {
      dohalt {%2}
    };
  };
  #CLASS stockclass CLOSE;
  cun %1;
  i;
  echo {checkthing}
};
#NOP {从仓库取tianqi,%1:数量，%2:后续指令};
#ALIAS {gettianqi} {
  gotodo {扬州城} {杂货铺} {gettianqi_start {%1} {%2}};
};
#ALIAS {gettianqi_start} {
  #VARIABLE {tianqiflag} {0};
  #CLASS stockclass KILL;
  #CLASS stockclass OPEN;
  #ACTION {^┃       ID             货  物               价  格 } {
    #VARIABLE {favourite} {};
    #CLASS dlistclass OPEN;
    #ACTION {^┃%!stianqi} {  
      #VARIABLE {tianqiflag} {1};
    };
    #ACTION {^┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛} {
      #CLASS dlistclass KILL;
    };
    #CLASS dlistclass CLOSE;
  };
  #ACTION {^你没有保存任何物品} {
    #VARIABLE {tianqiflag} {0};
  };
  #ACTION {^{设定环境变量：action \= \"checktianqi\"|你设定checktianqi为反馈信息}} {
    #CLASS dlistclass KILL;
    #DELAY {1} {
      #IF {@carryqty{tianqi} > 0} {
        #CLASS dlistclass KILL;
        dohalt {%2};
      };
      #ELSEIF {$tianqiflag == 0} {
        #CLASS dlistclass KILL;
        #VARIABLE {env[oot]} {1};
        dohalt {%2};
      };
      #ELSE {
        qu tianqi;
        dlist;
        i;
        echo {checktianqi};
      };
    };
  };
  #CLASS stockclass CLOSE;
  qu tianqi;
  dlist;
  i;
  echo {checktianqi};
};
#NOP {异常离开武馆};
#ALIAS {leavewg} {
  gotonpc {万震山} {leavewg_start};
};
#ALIAS {leavewg_start} {
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^你向万震山打听有关『狄云』的消息。} {
    #VARIABLE {scriptpause} {1};
    #LINE ONESHOT #ACTION {^一股暖流发自丹田流向全身，慢慢地你又恢复了知觉} {
      #CLASS commonclass KILL;
      #VARIABLE {scriptpause} {0};
      jobclear;
      loc {yz {preparestart}};
    } {1};
  };
  #CLASS commonclass CLOSE;
  ask wan zhenshan about 狄云;
};
#NOP {是否还有指定的busy状态,%1:busy，多个以分号隔开};
#FUNCTION hasBusy {
  #LOCAL {busies} {%1};
  #LIST {tempbusylist} {create} {$busies};
  #LOCAL {busyok} {0};
  #FOREACH {$tempbusylist[]} {b} {
    #IF {@eval{$hp[busy][$b]} > 0} {
      #LOCAL {busyok} {1};
      #BREAK;
    };
  };

  #RETURN {$busyok};
};
#NOP {打坐倒指定状态,%1:后续指令,%2:打坐标识,%3:提前中止条件，此参数如满足则忽略参数2的要求};
#NOP {打坐标识:0(空)-打坐倒角色对顶的neilithreshold,1-仅打坐一次,2-强制打坐涨一点内力,3-强制打坐倒双倍};
#NOP {提前中止条件字段，timestamp-直到时间戳,negative-负面状态消失后提前中止,busy-任务忙碌消失后提前中止};
#ALIAS {startfull} {
  #VARIABLE {idle} {0};
  #VARIABLE {increaseflag} {0};
  #VARIABLE {dzflag} {@eval{%2}};
  #VARIABLE {assertexpr} {%3};
  #VARIABLE {threshold_neili} {@eval{$hp[neili_max]*5/3}};
  #LOCAL {per} {$conf[neilithreshold]};
  #IF {$per >= 80 && $per <= 180} {
    #VARIABLE {threshold_neili} {@eval{$hp[neili_max] * $per / 100}};
  };
  #VARIABLE {fullthreshold} {$threshold_neili};
  #IF {$dzflag == 3} {
    #VARIABLE {fullthreshold} {@eval{$hp[neili_max] *5 / 3}};
  };
  #CLASS fullclass KILL;
  #CLASS fullclass OPEN;
  #ACTION {^你的内力修为已经达到圆满之境} {
    #IF {$dzflag == 2} {
      unset 积蓄;
      #VARIABLE {env[积蓄]} {0};
    };
  };
  #ACTION {^你的内力修为增加了一点！} {
    #IF {$dzflag == 2 && "$assertexpr" == ""} {
      #CLASS dzclass KILL;
      #CLASS fullclass KILL;
      #UNTICKER {dzticker};
      dohalt {
        yun qi;
        %1
      }
    }
  };
  #CLASS fullclass CLOSE;
  #IF {$dzflag == 2 || "$assertexpr[negative]" != ""} {
    closesaving;
  };
  #ELSE {
    opensaving;
  };
  #IF {$dazuo_point + 100 > $hp[qi_max]} {
    #CLASS fullclass KILL;
    #NOP {无法打坐，非预期场景，直接执行后续指令};
    %1;
  };
  #ELSEIF {$hp[neili] > $fullthreshold && ($dzflag == 0 || $dzflag == 3) && "$assertexpr" == ""} {
    #CLASS fullclass KILL;
    %1;
  };
  #ELSEIF {$dzflag == 2 && $hp[neili_max] >= $hp[neili_limit]} {
    #CLASS fullclass KILL;
    dzn {%1} {1} {} {%4}
  };
  #ELSEIF {$dzflag == 1 && "$assertexpr" == ""} {
    #CLASS fullclass KILL;
    dzn {%1} {1}
  };
  #ELSEIF {"$assertexpr[timestamp]" != "" && @now{} < $assertexpr[timestamp]} {
    dzn {%1} {%2} {%3} {%4}
  };
  #ELSEIF {"$assertexpr[negative]" != "" && "$hp[condition][$assertexpr[negative]]" == ""} {
    #CLASS fullclass KILL;
    %1;
  };
  #ELSEIF {"$assertexpr[busy]" != "" && @hasBusy{{$assertexpr[busy]}} == 0} {
    #CLASS fullclass KILL;
    %1;
  };
  #ELSE {
    dzn {startfull {%1} {%2} {%3} {%4}} {1} {%3} {%4};
  };
};
#NOP {打坐,%1:后续指令,%2:busy时始终不在尝试打坐,%3:提前中止条件,%4:是否尝试练习};
#NOP {条件字段非空时字段，tick-经过指定秒后中止,negative-负面状态消失后提前中止,busy-任务忙碌消失后提前中止};
#ALIAS {dzn} {
  #UNTICKER {dzticker};
  #VARIABLE {dzstartts} {0};
  #VARIABLE {abortexpr} {%3};
  #CLASS dzclass KILL;
  #CLASS dzclass OPEN;
  #ACTION {^你至少需要%*点的气来打坐} {
    #CLASS dzclass KILL;
    stopwalk;
    kungfuclear;
    commonclear;
    checkdazuopoint {
      loc {gotodo {$aimcity} {$aimroomid} {$aimdo}}
    };
  };
  #ACTION {^你现在正忙着呢} {
    #VARIABLE {idle} {0};
    #IF {"%2" != ""} {
      #CLASS dzclass KILL;
      #DELAY {1} {#IF {"%1" != ""} {%1}}
    };
    #ELSE {
      #DELAY {1} {dazuo $dazuo_point}
    };
  };
  #ACTION {^你气息不匀} {
    #VARIABLE {idle} {0};
    #IF {"%2" != ""} {
      #CLASS dzclass KILL;
      #IF {"%1" != ""} {%1};
    };
    #ELSE {
      #DELAY {0.5} {dazuo $dazuo_point}
    };
  };
  #ACTION {^{这里不准战斗|卧室不能打坐|你无法静下心来修炼|这里可不是让你}} {
    #CLASS dzclass KILL;
    #UNTICKER {dzticker};
    #LOCAL {localcity} {扬州城};
    #IF {"$city" != ""} {
      #LOCAL {localcity} {$city};
    };
    gotodo {$localcity} {$cities[$localcity][cityroom]} {dzn {%1} {%2} {%3} {%4}};
  };
  #ACTION {^你刚施用过内功，不能马上打坐} {
    #DELAY {0.5} {dazuo $dazuo_point}
  };
  #ACTION {^你现在手脚戴着镣铐，不能做出正确的姿势来打坐} {
    #CLASS dzclass KILL;
    sl_tiaoshui {gotoroom {$aimroomid} {$aimdo}};
  };
  #ACTION {^这里不准战斗，也不准打坐} {
    #CLASS dzclass KILL;
    #IF {@contains{{boatrooms}{$room}} == 0} {
      #LOCAL {localcity} {扬州城};
      #IF {"$city" != ""} {
        #LOCAL {localcity} {$city};
      };
      gotodo {$localcity} {$cities[$localcity][cityroom]} {%1};
    };
  };
  #ACTION {^你现在精不够，无法控制内息的流动} {
    #CLASS dzclass KILL;
    #NOP {检查精缺失和受伤情况，装备加了大量精的情况下即便jing_per > 75也不能打坐};
    #VARIABLE {idle} {0};
    #IF {$hp[jing] < @eval{$hp[jing_max]*4/5}} {
      yun jing;
      hp;
      #DELAY {2} {dzn {%1} {%2} {%3}}
    };
    #ELSEIF {@carryqty{huoxue dan} > 0} {
      fu huoxue dan;
      i;
      yun jing;
      hp;
      #DELAY {2} {dzn {%1} {%2} {%3}}
    };
    #ELSEIF {@startWiths{{$aimdo}{buyfulljing}} == 0} {
      buyfulljing {gotoroom {$aimroomid} {$aimdo}}
    };
    #ELSE {
      #DELAY {2} {walk}
    };
  };
  #ACTION {^{$dazuo_desc}} {
    #VARIABLE {dzstartts} {@now{}};
    #VARIABLE {workingflag} {1};
    #IF {"$abortexpr[timestamp]" != ""} {
      #TICKER {dzticker} {
        hp;
        echo {checkabort}
      } {1};
    };
    #ELSEIF {"$abortexpr[negative]" != ""} {
      #TICKER {dzticker} {cond;echo {checkabort}} {2};
    };
    #ELSEIF {"$abortexpr[busy]" != ""} {
      #TICKER {dzticker} {cond;echo {checkabort}} {2};
    };
  };
  #ACTION {^{$dazuo_halt}} {
    #CLASS dzclass KILL;
    #UNTICKER {dzticker};
    #VARIABLE {workingflag} {0}
  };
  #ACTION {^{$dazuo_over}} {
    #CLASS dzclass KILL;
    #UNTICKER {dzticker};
    #VARIABLE {env[dzduration]} {@elapsed{$dzstartts}};
    #VARIABLE {workingflag} {0};
    #IF {"%1" != ""} {%1}
  };
  #ACTION {^{设定环境变量：action \= \"checkabort\"|你设定checkabort为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {"$abortexpr[timestamp]" != "" && @now{} >= $abortexpr[timestamp]} {
      #CLASS dzclass KILL;
      #UNTICKER {dzticker};
      #IF {"$abortexpr[callback]" != ""} {
        $abortexpr[callback]
      };
      dohalt {%1}
    };
    #ELSEIF {"$abortexpr[negative]" != "" && "$hp[condition][$abortexpr[negative]]" == ""} {
      #CLASS dzclass KILL;
      #UNTICKER {dzticker};
      dohalt {%1}
    };
    #ELSEIF {"$abortexpr[busy]" != "" && @hasBusy{{$abortexpr[busy]}} == 0} {
      #CLASS dzclass KILL;
      #UNTICKER {dzticker};
      dohalt {%1}
    };
  };
  #CLASS dzclass CLOSE;
  #IF {"%4" != ""} {waitlian};
  #VARIABLE {idle} {0};
  execute {
    yun qi;
    hp;
    dazuo $dazuo_point
  };
};
#NOP {吐纳};
#ALIAS {tnn} {
  yun jing;
  hp;
  tuna $tuna_point
};
#NOP {等待站点重启};
#ALIAS {waitreboot} {
  gotodo {扬州城} {杂货铺} {waitreboot_start}
};
#ALIAS {waitreboot_start} {
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^{设定环境变量：action \= \"chekcountdown\"|你设定chekcountdown为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {$env[countdown] > 1} {
      #DELAY {6} {
        echo {chekcountdown};
      };
    };
    #ELSE {
      #CLASS commonclass KILL;
      doquit;
    };
  };
  #CLASS commonclass CLOSE;
  echo {chekcountdown};
};
#NOP {寻找武器,%1:武器id,%2:后续指令};
#ALIAS {findweapon} {
  #IF {"$common[baseweapon][%1]" != ""} {
    buybaseweapon {%1} {%2}
  };
  #ELSEIF {"$common[normalweapon][%1]" != ""} {
    buynormalweapon {%1} {%2}
  };
  #ELSEIF {"$common[uniqueweapon][%1]" != ""} {
    #SWITCH {"%1"} {
      #CASE {"yitian jian"} {findweapon_ytj {%2}};
      #CASE {"tulong dao"} {findweapon_tld {%2}};
      #CASE {"xuantie jian"} {findweapon_xtj {%2}};
      #CASE {"songwen jian"} {findweapon_swj {%2}};
    };
  };
  #ELSE {
    #NOP {未知武器,判定为打造武器,退出等刷新};
    doquit
  };
};
#NOP {是否用户打造武器,%1:武器id};
#FUNCTION isUserWeapon {
  #IF {"$common[baseweapon][%1]" != ""} {
    #RETURN {0};
  };
  #ELSEIF {"$common[normalweapon][%1]" != ""} {
    #RETURN {0};
  };
  #ELSEIF {"$common[uniqueweapon][%1]" != ""} {
    #RETURN {0};
  };
  #ELSE {
    #RETURN {1};
  };
};
#NOP {拿松纹古剑};
#ALIAS {findweapon_swj} {
  gotonpc {张三丰} {findweapon_swj_start {%1}}
};
#ALIAS {findweapon_swj_start} {
  #CLASS weaponclass KILL;
  #CLASS weaponclass OPEN;
  #ACTION {^你向张三丰打听有关『下山』的消息} {
    dohalt {
      i;
      ask zhang sanfeng about 教诲;
      echo {checkid}
    }
  };
  #ACTION {^{设定环境变量：action \= \"checkid\"|你设定checkid为反馈信息}} {
    #CLASS weaponclass KILL;
    #IF {@carryqty{songwen jian} == 0} {
      #NOP {必须退出};
      dohalt {doquit}
    };
    #ELSE {
      dohalt {%1}
    };
  };
  #CLASS weaponclass CLOSE;
  ask zhang sanfeng about 下山
};
#NOP {拿倚天剑};
#ALIAS {findweapon_ytj} {
  #SHOWME {<faa>未实现};
};
#NOP {拿玄铁剑,%1:后续指令,%2:失败指令,如果没有则等};
#ALIAS {findweapon_xtj} {
  gotodo {襄阳城} {剑冢} {findweapon_xtj_start {%1} {%2}}
};
#ALIAS {findweapon_xtj_start} {
  #CLASS weaponclass KILL;
  #CLASS weaponclass OPEN;
  #ACTION {^你{用力想提起玄铁剑|来晚了，玄铁剑已经给人取走了}} {
    #VARIABLE {idle} {0};
    #CLASS weaponclass KILL;
    #IF {"%2" != ""} {
      %2;
    };
    #ELSE {
      #DELAY {6} {
        findweapon_xtj_start {%1} {%2};
      };
    };
  };
  #ACTION {^於是你再俯提起，这次有了防备，提起七八十斤的重物自是不当一回事} {
    #CLASS weaponclass KILL;
    dohalt {%1};
  };
  #CLASS weaponclass CLOSE;
  uwwp;
  ti xuantiejian;
  i;
};
#NOP {拿屠龙刀};
#ALIAS {findweapon_tld} {
  #SHOWME {<faa>未实现};
};
#ALIAS {findweapon_tld_start} {
};
#NOP {拿小树枝,%1:后续指令};
#ALIAS {findweapon_xsz} {
  gotodo {苏州城} {虎丘山} {findweapon_xsz_start {%1}}
};
#ALIAS {findweapon_xsz_start} {
  #CLASS weaponclass KILL;
  #CLASS weaponclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkid\"|你设定checkid为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {@carryqty{xiao shuzhi} == 0} {
      #DELAY {2} {
        get xiao shuzhi;
        i;
        echo {checkid};
      };
    };
    #ELSE {
      #CLASS weaponclass KILL;
      %1；
    };
  };
  #CLASS weaponclass CLOSE;
  get xiao shuzhi;
  i;
  echo {checkid};
};
#NOP {拿chang jian,%1:后续指令};
#ALIAS {findweapon_cj} {
  gotodo {终南山} {剑室} {findweapon_cj_start {%1}}
};
#ALIAS {findweapon_cj_start} {
  #CLASS weaponclass KILL;
  #CLASS weaponclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkid\"|你设定checkid为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {@carryqty{chang jian} == 0} {
      #DELAY {1} {
        drop mu jian;
        drop chang jian;
        get chang jian;
        i;
        echo {checkid};
      };
    };
    #ELSE {
      #CLASS weaponclass KILL;
      dohalt {%1};
    };
  };
  #CLASS weaponclass CLOSE;
  get chang jian;
  i;
  echo {checkid};
};
#NOP {拿mu jian,%1:后续指令};
#ALIAS {findweapon_mj} {
  gotodo {襄阳城} {木匠铺} {findweapon_mj_start {%1}}
};
#ALIAS {findweapon_mj_start} {
  #CLASS weaponclass KILL;
  #CLASS weaponclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkid\"|你设定checkid为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {@carryqty{mu jian} == 0} {
      #DELAY {1} {
        drop mu jian;
        drop chang jian;
        buy mu jian;
        i;
        echo {checkid};
      }
    };
    #ELSE {
      #CLASS weaponclass KILL;
      dohalt {%1};
    };
  };
  #CLASS weaponclass CLOSE;
  buy mu jian;
  i;
  echo {checkid};
};
#NOP {找采矿师傅};
#ALIAS {findcaikuang} {
  #LIST {roomlist} {create} {1;405;406;407;408;409;410;411;413;415;416;417;418;419;420;421;422;423;424;425;427;428;429;430;431;433;435;436;437;438;439;440;441;442;443;444;445;446;447;448;449;450;451;452;453;454;455;456;457;458;459;460;461;462;463;464;465;466;467;470;471;472;473;474;475;478;479;480;481;484};
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^这里没有 caikuang shifu。} {
		#IF {&roomlist[] == 0} {
			#CLASS commonclass KILL;
			findsuanming {%1};
		};
		#ELSE {
			#LOCAL {temproomid} {$roomlist[+1]};
			#LIST {roomlist} {delete} {1};
			runwait {gotoroom {$temproomid} {follow caikuang shifu}};
		};
	};
  #ACTION {^你决定跟随采矿师傅一起行动。} {
    #CLASS commonclass KILL;
		%1
	};
  #CLASS joinclass CLOSE;
  follow none;
  #LOCAL {temproomid} {$roomlist[+1]};
  #LIST {roomlist} {delete} {1};
  gotoroom {$temproomid} {follow caikuang shifu}
};
#NOP {购买打铁武器,%1:要购买的武器tieqiao,tiechui,%2:后续指令};
#ALIAS {buysmithweapon} {
  localwithdraw {2} {200} {findcaikuang {buysmithweapon_start {%1} {%2}}}
};
#ALIAS {buysmithweapon_start} {
  #CLASS weaponclass KILL;
  #CLASS weaponclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkid\"|你设定checkid为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {@carryqty{%1} == 0} {
      #DELAY {1} {
        buy %1;
        i;
        echo {checkid}
      };
    };
    #ELSE {
      #CLASS weaponclass KILL;
      follow none;
      loc {%2}
    };
  };
  #CLASS weaponclass CLOSE;
  buy %1;
  i;
  echo {checkid}
};
#NOP {购买基础武器,%1:武器名称,%2:后续指令};
#ALIAS {buybaseweapon} {
  gotofor {扬州城} {兵器铺} {buybaseweapon_start {%1} {%2}}
};
#ALIAS {buybaseweapon_start} {
  #VARIABLE {askresult} {0};
  #CLASS weaponclass KILL;
  #CLASS weaponclass OPEN;
  #ACTION {^%*说道：「穷光蛋，一边呆着去！」} {
    #VARIABLE {askresult} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkid\"|你设定checkid为反馈信息}} {
    #IF {@carryqty{%1} > 0} {
      #CLASS weaponclass KILL;
      #VARIABLE {env[weapongs][%1]} {@now{}};
      #DELAY {0.5} {
        %2
      }
    };
    #ELSE {
      #IF {$askresult == 1} {
        n;#3 w;#3 n;w;qu 5 gold;i;e;#3 s;#3 e;s
      };
      #DELAY {1} {
        buy %1;
        i;
        echo {checkid};
      };
    };
  };
  #CLASS weaponclass CLOSE;
  buy %1;
  i;
  echo {checkid};
};
#NOP {购买普通武器,%1:武器名称,%2:后续指令};
#ALIAS {buynormalweapon} {
  gotofor {襄阳城} {兵器铺} {buynormalweapon_start {%1} {%2}}
};
#ALIAS {buynormalweapon_start} {
  #CLASS weaponclass KILL;
  #CLASS weaponclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkid\"|你设定checkid为反馈信息}} {
    #VARIABLE {idle} {0};
    #DELAY {1} {
      #IF {@carryqty{%1} == 0} {
        #IF {@carryqty{gold} < 20} {
          #CLASS weaponclass KILL;
          gotodo {襄阳城} {宝龙斋} {balanceex {50} {} {buynormalweapon {%1} {%2}}};
        };
        #ELSE {
          buy %1;
        };
        i;
        echo {checkid};
      };
      #ELSE {
        #CLASS weaponclass KILL;
        #VARIABLE {env[weapongs][%1]} {@now{}};
        #DELAY {0.5} {
          %2
        }
      };
    }
  };
  #CLASS weaponclass CLOSE;
  i;
  echo {checkid};
};
#NOP {苏州铁匠铁锄头，%1:后续指令};
#ALIAS {gettiechu} {
  gotodo {苏州城} {打铁铺} {gettiechu_start {%1}}
};
#ALIAS {gettiechu_start} {
  #VARIABLE {failflag} {0};
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^怎么这么贪，不是拿过一把了吗} {
    #VARIABLE {failflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checktiechu\"|你设定checktiechu为反馈信息}} {
    resonate {checktiechu};
    #VARIABLE {idle} {0};
    #IF {@carryqty{tie chu} == 0} {
      dohalt {
        ask jiang about 铁锄;
        i;
        echo {checktiechu}
      }
    };
    #ELSE {
      dohalt {%1}
    };
  };
  #CLASS commonclass CLOSE;
  ensure {
    ask jiang about 铁锄;
    i;
  } {checktiechu}
};
#NOP {获取炼丹药锄，%1:后续指令};
#ALIAS {getyaochu} {
  #VARIABLE {failflag} {0};
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^怎么这么贪，不是拿过一把了吗} {
    #VARIABLE {failflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkyaochu\"|你设定checkyaochu为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {$hp[food] < 50 || $hp[water] < 50} {
      #CLASS commonclass KILL;
      gofood {getyaochu {%1}}
    };
    #ELSEIF {@carryqty{yao chu} > 0} {
      #CLASS commonclass KILL;
      %1
    };
    #ELSEIF {$failflag == 1} {
      doquit
    };
    #ELSE {
      #DELAY {6} {
        find yao chu;
        i;
        echo {checkyaochu}
      };
    };
  };
  #CLASS commonclass CLOSE;
  gotodo {武当山} {武当广场} {
    find yao chu;
    i;
    echo {checkyaochu}
  }
};
#NOP {获取去后山的毛毯，%1:后续指令};
#ALIAS {getmaotan} {
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkmaotan\"|你设定checkmaotan为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {$hp[food] < 50 || $hp[water] < 50} {
      #CLASS commonclass KILL;
      gofood {getmaotan {%1}}
    };
    #ELSEIF {@carryqty{mao tan} > 0} {
      #CLASS commonclass KILL;
      %1
    };
    #ELSE {
      #DELAY {6} {
        find mao tan;
        i;
        echo {checkmaotan}
      }
    };
  };
  #CLASS commonclass CLOSE;
  #IF {"$hp[sex]" == "m"} {
    gotodo {武当山} {男休息室} {
      find mao tan;
      i;
      echo {checkmaotan}
    }
  };
  #ELSE {
    gotodo {武当山} {女休息室} {
      find mao tan;
      i;
      echo {checkmaotan}
    }
  };
};
#NOP {购买大还丹,%1:物品,%2:后续指令};
#ALIAS {buydhd} {
  gotodo {扬州城} {当铺} {buydhd_start {%1} {%2}}
};
#ALIAS {buydhd_start} {
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkdhd\"|你设定checkdhd为反馈信息}} {
    #IF {@carryqty{%1} == 1} {
      #CLASS commonclass KILL;
      dohalt {
        %2;
      };
    };
    #ELSE {
      #DELAY {1} {
        duihuan %1;
        i;
        echo {checkdhd};
      };
    };
  };
  duihuan %1;
  i;
  echo {checkdhd};
};
#NOP {购买衣服,%1:后续指令};
#ALIAS {buycloth} {
  gotodo {襄阳城} {成衣铺} {buycloth_start {%1}}
};
#ALIAS {buycloth_start} {
  #VARIABLE {clothname} {chang shan};
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkcloth\"|你设定checkcloth为反馈信息}} {
    #DELAY {0.5} {
      #IF {@carryqty{gold} < 2} {
        #CLASS commonclass KILL;
        localwithdraw {2} {} {buycloth {%1}}
      };
      #ELSEIF {@carryqty{$clothname} == 0} {
        buy $clothname;
        i;
        echo {checkcloth};
      };
      #ELSE {
        #CLASS commonclass KILL;
        #VARIABLE {env[naked]} {0};
        wear all;
        i;
        %1
      };
    }
  };
  #CLASS commonclass CLOSE;
  #IF {"$hp[sex]" != "m"} {
    #VARIABLE {clothname} {chang qun};
  };
  i;
  echo {checkcloth};
};
#NOP {响应qqll询问请求,%1:后续请求};
#ALIAS {responsewait} {
  yz {responsewait_start {%1}}
};
#NOP {只等一分钟};
#ALIAS {responsewait_start} {
  #VARIABLE {startts} {@now{}};
  #VARIABLE {requester} {none};
  #VARIABLE {okflag} {0};
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^%*向你打听有关『淳于蓝』的消息} {
    #VARIABLE {requester} {%%1};
  };
  #ACTION {^$requester对你抱拳道：“青山不改，绿水常流，咱们后会有期} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkresponse\"|你设定checkresponse为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {@elapsed{$startts} > 60 || $okflag == 1} {
      #CLASS commonclass KILL;
      #VARIABLE env[waitresponse] {0};
      %1;
    };
    #ELSE {
      #DELAY {2} {
        echo {checkresponse};
      };
    };
  };
  #CLASS commonclass CLOSE;
  echo {checkresponse};
};
#NOP {修理武器,%1:武器,%2:后续指令};
#ALIAS {repairweapon} {
  #NOP {判断下是否可以自己修理};
  #IF {"$kungfu[woker][@getWeaponSkill{%1}]" != "" && @getSkillLevel{duanzao} >= 220} {
    qusomething {tiechui} {repairweaponself {%1} {%2}}
  };
  #ELSE {
    gotodo {扬州城} {打铁铺} {repairweapon_start {%1} {%2}};
  };
};
#ALIAS {repairweapon_start} {
  #VARIABLE {okflag} {0};
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^你向铁匠打听有关『修理』的消息} {
    #CLASS commonresponseclass KILL;
    #CLASS commonresponseclass OPEN;
    #ACTION {^铁匠说道：「%*完好无损，无需修理} {
      #CLASS commonresponseclass KILL;
      #CLASS commonclass KILL;
      dohalt {%2};
    };
    #ACTION {^铁匠说道：「让我歇息歇息} {
      #CLASS commonresponseclass KILL;
      dohalt {
        ask tiejiang about 修理;
      };
    };
    #ACTION {^铁匠开始仔细的维修} {
      #CLASS commonresponseclass KILL;
      echo {checkover};
    };
    #CLASS commonresponseclass CLOSE;
  };
  #ACTION {^你本次修理花费%*} {
    joblog {修理武器【%1】花费%%1} {通用}
  };
  #ACTION {^铁匠仔细的维修} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkover\"|你设定checkover为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {$okflag == 1} {
      #CLASS commonclass KILL;
      dohalt {%2};
    };
    #ELSE {
      #DELAY {4} {
        echo {checkover};
      };
    };
  };
  #CLASS commonclass CLOSE;
  wwp {%1};
  ask tiejiang about 修理
};
#NOP {自己维修武器,%1:武器,%2:后续指令};
#ALIAS {repairweaponself} {
  #IF {@carryqty{tiechui} == 0} {
    buysmithweapon {tiechui} {repairweaponself {%1} {%2}}
  };
  #ELSE {
    gotodo {扬州城} {打铁铺} {repairweaponself_start {%1} {%2}};
  };
};
#ALIAS {repairweaponself_start} {
  #VARIABLE {okflag} {0};
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^你本次修理花费%*} {
    joblog {修理武器花费%%1} {通用}
  };
  #ACTION {^你开始仔细的维修} {
    #VARIABLE {workingflag} {1};
    #VARIABLE {okflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkrepair\"|你设定checkrepair为反馈信息}} {
    #IF {$okflag == 0} {
      #CLASS commonclass KILL;
      dohalt {jobprepare}
    };
    #ELSE {
      #VARIABLE {idle} {-120}
    };
  };
  #ACTION {^你仔细的维修%*总算大致恢复了它的原貌} {
    #CLASS commonclass KILL;
    #VARIABLE {workingflag} {0};
    dohalt {%2}
  };
  #CLASS commonclass CLOSE;
  wwp {tiechui};
  repair %1;
  echo {checkrepair}
};
#NOP {weilan打造,%1:武器,%2:后续指令};
#ALIAS {weilan} {
  #IF {@carryqty{pearl} == 0} {
    getpearl {weilan {%1} {%2}}
  };
  #ELSE {
    gotodo {扬州城} {兵器铺} {weilan_start {%1} {%2}}
  };
};
#ALIAS {weilan_start} {
  #VARIABLE {tempdamage} {};
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^你向铸剑师打听有关『weilan』的消息} {
    #CLASS commonresponseclass KILL;
    #CLASS commonresponseclass OPEN;
    #ACTION {^铸剑师在你的耳边悄声说道：看在你练功那么努力的份上，我就叫韦兰铁匠来吧} {
      #CLASS commonresponseclass KILL;
      da %1
    };
    #ACTION {^铸剑师说道：「韦兰铁匠现在很忙，你还是好好练功，过段时间再来吧} {
      #CLASS commonresponseclass KILL;
      #CLASS commonclass KILL;
      dohalt {%2};
    };
    #ACTION {^铸剑师说道：「我正忙着呢} {
      #CLASS commonresponseclass KILL;
      dohalt {
        ask zhujian shi about weilan;
      };
    };
    #CLASS commonresponseclass CLOSE;
  };
  #ACTION {^韦兰铁匠给你一%*} {
    look %1;
    echo {checkdamage};
  };
  #ACTION {^看起来%*具有%*的威力} {
    #VARIABLE {tempdamage} {%%2};
  };
  #ACTION {^{设定环境变量：action \= \"checkdamage\"|你设定checkdamage为反馈信息}} {
    #IF {@contains{{damagedes}{$tempdamage}} == 0} {
      sell %1;
      ask zhujian shi about weilan;
    };
    #ELSE {
      #CLASS commonclass KILL;
      #SHOWME {<faa>有收获};
      dohalt {%2};
    };
  };
  #CLASS commonclass CLOSE;
  pray pearl;
  ask zhujian shi about weilan;
};
#NOP {加入官府,%1:后续指令};
#ALIAS {joingf} {
  gotodo {大理城} {王府大厅} {joingf_start {%1}};
};
#ALIAS {joingf_start} {
  #CLASS joinclass KILL;
  #CLASS joinresponseclass KILL;
  #CLASS joinclass OPEN;
  #ACTION {^你向傅思归打听有关『join』的消息。} {
    #CLASS joinresponseclass OPEN;
    #ACTION {^傅思归说道} {
      #CLASS joinresponseclass KILL;
      #CLASS joinclass KILL;
      #VARIABLE {env[guanfu]} {1};
      dohalt {joinhg {%1}}
    };
    #CLASS joinclass CLOSE;
  };
  #CLASS joinclass CLOSE;
  ask fu sigui about join
};
#NOP {加入皇宫,%1:后续指令};
#ALIAS {joinhg} {
  gotodo {大理城} {善阐侯府} {joinhg_start {%1}};
};
#ALIAS {joinhg_start} {
  #CLASS joinclass KILL;
  #CLASS joinresponseclass KILL;
  #CLASS joinclass OPEN;
  #ACTION {^你向高升泰打听有关『入皇宫』的消息。} {
    #CLASS joinresponseclass OPEN;
    #ACTION {^高升泰说道} {
      #VARIABLE {env[huanggong]} {1};
      #CLASS joinresponseclass KILL;
      #CLASS joinclass KILL;
      dohalt {%1}
    };
    #CLASS joinresponseclass CLOSE;
  };
  #CLASS joinclass CLOSE;
  ask gao shengtai about 入皇宫
};
#NOP {加入五毒教,%1:后续指令};
#ALIAS {joinwdj} {
  #CLASS joinclass KILL;
  #CLASS joinclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkcond\"|你设定checkcond为反馈信息}} {
    #LOCAL {qty} {0};
    #NOP {按每个活血丹+500精上限计算,发作时间间隔平均20s，每次减少24点精上限};
    #IF {"$hp[condition][蔓陀萝花]" != ""} {
      #MATH {qty} {$hp[condition][蔓陀萝花]*24/20/500};
    };
    #IF {$qty < 4} {
      #LOCAL {qty} {4};
    };
    #IF {$qty > 5} {
      #LOCAL {qty} {5};
    };
    #IF {"$id[things][fire]" == ""} {
      buyfire {joinwdj {%1} {%2}};
    };
    #ELSEIF {@carryqty{huoxue dan} < $qty} {
      buymedicine {huoxue dan} {$qty} {joinwdj {%1} {%2}};
    };
    #ELSE {
      gotonpc {程灵素} {joinwdj_ask {%1} {%2}};
    };
  };
  #CLASS joinclass CLOSE;
  cond;
  echo {checkcond};
};
#NOP {五毒教归还蜘蛛,%1:后续指令};
#ALIAS {feedbackwdj} {
  #IF {@carryqty{fire} == 0} {
    buyfire {feedbackwdj {%1}}
  };
  #ELSE {
    gotodo {苗疆} {山洞} {joinwdj_yao {%1}}
  };
};
#NOP {程灵素对你微微一笑，说道：祝你此行顺利。另外希望你能言而有信};
#NOP {程灵素说道：「你上次答应我的事情还没做，怎麽又来要？ 程灵素皱了皱眉};
#NOP {%1:后续指令};
#ALIAS {joinwdj_ask} {
  #CLASS joinclass OPEN;
  #ACTION {^你向程灵素打听有关『五毒教』的消息。} {
    #CLASS joinresponseclass OPEN;
    #ACTION {^程灵素说道：「五毒教的禁地种满了各种奇花异草} {
      #VARIABLE {env[wudujiao]} {RSP};
      set env_wudujiao RSP
    };
    #ACTION {^程灵素说道：「你上次答应我的事情还没做，怎麽又来要？ 程灵素皱了皱眉} {
    };
    #CLASS joinresponseclass CLOSE;
  };
  #ACTION {^{设定环境变量：action \= \"checkfake\"|你设定checkfake为反馈信息}} {
    #CLASS joinresponseclass KILL;
    yes;
    dohalt {
      fu jiuxuebiyun dan;
      gotodo {苗疆} {山洞} {joinwdj_yao {%1}}
    };
  };
  #CLASS joinclass CLOSE;
  ask cheng lingsu about 五毒教;
  echo {checkfake};
};
#NOP {摇树,%1:后续指令};
#ALIAS {joinwdj_yao} {
  #CLASS joinclass KILL;
  #CLASS joinclass OPEN;
  #ACTION {^你晃动了半天，发现什麽也没有。} {
    #CLASS joinclass KILL;
    #VARIABLE {idle} {0};
    #IF {"$env[wudujiao]" != ""} {
      #NOP {如果是RSP或者YES直接溜，定时过来尝试还账};
      #VARIABLE {env[wdjrsp]} {@now{}};
      #IF {@eavl{$hp[condition][蔓陀萝花]} >= 18000} {
        curepoison {%1};
      };
      #ELSE {
        %1;
      };
    };
    #ELSEIF {$hp[jing_per] < 80 && @carryqty{huoxue dan} == 0} {
      #CLASS joinclass KILL;
      #NOP {回去补充药物继续过来等};
      joinwdj {%1};
    };
    #ELSE {
      #NOP {打坐练功吧};
      closesaving;
      waitlian;
      dzn {joinwdj_yao {%1}};
    };
  };
  #ACTION {^你轻轻摇晃树藤，忽然掉下一只雪蛛。} {
    #CLASS joinclass KILL;
    opensaving;
    joinwdj_fight {%1};
  };
  #CLASS joinclass CLOSE;
  hp;
  i;
  dian fire;
  yao shuteng
};
#NOP {找蜘蛛,%1:后续指令};
#ALIAS {joinwdj_fight} {
  #CLASS joinclass KILL;
  #CLASS joinclass OPEN;
  #ACTION {^{雪蛛「啪」的一声倒在地上|雪蛛突然蹿到地上不见了|你附近没有这样东西}} {
    #CLASS joinclass KILL;
    joinwdj_yao {%1};
  };
  #ACTION {^雪蛛挣扎了几下，一个不稳晕倒过去。} {
    dohalt {
      get xue zhu;
    };
  };
  #ACTION {^你将雪蛛扶了起来背在背上。} {
    #CLASS joinclass KILL;
    openwimpy;
    wwp;
    bei @getFistSkill{};
    gotodo {苗疆} {药王居} {joinwdj_finish {%1}}
  };
  #CLASS joinclass CLOSE;
  closewimpy;
  #IF {$hp[exp] > 800000} {
    uwwp;
    bei none
  };
  #ELSE {
    wwp;
  };
  hit xue zhu;
  jiali max;
};
#NOP {交还蜘蛛,%1:后续指令};
#ALIAS {joinwdj_finish} {
  #CLASS joinclass OPEN;
  #ACTION {^你身上没有这样东西} {
    #CLASS joinclass KILL;
    #VARIABLE {env[wdjrsp]} {@now{}};
    runwait {%1}
  };
  #ACTION {^程灵素对你表示衷心的感谢。} {
    #CLASS joinclass KILL;
    runwait {
      #IF {"$env[wudujiao]" == ""} {
        #NOP {如果没有标识,表明没有吃过真丹,继续要};
        joinwdj {%1} {1};
      };
      #ELSE {
        #VARIABLE {env[wudujiao]} {YES};
        set env_wudujiao;
        %1
      };
    }
  };
  #CLASS joinclass CLOSE;
  give xue zhu to cheng lingsu
};
#NOP {铁掌闹鬼,%1:后续指令};
#ALIAS {tznaogui} {
  #LIST {qqzroomlist} {create} {678;679;677;680;681};
  #VARIABLE {qqzindex} {1};
  gotoroom {$qqzroomlist[+$qqzindex]} {tznaogui_start {%1}};
};
#NOP {铁掌闹鬼问,%1:后续指令};
#ALIAS {tznaogui_start} {
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {你向裘千丈打听有关『闹鬼』的消息} {
    #CLASS commonclass KILL;
    dohalt {
      %1
    };
  };
  #ACTION {^这里没有这个人。} {
    #CLASS commonclass KILL;
    #MATH {qqzindex} {$qqzindex + 1};
    #IF {$qqzindex > &qqzroomlist[]} {
      #VARIABLE {qqzindex} {1};
    };
    runwait {
      gotoroom {$qqzroomlist[+$qqzindex]} {tznaogui_start {%1}};
    }
  };
  #CLASS commonclass CLOSE;
  ask qiu qianzhang about 闹鬼;
};
#NOP {吃饭,%1:后续指令};
#ALIAS {gofood} {
  gotodo {大理城} {茶馆} {gowater_start {%1}};
};
#NOP {开始吃喝};
#ALIAS {gowater_start} {
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkwater\"|你设定checkwater为反馈信息}} {
    #VARIABLE {idle} {0};
    #DELAY {1} {
      #IF {$hp[water] < 100} {
        #8 drink;
        hp;
        echo {checkwater};
      };
      #ELSE {
        #CLASS commonclass KILL;
        gotodo {大理城} {风味小吃店} {gofood_start {%1}}
      };
    };
  };
  #CLASS commonclass CLOSE;
  #8 drink;
  hp;
  echo {checkwater};
};
#NOP {开始吃喝};
#ALIAS {gofood_start} {
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkfood\"|你设定checkfood为反馈信息}} {
    #VARIABLE {idle} {0};
    #DELAY {1} {
      #IF {@carryqty{gold} < 2} {
        #CLASS commonclass KILL;
        gotodo {大理城} {大理钱庄} {balanceex {2} {} {gotodo {大理城} {风味小吃店} {gofood_start {%1}}}};
      };
      #ELSEIF {$hp[food] < 50} {
        buy dong;
        #10 eat dong;
        drop dong;
        hp;
        echo {checkfood};
      };
      #ELSE {
        #CLASS commonclass KILL;
        loc {%1}
      };
    };
  };
  #CLASS commonclass CLOSE;
  i;
  echo {checkfood};
};
#NOP {是否需要补货，%1:药品名称};
#FUNCTION isNeedReplenish {
  #LOCAL {qty} {};
  #IF {"$conf[medicine][%1][carry]" != ""} {
    #LOCAL {qty} {@eval{$conf[medicine][%1][carry]}};
  };
  #ELSE {
    #LOCAL {qty} {@eval{$common[medicine][%1]}};
  };
  #IF {@carryqty{%1} < $qty} {
    #RETURN {1};
  };
  #ELSE {
    #RETURN {0};
  };
};
#NOP {补货指定药品，%1:药品名称，%2:后续指令};
#ALIAS {replenishmedicine_cd} {
  #LOCAL {qty} {};
  #IF {"$conf[medicine][%1][carry]" != ""} {
    #LOCAL {qty} {@eval{$conf[medicine][%1][carry]}};
  };
  #ELSE {
    #LOCAL {qty} {@eval{$common[medicine][%1]}};
  };
  #IF {$qty <= 0} {
    #LOCAL {qty} {1};
  };
  #IF {@carryqty{%1} < $qty} {
    buymedicine_cd {%1} {$qty} {%2}
  };
  #ELSE {
    %2
  };
};
#NOP {补货指定药品，%1:药品名称，%2:后续指令};
#ALIAS {replenishmedicine_dl} {
  #LOCAL {qty} {};
  #IF {"$conf[medicine][%1][carry]" != ""} {
    #LOCAL {qty} {@eval{$conf[medicine][%1][carry]}};
  };
  #ELSE {
    #LOCAL {qty} {@eval{$common[medicine][%1]}};
  };
  #IF {$qty <= 0} {
    #LOCAL {qty} {1};
  };
  #IF {@carryqty{%1} < $qty} {
    buymedicine_dl {%1} {$qty} {%2}
  };
  #ELSE {
    %2
  };
};
#NOP {买药疗精，%1:后续指令};
#ALIAS {buyfulljing} {
  #VARIABLE {medicine} {huoxue dan};
  #IF {$hp[neili_max] < 2000} {
    #VARIABLE {medicine} {liaojing dan};
  };
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkhp\"|你设定checkhp为反馈信息}} {
    #CLASS commonclass KILL;
    #VARIABLE {idle} {0};
    #DELAY {1} {
      #IF {$hp[jing_per] < 90} {
        buyfulljing {%1}
      };
      #ELSE {
        %1
      };
    };
  };
  #CLASS commonclass CLOSE;
  #IF {@carryqty{$medicine} == 0} {
    #CLASS commonclass KILL;
    buymedicine {$medicine} {2} {buyfulljing {%1}};
  };
  #ELSE {
    fu $medicine;
    yun jing;
    i;
    hp;
    echo {checkhp}
  };
};
#NOP {买药疗伤，%1:后续指令};
#ALIAS {buyfullqi} {
  #VARIABLE {medicine} {chantui yao};
  #IF {$hp[neili_max] < 2000} {
    #VARIABLE {medicine} {jinchuang yao};
  };
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkhp\"|你设定checkhp为反馈信息}} {
    #CLASS commonclass KILL;
    #VARIABLE {idle} {0};
    #DELAY {1} {
      #IF {$hp[qi_per] < 90} {
        buyfullqi {%1}
      };
      #ELSE {
        %1
      };
    };
  };
  #CLASS commonclass CLOSE;
  #IF {@carryqty{$medicine} == 0} {
    #CLASS commonclass KILL;
    buymedicine {$medicine} {2} {buyfullqi {%1}};
  };
  #ELSE {
    fu $medicine;
    yun qi;
    i;
    hp;
    echo {checkhp}
  };
};
#NOP {购买药物,%1:药物,%2:数量,%3:后续指令};
#ALIAS {buymedicine} {
  #IF {@contains{{common[raredrugs]}{%1}} > 0} {
    tbbuy {%1} {%2} {%3};
  };
  #ELSEIF {@contains{{common[normaldrugs]}{%1}} > 0} {
    #IF {@rnd{{0}{1}} == 0} {
      buymedicine_cd {%1} {%2} {%3};
    };
    #ELSE {
      buymedicine_dl {%1} {%2} {%3};
    };
  };
  #ELSE {
    %3
  };
};
#NOP {去成都买};
#ALIAS {buymedicine_cd} {
  gotodo {成都城} {药店} {buymedicine_cd_start {%1} {%2} {%3}}
};
#NOP {去大理买};
#ALIAS {buymedicine_dl} {
  gotodo {大理城} {药铺} {buymedicine_dl_start {%1} {%2} {%3}}
};
#NOP {执行买药,%1:药物,%2:数量,%3:后续指令};
#ALIAS {buymedicine_cd_start} {
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkmedicine\"|你设定checkmedicine为反馈信息}} {
    #VARIABLE {idle} {0};
    #DELAY {1} {
      #IF {@carryqty{%1} >= %2} {
        #CLASS commonclass KILL;
        %3
      };
      #ELSEIF {@carryqty{gold} < 10} {
        #CLASS commonclass KILL;
        gotodo {成都城} {墨玉斋} {
          balanceex {20} {} {buymedicine_cd {%1} {%2} {%3}};
        };
      };
      #ELSE {
        buy %1;
        i;
        echo {checkmedicine};
      };
    };
  };
  #CLASS commonclass CLOSE;
  buy %1;
  i;
  echo {checkmedicine};
};
#NOP {执行买药,%1:药物,%2:数量,%3:后续指令};
#ALIAS {buymedicine_dl_start} {
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkmedicine\"|你设定checkmedicine为反馈信息}} {
    #VARIABLE {idle} {0};
    #DELAY {1} {
      #IF {@carryqty{%1} >= %2} {
        #CLASS commonclass KILL;
        %3
      };
      #ELSEIF {@carryqty{gold} < 10} {
        #CLASS commonclass KILL;
        gotodo {大理城} {大理钱庄} {
          balanceex {20} {} {buymedicine_dl {%1} {%2} {%3}};
        };
      };
      #ELSE {
        buy %1;
        i;
        echo {checkmedicine};
      };
    };
  };
  #CLASS commonclass CLOSE;
  buy %1;
  i;
  echo {checkmedicine};
};
#NOP {去获取药物,%1:后续指令};
#ALIAS {godrugdealer} {
  gotoroom {$conf[drugdealer][room]} {godrugdealer_start {%1}}
};
#ALIAS {godrugdealer_start} {
  #VARIABLE {okflag} {0};
  #VARIABLE {dealts} {@now{}};
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^你要对谁做这个动作} {
    #CLASS commonclass KILL;
    %1
  };
  #ACTION {^你双手抱拳，对%*作了个揖道} {
    tell $conf[drugdealer][id] drugs_zhengqi dan_10;
    echo {checkdeal}
  };
  #ACTION {^%*痛快地对你说道：好吧！} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkdeal\"|你设定checkdeal为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {$okflag == 1} {
      #CLASS commonclass KILL;
      %1
    };
    #ELSEIF {@elapsed{$dealts} > 3} {
      #CLASS commonclass KILL;
      %1
    };
    #ELSE {
      #DELAY {1} {
        i;
        echo {checkdeal}
      }
    };
  };
  #CLASS commonclass CLOSE;
  hi $conf[drugdealer][id]
};
#NOP {转为正神,%1:目标值,%2:后续指令};
#ALIAS {gozshen} {
  #IF {"$hp[shen]" == "正气" && $hp[shen_num] >= @eval{%1}} {
    %2
  };
  #ELSEIF {"$hp[shen]" == "戾气" && $hp[shen_num] > 30000} {
    reduce_fs_dy {100} {gozshen {%1} {%2}}
  };
  #ELSE {
    improve_zs_eat {%1} {gozshen {%1} {%2}}
  };
};
#NOP {狄云面壁降负神%1:降到多少负神，%2:后续指令};
#ALIAS {reduce_fs_dy} {
  gotonpc {狄云} {reduce_fs_dy_start {%1} {%2}}
};
#ALIAS {reduce_fs_dy_start} {
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkshen\"|你设定checkshen为反馈信息}} {
    #VARIABLE {idle} {0};
    #DELAY {1} {
      #IF {"$hp[shen]" == "正气" || ("$hp[shen]" == "戾气" && $hp[shen_num] <= %1)} {
        #CLASS commonclass KILL;
        dohalt {loc {%2}}
      };
      #ELSE {
        #10 mianbi;
        hp;
        echo {checkshen};
      };
    };
  };
  #CLASS commonclass CLOSE;
  echo {checkshen};
};
#NOP {吃正气丹增加正神,%1:目标值,%2:后续指令,每颗按7000计算预期需要的药物数量};
#ALIAS {improve_zs_eat} {
  #LOCAL {diff_shen} {%1};
  #IF {"$hp[shen]" == "戾气"} {
    #LOCAL {diff_shen} {@eval{$diff_shen + $hp[shen_num]}};
  };
  #LOCAL {mcount} {@eval{$diff_shen / 7000 + 1 - @carryqty{zhengqi dan}}};
  #LOCAL {mgold} {@eval{$mcount * 3} - @carryqty{gold}};
  #IF {$mgold > 0} {
    localwithdraw {@eval{$mcount * 3}} {} {improve_zs_eat {%1} {%2}}
  };
  #ELSEIF {$mcount > 0} {
    #IF {@rnd{{0}{1}} == 0} {
      gotodo {成都城} {药店} {improve_zs_buyeat_start {%1} {%2}}
    };
    #ELSE {
      gotodo {大理城} {药铺} {improve_zs_buyeat_start {%1} {%2}}
    };
  };
  #ELSE {
    improve_zs_eat_start {%1} {%2}
  };
};
#NOP {吃正气丹增加神,%1:目标数值,%2:后续指令};
#ALIAS {improve_zs_eat_start} {
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkshen\"|你设定checkshen为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {("$hp[shen]" == "正气" && $hp[shen_num] >= %1) || @carryqty{zhengqi dan} == 0} {
      #CLASS commonclass KILL;
      %2
    };
    #ELSEIF {"$hp[shen]" == "正气" && @eval{%1 - $hp[shen_num]} < 2000} {
      #CLASS commonclass KILL;
      improve_zs_ketou {%1} {%2}
    };
    #ELSE {
      fu zhengqi dan;
      i;
      hp;
      #DELAY {1} {echo {checkshen}}
    };
  };
  #CLASS commonclass CLOSE;
  echo {checkshen};
};
#NOP {买正气丹增加神,%1:目标数值,%2:后续指令};
#ALIAS {improve_zs_buyeat_start} {
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkshen\"|你设定checkshen为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {"$hp[shen]" == "正气" && $hp[shen_num] >= %1} {
      #CLASS commonclass KILL;
      %2
    };
    #ELSEIF {@carryqty{zhengqi dan} == 0 && @carryqty{gold} < 2} {
      localwithdraw {20} {} {gotoroom {$roomid} {improve_zs_buyeat_start {%1} {%2}}}
    };
    #ELSEIF {"$hp[shen]" == "正气" && @eval{%1 - $hp[shen_num]} < 2000} {
      #CLASS commonclass KILL;
      improve_zs_ketou {%1} {%2}
    };
    #ELSE {
      #IF {@carryqty{zhengqi dan} == 0} {
        buy zhengqi dan
      };
      fu zhengqi dan;
      i;
      hp;
      #DELAY {1} {echo {checkshen}}
    };
  };
  #CLASS commonclass CLOSE;
  echo {checkshen};
};
#NOP {武当磕头增加正神,%1:目标神,%2:后续指令};
#ALIAS {improve_zs_ketou} {
  #LOCAL {diff_shen} {0};
  #IF {"$hp[shen]" == "戾气"} {
    #LOCAL {diff_shen} {@eval{%1 + $hp[shen_num]}};
  };
  #ELSE {
    #LOCAL {diff_shen} {@eval{%1 - $hp[shen_num]}};
  };
  #NOP {平均按300一次算};
  #LOCAL {mgold} {@eval{$diff_shen / 300 + 1}};
  #IF {$mgold <= @carryqty{gold}} {
    gotodo {武当山} {遇真宫} {improve_zs_ketou_start {%1} {%2}}
  };
  #ELSE {
    localwithdraw {$mgold} {} {improve_zs_ketou {%1} {%2}}
  };
};
#ALIAS {improve_zs_ketou_start} {
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkshen\"|你设定checkshen为反馈信息}} {
    #LOCAL {diff_shen} {0};
    #IF {"$hp[shen]" == "戾气"} {
      #LOCAL {diff_shen} {@eval{%1 + $hp[shen_num]}};
    };
    #ELSE {
      #LOCAL {diff_shen} {@eval{%1 - $hp[shen_num]}};
    };
    #NOP {平均按500一次算};
    #LOCAL {mcount} {@eval{$diff_shen / 500 + 1}};
    #IF {$mcount > 5} {
      #LOCAL {mcount} {5}
    };
    #IF {@carryqty{gold} <= 0} {
      #CLASS commonclass KILL;
      %2
    };
    #ELSEIF {"$hp[shen]" == "正气" && ($hp[shen_num] > %1 || $hp[shen_num] > 80000)} {
      #CLASS commonclass KILL;
      %2
    };
    #ELSE {
      #$mcount ketou xiang;
      i;
      hp;
      #DELAY {0.5} {echo {checkshen}}
    };
  };
  #CLASS commonclass CLOSE;
  hp;
  echo {checkshen}
};
#NOP {是否可快速降为负神};
#FUNCTION canQuickFushen {
  #IF {@eval{$kungfu[spec][jiuyin-baiguzhua][lv]} >= 120 && @eval{$kungfu[base][claw][lv]} >= 500} {
    #RETURN {1};
  };
  #ELSE {
    #RETURN {0};
  };
};
#NOP {转为负神,%1:目标值,%2:后续指令};
#ALIAS {gofshen} {
  #NOP {计算差值};
  #LOCAL {diff_shen} {0};
  #IF {"$hp[shen]" == "正气"} {
    #LOCAL {diff_shen} {@eval{%1 + $hp[shen_num]}};
  };
  #ELSE {
    #LOCAL {diff_shen} {@eval{%1 - $hp[shen_num]}};
  };
  #IF {"$hp[shen]" == "戾气" && $hp[shen_num] >= @eval{%1}} {
    %2
  };
  #ELSEIF {$diff_shen > 200000} {
    improve_fs_hat {%1} {gofshen {%1} {%2}}
  };
  #ELSEIF {@canQuickFushen{} == 1} {
    improve_fs_skill {%1} {gofshen {%1} {%2}}
  };
  #ELSEIF {$diff_shen > 30000} {
    reduce_zs_wp {%1} {gofshen {%1} {%2}}
  };
  #ELSE {
    improve_fs_eat {%1} {%2}
  };
};
#NOP {华山玄坛庙给钱降神，%1:目标负神，%2:后续指令。1gold降低random(5000)，计算下携带的钱};
#ALIAS {reduce_zs_wp} {
  #IF {"$hp[shen]" == "戾气" && $hp[shen_num] >= %2} {
    %2
  };
  #ELSE {
    #NOP {计算需要给的gold};
    #LOCAL {costgold} {0};
    #IF {"$hp[shen]" == "正气"} {
      #MATH {costgold} {($hp[shen_num] + %1) / 2000};
    };
    #ELSE {
      #MATH {costgold} {(%1 - $hp[shen_num]) / 2000};
    };
    #LOCAL {costgold} {@eval{$costgold * 2}};
    #IF {$costgold > @carryqty{gold}} {
      localwithdraw {$costgold} {} {reduce_zs_wp {%1} {%2}}
    };
    #ELSE {
      gotodo {华山村} {玄坛庙} {reduce_zs_wp_start {%1} {%2}}
    };
  };
};
#NOP {华山巫婆诅咒，%1:目标神，%2:后续指令};
#ALIAS {reduce_zs_wp_start} {
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkshen\"|你设定checkshen为反馈信息}} {
    #CLASS commonclass KILL;
    #IF {"$hp[shen]" ==  "戾气"} {
      dohalt {%2};
    };
    #ELSE {
      reduce_zs_wp_start {%1} {%2}
    };
  };
  #CLASS commonclass CLOSE;
  #NOP {计算需要给的gold};
  #LOCAL {costgold} {0};
  #IF {"$hp[shen]" == "正气"} {
    #MATH {costgold} {($hp[shen_num] + %1) / 2000};
  };
  #ELSE {
    #MATH {costgold} {(%1 - $hp[shen_num]) / 2000};
  };
  #IF {$costgold > @carryqty{gold}} {
    #CLASS commonclass KILL;
    %2
  };
  #ELSE {
    #IF {$costgold < 2} {
      #LOCAL {costgold} {2};
    };
    ask wu po about 诅咒;
    dohalt {
      give $costgold gold to wu po;
      hp;
      i;
      cond;
      echo {checkshen}
    };
  };  
};
#NOP {增加负神,%1:目标值,%2:后续指令};
#ALIAS {improve_fs_hat} {
  #IF {@carryqty{zhen} > 0} {
    gotonpc {洪安通} {improve_fs_hat_start {%1} {%2}}
  };
  #ELSE {
    buynormalweapon {zhen} {improve_fs_hat {%1} {%2}};
  };
};
#NOP {增加负神,%1:目标值,%2:后续指令};
#ALIAS {improve_fs_hat_start} {
  #VARIABLE {okflag} {0};
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^你身上没有这样东西。} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkshen\"|你设定checkshen为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {"$hp[shen]" == "正气"} {
      #LOCAL {diff_shen} {@eval{%1 + $hp[shen_num]}};
    };
    #ELSE {
      #LOCAL {diff_shen} {@eval{%1 - $hp[shen_num]}};
    };
    #IF {$okflag == 1 || ("$hp[shen]" == "戾气" && $hp[shen_num] >= %1)} {
      #CLASS commonclass KILL;
      %2
    };
    #ELSE {
      #LOCAL {gcount} {@eval{$diff_shen / 100000 + 1}};
      #IF {$gcount > 5} {
        #LOCAL {gcount} {5}
      };
      #$gcount give 1 zhen to hong;
      i;
      hp;
      #DELAY {0.5} {echo {checkshen}}
    };
  };
  #CLASS commonclass CLOSE;
  echo {checkshen};
};
#NOP {通过技能提高负神,%1:目标值,%2:后续指令};
#ALIAS {improve_fs_skill} {
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkskill\"|你设定checkskill为反馈信息}} {
    #LOCAL {diff_lv} {@eval{$kungfu[spec][jiuyin-baiguzhua][lv] - 120}};
    #IF {$diff_lv > 0} {
      fangqi jiuyin-baiguzhua $diff_lv;
      #SEND {y};
      skills;
      dohalt {echo {checkshen}}
    };
    #ELSE {
      echo {checkshen}
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkshen\"|你设定checkshen为反馈信息}} {
    #VARIABLE {ignore_levelup} {0};
    #IF {"$hp[shen]" == "戾气" && $hp[shen_num] >= %1} {
      #CLASS commonclass KILL;
      prepareforce;
      %2
    };
    #ELSE {
      #NOP {计算要达到指定的神需要升几级，练习多少次};
      bei none;
      jifa claw jiuyin-baiguzhua;
      bei claw;
      #LOCAL {diff_shen} {0};
      #IF {"$hp[shen]" == "正气"} {
        #LOCAL {diff_shen} {@eval{%1 + $hp[shen_num]}};
      };
      #ELSE {
        #LOCAL {diff_shen} {@eval{%1 - $hp[shen_num]}};
      };
      #NOP {需要练习的熟练点数};
      #LOCAL {expoints} {0};
      #LOOP {121} {200} {lv} {
        #LOCAL {diff_shen} {@eval{$diff_shen - $lv * 100}};
        #LOCAL {expoints} {@eval{$expoints + $lv**2 + 1}};
        #IF {$diff_shen < 0} {
          #BREAK;
        };
      };
      #LOCAL {expoints} {@eval{$expoints - $kungfu[spec][jiuyin-baiguzhua][point]}};
      
      #NOP {需要练习的次数};
      #LOCAL {expone} {@eval{$kungfu[base][claw][lv]/5 + 1}};
      #LOCAL {ptimes} {@eval{$expoints / $expone + 1}};
      #IF {$expoints < 0} {
        #LOCAL {ptimes} {1}
      };
      #LOCAL {maxliantimes} {@eval{$hp[jingli_max]/ 40}};
      #IF {$maxliantimes > 100} {
        #LOCAL {maxliantimes} {@eval{$maxliantimes/100*100}};
      };
      #ELSE {
        #LOCAL {maxliantimes} {@eval{$maxliantimes/10*10}};
      };
      #NOP {组织练习的指令};
      #WHILE {$ptimes > 0} {
        #IF {$ptimes > $maxliantimes} {
          addcmd {lian claw $maxliantimes};
        };
        #ELSE {
          addcmd {lian claw $ptimes};
        };
        #LOCAL {ptimes} {@eval{$ptimes - $maxliantimes}};
        addcmd {yun jingli};
        printvar cmdqueue;
        #IF {@cmdqueue[] > 8} {
          #BREAK;
        };
      };
      addcmd {hp};
      addcmd {skills};
      #VARIABLE {ignore_levelup} {1};
      uwwp;
      execute;
      #DELAY {0.5} {echo {checkshen}};
    };
  };
  #CLASS commonclass CLOSE;
  skills;
  dohalt {echo {checkskill}}
};
#NOP {吃邪气丹增加负神,%1:目标值,%2:后续指令,每颗按7000计算预期需要的药物数量};
#ALIAS {improve_fs_eat} {
  #LOCAL {diff_shen} {%1};
  #IF {"$hp[shen]" == "正气"} {
    #LOCAL {diff_shen} {@eval{$diff_shen + $hp[shen_num]}};
  };
  #LOCAL {mcount} {@eval{$diff_shen / 7000 + 1 - @carryqty{zhengqi dan}}};
  #LOCAL {mgold} {@eval{$mcount * 3} - @carryqty{gold}};
  #IF {$mgold > 0} {
    localwithdraw {@eval{$mcount * 3}} {} {improve_fs_eat {%1} {%2}}
  };
  #ELSEIF {$mcount > 0} {
    #IF {@rnd{{0}{1}} == 0} {
      gotodo {成都城} {药店} {improve_fs_buyeat_start {%1} {%2}}
    };
    #ELSE {
      gotodo {大理城} {药铺} {improve_fs_buyeat_start {%1} {%2}}
    };
  };
  #ELSE {
    improve_fs_eat_start {%1} {%2}
  };
};
#NOP {吃邪气丸增加负神,%1:目标数值,%2:后续指令};
#ALIAS {improve_fs_eat_start} {
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkshen\"|你设定checkshen为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {"$hp[shen]" == "戾气" && $hp[shen_num] >= %1 || @carryqty{xieqi wan} == 0} {
      #CLASS commonclass KILL;
      %2
    };
    #ELSE {
      #DELAY {1} {
        fu xieqi wan;
        i;
        hp;
        echo {checkshen};
      }
    };
  };
  #CLASS commonclass CLOSE;
  echo {checkshen};
};
#NOP {买邪气丸增加负神,%1:目标数值,%2:后续指令};
#ALIAS {improve_fs_buyeat_start} {
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkshen\"|你设定checkshen为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {"$hp[shen]" == "戾气" && $hp[shen_num] >= %1} {
      #CLASS commonclass KILL;
      %2
    };
    #ELSEIF {@carryqty{xieqi wan} == 0 && @carryqty{gold} < 2} {
      localwithdraw {20} {} {gotoroom {$roomid} {improve_fs_buyeat_start {%1} {%2}}}
    };
    #ELSE {
      #IF {@carryqty{xieqi wan} == 0} {
        buy xieqi wan;
      };
      #DELAY {1} {
        fu xieqi wan;
        i;
        hp;
        echo {checkshen}
      }
    };
  };
  #CLASS commonclass CLOSE;
  echo {checkshen};
};
#NOP {蛤蟆功驱毒,%1:后续指令};
#ALIAS {hmgqudu} {
  #IF {"$kungfu[base][force][jifa]" != "hamagong"} {
    #IF {@carryqty{chuanbei wan} == 0} {
      buymedicine {chuanbei wan} {2} {hmgqudu {%1}}
    };
    #ELSE {
      dohalt {
        jifa force hamagong;
        jifa;
        fu chuanbei wan;
        hmgqudu_do {%1}
      };
    };
  };
  #ELSE {
    hmgqudu_do {%1}
  };
};
#NOP {蛤蟆功驱毒,%1:后续指令};
#ALIAS {hmgqudu_do} {
  #VARIABLE {checkresult} {0};
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^你气息不匀，暂时不能施用内功} {
    #VARIABLE {checkresult} {1};
  };
  #ACTION {^你{倒运气息，头下脚上，气血逆行，将毒气从进入身子之处逼了出去|深深吸了口气，口中“咕咕。。。”地叫了几声}} {
    #CLASS commonclass KILL;
    dohalt {%1};
  };
  #ACTION {^你的{真气不够|当前内力不够}} {
    #VARIABLE {checkresult} {2};
  };
  #ACTION {^你所用的内功中没有这种功能} {
    #VARIABLE {checkresult} {3};
  };
  #ACTION {^{设定环境变量：action \= \"checkqudu\"|你设定checkqudu为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {$checkresult == 1} {
      #CLASS commonclass KILL;
      #DELAY {2} {hmgqudu_do {%1}}
    };
    #ELSEIF {$checkresult == 2} {
      #CLASS commonclass KILL;
      #IF {@carryqty{chuanbei wan} > 0} {
        fu chuanbei wan;
        i;
        #DELAY {2} {hmgqudu_do {%1}}
      };
      #ELSEIF {@carryqty{dahuan dan} > 0 || @carryqty{da huandan} > 0} {
        fu dahuan dan;
        i;
        #DELAY {2} {hmgqudu_do {%1}}
      };
      #ELSEIF {"$hp[condition][化骨绵掌]" != ""} {
        waitnegative {%1}
      };
      #ELSE {
        startfull {hmgqudu_do {%1}} {1}
      };
    };
    #ELSEIF {$checkresult == 3} {
      #CLASS commonclass KILL;
      %1
    };
  };
  #CLASS commonclass CLOSE;
  dohalt {
    yun qudu;
    echo {checkqudu};
  }
};
#NOP {武当驱毒};
#ALIAS {wdqudu} {
  #VARIABLE {checkresult} {0};
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^你气息不匀，暂时不能施用内功} {
    #VARIABLE {checkresult} {1};
  };
  #ACTION {^你的真气不够} {
    #VARIABLE {checkresult} {2};
  };
  #ACTION {^你所用的内功中没有这种功能} {
    #VARIABLE {checkresult} {3};
  };
  #ACTION {^你坐在一旁，慢慢以真气通走三关，鼓荡丹田中的「氤氲紫气」，将体内的蔓陀萝花毒一丝丝的化掉。} {
    #VARIABLE {idle} {0};
  };
  #ACTION {^你{将内力循环一周，身子如灌甘露|并没有中毒}} {
    dohalt {%1}
  };
  #ACTION {^{设定环境变量：action \= \"checkqudu\"|你设定checkqudu为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {$checkresult == 1} {
      #DELAY {2} {
        yun qudu;
        echo {checkqudu};
      };
    };
    #ELSEIF {$checkresult == 2} {
      fu chuanbei wan;
      #DELAY {1} {
        yun qudu;
        echo {checkqudu};
      };
    };
    #ELSEIF {$checkresult == 3} {
      #CLASS commonclass KILL;
      %1
    };
  };
  #CLASS commonclass CLOSE;
  yun qudu;
  echo {checkqudu};
};
#NOP {抗毒,%1:毒,%2:后续操作};
#NOP {基本上也就是星宿毒需要关注下，星宿毒时间太长会暴毙。};
#ALIAS {antipoison} {
  #NOP {对于危险的星宿毒，如果通宝够多且时间很长考虑直接吃冰蟾};
  #IF {"%1" == "星宿掌" && @eval{$hp[condition][%1]} > 1200 && $hp[tongbao] > 10000} {
    curepoison {%2}
  };
  #ELSE {
    gotodo {大理城} {药铺} {antipoisonstart {%1} {%2}}
  };
};
#NOP {吃药硬抗毒,%1:毒,%2:后续指令};
#ALIAS {antipoisonstart} {
  #VARIABLE {attackflag} {0};
  #VARIABLE {okflag} {0};
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^{忽然一阵刺骨的奇寒袭来|忽然一股寒气犹似冰箭|你忽然感到身体僵直|你突然觉得一股恶臭从腹中升起}} {
    #VARIABLE {attackflag} {1};
  } {1};
  #ACTION {^你服下一颗活血疗精丹，顿时感觉精血不再流失} {
    #VARIABLE {okflag} {1};
  } {1};
  #ACTION {^{设定环境变量：action \= \"checkjing\"|你设定checkjing为反馈信息}} {
    #VARIABLE {idle} {0};
    #DELAY {1} {
      #IF {@carryqty{huoxue dan} == 0} {
        buy huoxue dan;
        i;
        echo {checkjing}
      };
      #ELSEIF {$hp[jing_per] < 80} {
        #VARIABLE {okflag} {0};
        fu huoxue dan;
        yun jing;
        echo {checkfu}
      };
      #ELSE {
        dohalt {
          #VARIABLE {attackflag} {0};
          yun jing;
          yun qi;
          yun heal;
          echo {checkcondition}
        };
      };
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkfu\"|你设定checkfu为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {$okflag == 0} {
      #DELAY {1} {
        fu huoxue dan;
        echo {checkfu}
      };
    };
    #ELSE {
      dohalt {
        #VARIABLE {attackflag} {0};
        buy huoxue dan;
        yun jing;
        yun qi;
        yun heal;
        echo {checkcondition}
      };
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkcondition\"|你设定checkcondition为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {"$hp[condition][%1]" == ""} {
      #CLASS commonclass KILL;
      dohalt {
        doheal {%2};
      };
    };
    #ELSEIF {@carryqty{gold} < 10} {
      #CLASS commonclass KILL;
      gotodo {大理城} {大理钱庄} {
        balanceex {100} {} {antipoison {%1} {%2}};
      };
    };
    #ELSEIF {$hp[neili] < 500} {
      #IF {@carryqty{chuanbei wan} == 0} {
        buy chuanbei wan;
      };
      #ELSE {
        fu chuanbei wan;
      };
      #DELAY {1} {
        i;
        hp;
        echo {checkcondition}
      };
    };
    #ELSEIF {$attackflag == 1} {
      hp;
      echo {checkjing}
    };
    #ELSE {
      #DELAY {1} {
        cond;
        i;
        hp;
        echo {checkcondition}
      };
    };
  };
  #CLASS commonclass CLOSE;
  fu huoxue dan;
  cond;
  i;
  hp;
  echo {checkcondition}
};
#NOP {治疗怪蛇毒,%1:后续指令};
#ALIAS {healguaishe} {
  #IF {@carryqty{dahuan dan} > 0 || @carryqty{da huandan} > 0} {
    healguaishe_start {%1}
  };
  #ELSE {
    tbbuy {da huandan} {1} {healguaishe {%1}}
  };
};
#NOP {吃大还丹硬抗};
#ALIAS {healguaishe_start} {
  #VARIABLE {gs_lastwound} {0};
  #VARIABLE {gs_tickwound} {0};
  #CLASS commonclass OPEN;
  #CLASS commonclass KILL;
  #ACTION {你忽然感到身体僵直，已经} {
    fear
  } {1};
  #ACTION {^{设定环境变量：action \= \"checkwound\"|你设定checkwound为反馈信息}} {
    resonate {checkwound};
    #VARIABLE {idle} {0};
    #IF {$gs_tickwound == 0} {
      #IF {$gs_lastwound == 0 || $gs_lastwound <= $hp[jing_per]} {
        #VARIABLE {gs_lastwound} {$hp[jing_per]};
      };
      #ELSE {
        #VARIABLE {gs_tickwound} {@eval{$gs_lastwound - $hp[jing_per]}};
      };
    };
    #IF {"$hp[condition][怪蛇]" == ""} {
      #CLASS commonclass KILL;
      logbuff {guaishe};
      %1
    };
    #ELSEIF {"$hp[condition][闭气]" == "" && @getSkillLevel{hamagong} > 200} {
      #CLASS commonclass KILL;
      #NOP {没有闭气的情况下蛤蟆功可以自行驱毒};
      #IF {$hp[jing_per] < 50} {
        fu dahuan dan;
        i;
        hp;
      };
      hmgqudu {%1}
    };
    #ELSEIF {@carryqty{da huandan} == 0 && @carryqty{dahuan dan} == 0} {
      #CLASS commonclass KILL;
      healguaishe {%1}
    };
    #ELSEIF {$gs_tickwound == 0 && $hp[jing_per] < 50} {
      dohalt {
        fu dahuan dan;
        i;
        hp;
        cond;
        #DELAY {0.5} {echo {checkwound}};
      };
    };
    #ELSEIF {$gs_tickwound > 0 && $hp[jing_per] < @eval{$gs_tickwound + 5}} {
      dohalt {
        fu dahuan dan;
        i;
        hp;
        cond;
        #DELAY {0.5} {echo {checkwound}};
      };
    };
    #ELSE {
      #DELAY {2} {
        hp;
        cond;
        echo {checkwound}
      }
    };
  };
  #CLASS commonclass CLOSE;
  ensure {hp;cond} {checkwound}
};
#NOP {吃药疗毒,%1:毒,%2:后续指令};
#ALIAS {healpoison} {
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checktianqi\"|你设定checktianqi为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {"$hp[condition][%1]" == "" || @carryqty{tianqi} == 0} {
      #CLASS commonclass KILL;
      %2;
    };
    #ELSE {
      dohalt {
        eat tianqi;
        i;
        cond;
        #DELAY {0.5} {echo {checktianqi}};
      };
    };
  };
  #CLASS commonclass CLOSE;
  #DELAY {0.5} {echo {checktianqi}};
};
#ALIAS {healpoison_start} {
  #VARIABLE {ooq} {0};
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^你并没有保存该物品} {
    #VARIABLE {ooq} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checktianqi\"|你设定checktianqi为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {$ooq == 1} {
      #CLASS commonclass KILL;
      %1;
    };
    #ELSEIF {@carryqty{tianqi} == 0} {
      #DELAY {1} {
        dohalt {
          qu tianqi;
          i;
          echo {checktianqi};
        };
      };
    };
    #ELSE {
      dohalt {
        eat tianqi;
        i;
        cond;
        echo {checkcond};
      };
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkcond\"|你设定checkcond为反馈信息}} {
    #VARIABLE {idle} {0};
    #CLASS commonclass KILL;
    %1;
  };
  #CLASS commonclass CLOSE;
  qu tianqi;
  i;
  echo {checktianqi};
};
#NOP {通宝吃药疗毒,%1:后续指令};
#ALIAS {curepoison} {
  gotodo {扬州城} {当铺} {curepoison_start {%1}}
};
#ALIAS {curepoison_start} {
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkbingchan\"|你设定checkbingchan为反馈信息}} {
    #DELAY {1} {
      dohalt {
        #IF {@carryqty{bingchan} == 0} {
          duihuan bingchan;
          i;
          echo {checkbingchan};
        };
        #ELSE {
          #CLASS commonclass KILL;
          xidu;
          %1;
        };
      };
    };
  };
  #CLASS commonclass CLOSE;
  echo {checkbingchan};
};
#NOP {古墓疗毒,%1:后续指令};
#ALIAS {gmpoison} {
  gotoroom {3163} {gmpoison_start {%1}}
};
#NOP {古墓疗毒};
#ALIAS {gmpoison_start} {
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^你然只觉一阵奇寒侵入身体，想是两次疗伤时隔太短，身子尚未复原之故} {
    #LINE ONESHOT #ACTION {^一股暖流发自丹田流向全身，慢慢地你又恢复了知觉} {
      #CLASS commonclass KILL;
      #VARIABLE {env[gmbed]} {@now{}};
      jobcheck
    } {1}
  };
  #ACTION {^不一会儿，你只觉得出了一身大汗，毒伤已然减轻不少} {
    #CLASS commonclass KILL;
    #VARIABLE {env[gmbed]} {@now{}};
    dohalt {%1}
  };
  #ACTION {^你好象没有中毒吧} {
    #CLASS commonclass KILL;
    dohalt {%1}
  };
  #CLASS commonclass CLOSE;
  dohalt {liaodu bed}
};
#NOP {嗑药抗怪蛇毒};
#ALIAS {drug_guaishe} {
};
#NOP {是否有负面状态，%1-指定的状态名称，可以为列表用分号隔开};
#FUNCTION hasNegative {
  #IF {"%1" != ""} {
    #LIST {neglist} {create} {%1};
    #FOREACH {$neglist[]} {neg} {
      #IF {"$hp[condition][$neg]" != ""} {
        #RETURN {1};
      };
    };
    #RETURN {0};
  };
  #ELSEIF {"@getNegative{}" == ""} {
    #RETURN {0}
  };
  #ELSE {
    #RETURN {1}
  };
};
#FUNCTION getNegative {
  #IF {"$hp[condition][七伤拳]" != ""} {
    #RETURN {七伤拳};
  };
  #IF {"$hp[condition][一指禅]" != ""} {
    #RETURN {一指禅};
  };
  #IF {"$hp[condition][气息不匀]" != ""} {
    #RETURN {气息不匀};
  };
  #IF {"$hp[condition][封招]" != ""} {
    #RETURN {封招};
  };
  #IF {"$hp[condition][闭气]" != ""} {
    #RETURN {闭气};
  };
  #IF {"$hp[condition][化骨绵掌]" != ""} {
    #RETURN {化骨绵掌};
  };
  #RETURN {};
};
#NOP {等待负面效果消失，%1:后续指令};
#ALIAS {waitnegative} {
  closesaving;
  #LOCAL {negcond} {@getNegative{}};
  #SHOWME {<faa>当前负面状态: $negcond};
  #IF {"$negcond" == ""} {
    #CLASS commonclass KILL;
    %1
  };
  #ELSEIF {$hp[qi] < @eval{$dazuo_point*1.5} && $hp[neili] < 500} {
    #IF {@carryqty{chuanbei wan} > 0} {
      #DELAY {1} {
        dohalt {
          fu chuanbei wan;
          yun qi;
          hp;
          i;
          waitnegative {%1}
        }
      }
    };
    #ELSEIF {@carryqty{dahuan dan} > 0 || @carryqty{da huandan} > 0} {
      #DELAY {1} {
        dohalt {
          fu dahuan dan;
          yun qi;
          hp;
          i;
          waitnegative {%1}
        }
      }
    };
    #ELSE {
      buymedicine {chuanbei wan} {2} {waitnegative {%1}}
    };
  };
  #ELSEIF {"$negcond" != "化骨绵掌" && "$hp[condition][气息不匀]" == "" && "$hp[condition][闭气]" == "" && "@getLingwuSkill{}" != ""} {
    golingwu {%1} {{negtive}{七伤拳;一指禅;封招}}
  };
  #ELSE {
    startfull {waitnegative {%1}} {} {{negative}{$negcond}}
  };
};
#NOP {购买火折子,%1:后续指令};
#ALIAS {buyfire} {
  gotodo {扬州城} {宝昌客栈} {buyfire_start {%1}}
};
#NOP {开始购买};
#ALIAS {buyfire_start} {
  #VARIABLE {oomflag} {0};
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^店小二说道：「穷光蛋，一边呆着去} {
    #VARIABLE {oomflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkfire\"|你设定checkfire为反馈信息}} {
    runwait {
      #IF {$oomflag == 1} {
        #CLASS commonclass KILL;
        gotodo {扬州城} {天阁斋} {
          qu 10 gold;
          runwait {buyfire {%1}};
        };
      };
      #ELSEIF {@carryqty{fire} == 0} {
        buy fire;
        i;
        echo {checkfire};
      };
      #ELSE {
        %1
      };
    };
  };
  #CLASS commonclass CLOSE;
  buy fire;
  i;
  echo {checkfire};
};
#ALIAS {buyxionghuang} {
  gotodo {扬州城} {杂货铺} {buyxionghuang_start {%1}}
};
#ALIAS {buyxionghuang_start} {
  #VARIABLE {oomflag} {0};
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^杂货铺老板说道：「穷光蛋，一边呆着去} {
    #VARIABLE {oomflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkxionghuang\"|你设定checkxionghuang为反馈信息}} {
    runwait {
      #IF {$oomflag == 1} {
        #CLASS commonclass KILL;
        gotodo {扬州城} {天阁斋} {
          qu 10 gold;
          runwait {buyxionghuang {%1}};
        };
      };
      #ELSEIF {@carryqty{xiong huang} == 0} {
        buy xiong huang;
          i;
          echo {checkxionghuang};
      };
      #ELSE {
        %1
      };
    };
  };
  #CLASS commonclass CLOSE;
  buy xiong huang;
  i;
  echo {checkxionghuang};
};
#NOP {当铺兑换通宝物品,%1:物品,%2:数量,%3:后续指令};
#ALIAS {tbbuy} {
  gotodo {扬州城} {当铺} {tbbuy_start {%1} {%2} {%3}}
};
#NOP {当铺兑换通宝物品,%1:物品,%2:数量,%3:后续指令};
#ALIAS {tbbuy_start} {
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkqty\"|你设定checkqty为反馈信息}} {
    #IF {@carryqty{%1} >= @eval{%2}} {
      #CLASS commonclass KILL;
      dohalt {%3};
    };
    #ELSE {
      #DELAY {1} {
        duihuan %1;
        i;
        echo {checkqty};
      };
    };
  };
  #CLASS commonclass CLOSE;  
  i;
  echo {checkqty};
};
#NOP {少林寺罗汉大阵,%1:后续指令};
#ALIAS {joinsl} {
  #IF {@carryqty{neixi wan} == 0} {
    gotodo {成都城} {墨玉斋} {
      balanceex {0} {80} {buymedicine_cd {neixi wan} {1} {joinsl {%1}}}
    };
  };
  #ELSEIF {@carryqty{jinchuang yao} == 0} {
    gotodo {成都城} {墨玉斋} {
      balanceex {0} {80} {buymedicine_cd {jinchuang yao} {1} {joinsl {%1}}}
    };
  };
  #ELSEIF {@carryqty{gold} < 1} {
    gotodo {扬州城} {天阁斋} {balanceex {1} {} {joinsl {%1}}}
  };
  #ELSE {
    joinsl_prepare {%1}
  };
};
#ALIAS {joinsl_prepare} {
  #NOP {检查技能};
  #LIST {fqskills} {clear} {};
  #CLASS commonclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkskills\"|你设定checkskills为反馈信息}} {
    #IF {&fqskills[] == 0} {
      #CLASS commonclass KILL;
      dohalt {joinsl_prepare {%1}}
    };
    #ELSE {
      dohalt {
        fangqi $fqskills[+1];
        Y;
        #UNVARIABLE {kungfu[know][$fqskills[+1]]};
        #UNVARIABLE {kungfu[base][$fqskills[+1]]};
        #UNVARIABLE {kungfu[spec][$fqskills[+1]]};
        #LIST {fqskills} {delete} {1};
        echo {checkskills};
      }
    };
  };
  #CLASS commonclass CLOSE;
  #FOREACH {*kungfu[know][]} {sk} {
    #IF {"$sk" == "literate"} {
      #CONTINUE;
    };
    #IF {$kungfu[know][$sk][lv] < 80} {
      #LIST {fqskills} {add} {$sk};
    };
  };
  #FOREACH {*kungfu[base][]} {sk} {
    #IF {$kungfu[base][$sk][lv] < 80} {
      #LIST {fqskills} {add} {$sk};
    };
  };
  #FOREACH {*kungfu[spec][]} {sk} {
    #IF {$kungfu[spec][$sk][lv] < 80} {
      #LIST {fqskills} {add} {$sk};
    };
  };
  wwp;
  #IF {&fqskills[] == 0} {
    #CLASS commonclass KILL;
    #IF {"$hp[party]" != "少林派" && @carryqty{yingxiong ling} == 0} {
      yz {joinsl_findling {gotonpc {玄苦大师} {joinsl_askluohan {xuanku dashi} {%1}}}}
    };
    #ELSE {
      gotonpc {玄苦大师} {joinsl_askluohan {xuanku dashi} {%1}}
    };
  };
  #ELSE {
    echo {checkskills};
  };
};
#NOP {找托钵僧};
#ALIAS {joinsl_findling} {
  #LIST {roomlist} {create} {1;405;406;407;408;409;410;411;413;415;416;417;418;419;420;421;422;423;424;425;427;428;429;430;431;433;435;436;437;438;439;440;441;442;443;444;445;446;447;448;449;450;451;452;453;454;455;456;457;458;459;460;461;462;463;464;465;471;472;473;474;475;478;479;480;481;483;484;492;493;494;495;496;497;498;500;501;502;503;504;505;506;507;508;537;538;539};
  #CLASS joinclass KILL;
  #CLASS joinresponseclass KILL;
  #CLASS joinclass OPEN;
  #ACTION {^这里没有 tuobo seng。} {
		#IF {&roomlist[] == 0} {
			#CLASS joinclass KILL;
			joinsl_findling {%1};
		};
		#ELSE {
			#LOCAL {temproomid} {$roomlist[+1]};
			#LIST {roomlist} {delete} {1};
			runwait {gotodo {扬州城} {$temproomid} {follow tuobo seng}};
		};
	};
  #ACTION {^你决定跟随托钵僧一起行动。} {
		give 1 gold to tuobo seng
	};
  #ACTION {^托钵僧给你一块铁铸令牌。} {
    #DELAY {1} {
      gotonpc {玄苦大师} {joinsl_askluohan {xuanku dashi} {%1}}
    };
  };
  #CLASS joinclass CLOSE;
  follow none;
  #LOCAL {temproomid} {$roomlist[+1]};
  #LIST {roomlist} {delete} {1};
  fangqi trade;
  gotodo {扬州城} {$temproomid} {follow tuobo seng};
};
#NOP {玄苦大师说道：「真是对不起，罗汉堂中有人不在，无法举行罗汉大阵。」};
#NOP {问罗汉阵,%1:大师,%2:后续指令};
#ALIAS {joinsl_askluohan} {
  #VARIABLE {askresult} {0};
  #CLASS joinclass OPEN;
  #ACTION {^你向%*大师打听有关『罗汉大阵』的消息。} {
    #CLASS joinresponseclass OPEN;
    #ACTION {^%%1说道：「真是对不起} {
      #VARIABLE {askresult} {2};
    };
    #ACTION {^%%1说道：好吧} {
      #VARIABLE {askresult} {1};
    };
    #ACTION {^%*大师说道：「%*已然闯过罗汉大阵，可不要拿老衲开这等玩笑。」} {
      #VARIABLE {askresult} {3};
    };
    #ACTION {^{设定环境变量：action \= \"checkresponse\"|你设定checkresponse为反馈信息}} {
      #DELAY {3} {
        dohalt {
          #IF {$askresult == 1} {
            #IF {"%%1" == "玄苦"} {
              gotonpc {玄难大师} {joinsl_askluohan {xuannan dashi} {%2}}
            };
            #ELSE {
              #CLASS joinclass KILL;
              gotodo {嵩山少林} {2581} {joinsl_fight {%2}}
            };
          };
          #ELSEIF {$askresult == 3} {
            #CLASS joinresponseclass KILL;
            #CLASS joinclass KILL;
            set env_shaolin;
            #VARIABLE {env[shaolin]} {YES};
            #IF {"$room" == "后殿广场"} {
              %2;
            };
            #ELSE {
              gotodo {嵩山少林} {前殿} {%2};
            };
          };
          #ELSE {
            #CLASS joinresponseclass KILL;
            #CLASS joinclass KILL;
            joinsl_askluohan {%1} {%2}
          };
        }
      }
    };
    #CLASS joinresponseclass CLOSE;
  };
  #CLASS joinclass CLOSE;
  ask %1 about 罗汉大阵;
  echo {checkresponse};
};
#NOP {过罗汉阵};
#ALIAS {joinsl_fight} {
  #CLASS joinclass KILL;
  #CLASS joinresponseclass KILL;
  #CLASS joinclass OPEN;
  #ACTION {^玄苦又道：准备，罗汉大阵即刻发动！} {
    hp;
    echo {checkhp};
  };
  #ACTION {^{设定环境变量：action \= \"checkhp\"|你设定checkhp为反馈信息}} {
    #IF {$hp[qi_per] < 70 && @carryqty{jinchuang yao} > 0} {
      fu yao
    };
    #ELSEIF {$hp[neili] < 100 && @carryqty{neixi wan} > 0} {
      fu wan
    };
    #ELSEIF {@eval{$hp[qi_max] - $hp[qi]} > @eval{$hp[qi_max] / 4}} {
      yun qi;
    };
    #DELAY {1} {
      hp;
      echo {checkhp};
    }
  };
  #ACTION {^玄难大师说道：「好，好，好！} {
    #CLASS joinclass KILL;
    #VARIABLE {idle} {0};
    drop ling;
    hp;
    set env_shaolin;
    #DELAY {5} {
      %1
    };
  };
  #ACTION {^玄难大师说道：「%*武功卓绝，兼又手下留情，品德更是过人一筹，老衲不胜佩服之至！」} {
    #CLASS joinclass KILL;
    #VARIABLE {idle} {0};
    drop ling;
    hp;
    set env_shaolin;
    #DELAY {5} {
      fu jinchuang yao;
      fu neixi wan;
      %1
    };
  };
  #CLASS joinclass CLOSE;
  fu chuanbei wan;
  wwp;
};
#NOP {回疆大戈壁补水,%1:后续指令};
#ALIAS {hjwater} {
  gotofor {回疆} {哈萨克小店} {hjwater_start {%1}}
};
#ALIAS {hjwater_start} {
  #CLASS hjwaterclass KILL;
  #CLASS hjwaterclass OPEN;
  #ACTION {^你以%*的价格从%*那里买下了一只水囊} {
    #CLASS hjwaterclass KILL;
    #VARIABLE {aimdo} {};
    #10 drink shuinang;
    drop shuinang;
    hp;
    %1;
  };
  #ACTION {^哟，抱歉啊，我这儿正忙着呢……您请稍候。} {
    #DELAY {2} {buy shuinang};
  };
  #CLASS hjwaterclass CLOSE;
  buy shuinang
};
#NOP {装备武器,%1:武器名称};
#ALIAS {wwp} {
  #VARIABLE {targetweapon} {};
  #IF {"%1" != "" && @carryqty{%1} > 0} {
    #VARIABLE {targetweapon} {%1};
  };
  #ELSEIF {"$conf[weapon][primary]" != "" && @carryqty{$conf[weapon][primary]} > 0} {
    #VARIABLE {targetweapon} {$conf[weapon][primary]};
  };
  #ELSEIF {"$conf[weapon][secondary]" != "" && @carryqty{$conf[weapon][secondary]} > 0} {
    #VARIABLE {targetweapon} {$conf[weapon][secondary]};
  };
  #IF {"$targetweapon" != "$id[weapon]"} {
    #IF {"$id[weapon]" != ""} {
      addcmd {unwield $id[weapon]};
    };
    #IF {"$targetweapon" != ""} {
      #IF {"$conf[armor][glove]" != ""} {
        addcmd {remove $conf[armor][glove]};
      };
      addcmd {wield $targetweapon};
    };
    #ELSE {
      #IF {"$conf[armor][glove]" != ""} {
        addcmd {wear $conf[armor][glove]};
      };
    };
  };
  #VARIABLE {id[weapon]} {$targetweapon};
  addcmd {i};
  execute;
};
#NOP {装备chop武器};
#ALIAS {wcwp} {
  #IF {"$conf[weapon][chop]" != ""} {
    wwp {$conf[weapon][chop]}
  };
};
#NOP {装备悟性武器};
#ALIAS {wwxwp} {
  #IF {"$conf[weapon][wuxing]" != ""} {
    wwp {$conf[weapon][wuxing]}
  };
};
#NOP {卸载武器,%1:指定};
#ALIAS {uwwp} {
  #IF {"%1" != ""} {
    unwield %1
  };
  #ELSEIF {"$id[weapon]" != ""} {
    unwield $id[weapon]
  };
  #VARIABLE {id[weapon]} {};
  i;
};
#NOP {本地存取，%1:gold,%2:silver,%3:后续指令};
#ALIAS {localwithdraw} {
  #LOCAL {bank} {@getLocalBank{}};
  #IF {"$roomid" == "1404"} {
    #NOP {如果是在兰州外侧则需要去天阁斋分店};
    gotoroom {1433} {balanceex {%1} {%2} {%3}}
  };
  #ELSE {
    gotodo {$bank[city]} {$bank[room]} {balanceex {%1} {%2} {%3}}
  };
};
#NOP {是否需要存取款};
#FUNCTION isNeedDeposit {
  #LOCAL {llgold} {1};
  #IF {"$conf[money][gold][warning]" != ""} {
    #LOCAL {llgold} {@eval{$conf[money][gold][warning]}};
  };
  #LOCAL {lugold} {10};
  #IF {"$conf[money][gold][deposit]" != ""} {
    #LOCAL {lugold} {@eval{$conf[money][gold][deposit]}};
  };
  #LOCAL {llsilver} {1};
  #IF {"$conf[money][silver][warning]" != ""} {
    #LOCAL {llsilver} {@eval{$conf[money][silver][warning]}};
  };
  #LOCAL {lusilver} {10};
  #IF {"$conf[money][silver][deposit]" != ""} {
    #LOCAL {lusilver} {@eval{$conf[money][silver][deposit]}};
  };
  #LOCAL {cgold} {@carryqty{gold}};
  #LOCAL {csilver} {@carryqty{silver}};
  #IF {$cgold < $llgold || $cgold > $lugold || $csilver < $llsilver || $csilver > $lusilver} {
    #RETURN {1};
  };
  #ELSE {
    #RETURN {0};
  };
};
#NOP {保持身上的金银,%1:后续指令};
#ALIAS {balance} {
  #LOCAL {goldamount} {2};
  #IF {"$conf[money][gold][carry]" != ""} {
    #LOCAL {goldamount} {$conf[money][gold][carry]};
  };

  #LOCAL {silveramount} {50};
  #IF {"$conf[money][silver][carry]" != ""} {
    #LOCAL {silveramount} {$conf[money][silver][carry]};
  };
  balanceex {$goldamount} {$silveramount} {%1};
};
#NOP {保持身上的金银,%1:gold,%2:silver,%3:后续操作，如未配置无需确认数量};
#ALIAS {balanceex} {
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkbalance\"|你设定checkbalance为反馈信息}} {
    #CLASS commonclass KILL;
    #DELAY {1} {
      #IF {$hp[balance] < 100} {
        funds_call {gotodo {$aimcity} {$aimroomid} {balanceex {%1} {%2} {%3}}};
      };
      #ELSE {
        balanceex {%1} {%2} {%3};
      };
    };
  };
  #CLASS commonclass CLOSE;
  #LOCAL {depositflag} {0};
  #NOP {丢弃coin};
  #IF {"$id[things][coin]" != ""} {
    drop $id[things][coin][+2] coin;
  };
  #NOP {存千两银票};
  #IF {"$id[things][thousand-cash]" != ""} {
    cun $id[things][thousand-cash][qty] thousand-cash;
    #LOCAL {depositflag} {0};
  };
  #NOP {处理gold};
  #IF {"%1" != ""} {
    #LOCAL {goldqty} {@carryqty{gold}};
    #IF {%1 > $goldqty} {
      qu @eval{%1 - $goldqty} gold;
      #LOCAL {depositflag} {1};
    };
    #ELSEIF {%1 < $goldqty} {
      cun @eval{$goldqty - %1} gold;
      #LOCAL {depositflag} {1};
    };
  };
  #NOP {处理silver};
  #IF {"%2" != ""} {
    #LOCAL {silverqty} {@carryqty{silver}};
    #IF {%2 > $silverqty} {
      qu @eval{%2 - $silverqty} silver;
      #LOCAL {depositflag} {1};
    };
    #ELSEIF {%2 < $silverqty} {
      cun @eval{$silverqty - %2} silver;
      #LOCAL {depositflag} {1};
    };
  };
  #IF {$depositflag == 1} {
    i;
    echo {checkbalance};
  };
  #ELSE {
    #CLASS commonclass KILL;
    %3
  };
};
#NOP {busy操作,%1:要进行的动作,%2:延迟的时间间隔};
#ALIAS {dohalt} {
  #VARIABLE {haltticks} {0.5};
  #IF {"%2" != ""} {
    #VARIABLE {haltticks} {%2};
  };
  #IF {$haltticks < 0.1} {
    #VARIABLE {haltticks} {0.1};
  };
  #CLASS haltclass KILL;
  #CLASS haltclass OPEN;
  #ACTION {^{$dazuo_halt}} {
    #DELAY {$haltticks} {halt};
  };
  #ACTION {^你现在很忙，停不下来。} {
    #VARIABLE {idle} {0};
    #DELAY {$haltticks} {halt};
  };
  #ACTION {^你正在使用%*暂时无法停止战斗} {
    #VARIABLE {idle} {0};
    #CLASS haltclass KILL;
    %1
  };
  #ACTION {^{$dazuo_halt}} {
    #VARIABLE {workingflag} {0};
    #DELAY {$haltticks} {halt};
  };
  #ACTION {^{你现在不忙|你身形向后一跃}} {
    #CLASS haltclass KILL;
    %1
  };
  #CLASS haltclass CLOSE;
  halt
};
#NOP {终止当前操作};
#ALIAS {doabort} {
  #LOCAL {abortdo} {};
  #SWITCH {"$conf[role]"} {
    #CASE {"job"} {jobfangqi};
    #CASE {"fish"} {};
    #CASE {"zhanbu"} {};
    #DEFAULT {#SHOWME {<faa>没事干}};
  };
  loc {
    $abortdo
  };
};
#NOP {指令一串指令后再执行另外一串指令,%1-前指令,%2-后指令};
#ALIAS {donext} {
  #CLASS nextclass KILL;
  #CLASS nextclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checknext\"|你设定checknext为反馈信息}} {
    #CLASS nextclass KILL;
    resonate {checknext};
    #VARIABLE {idle} {0};
    %2
  };
  #CLASS nextclass CLOSE;
  ensure {%1} {checknext}
};
#NOP {购买指定数量玉肌丸,%1:数量,%2:后续指令};
#ALIAS {dobuyyuji} {
  gotodo {扬州城} {当铺} {dobuyyuji_start {%1} {%2}}
};
#NOP {购买指定数量玉肌丸,%1:数量,%2:后续指令};
#ALIAS {dobuyyuji_start} {
  #VARIABLE {tempdo} {
    duihuan yuji wan;
    i;
    echo {checkyuji};
  };
  #CLASS commonclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkyuji\"|你设定checkyuji为反馈信息}} {
    #VARIABLE {idle} {0};
    dohalt {
      #IF {@carryqty{yuji wan} < %1} {
        $tempdo 
      };
      #ELSE {
        #CLASS commonclass KILL;
        %2
      };
    }
  };
  #CLASS commonclass CLOSE;
  $tempdo 
};
#NOP {4000以下吃玉肌丸,%1:后续指令,%2:强制吃到饱};
#ALIAS {doeatyuji} {
  gotodo {扬州城} {当铺} {doeatyuji_start {%1} {%2}}
};
#ALIAS {doeatyuji_start} {
  #VARIABLE {tempdo} {
    duihuan yuji wan;
    i;
    echo {checkyuji};
  };
  #CLASS commonclass OPEN;
  #ACTION {^你现在需要激发内功，才能服下玉肌丸} {
    jifa all;
  };
  #ACTION {^{设定环境变量：action \= \"checkyuji\"|你设定checkyuji为反馈信息}} {
    #IF {@carryqty{yuji wan} > 0} {
      dohalt {
        fu yuji;
        hp;
        echo {checkhp};
      }
    };
    #ELSE {
      dohalt {
        $tempdo
      };
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkhp\"|你设定checkhp为反馈信息}} {
    #IF {($hp[neili_max] < 600 || "%2" != "") && @eval{$hp[neili_limit_max] - $hp[neili_max]} > 100} {
      $tempdo;
    };
    #ELSEIF {@eval{$hp[neili_limit] - $hp[neili_max]} > 100} {
      $tempdo;
    };
    #ELSE {
      #CLASS commonclass KILL;
      %1
    };
  };
  #CLASS commonclass CLOSE;
  $tempdo
};
#NOP {吃到容貌30,%1:后续指令};
#ALIAS {fullper} {
  gotodo {扬州城} {当铺} {fullper_start {%1}};
};
#ALIAS {fullper_start} {
  #VARIABLE {perflag} {0};
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkyoutan\"|你设定checkyoutan为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {@carryqty{youtan} > 0} {
      dohalt {
        fu youtan;
        i;
        echo {checkfu}
      }
    };
    #ELSEIF {$hp[tongbao] < 1000} {
      #CLASS commonclass KILL;
      %1
    };
    #ELSE {
      #DELAY {1} {
        duihuan youtan;
        score;
        i;
        echo {checkyoutan};
      };
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkfu\"|你设定checkfu为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {@carryqty{youtan} > 0} {
      #NOP {满了};
      #CLASS commonclass KILL;
      %1
    };
    #ELSE {
      echo {checkyoutan};
    };
  };
  #CLASS commonclass CLOSE;
  i;
  score;
  echo {checkyoutan};
};
#NOP {最近一次打坐用的房间};
#VARIABLE {fullroom} {0};
#NOP {打坐内力,%1:后续指令,%2:随机房间标识,如未指定则使用fullroom};
#ALIAS {fullneili} {
  #IF {"%2" == "" || $fullroom == 0} {
    #NOP {为防止聚集,随机房间};
    #LOCAL {pindex} {@rnd{{1}{&practicerooms[]}}};
    #VARIABLE {fullroom} {$practicerooms[+$pindex]};
  };
  gotodo {扬州城} {$fullroom} {fullneili_start {%1}}
};
#ALIAS {fullneili_start} {
  #VARIABLE {increaseflag} {0};
  #CLASS commonclass KILl;
  #CLASS commonclass OPEN;
  #ACTION {^你的内力修为增加了一点！} {
    #VARIABLE {increaseflag} {1};
  };
  #CLASS commonclass CLOSE;
  #IF {$hp[neili_max] >= $hp[neili_limit]} {
    #CLASS commonclass KILL;
    opensaving;
    %1
  };
  #ELSEIF {$increaseflag == 1} {
    #CLASS commonclass KILL;
    #VARIABLE {increaseflag} {0};
    checkrequest {checkfestival {checkquest {fullneili {%1} {1}}}};
  };
  #ELSEIF {@contains{{conf[extendjob]}{官府}} > 0 && $env[wanted] != 0} {
    #CLASS commonclass KILL;
    jobgo_guanfu {checkweapon {fullneili {%1} {1}}};
  };
  #ELSE {
    closesaving;
    dzn {fullneili_start {%1}}
  };
};
#NOP {吐纳精力,%1:后续指令};
#ALIAS {fulljingli} {
  gotodo {扬州城} {坟墓} {fulljingli_start {%1}}
};
#ALIAS {fulljingli_start} {
  #VARIABLE {increaseflag} {0};
  #VARIABLE {tnpoint} {@eval{$hp[jing] / 2}};
  #CLASS commonclass OPEN;
  #ACTION {^你的精力修为增加了一点！} {
    #VARIABLE {increaseflag} {1};
  };
  #ACTION {^你吐纳完毕，睁开双眼，站了起来} {
    #VARIABLE {idle} {0};
    yun jing;
    hp;
    #IF {$hp[jingli_max] == $hp[jingli_limit]} {
      #CLASS commonclass KILL;
      %1
    };
    #ELSEIF {$hp[neili] < 400} {
      #CLASS commonclass KILL;
      startfull {fulljingli_start {%1}}
    };
    #ELSEIF {$increaseflag == 1} {
      #CLASS commonclass KILL;
      #VARIABLE {increaseflag} {0};
      checkquest {fulljingli {%1}}
    };
    #ELSE {
      tuna $tnpoint
    };
  };
  #CLASS commonclass CLOSE;
  unset 积蓄;
  tuna $tnpoint
};
#NOP {进行疗伤,%1:后续指令};
#ALIAS {doheal} {
  #IF {$heal_point == 0} {
    #VARIABLE {heal_point} {100};
  };
  #LOCAL {lostpoints} {@eval{$hp[qi_max] * 100 / $hp[qi_per] - $hp[qi_max]}};
  #LOCAL {healtime} {@eval{$lostpoints / $heal_point}};
  #IF {$hp[exp] < 2000000} {
    #IF {$hp[qi_per] <= 25} {
      medicineheal {forceheal {%1}}
    };
    #ELSE {
      forceheal {%1}
    };
  };
  #ELSEIF {$healtime < 30 && $lostpoints >= 0} {
    forceheal {%1}
  };
  #ELSEIF {"$hp[party]" == "古墓派" && @elapsed{$env[gmbed]} > 400} {
    gmheal {%1}
  };
  #ELSE {
    gotonpc {薛慕华} {xueheal {%1}}
  };
};
#NOP {后续指令,%:1};
#ALIAS {xueheal} {
  #LIST {tskills} {create} {*kungfu[spec][]};
  #VARIABLE {skindex} {1};
  #VARIABLE {teachmode} {0};
  #CLASS commonclass OPEN;
  #ACTION {^你向薛慕华打听有关『疗伤』的消息} {
    #CLASS responseclass OPEN;
    #ACTION {^薛慕华说道：「%*武功是否能指导我一些？」} {
      #CLASS responseclass KILL;
      dohalt {
        teach xue $tskills[+$skindex];
      }
    };
    #ACTION {^薛慕华狠狠地敲着你的头} {
      #CLASS responseclass KILL;
      #CLASS commonclass KILL;
      dohalt {%1}
    };
    #ACTION {^薛神医拿出一根银针轻轻捻入你受伤部位附近的穴道} {
      #CLASS responseclass KILL;
      #CLASS commonclass KILL;
      dohalt {
        yun qi;
        hp;
        %1
      };
    };
    #CLASS responseclass CLOSE;
  };
  #ACTION {^这里没有这个人} {
    #DELAY {1} {selfheal {%1}}
  };
  #ACTION {^你向薛慕华仔细地解说} {
    #VARIABLE {teachmode} {1};
    teach xue $tskills[+$skindex];
  };
  #ACTION {^{你的这个技能太差了|薛神医的这个技能已经不能再进步了}} {
    #IF {$teachmode == 0} {
      #MATH {skindex} {$skindex + 1};
      #IF {$skindex <= &tskills[]} {
        teach xue $tskills[+$skindex];
      };
      #ELSE {
        #CLASS commonclass KILL;
        #DELAY {1} {dohalt {pingheal {%1}}}
      };
    };
    #ELSE {
      #VARIABLE {teachmode} {0};
      ask xue muhua about 疗伤;
    };
  };
  #CLASS commonclass CLOSE;
  yun qi;
  hp;
  ask xue muhua about 疗伤;
};
#NOP {平一指治疗,%1:后续指令};
#ALIAS {pingheal} {
  #IF {@carryqty{gold} < 10} {
    xy {balanceex {12} {} {pingheal {%1}}}
  };
  #ELSE {
    gotonpc {平一指} {pingheal_ask {%1}}
  };
};
#ALIAS {pingheal_ask} {
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^你向平一指打听有关『疗伤』的消息} {
    #CLASS responseclass KILL;
    #CLASS responseclass OPEN;
    #ACTION {^平一指说道：「我可不随便救人，要我疗伤先帮我杀个人再说} {
      #CLASS responseclass KILL;
      #CLASS commonclass KILL;
      #NOP {直接自行治疗};
      dohalt {selfheal {%1}}
    };
    #ACTION {^平一指骂道：“ 嚷嚷什么，你没看见大爷我这儿正忙着吗} {
      #CLASS responseclass KILL;
      #CLASS commonclass KILL;
      dohalt {pingheal_ask {%1}}
    };
    #ACTION {^平一指想着心中的坏主意，忍不住得意地「嘿嘿嘿」奸笑几声} {
      #CLASS responseclass KILL;
      #CLASS commonclass KILL;
      dohalt {localwithdraw {12} {} {pingheal {%1}}}
    };
    #ACTION {^平一指手指微动，连续点过你身上十八处大穴！你感觉舒服多了} {
      #CLASS responseclass KILL;
      #CLASS commonclass KILL;
      #VARIABLE {env[pingkilled]} {0};
      dohalt {hp;%1}
    };
    #CLASS responseclass CLOSE;
  };
  #CLASS commonclass CLOSE;
  give 10 gold to ping yizhi;
  ask ping yizhi about 疗伤
};
#FUNCTION isReceivePingKill {
  #IF {@startWiths{{$joblocation}{桃源县}} > 0 && "$city" != "桃源县"} {
    #RETURN {1};
  };
  #IF {@startWiths{{$joblocation}{神龙岛}} > 0 && "$city" != "神龙岛"} {
    #RETURN {1};
  };
  #IF {@startWiths{{$joblocation}{燕子坞}} > 0 && "$hp[party]" != "姑苏慕容" && "$city" != "燕子坞"} {
    #RETURN {1};
  };
  #IF {@startWiths{{$joblocation}{曼佗罗山庄}} > 0 && "$hp[party]" != "姑苏慕容" && "$city" != "曼佗罗山庄"} {
    #RETURN {0};
  };
  #RETURN {1};
};
#ALIAS {pingkill} {
  gotonpc {平一指} {pingkill_ask {%1}}
};
#NOP {你给平一指一颗游客的首级};
#ALIAS {pingkill_ask} {
  #VARIABLE {jobnpc_pingkill_id} {};
  #VARIABLE {jobnpc_pingkill_name} {};
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^你向平一指打听有关『杀人』的消息} {
    #CLASS responseclass KILL;
    #CLASS responseclass OPEN;
    #ACTION {^平一指说道：「现在我想不出什么人好杀，你等一会再问吧} {
      #CLASS responseclass KILL;
      #CLASS commonclass KILL;
      dohalt {%1}
    };
    #ACTION {^平一指说道：「你去帮我杀了%*(%*)} {
      #VARIABLE {jobnpc_pingkill_name} {%%%1};
      #VARIABLE {jobnpc_pingkill_id} {%%%2};
    };
    #ACTION {^平一指说道：「你去%*找找看} {
      #CLASS responseclass KILL;
      #CLASS commonclass KILL;
      #IF {"$jobnpc_pingkill_name" == "钟阿四" || @isReceivePingKill{%%%1} == 0} {
        dohalt {pingkill_fangqi_ask {%1}}
      };
      #ELSE {
        dohalt {parsejoblocation {%%%1} {jobnextroom {pingkill_check {%1}} {pingkill_fangqi {%1}}} {pingkill_fangqi {%1}}}
      };
    };
    #ACTION {^平一指说道：「你已经帮我杀过人} {
      #CLASS responseclass KILL;
      #CLASS commonclass KILL;
      #VARIABLE {env[pingkilled]} {1};
      %1
    };
    #ACTION {^平一指说道：「没用的东西，你还敢去杀人} {
      #NOP {放弃了，CD不够，直接溜};
      #CLASS responseclass KILL;
      #CLASS commonclass KILL;
      dohalt {%1}
    };
    #CLASS responseclass CLOSE;
    #VARIABLE {env[pingkillts]} {@now{}};
  };
  #CLASS commonclass CLOSE;
  ask ping yizhi about 杀人
};
#ALIAS {pingkill_check} {
  #VARIABLE {cuthead} {0};
  #VARIABLE {checkcount} {0};
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^这里没有 $jobnpc_pingkill_id} {
    #CLASS commonclass KILL;
    jobnextroom {pingkill_check {%1}} {pingkill_fangqi {%1}}
  };
  #ACTION {^你决定跟随%*一起行动} {
    createpfm;
    kill $jobnpc_pingkill_id;
    autopfm
  };
  #ACTION {^$jobnpc_pingkill_name「啪」的一声倒在地上，挣扎着抽动了几下就死了} {
    dohalt {
      wcwp;
      qie corpse;
      echo {checkhead}
    }
  };
  #ACTION {^你扬起%*，对准$jobnpc_pingkill_name的尸体的脖子比了比，猛斩了下去！} {
    #VARIABLE {cuthead} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkhead\"|你设定checkhead为反馈信息}} {
    #VARIABLE {checkcount} {@eval{$checkcount + 1}};
    #IF {$cuthead == 1} {
      #CLASS commonclass KILL;
      dohalt {pingkill_finish {%1}}
    };
    #ELSEIF {$checkcount >= 5} {
      #CLASS commonclass KILL;
      dohalt {pingkill_fangqi {%1}}
    };
    #ELSE {
      #DELAY {1} {
        wcwp;
        qie corpse;
        echo {checkhead}
      }
    };
  };
  #CLASS commonclass CLOSE;
  follow $jobnpc_pingkill_id
};
#ALIAS {pingkill_finish} {
  gotonpc {平一指} {pingkill_finish_ask {%1}}
};
#ALIAS {pingkill_finish_ask} {
  #VARIABLE {okflag} {0};
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^你给平一指一颗$jobnpc_pingkill_name的首级} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkgive\"|你设定checkgive为反馈信息}} {
    #CLASS commonclass KILL;
    #IF {$okflag == 1} {
      #VARIABLE {env[pingkilled]} {1};
      %1
    };
    #ELSE {
      pingkill_fangqi_ask {%1}
    };
  };
  #CLASS commonclass CLOSE;
  give head to ping yizhi;
  echo {checkgive}
};
#ALIAS {pingkill_fangqi} {
  gotonpc {平一指} {pingkill_fangqi_ask {%1}}
};
#ALIAS {pingkill_fangqi_ask} {
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^你向平一指打听有关『放弃』的消息} {
    #CLASS commonclass KILL;
    dohalt {%1}
  };
  #CLASS commonclass CLOSE;
  ask ping yizhi about 放弃
};
#NOP {古墓门派福利疗伤,%1:后续指令};
#ALIAS {gmheal} {
  gotoroom {3163} {gmheal_start {%1}}
};
#ALIAS {gmheal_start} {
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^你突然只觉一阵奇寒侵入身体，想是两次疗伤时隔太短，身子尚未复原之故} {
    #LINE ONESHOT #ACTION {^一股暖流发自丹田流向全身，慢慢地你又恢复了知觉} {
      #CLASS commonclass KILL;
      #VARIABLE {env[gmbed]} {@now{}};
      jobcheck
    } {1}
  };
  #ACTION {^你已经受伤过重，经受不起真气震荡} {
    #CLASS commonclass KILL;
    gotonpc {薛慕华} {xueheal {%1}}
  };
  #ACTION {^不一会儿，你只觉得神采奕奕，伤口已然痊愈} {
    #CLASS commonclass KILL;
    #VARIABLE {env[gmbed]} {@now{}};
    dohalt {%1}
  };
  #ACTION {^你现在身上没有受到任何伤害} {
    #CLASS commonclass KILL;
    dohalt {%1}
  };
  #CLASS commonclass CLOSE;
  dohalt {liao bed}
};
#NOP {自行疗伤};
#ALIAS {selfheal} {
  #LOCAL {lostpoints} {@eval{$hp[qi_max] * 100 / $hp[qi_per] - $hp[qi_max]}};
  #LOCAL {healtime} {@eval{$lostpoints / $heal_point}};
  #IF {( $lostpoints < 0 || $healtime > 90) && $hp[exp] > 10000000 && $hp[tongbao] > 1000} {
    drugheal {%1}
  };
  #ELSEIF {$hp[qi_per] <= 25} {
    medicineheal {forceheal {%1}}
  };
  #ELSE {
    forceheal {%1}
  };
};
#NOP {运功疗伤};
#ALIAS {forceheal} {
  #VARIABLE {yunflag} {0};
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^你没有内功} {
    #CLASS commonclass KILL;
    #VARIABLE {yunflag} {1};
    %1;
  };
  #ACTION {^你对本草术理的研究还不够} {
    #VARIABLE {yunflag} {1};
    #CLASS commonclass KILL;
    %1
  };
  #ACTION {^你还没有选择你要使用的内功} {
    #CLASS commonclass KILL;
    #VARIABLE {yunflag} {1};
    prepareskills {doheal {%1}};
  };
  #ACTION {^你已经受伤过重，经受不起真气震荡} {
    #CLASS commonclass KILL;
    drugheal {%1}
  };
  #ACTION {^{$heal_desc}} {
    #VARIABLE {workingflag} {2};
  };
  #ACTION {^你心头一动，急忙吐气纳息} {
    #VARIABLE {workingflag} {1};
  };
  #ACTION {^你{双手互握|开口吐出一口瘀血|将双掌掌心相对|真气在体内转三转|打坐运气，调息了大半个时辰|体内真气逐步贯通}} {
    #VARIABLE {idle} {0}; 
  };
  #ACTION {^{良久，你感觉通过自己的内息运行|你并没有受伤}} {
    #CLASS commonclass KILL;
    #VARIABLE {workingflag} {0};
    #VARIABLE {yunflag} {1};
    %1
  };
  #ACTION {^你气息不匀} {
    #VARIABLE {idle} {0};
    #DELAY {2} {
      yun heal
    };
  };
  #ACTION {^( 你上一个动作还没有完成，不能施用内功。)} {
    #DELAY {2} {
      yun heal
    };
  };
  #ACTION {^你还没有选择你要使用的内功} {
    jifa all;
    yun heal
  };
  #ACTION {^你并没有受伤} {
    #CLASS commonclass KILL;
    #VARIABLE {yunflag} {1};
    hp;
    %1;
  };
  #ACTION {^你的真气不够} {
    #DELAY {2} {
      #IF {@carryqty{chuanbei wan} > 0} {
        fu chuanbei wan;
        yun heal
      };
      #ELSE {
        #CLASS commonclass KILL;
        #VARIABLE {yunflag} {1};
        buymedicine {chuanbei wan} {2} {doheal {%1}}
      };
    }
  };
  #ACTION {^你呼出一口气站了起来，可惜伤势还没有完全恢复} {
    #VARIABLE {workingflag} {1};
    #DELAY {2} {
      #IF {@carryqty{chuanbei wan} > 0} {
        fu chuanbei wan;
        yun heal
      };
      #ELSE {
        #CLASS commonclass KILL;
        #VARIABLE {yunflag} {1};
        buymedicine {chuanbei wan} {2} {doheal {%1}}
      };
    };
  };
  #CLASS commonclass CLOSE;
  hp;
  yun heal
};
#NOP {吃蝉蜕药疗伤};
#ALIAS {medicineheal} {
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkhp\"|你设定checkhp为反馈信息}} {
    #IF {$hp[qi_per] <= 25} {
      #IF {@carryqty{chantui yao} > 0} {
        fu chantui yao;
        i;
        yun qi;
        hp;
        #DELAY {1} {echo {checkhp}}
      };
      #ELSE {
        #CLASS commonclass KILL;
        buymedicine {chantui yao} {2} {medicineheal {%1}}
      };
    };
    #ELSE {
      #CLASS commonclass KILL;
      forceheal {%1};
    };
  };
  #CLASS commonclass CLOSE;
  echo {checkhp}
};
#NOP {吃药大还丹疗伤};
#ALIAS {drugheal} {
  #IF {@carryqty{dahuan dan} > 0 || @carryqty{da huandan} > 0} {
    dohalt {
      fu dahuan dan;
      i;
      hp;
      %1
    }
  };
  #ELSEIF {$hp[tongbao] < 1000} {
    forceheal {%1}
  };
  #ELSE {
    tbbuy {dahuan dan} {1} {drugheal {%1}}
  };
};
#NOP {杂货铺保存物品,%1:物品,%2:后续指令};
#ALIAS {storethings} {
  gotodo {扬州城} {杂货铺} {storethings_start {%1} {%2}}
};
#ALIAS {storethings_start} {
  #VARIABLE {okflag} {0};
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^你从身上拿出%*放入自己的个人储物箱} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkthings\"|你设定checkthings为反馈信息}} {
    #DELAY {1} {
      dohalt {
        #IF {$okflag == 1 || @carryqty{%1} == 0} {
          #CLASS commonclass KILL;
          #IF {"%1" == "tianqi"} {
            #VARIABLE {env[oot]} {0};
          };
          dohalt {%2}
        };
        #ELSE {
          cun %1;
          i;
          echo {checkthings};
        };
      };
    };
  };
  #CLASS commonclass CLOSE;
  cun %1;
  i;
  echo {checkthings};
};
#NOP {襄阳密函,%1:后续指令};
#ALIAS {checkxymihan} {
  gotonpc {郭靖} {checkxymihan_start {%1}}
};
#ALIAS {checkxymihan_start} {
  give mihan to guo jing;
  drop mihan;
  runwait {%1}
};
#NOP {蒙古密函,%1:后续指令};
#ALIAS {checkmgmihan} {
  gotonpc {蒙哥} {checkmgmihan_start {%1}}
};
#ALIAS {checkmgmihan_start} {
  give mihan to meng ge;
  drop mihan;
  runwait {%1}
};
#NOP {锦盒挖宝,%1:后续指令};
#ALIAS {checkjinhe} {
  #CLASS commonclass OPEN;
  #ACTION {^你要看什么} {
    #CLASS commonclass KILL;
    %1
  };
  #ACTION {^你看了半天，也没有明白这盒子到底是怎么回事。} {
    dohalt {jiancha jinhe}
  };
  #ACTION {^吾纵横江湖时曾在%*留下些许物事} {
    #CLASS commonclass KILL;
    parsejoblocation {%%1} {digtreasure {%1}} {drop jinhe;%1}
  };
  #CLASS commonclass CLOSE;
  l jin he;
  jiancha jinhe
};
#ALIAS {digtreasure} {
  jobnextroom {digtreasure_start {%1}} {%1};
};
#NOP {挖宝,%1:后续指令};
#ALIAS {digtreasure_start} {
  #VARIABLE {digresult} {0};
  #VARIABLE {checkcount} {0};
  #CLASS commonclass OPEN;
  #ACTION {^你挖了一阵，什么也没有找到} {
  };
  #ACTION {^你打算拆屋呀？} {
    #VARIABLE {digresult} {2};
  };
  #ACTION {^你{突然听到"当"的一声|突然挖到}} {
    #VARIABLE {digresult} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkdig\"|你设定checkdig为反馈信息}} {
    dohalt {
      #IF {$digresult == 1} {
        %1
      };
      #ELSEIF {$digresult == 2} {
        drop jin he;
        %1;
      };
      #ELSE {
        #MATH {checkcount} {$checkcount + 1};
        #IF {$checkcount > 3} {
          jobnextroom {digtreasure_start {%1}} {%1};
        };
        #ELSE {
          dig;
          echo {checkdig};
        };
      };
    }
    
  };
  #CLASS commonclass CLOSE;
  dig;
  echo {checkdig};
};
#NOP {检查玉,%1:玉,%2:后续指令};
#ALIAS {checkjade} {
  #LIST {keeplist} {create} {fenglei yu;longling yu;xiangni yu;lvyu sui;fengling yu};
  #VARIABLE {pureattr} {0};
  #VARIABLE {damagevalue} {0};
  #VARIABLE {defensevalue} {0};
  #VARIABLE {attrvalue} {0};
  #VARIABLE {attackvalue} {0};
  #CLASS commonclass OPEN;
  #ACTION {防御力+%d} {
    #MATH {defensevalue} {$defensevalue + %%1};
  };
  #ACTION {伤害力+%d} {
    #MATH {damagevalue} {$damagevalue + %%1};
  };
  #ACTION {悟性+%d} {
    #MATH {attrvalue} {$attrvalue + %%1};
  };
  #ACTION {臂力+%d} {
    #MATH {attrvalue} {$attrvalue + %%1};
  };
  #ACTION {根骨+%d} {
    #MATH {attrvalue} {$attrvalue + %%1};
  };
  #ACTION {身法+%d} {
    #MATH {attrvalue} {$attrvalue + %%1};
  };
  #ACTION {增加命中+%d} {
    #MATH {attackvalue} {$attackvalue + %%1};
  };
  #ACTION {增加所有属性} {
    #VARIABLE {attrvalue} {$attrvalue + 1};
  };
  #ACTION {极品纯度} {
    #VARIABLE {pureattr} {1};
  };
  #ACTION {纯度增加} {
    #VARIABLE {pureattr} {1};
  };
  #ACTION {增加纯度} {
    #VARIABLE {pureattr} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkvalue\"|你设定checkvalue为反馈信息}} {
    #CLASS commonclass KILL;
    #LOCAL {okflag} {0};
    #IF {@contains{{keeplist}{%1}} > 0} {
      #IF {$attrvalue >= 5 || $attackvalue >= 60} {
        #NOP {对于攻玉，伤害大于100或者大于80带纯度，防御无要求};
        #IF {$damagevalue == 0} {
          #LOCAL {okflag} {1};
        };
        #ELSEIF {$damagevalue >= 100 || ($damagevalue >= 80 && $pureattr == 1)} {
          #LOCAL {okflag} {1};
        };
      };
    };
    #IF {$okflag == 1} {
      storethings {%1} {%2}
    };
    #ELSE {
      sellthing {%1} {%2};
    };
  };
  #CLASS commonclass CLOSE;
  look %1;
  echo {checkvalue}
};
#NOP {店铺卖东西,%1:物品,%2:后续指令};
#ALIAS {sellthing} {
  gotodo {扬州城} {当铺} {sellthing_start {%1} {%2}}
};
#NOP {开始卖物品};
#ALIAS {sellthing_start} {
  #VARIABLE {okflag} {0};
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^你以%*的价格卖掉了%*给当铺老板。} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkthings\"|你设定checkthings为反馈信息}} {
    #CLASS commonclass KILL;
    #DELAY {0.5} {
      #IF {@carryqty{%1} == 0 || $okflag == 1} {
        %2
      };
      #ELSE {
        sellthing_start {%1} {%2}
      };
    };
  };
  #CLASS commonclass CLOSE;
  sell %1;
  i;
  echo {checkthings};
};
#NOP {人物传记处理,%1:后续指令};
#ALIAS {checkzhuanji} {
  %1
};
#NOP {寻找查老先生,%1:成功,%2:失败};
#ALIAS {findzhasir} {
  gotodo {扬州城} {宝昌客栈} {findzhasir_ask {%1} {%2}}
};
#ALIAS {findzhasir_ask} {
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^你向店小二打听有关『查老学士』的消息} {
    #CLASS commonresponseclass KILL;
    #CLASS commonresponseclass OPEN;
    #ACTION {^店小二说道：「查老学士就在这里呀...」} {
      #CLASS commonresponseclass KILL;
      dohalt {%1}
    };
    #ACTION {^店小二说道：「%!*听我店里的过客谈起，查老学士曾在%*一带出现过} {
      #CLASS commonresponseclass KILL;
      dohalt {
        parsejoblocation {%%%1} {findzhasir_search {%1} {%2}} {
          #VARIABLE {env[zhasirts]} {@now{}};
          %2
        } {2}
      }
    };
    #CLASS commonresponseclass CLOSE;
  };
  #CLASS commonclass CLOSE;
  ask xiao er about 查老学士
};
#NOP {寻找查老学士,%1:成功,%2:失败};
#ALIAS {findzhasir_search} {
  follow none;
  jobnextroom {findzhasir_check {%1}} {%2}
};
#ALIAS {findzhasir_check} {
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^这里没有 zha xueshi} {
    #CLASS commonclass KILL;
    jobnextroom {findzhasir_check {%1}} {%2}
  };
  #ACTION {^你决定跟随渡安一起行动} {
    #CLASS commonclass KILL;
    %1
  };
  #CLASS commonclass CLOSE;
  follow zha xueshi
};
#NOP {桃花岛弟子教诲,%1:后续指令};
#ALIAS {gojiaohui} {
  gotonpc {黄药师} {gojiaohui_start {%1}}
};
#ALIAS {gojiaohui_start} {
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^你被黄药师一顿教训} {
    #CLASS commonclass KILL;
    #VARIABLE {idle} {0};
    hp;
    dohalt {%1};
  };
  #ACTION {^这里没有这个人。} {
    #CLASS commonclass KILL;
    %1;
  };
  #CLASS commonclass CLOSE;
  ask huang yaoshi about 教诲
};
#NOP {开启鬼谷,%1后续指令};
#ALIAS {openguigu} {
  #IF {$env[guigu] == 1} {
    %1
  };
  #ELSE {
    gotonpc {瑛姑} {openguigu_start {%1}};
  };
};
#ALIAS {openguigu_start} {
  #CLASS commonclass KILL;
  #CLASS commonresponseclass KILL;
	#CLASS commonclass OPEN;
	#ACTION {^你向瑛姑打听有关『start』的消息。} {
		#CLASS commonresponseclass OPEN;
		#ACTION {^{瑛姑说道：「你本周|你不是正在}} {
			#VARIABLE {env[guigu]} {1};
      #VARIABLE {env[guiguts]} {@now{}};
      joblog {开启鬼谷} {学习领悟};
      set env_guigu;
			#CLASS commonresponseclass KILL;
			#CLASS commonclass KILL;
			dohalt {%1}
		};
    #ACTION {^瑛姑说道：「你虽然天资聪慧} {
      #CLASS commonresponseclass KILL;
			#CLASS commonclass KILL;
      dohalt {buyguigu {openguigu {%1}}};
    };
		#CLASS commonresponseclass CLOSE;
	};
	#CLASS commonclass CLOSE;
	ask ying gu about start
};
#NOP {关闭鬼谷,%1后续指令};
#ALIAS {closeguigu} {
  #IF {$env[guigu] == 0} {
    %1
  };
  #ELSE {
    gotonpc {瑛姑} {closeguigu_start {%1}}
  };
};
#ALIAS {closeguigu_start} {
  #VARIABLE {okflag} {0};
  #VARIABLE {gghours} {0};
  #CLASS learnclass KILL;
	#CLASS lingwuclass KILL;
  #CLASS commonclass KILL;
	#CLASS commonclass OPEN;
  #ACTION {^瑛姑说道：「你本周还可以使用鬼谷算术%*小时} {
    #VARIABLE {gghours} {@ctd{%%1}};
  };
	#ACTION {^你向瑛姑打听有关『over』的消息。} {
    #VARIABLE {okflag} {1};
	};
  #ACTION {^{设定环境变量：action \= \"checkover\"|你设定checkover为反馈信息}} {
    #IF {$okflag == 1} {
      #CLASS commonclass KILL;
      #VARIABLE {env[guigu]} {0};
      unset env_guigu;
      dohalt {
        #IF {$gghours < 2} {
          buyguigu {cun_pot {%1}};
        };
        #ELSE {
          joblog {关闭鬼谷，耗时【@elapsed{$env[guiguts]}】秒。} {学习领悟};
          cun_pot {%1};
        };
      };
    };
    #ELSE {
      #DELAY {1} {
        hp;
        ask ying gu about over;
        echo {checkover};
      };
    };
  };
	#CLASS commonclass CLOSE;
  hp;
  ask ying gu about over;
  echo {checkover};
};
#NOP {购买鬼谷时间,%1:后续操作};
#ALIAS {buyguigu} {
  buyjinpa {gotonpc {瑛姑} {buyguigu_start {%1}}};
};
#ALIAS {buyguigu_start} {
  guihuan ying gu;
  runwait {%1};
};
#ALIAS {buyjinpa} { 
  gotodo {扬州城} {当铺} {buyjinpa_start {%1}};
};
#ALIAS {buyjinpa_start} {
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkjingpa\"|你设定checkjingpa为反馈信息}} {
    #IF {@carryqty{jingpa} > 0} {
      #CLASS commonclass KILL;
      dohalt {%1};
    };
    #ELSE {
      #DELAY {1} {
        duihuan jingpa;
        i;
        echo {checkjingpa};
      };
    };
  };
  #CLASS commonclass CLOSE;
  i;
  echo {checkjingpa};
};
#NOP {打开武当后山茅屋,%1:后续指令,%2:失败指令};
#ALIAS {openwudangmaowu} {
  #CLASS commonresponseclass KILL;
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^你向采药道长打听有关『只是』的消息} {
    #CLASS commonresponseclass OPEN;
    #ACTION {^采药道长交给你一本痘疹定论} {
      #CLASS commonresponseclass KILL;
      dohalt {
        gotonpc {桃花姑娘} {ask tao hua about rumor}
      };
    };
    #ACTION {^采药道长说道：「已经有人在帮我打探} {
      #CLASS commonresponseclass KILL;
      #CLASS commonclass KILL;
      dohalt {%2}
    };
    #ACTION {^采药道长说道：「%*是否打探到了结果} {
      #CLASS commonresponseclass KILL;
      dohalt {
        gotonpc {桃花姑娘} {ask tao hua about rumor}
      };
    };
    #CLASS commonresponseclass CLOSE;
  };
  #ACTION {^你向桃花姑娘打听有关『rumor』的消息} {
    #CLASS commonresponseclass OPEN;
    #ACTION {^桃花姑娘说道：「听说有一位老者隐居在后山，人们很少看到他，也不知道是真是假} {
      #CLASS commonresponseclass KILL;
      #CLASS commonclass KILL;
      dohalt {
        drop douzhen dinglun;
        %1;
      };
    };
    #ACTION {^桃花姑娘说道：「%*想知道那方面的事情？」} {
      #CLASS commonresponseclass KILL;
      #CLASS commonclass KILL;
      dohalt {%2}
    };
    #CLASS commonresponseclass CLOSE;
  };
  #CLASS commonclass CLOSE;
  gotonpc {采药道长} {ask caiyao daozhang about 只是}
};
#NOP {存潜能,%1后续指令};
#ALIAS {cun_pot} {
  #IF {$hp[pot] <= @eval{$hp[pot_max] / 10}} {
    %1
  };
  #ELSE {
    gotodo {襄阳城} {潜能银行} {cun_pot_start {%1}};
  };
};
#NOP {存潜能,%1:后续指令};
#ALIAS {cun_pot_start} {
  #CLASS commonclass KILL;
	#CLASS commonclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkhp\"|你设定checkhp为反馈信息}} {
    #CLASS commonclass KILL;
    #LOCAL {pot} {@eval{$hp[pot] - @eval{$hp[pot_max] / 10}}};
    #IF {$pot > 0} {
      qn_cun $pot;
      qn_cha;
      hp
    };
    #DELAY {1} {
      %1
    };
  };
	#CLASS commonclass CLOSE;
  #VARIABLE {env[oop]} {0};
  hp;
  echo {checkhp};
};
#NOP {取潜能,%1:后续指令,%2:数量};
#ALIAS {qu_pot} {
  #LOCAL {pots} {@eval{%2}};
  #IF {$pots <= 0} {
    #LOCAL {pots} {@eval{(($hp[pot_max] - 100)/100)*1000}};
  };
  #IF {$pots < 2000} {
    #LOCAL {pots} {2010};
  };
  #MATH {pots} {$pots - $hp[pot]};
  #IF {$pots < 100} {
    #LOCAL {pots} {100};
  };
  gotodo {襄阳城} {潜能银行} {qu_pot_start {%1} {$pots}};
};
#NOP {取潜能,%1:指令,%2:数量};
#ALIAS {qu_pot_start} {
  #CLASS commonclass KILL;
	#CLASS commonclass OPEN;
  #ACTION {^你存的潜能不够取。} {
    #VARIABLE {env[oop]} {1}
  };
  #ACTION {^{设定环境变量：action \= \"checkhp\"|你设定checkhp为反馈信息}} {
    #DELAY {1} {
      #IF {$hp[pot] >= 2000 || $env[oop] == 1} {
        #CLASS commonclass KILL;
        %1;
      };
      #ELSE {
        qn_qu %2;
        qn_cha;
        hp;
        echo {checkhp}
      };
    };
  };
	#CLASS commonclass CLOSE;
  qn_qu %2;
  qn_cha;
  hp;
  echo {checkhp}
};
#NOP {获取特殊技能对应的基础武器,%1:特殊技能的id或名称,如果要学的技能不存在则通过guide配置寻找};
#FUNCTION getBaseWeapon {
  #IF {"$guide[$conf[newbie][party]][weapons][%1]" != ""} {
    #RETURN {$common[weaponmapping][$guide[$conf[newbie][party]][weapons][%1]][+1]};
  };
  #ELSE {
    #FOREACH {*kungfu[spec][]} {sk} {
      #IF {"$sk" == "%1" || "$kungfu[spec][$sk][name]" == "%1"} {
        #IF {"$kungfu[spec][$sk][jifa]" == ""} {
          verify $sk
        };
        #FOREACH {*kungfu[spec][$sk][jifa][]} {bs} {
          #IF {"@getDefineWeapon{$bs}" == ""} {
            #CONTINUE;
          };
          #IF {"$common[weaponmapping][$bs]" == ""} {
            #CONTINUE;
          };
          #RETURN {$common[weaponmapping][$bs][+1]};
        };
      };
    };
  };
	#RETURN {};
};
#NOP {定义用于学习和领悟的基础武器目录};
#ALIAS {defineweaponmap} {
  #VARIABLE {common[weaponmapping]} {};
  #NOP {清空所有基础武器的的列表};
  #FOREACH {$common[normalweapon][]} {bsk} {
    #LIST {common[weaponmapping][$bsk]} {clear};
  };
  #FOREACH {*common[uniqueweapon][]} {w} {
    #IF {@carryqty{$w} == 0} {
      #CONTINUE;
    };
    #LIST {common[weaponmapping][$common[uniqueweapon][$w]]} {add} {$w};
  };
  #FOREACH {*common[normalweapon][]} {w} {
    #IF {@carryqty{$w} == 0} {
      #CONTINUE;
    };
    #LIST {common[weaponmapping][$common[normalweapon][$w]]} {add} {$w};
  };
  #FOREACH {*common[baseweapon][]} {w} {
    #IF {@carryqty{$w} == 0} {
      #CONTINUE;
    };
    #LIST {common[weaponmapping][$common[baseweapon][$w]]} {add} {$w};
  };
  #NOP {这里添加打造武器的支持};
  #FOREACH {*conf[weapon][userweapons][]} {w} {
    #LIST {uweapons} {create} {$conf[weapon][userweapons][$w]};
    #NOP {打造武器靠前};
    #IF {&uweapons[] > 0} {
      #LOOP {1} {&uweapons[]} {i} {
        #IF {@carryqty{$uweapons[+$i]} > 0} {
          #LIST {common[weaponmapping][$w]} {insert} {$i} {$uweapons[+$i]};
        };
      };
    };
  };
};
#NOP {获取定义的基本武器技能对应的武器,%1:基础武器技能如sword,blade};
#FUNCTION getDefineWeapon {
  #VARIABLE {baseskills} {};
  #FOREACH {*common[baseweapon][]} {w} {
    #VARIABLE {baseskills[$common[baseweapon][$w]]} {$w};
  };
  #FOREACH {*common[normalweapon][]} {w} {
    #VARIABLE {baseskills[$common[normalweapon][$w]]} {$w};
  };
  #NOP {为了防止新手意外，这里选择用changjian代替yinshe sword和lanyu duzhen};
  #VARIABLE {baseskills[sword]} {changjian};
  #RETURN {$baseskills[%1]};
};
#NOP {获取武器对应的基础技能,%1:武器ID};
#FUNCTION getWeaponSkill {
  #NOP {宝物};
  #IF {"$common[uniqueweapon][%1]" != ""} {
    #RETURN {$common[uniqueweapon][%1]};
  };
  #NOP {襄阳武器};
  #IF {"$common[normalweapon][%1]" != ""} {
    #RETURN {$common[normalweapon][%1]};
  };
  #NOP {扬州武器};
  #IF {"$common[baseweapon][%1]" != ""} {
    #RETURN {$common[baseweapon][%1]};
  };
  #NOP {用户武器};
  #FOREACH {*conf[weapon][userweapons][]} {w} {
    #LIST {uweapons} {create} {$conf[weapon][userweapons][$w]};
    #IF {@contains{{uweapons}{%1}} > 0} {
      #RETURN {$w};
    };
  };
  #RETURN {};
};
#NOP {获取指定武器的替换武器,%1:指定的武器};
#FUNCTION getReplaceWeapon {
  #LOCAL {baseskill} {@getWeaponSkill{%1}};
  #IF {"$baseskill" == ""} {
    #RETURN {};
  };

  #FOREACH {$common[weaponmapping][$baseskill][]} {wp} {
    #IF {"$wp" == "%1"} {
      #CONTINUE;
    };
    #IF {@carryqty{$wp} > 0} {
      #RETURN {$wp};
    };
  };

  #RETURN {};
};
#NOP {获取缺少的基础武器,用于学习和领悟};
#FUNCTION getMissingBaseWeapon {
  #VARIABLE {baseskills} {};
  #FOREACH {*common[baseweapon][]} {w} {
    #IF {"$baseskills[$common[baseweapon][$w]]" == ""} {
      #VARIABLE {baseskills[$common[baseweapon][$w]]} {$w};
    };
  };
  #FOREACH {*common[normalweapon][]} {w} {
    #VARIABLE {baseskills[$common[normalweapon][$w]]} {$w};
  };
  #VARIABLE {baseskills[sword]} {changjian};
  #LOCAL {bsw} {};
	#FOREACH {*kungfu[spec][]} {sk} {
    #IF {@contains{{conf[ignoreskills]}{$sk}} > 0} {
      #CONTINUE;
    };
		#FOREACH {*kungfu[spec][$sk][jifa][]} {bs} {
      #IF {"$baseskills[$bs]" == ""} {
        #CONTINUE;;
      };
      #IF {"$common[weaponmapping][$bs]" != ""} {
        #CONTINUE;
      };
      #RETURN {$baseskills[$bs]};
		};
	};
  #NOP {对于guide模式下，需要检查当前师傅的技能};
  #VARIABLE {fskills} {$guide[$conf[newbie][party]][masters][$hp[master][name]][favourites]};
  #FOREACH {$fskills[]} {sk} {
    #LOCAL {tweapon} {$guide[$hp[party]][weapons][$sk]};
    #IF {"$tweapon" == ""} {
      #CONTINUE;
    };
    #IF {"$common[weaponmapping][$tweapon]" == ""} {
      #RETURN {@getDefineWeapon{$tweapon}};
    };
  };
	#RETURN {};
};
#NOP {拿铁八卦};
#ALIAS {gettiebagua} {
  gotonpc {陆乘风} {gettiebagua_start {%1}};
};
#ALIAS {gettiebagua_start} {
  #VARIABLE {tempflag} {0};
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^你要看什么} {
    #NOP {拿过八卦了，需要退出};
    #VARIABLE {tempflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkbagua\"|你设定checkbagua为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {@carryqty{tie bagua} == 1} {
      #CLASS commonclass KILL;
      runwait {%1};
    };
    #ELSEIF {$tempflag == 1} {
      #CLASS commonclass KILL;
      runwait {doquit};
    };
    #ELSE {
      #DELAY {6} {
        look bagua;
        qu bagua;
        i;
        echo {checkbagua};
      };
    };
  };
  #CLASS commonclass CLOSE;
  look bagua;
  qu bagua;
  i;
  echo {checkbagua};
};
#NOP {黑木崖黑钥匙,%1:后续指令};
#ALIAS {goheiyaoshi} {
  #VARIABLE {tempindex} {1};
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^你仔细看了看这些书籍} {
    na shu $tempindex from jia;
    fan shu
  };
  #ACTION {^你翻看了几页} {
    #DELAY {0.5} {
      #IF {$tempindex >= 5} {
        #VARIABLE {tempindex} {0};
      };
      #MATH {tempindex} {$tempindex + 1};
      na shu $tempindex from jia;
      fan shu
    }
  };
  #ACTION {^你{突然发现这本古籍|已经发现这本书有不同了}} {
    dohalt {open shu};
  };
  #ACTION {^你缓缓打开手中古籍的夹层，取出了钥匙。} {
    #CLASS commonclass KILL;
    i;
    #IF {"%1" != ""} {
      %1
    };
  };
  #ACTION {^%*对着你大喝一声：大胆} {
    #LINE ONESHOT #ACTION {%%1(%*)} {
      createpfm {} {1};
      kill @lower{%%%1};
      startfight
    };
    #LINE ONESHOT #ACTION {^%%1{大喊一声：不好|「啪」的一声倒在地上}} {
      stopfight;
      dohalt {
        goheiyaoshi {%1}
      };
    };
    look
  };
  #CLASS commonclass CLOSE;
  gotodo {黑木崖} {书房} {l shujia;l shuji}
};
#NOP {少林寺挑水,%1:后续指令};
#ALIAS {sl_tiaoshui} {
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checktong\"|你设定checktong为反馈信息}} {
    #IF {@carryqty{tie tong} == 0} {
      ask huikong zunzhe about 挑水;
      dohalt {
        get tie tong;
        i;
        echo {checktong}
      };
    };
    #ELSE {
      #CLASS commonclass KILL;
      sl_tiaoshuistart {%1};
    };
  };
  #CLASS commonclass CLOSE;
  gotodo {嵩山少林} {后殿} {
    ask huikong zunzhe about 挑水;
    dohalt {
      get tie tong;
      i;
      echo {checktong}
    };
  };
};
#ALIAS {sl_tiaoshuistart} {
  #VARIABLE {okflag} {0};
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^你将大铁桶装满清水。} {
    runwait {gotodo {嵩山少林} {后殿} {
      dao gang;
      echo {checkok};
    }};
  };
  #ACTION {^你干完活，丢下镣铐和铁桶，伸了个懒腰} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^你将清水倒入大水缸中。} {
  };
  #ACTION {^{设定环境变量：action \= \"checkok\"|你设定checkok为反馈信息}} {
    runwait {
      #IF {$okflag == 0} {
        runwait {gotodo {嵩山少林} {佛心井} {fill tong}};
      };
      #ELSE {
        #CLASS commonclass KILL;
        ask huikong zunzhe about 挑水;
        dohalt {%1};
      };
    };
  };
  #CLASS commonclass CLOSE;
  gotodo {嵩山少林} {佛心井} {fill tong};
};
#ALIAS {sl_guilty} {
  #IF {"$hp[master][name]" == "无名老僧" && @getSkillLevel{buddhism} >= 200} {
    gotonpc {无名老僧} {sl_guilty_wuming {%1}};
  };
  #ELSE {
    gotodo {嵩山少林} {戒律院} {sl_guilty_jly {%1}};
  };
};
#ALIAS {sl_guilty_jly} {
  #VARIABLE {okflag} {0};
  #VARIABLE {checkcount} {0};
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^玄寂盯着你看了半饷，说道：%*你惩恶扬善，锄暴安良，当得表彰} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^玄寂一声大喝：%*你离寺仅有数日，却在外杀人越货，胡作非为，累犯大戒，败坏本寺千载清誉} {
    #VARIABLE {okflag} {2};
  };
  #ACTION {^{设定环境变量：action \= \"checkok\"|你设定checkok为反馈信息}} {
    #MATH {checkcount} {$checkcount + 1};
    #IF {$okflag == 0 && $checkcount <= 4} {
      #DELAY {1} {
        echo {checkok}
      };
    };
    #ELSEIF {$okflag == 1} {
      #CLASS commonclass KILL;
      #VARIABLE {env[guilty]} {0};
      %1;
    };
  };
  #ACTION {^玄寂喝道：杖责三百，将%*罚去后殿挑水五十桶，非洗心悔改，不得释放！意图偷懒者罪加一等！} {
    #VARIABLE {env[guilty]} {0};
    #LINE ONESHOT #ACTION {^一股暖流发自丹田流向全身，慢慢地你又恢复了知觉} {
      execute {
        yun jing;
        yun qi;
        hp
      };
      loc {
        sl_tiaoshui {%1};
      }
    } {1};
  };
  #CLASS commonclass CLOSE;
  echo {checkok}
};
#ALIAS {sl_guilty_wuming} {
  #VARIABLE {okflag} {0};
  #VARIABLE {checkcount} {0};
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^无名老僧点了点头，笑道：南无阿弥陀佛！善哉！善哉！} {
    #CLASS commonclass KILL;
    dohalt {%1};
  };
  #ACTION {^你沉思良久，若有所捂} {
    #CLASS commonclass KILL;
    dohalt {%1};
  };
  #CLASS commonclass CLOSE;
  ask wuming laoseng about 佛法
};
#NOP {少林无相禅师问佛法，%1:后续指令};
#ALIAS {sl_wxff} {
  gotonpc {无相禅师} {sl_wxff_start {%1}}
};
#ALIAS {sl_wxff_start} {
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^你向无相禅师打听有关『佛法』的消息。} {
    #CLASS commonclass KILL;
    dohalt {%1}
  };
  #CLASS commonclass CLOSE;
  ask wuxiang chanshi about 佛法
};
#NOP {洗澡满内力，%1:后续指令};
#ALIAS {showerfull} {
  #IF {"$hp[sex]" != "m" && "$hp[sex]" != "f"} {
    #SHOWME {<faa>不男不女不能洗澡};
    %1
  };
  #ELSEIF {@carryqty{gold} == 0} {
    gotodo {长安城} {威信钱庄} {balanceex {10} {} {showerfull {%1}}};
  };
  #ELSEIF {"$hp[sex]" == "m"} {
    gotodo {长安城} {龙池} {showerfull_start {%1}};
  };
  #ELSE {
    gotodo {长安城} {凤池} {showerfull_start {%1}};
  };
};
#ALIAS {showerfull_start} {
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^不要那么紧张啊，洗澡还拿着武器} {
    #DELAY {1} {
      uwwp;
      wash
    };
  };
  #ACTION {^身上穿着衣服怎么洗澡} {
    #DELAY {1} {
      remove all;
      wash
    };
  };
  #ACTION {^你走进浴池，将全身都浸泡到水中} {
    #VARIABLE {idle} {-20};
  };
  #ACTION {^你精神抖擞的从浴池中走了出来} {
    #CLASS commonclass KILL;
    #DELAY {1} {
      wear all;
      hp;
      %1
    };
  };
  #CLASS commonclass CLOSE;
  remove all;
  uwwp;
  wash;
};
#NOP {减少食物，%1:后续指令};
#ALIAS {reducefood} {
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkfood\"|你设定checkfood为反馈信息}} {
    #IF {$hp[food] <= 50} {
      %1
    };
    #ELSE {
      #CLASS commonclass KILL;
      getshedan {
        eat mangshe dan;
        reducefood {%1}
      }
    };
  };
  #CLASS commonclass CLOSE;
  i;
  hp;
  echo {checkfood};
};
#NOP {获取蟒蛇胆，%1:后续指令};
#ALIAS {getshedan} {
  doheal {gotodo {峨嵋山} {千佛庵} {getshedan_start {%1}}}
};
#ALIAS {getshedan_start} {
  #VARIABLE {meetok} {0};
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^忽然一阵腥风袭来，一条巨蟒从身旁大树上悬下，把你卷走了} {
    #VARIABLE {meetok} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkmeet\"|你设定checkmeet为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {$hp[neili] < 1000} {
      #CLASS commonclass KILL;
      startfull {getshedan_start {%1}} {3};
    };
    #ELSEIF {@carryqty{mangshe dan} > 0 || $hp[food] <= 50} {
      #CLASS commonclass KILL;
      %1;
    };
    #ELSEIF {$meetok == 0} {
      #DELAY {0.8} {
        execute {yun jingli;hp;sw;wu;sw;wu;sw;ne;ed;ne;ed;ne};
        echo {checkmeet}
      };
    };
    #ELSE {
      kill ju mang;
    };
  };
  #ACTION {^巨蟒抽搐了几下，身体缩在一起，死了} {
    dohalt {
      get mangshe dan;
      i;
      d;
      loc {%1};
    }
  };
  #CLASS commonclass CLOSE;
  echo {checkmeet};
};
#NOP {注销通缉，%1:后续指令};
#ALIAS {cancelwanted} {
  gotonpc {侯君集} {cancelwanted_ask {%1}}
};
#NOP {注销通缉};
#ALIAS {cancelwanted_ask} {
  #VARIABLE {cashcount} {0};
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^边防武将「啪」的一声倒在地上，挣扎着抽动了几下就死了。} {
    kill wu jiang;
  };
  #ACTION {^这里没有这个人。} {
    dohalt {ask hou junji about 帮忙}
  };
  #ACTION {^侯君集说道：「帮你什么忙？别烦我！」} {
    dohalt {%1};
  };
  #ACTION {^侯君集在你的耳边悄声说道：%*两黄金。} {
    #MATH {cashcount} {@ctd{%%1}/1000 + 1};
    dohalt {
      s;
      qu $cashcount thousand-gold;
      echo {checkcash}
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkcash\"|你设定checkcash为反馈信息}} {
    #IF {@carryqty{thousand-gold} >= $cashcount} {
      #CLASS commonclass KILL;
      n;
      give $cashcount thousand-cash to hou junji;
      %1
    };
    #ELSE {
      #DELAY {2} {
        qu $cashcount thousand-gold;
        echo {checkcash}
      };
    };
  };
  #CLASS commonclass CLOSE;
  kill wu jiang;
};
#NOP {兑换内力,%1:要兑换的内力,%2:后续指令};
#ALIAS {exchangeneili} {
  #LOCAL {exneili} {@eval{%1}};
  #IF {$exneili == 0} {
    #LOCAL {exneili} {@eval{$hp[neili_limit] - $hp[neili_max]}};
  };
  #IF {$exneili < 100} {
    %2
  };
  #ELSE {
    #LOCAL {exneili} {@eval{$exneili*10/15}};
    #LOCAL {exneili} {@eval{$exneili/10*9}};
    #NOP {兑换后不得出现降级情况};
    #LOCAL {exexp} {@eval{$exneili * 500}};
    #LOCAL {overexp} {@eval{$hp[exp]-($hp[max_lv]-1)**3/10}};
    #IF {$overexp > $exexp} {
      joblog {去峨嵋山泡脚} {公共};
      gotodo {峨嵋山} {牛心石} {exchangeneili_start {$exneili} {%2}};
    };
    #ELSE {
      %2
    };
  };
};
#ALIAS {exchangeneili_start} {
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkcanwu\"|你设定checkcanwu为反馈信息}} {
    #CLASS commonclass KILL;
    joblog {成功兑换【%1】内力，消耗【@eval{%1*500}】经验。} {公共};
    dohalt {
      wear all;
      %2
    };
  };
  #CLASS commonclass CLOSE;
  dohalt {
    canwu @eval{%1 * 500} exp to neili;
    hp;
    echo {checkcanwu}
  }
};
#NOP {调整天赋,%1:str,%2:con,%3:dex,%4:int,%5:后续指令};
#ALIAS {adjustgift} {
  #VARIABLE {srcpoint} {0};
  #VARIABLE {srcattr} {};
  #VARIABLE {dstpoint} {0};
  #VARIABLE {dstattr} {0};
  #CLASS newbieclass OPEN;
  #ACTION {^确认修改属性成功} {
    score;
    echo {checktalent};
  };
  #ACTION {^{设定环境变量：action \= \"checktalent\"|你设定checktalent为反馈信息}} {
    #CLASS newbieclass KILL;
    dohalt {adjustgift {%1} {%2} {%3} {%4} {%5}}
  };
  #CLASS newbieclass CLOSE;
  #IF {$hp[str_xt] > %1} {
    #VARIABLE {srcattr} {str};
    #VARIABLE {srcpoint} {@eval{$hp[str_xt] - %1}};
  };
  #ELSEIF {$hp[str_xt] < %1} {
    #VARIABLE {dstattr} {str};
    #VARIABLE {dstpoint} {@eval{%1 - $hp[str_xt]}};
  };
  #IF {$hp[con_xt] > %2} {
    #VARIABLE {srcattr} {con};
    #VARIABLE {srcpoint} {@eval{$hp[con_xt] - %2}};
  };
  #ELSEIF {$hp[con_xt] < %2} {
    #VARIABLE {dstattr} {con};
    #VARIABLE {dstpoint} {@eval{%2 - $hp[con_xt]}};
  };
  #IF {$hp[dex_xt] > %3} {
    #VARIABLE {srcattr} {dex};
    #VARIABLE {srcpoint} {@eval{$hp[dex_xt] - %3}};
  };
  #ELSEIF {$hp[dex_xt] < %3} {
    #VARIABLE {dstattr} {dex};
    #VARIABLE {dstpoint} {@eval{%3 - $hp[dex_xt]}};
  };
  #IF {$hp[int_xt] > %4} {
    #VARIABLE {srcattr} {int};
    #VARIABLE {srcpoint} {@eval{$hp[int_xt] - %4}};
  };
  #ELSEIF {$hp[int_xt] < %4} {
    #VARIABLE {dstattr} {int};
    #VARIABLE {dstpoint} {@eval{%4 - $hp[int_xt]}};
  };
  #IF {$srcpoint == 0 || $dstpoint == 0} {
    #CLASS newbieclass KILL;
    score;
    %5
  };
  #ELSE {
    #IF {$srcpoint > $dstpoint} {
      cgift $dstpoint $srcattr to $dstattr
    };
    #ELSE {
      cgift $srcpoint $srcattr to $dstattr
    };
    hp
  };
};
#VARIABLE {orginalgift} {};
#NOP {加载属性,%1:str,%2:con,%3:dex,%4:int,%5:后续指令};
#ALIAS {setupgift} {
  #VARIABLE {orginalgift} {
    {str} {$hp[str_xt]}
    {con} {$hp[con_xt]}
    {dex} {$hp[dex_xt]}
    {int} {$hp[int_xt]}
  };
  adjustgift {%1} {%2} {%3} {%4} {%5}
};
#NOP {恢复属性,%1:后续指令};
#ALIAS {restoregift} {
  #LOCAL {tempgift} {$orginalgift};
  #IF {"$tempgift" == ""} {
    %1
  };
  #ELSE {
    #VARIABLE {orginalgift} {
      {str} {$tempgift[str]}
      {con} {$tempgift[con]}
      {dex} {$tempgift[dex]}
      {int} {$tempgift[int]}
    };
    adjustgift {$tempgift[str]} {$tempgift[con]} {$tempgift[dex]} {$tempgift[int]} {%1}
  };
};
#NOP {通知护卫到指定地点,%1:城市,%2:房间};
#ALIAS {notifyguard} {
  #IF {"$conf[guarder]" != ""} {
    tell $conf[guarder] wait at %1 %2
  };
};
#NOP {召唤护卫到指定地点,%1:城市,%2:房间};
#ALIAS {callguard} {
  #IF {"$conf[guarder]" != ""} {
    tell $conf[guarder] guard at %1 %2
  };
};
#NOP {召唤护卫到指定地点,%1:城市,%2:房间};
#ALIAS {callkill} {
  #IF {"$conf[guarder]" != ""} {
    tell $conf[guarder] kill at %1 %2
  };
};
#NOP {通知护卫复位};
#ALIAS {resetguard} {
  #IF {"$conf[guarder]" != ""} {
    tell $conf[guarder] guardreset
  };
};
#NOP {加载盘点数据};
#ALIAS {pdload} {
  #UNVARIABLE {pdjades};
  #READ {stock.tin};
}
#NOP {保存盘点数据};
#ALIAS {pdsave} {
  #CLASS stockmodule {WRITE} {stock.tin}
}
#NOP {盘点入库};
#ALIAS {pdin} {
  #VARIABLE {targetyu} {};
  #VARIABLE {_curjades} {};
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkjade\"|你设定checkjade为反馈信息}} {
    #VARIABLE {targetyu} {};
    #IF {@carryqty{longling yu} > 0} {
      #VARIABLE {targetyu} {longling yu};
    };
    #ELSEIF {@carryqty{fenglei yu} > 0} {
      #VARIABLE {targetyu} {fenglei yu};
    };
    #ELSEIF {@carryqty{fengling yu} > 0} {
      #VARIABLE {targetyu} {fengling yu};
    };
    #ELSEIF {@carryqty{lvyu sui} > 0} {
      #VARIABLE {targetyu} {lvyu sui};
    };
    #ELSEIF {@carryqty{xiangni yu} > 0} {
      #VARIABLE {targetyu} {xiangni yu};
    };
    #IF {"$targetyu" != ""} {
      look $targetyu;
      echo {checkattr}
    };
    #ELSE {
      #CLASS commonclass KILL;
      #UNVARIABLE {pdjades};
      pdload;
      #NOP {加载原有的盘点数据，并移除次账号的盘点数据};
      #NOP {将最新的盘点数据写入};
      #LOCAL {slot} {1};
      #FOREACH {$_curjades[]} {jade} {
        #WHILE {1 == 1} {
          #IF {"$pdjades[$slot]" == ""} {
            #BREAK;
          };
          #MATH {slot} {$slot + 1};
        };
        #VARIABLE {pdjades[$slot]} {$jade};
      };
      pdsave;
      #SHOWME {<faa>盘点完毕};
    };
  };
  #ACTION {^一排古篆字写着「%*」具体功能：} {
    #VARIABLE {jade_attr} {};
    #VARIABLE {jade_attr} {
      {所有人} {$hp[id]}
      {名称} {%%1}
      {护甲} {0}
      {伤害} {0}
      {命中} {0}
      {臂力} {0}
      {根骨} {0}
      {身法} {0}
      {悟性} {0}
      {三才} {0}
    };
    #CLASS jadeattrclass KILL;
    #CLASS jadeattrclass OPEN;
    #ACTION {防御力+%*} {
      #VARIABLE {jade_attr[护甲]} {%%%1}
    };
    #ACTION {伤害力+%*} {
      #VARIABLE {jade_attr[伤害]} {%%%1}
    };
    #ACTION {臂力+%*} {
      #VARIABLE {jade_attr[臂力]} {%%%1}
    };
    #ACTION {根骨+%*} {
      #VARIABLE {jade_attr[根骨]} {%%%1}
    };
    #ACTION {身法+%*} {
      #VARIABLE {jade_attr[身法]} {%%%1}
    };
    #ACTION {悟性+%*} {
      #VARIABLE {jade_attr[悟性]} {%%%1}
    };
    #ACTION {增加命中+%*} {
      #VARIABLE {jade_attr[命中]} {%%%1}
    };
    #ACTION {增加所有属性} {
      #VARIABLE {jade_attr[三才]} {1}
    };
    #CLASS jadeattrclass CLOSE;
  };
  #ACTION {^{设定环境变量：action \= \"checkattr\"|你设定checkattr为反馈信息}} {
    #CLASS jadeattrclass KILL;
    #LOCAL {nextjade} {@eval{*_curjades[+&_curjades[]] + 1}};
    #VARIABLE {_curjades[$nextjade]} {$jade_attr};
    cun $targetyu;
    dohalt {
      i;
      echo {checkjade};
    }
  };
  #CLASS commonclass CLOSE;
  i;
  echo {checkjade};
};
#NOP {盘点仓库};
#ALIAS {pdstock} {
  #VARIABLE {_curjades} {};
  #VARIABLE {stockitems} {};
  #VARIABLE {targetitem} {};
  #VARIABLE {targetindex} {0};
  #VARIABLE {okflag} {1};
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^┃       ID             货  物               价  格 } {
    #VARIABLE {favourite} {};
    #CLASS dlistclass OPEN;
    #ACTION {^┃%!s{ebook|fenglei yu|longling yu|xiangni yu|lvyu sui|fengling yu|shensheng zhufu|yuehua shi}} {  
      #VARIABLE {stockitems[%%%1]} {@eval{@eval{$stockitems[%%%1]} + 1}};
    };
    #ACTION {^┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛} {
      #CLASS dlistclass KILL;
    };
    #CLASS dlistclass CLOSE;
  };
  #ACTION {^你并没有保存该物品} {
    #VARIABLE {okflag} {0};
  };
  #ACTION {^{设定环境变量：action \= \"checkstock\"|你设定checkstock为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {&stockitems[] == 0} {
      #CLASS commonclass KILL;
      #VARIABLE {idle} {-600};
      #UNVARIABLE {pdjades};
      pdload;
      #NOP {加载原有的盘点数据，并移除次账号的盘点数据};
      #FOREACH {*pdjades[]} {p} {
        #IF {"$pdjades[$p][所有人]"  == "$hp[id]"} {
          #UNVARIABLE {pdjades[$p]};
        };
      };
      #NOP {将最新的盘点数据写入};
      #LOCAL {slot} {1};
      #FOREACH {$_curjades[]} {jade} {
        #WHILE {1 == 1} {
          #IF {"$pdjades[$slot]" == ""} {
            #BREAK;
          };
          #MATH {slot} {$slot + 1};
        };
        #VARIABLE {pdjades[$slot]} {$jade};
      };
      pdsave;
      #SHOWME {<faa>盘点完毕};
    };
    #ELSE {
      #VARIABLE {targetitem} {*stockitems[+1]};
      #MATH {targetindex} {$targetindex + 1};
      #VARIABLE {okflag} {1};
      dohalt {
        qu $targetitem $targetindex;
        i;
        look $targetitem;
        echo {checkitem}
      }
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkitem\"|你设定checkitem为反馈信息}} {
    #IF {$okflag == 0} {
      #NOP {取完了，一般不会出现};
      #UNVARIABLE {stockitems[$targetitem]};
      #VARIABLE {targetindex} {0};
      echo {checkstock}
    };
    #ELSEIF {@carryqty{$targetitem} > 0} {
      #LOCAL {nextjade} {@eval{*_curjades[+&_curjades[]] + 1}};
      #VARIABLE {_curjades[$nextjade]} {$jade_attr};
      dohalt {
        cun $targetitem;
        echo {checkstock}
      };
    };
    #ELSE {
      dohalt {
        qu $targetitem $targetindex;
        i;
        echo {checkitem}
      }
    };
  };
  #ACTION {^一排古篆字写着「%*」具体功能：} {
    #VARIABLE {jade_attr} {};
    #VARIABLE {jade_attr} {
      {所有人} {$hp[id]}
      {名称} {%%1}
      {护甲} {0}
      {伤害} {0}
      {命中} {0}
      {臂力} {0}
      {根骨} {0}
      {身法} {0}
      {悟性} {0}
      {三才} {0}
    };
    #CLASS jadeattrclass KILL;
    #CLASS jadeattrclass OPEN;
    #ACTION {防御力+%*} {
      #VARIABLE {jade_attr[护甲]} {%%%1}
    };
    #ACTION {伤害力+%*} {
      #VARIABLE {jade_attr[伤害]} {%%%1}
    };
    #ACTION {臂力+%*} {
      #VARIABLE {jade_attr[臂力]} {%%%1}
    };
    #ACTION {根骨+%*} {
      #VARIABLE {jade_attr[根骨]} {%%%1}
    };
    #ACTION {身法+%*} {
      #VARIABLE {jade_attr[身法]} {%%%1}
    };
    #ACTION {悟性+%*} {
      #VARIABLE {jade_attr[悟性]} {%%%1}
    };
    #ACTION {增加命中+%*} {
      #VARIABLE {jade_attr[命中]} {%%%1}
    };
    #ACTION {增加所有属性} {
      #VARIABLE {jade_attr[三才]} {1}
    };
    #CLASS jadeattrclass CLOSE;
  };
  #CLASS commonclass CLOSE;
  dlist;
  echo {checkstock};
};
#NOP {盘点出库,%1:所有者,%2:玉名称(中文),%3:攻击,%4:防御};
#ALIAS {pdout} {
  #LIST {queryresult} {clear} {};
  #UNVARIABLE {pdjades};
  pdload;
  #FOREACH {*pdjades[]} {i} {
    #IF {"$pdjades[$i][所有人]" != "%1"} {
      #CONTINUE;
    };
    #IF {"$pdjades[$i][名称]" != "%2"} {
      #CONTINUE;
    };
    #IF {"%3" != "" && "$pdjades[$i][伤害]" != "%3"} {
      #CONTINUE;
    };
    #IF {"%4" != "" && "$pdjades[$i][护甲]" != "%4"} {
      #CONTINUE;
    };
    #LIST {queryresult} {add} {$i};
  };
  #IF {&queryresult[] > 1} {
    #SHOWME {<faa>匹配到多个数据，请进一步执行参数%%3:伤害,%%4:护甲。};
  };
  #ELSE {
    #UNVARIABLE {pdjades[$queryresult[+1]]};
    pdsave;
    #SHOWME {<afa>出库成功。};
  };
};
#NOP {显示库存,%1:排序的属性,倒序打印,%2:打印的数量,为空则打印所有,%3:所属用户};
#ALIAS {pdview} {
  #UNVARIABLE {pdjades};
  pdload;
  #VARIABLE {sordfield} {伤害};
  #IF {"%1" != ""} {
    #VARIABLE {sordfield} {%1};
  };
  #VARIABLE {viewjades} {@sort{{pdjades}{$sordfield}{desc}}};
  #LOCAL {gridtitle} {@padRight{{账号}{12}}@padRight{{名称}{16}}@padRight{{护甲}{8}}@padRight{{伤害}{8}}@padRight{{命中}{8}}@padRight{{臂力}{8}}@padRight{{根骨}{8}}@padRight{{身法}{8}}@padRight{{悟性}{8}}@padRight{{三才}{8}}};
  #LINE IGNORE {#SHOWME {$gridtitle}};
  #VARIABLE {maxcount} {&viewjades[]};
  #IF {"%2" != "" && @eval{%2} < $maxcount} {
    #VARIABLE {maxcount} {@eval{%2}};
  };
  #LOOP 1 $maxcount {i} {
    #IF {"%3" != "" && "$viewjades[+$i][所有人]" != "%3"} {
      #CONTINUE;
    };
    #LOCAL {gridrow} {<fff>@padRight{{$viewjades[+$i][所有人]}{12}}@padRight{{$viewjades[+$i][名称]}{16}}};
    #IF {"$sordfield" == "护甲"} {
      #LOCAL {gridrow} {$gridrow<afa>};
    };
    #ELSE {
      #LOCAL {gridrow} {$gridrow<fff>};
    };
    #LOCAL {gridrow} {$gridrow@padRight{{$viewjades[+$i][护甲]}{8}}};
    #IF {"$sordfield" == "伤害"} {
      #LOCAL {gridrow} {$gridrow<afa>};
    };
    #ELSE {
      #LOCAL {gridrow} {$gridrow<fff>};
    };
    #LOCAL {gridrow} {$gridrow@padRight{{$viewjades[+$i][伤害]}{8}}};
    #IF {"$sordfield" == "命中"} {
      #LOCAL {gridrow} {$gridrow<afa>};
    };
    #ELSE {
      #LOCAL {gridrow} {$gridrow<fff>};
    };
    #LOCAL {gridrow} {$gridrow@padRight{{$viewjades[+$i][命中]}{8}}};
    #IF {"$sordfield" == "臂力"} {
      #LOCAL {gridrow} {$gridrow<afa>};
    };
    #ELSE {
      #LOCAL {gridrow} {$gridrow<fff>};
    };
    #LOCAL {gridrow} {$gridrow@padRight{{$viewjades[+$i][臂力]}{8}}};
    #IF {"$sordfield" == "根骨"} {
      #LOCAL {gridrow} {$gridrow<afa>};
    };
    #ELSE {
      #LOCAL {gridrow} {$gridrow<fff>};
    };
    #LOCAL {gridrow} {$gridrow@padRight{{$viewjades[+$i][根骨]}{8}}};
    #IF {"$sordfield" == "身法"} {
      #LOCAL {gridrow} {$gridrow<afa>};
    };
    #ELSE {
      #LOCAL {gridrow} {$gridrow<fff>};
    };
    #LOCAL {gridrow} {$gridrow@padRight{{$viewjades[+$i][身法]}{8}}};
    #IF {"$sordfield" == "悟性"} {
      #LOCAL {gridrow} {$gridrow<afa>};
    };
    #ELSE {
      #LOCAL {gridrow} {$gridrow<fff>};
    };
    #LOCAL {gridrow} {$gridrow@padRight{{$viewjades[+$i][悟性]}{8}}};
    #IF {"$sordfield" == "三才"} {
      #LOCAL {gridrow} {$gridrow<afa>};
    };
    #ELSE {
      #LOCAL {gridrow} {$gridrow<fff>};
    };
    #LOCAL {gridrow} {$gridrow@padRight{{$viewjades[+$i][三才]}{8}}};
    #LINE IGNORE {#SHOWME {$gridrow}};
  };
};
#NOP {重置仓库,%1:后续操作};
#ALIAS {resetstock} {
  gotodo {扬州城} {杂货铺} {resetstock_start {%1}};
};
#ALIAS {resetstock_start} {
  #VARIABLE {stockitems} {};
  #VARIABLE {targetitem} {};
  #VARIABLE {targetindex} {0};
  #VARIABLE {okflag} {1};
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^┃       ID             货  物               价  格 } {
    #VARIABLE {favourite} {};
    #CLASS dlistclass OPEN;
    #ACTION {^┃%!s{ebook|yitian canpian|tulong canpian|tianqi|mizong longyangsan|qiqiaolinglong yu|yuehua shi|shensheng zhufu|fenglei yu|longling yu|xiangni yu|lvyu sui|fengling yu}} {  
      #VARIABLE {stockitems[%%%1]} {@eval{@eval{$stockitems[%%%1]} + 1}};
    };
    #ACTION {^┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛} {
      #CLASS dlistclass KILL;
    };
    #CLASS dlistclass CLOSE;
  };
  #ACTION {^你并没有保存该物品} {
    #VARIABLE {okflag} {0};
  };
  #ACTION {^{设定环境变量：action \= \"checkstock\"|你设定checkstock为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {&stockitems[] == 0} {
      #CLASS commonclass KILL;
      #VARIABLE {idle} {-600};
      #SHOWME {<faa>重置仓库完毕};
      #VARIABLE {env[pdts]} {@now{}};
      set env_month $env[pdts];
      #IF {"%1" != ""} {
        %1
      };
    };
    #ELSE {
      #VARIABLE {targetitem} {*stockitems[+1]};
      #MATH {targetindex} {$targetindex + 1};
      #VARIABLE {okflag} {1};
      dohalt {
        qu $targetitem $targetindex;
        i;
        echo {checkitem}
      }
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkitem\"|你设定checkitem为反馈信息}} {
    #SHOWME {<faa>$targetitem $targetindex of $stockitems[+1]};
    #IF {$okflag == 0} {
      #NOP {取完了，一般不会出现};
      #UNVARIABLE {stockitems[$targetitem]};
      #VARIABLE {targetindex} {0};
      echo {checkstock}
    };
    #ELSEIF {@carryqty{$targetitem} > 0} {
      dohalt {
        cun $targetitem;
        echo {checkstock}
      };
    };
    #ELSE {
      dohalt {
        qu $targetitem $targetindex;
        i;
        echo {checkitem}
      }
    };
  };
  #CLASS commonclass CLOSE;
  dlist;
  echo {checkstock};
};
#NOP {仓库查找物品,%1-物品名称};
#ALIAS {stockfind} {
  #VARIABLE {stockitems} {};
  #LIST {indexes} {clear} {};
  #VARIABLE {okflag} {1};
  #CLASS commonclass KILL;
  #CLASS commonclass OPEN;
  #ACTION {^┃       ID             货  物               价  格 } {
    #VARIABLE {favourite} {};
    #CLASS dlistclass OPEN;
    #ACTION {^┃%!{\s+(ebook|yitian canpian|tulong canpian|tianqi|mizong longyangsan|qiqiaolinglong yu|yuehua shi|shensheng zhufu|fenglei yu|longling yu|xiangni yu|lvyu sui|fengling yu)\s+(\S+)\s+}} {  
      #VARIABLE {stockitems[%%%1]} {@eval{@eval{$stockitems[%%%1]} + 1}};
      #IF {"%%%2" == "%1"} {
        #LIST {indexes} {add} {$stockitems[%%%1]};
      };
    };
    #ACTION {^┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛} {
      #CLASS dlistclass KILL;
    };
    #CLASS dlistclass CLOSE;
  };
  #ACTION {^你并没有保存该物品} {
    #VARIABLE {okflag} {0};
  };
  #ACTION {^{设定环境变量：action \= \"checkstock\"|你设定checkstock为反馈信息}} {
    #CLASS commonclass KILL;
    #LIST {indexes} {collapse} {,};
    #SHOWME {<afa>%1 保存在 $indexes。 };
  };
  #CLASS commonclass CLOSE;
  dlist;
  echo {checkstock};
};
#NOP {任务开始};
#ALIAS {taskbegin} {
  #VARIABLE {env[statistics][begin]} {@now{}};
  #VARIABLE {env[statistics][gain]} {0};
  #VARIABLE {env[statistics][tongbao]} {0};
};
#ALIAS {taskrecord} {
  #NOP {这里记录下同名房间列表，方便结束时进行统计};
  #VARIABLE {env[statistics][rooms]} {};
  #FOREACH {$jobroomlist[]} {r} {
    #IF {"@getRoomInfo{{$r}{ROOMNAME}}" != "$jobroom"} {
      #CONTINUE;
    };
    #VARIABLE {env[statistics][rooms][$r]} {$jobroom}
  };
};
#NOP {经验增加,%1:经验,%2:通宝};
#ALIAS {taskgain} {
  #VARIABLE {env[statistics][gain]} {@eval{$env[statistics][gain] + %1}};
  #VARIABLE {env[statistics][tongbao]} {@eval{$env[statistics][tongbao] + @eval{%2}}};
};
#ALIAS {taskend} {
  #LOCAL {times} {0};
  #LOCAL {duration} {0};
  #LOCAL {exp} {0};
  #IF {"$env[statistics][$currentjob]" != ""} {
    #LOCAL {times} {$env[statistics][$currentjob][times]};
    #LOCAL {duration} {$env[statistics][$currentjob][duration]};
    #LOCAL {exp} {$env[statistics][$currentjob][exp]};
    #LOCAL {tongbao} {$env[statistics][$currentjob][tongbao]};
  };
  #LOCAL {duration} {@eval{$duration + @elapsed{$env[statistics][begin]}}};
  #LOCAL {times} {@eval{$times + 1}};
  #LOCAL {exp} {@eval{$exp + $env[statistics][gain]}};
  #LOCAL {tongbao} {@eval{$tongbao + $env[statistics][tongbao]}};
  #VARIABLE {env[statistics][$currentjob]} {
    {times} {$times}
    {duration} {$duration}
    {exp} {$exp}
    {tongbao} {$tongbao}
    {ept} {@eval{$exp/$times}}
    {eph} {@eval{$exp*3600/$duration}}
    {tpt} {@eval{$duration/$times}}
  };
#NOP joblog {共计完成【$env[statistics][$currentjob][times]】次，总耗时【$env[statistics][$currentjob][duration]】秒，累计经验【$env[statistics][$currentjob][exp]】，平均每次耗时【$env[statistics][$currentjob][tpt]】，平均每次经验【$env[statistics][$currentjob][ept]】，综合速度【$env[statistics][$currentjob][eph]】，累计获取通宝【$env[statistics][$currentjob][tongbao]】个。};
};
#NOP {统计偏差,%1:任务分类};
#ALIAS {taskstats} {
  #LOCAL {maxdis} {0};
  #LOCAL {mindis} {999};
  #FOREACH {*env[statistics][rooms][]} {r} {
    #IF {$r == $roomid} {
      #LOCAL {mindis} {0};
    };
    #ELSE {
      #LOCAL {ws} {@getPathLength{{$roomid}{$r}}};
      #IF {$ws > $maxdis} {
        #LOCAL {maxdis} {$ws};
      };
      #IF {$ws < $mindis} {
        #LOCAL {mindis} {$ws};
      };
    };
  };

  #NOP joblog {任务NPC地点【$room】距离给定房间最近【$mindis】步，最远【$maxdis】步。} {%1}
};

#NOP {颂摩崖、守卫襄阳等待时间久的任务疗伤};
#ALIAS {taskheal} {
  #VARIABLE {env[statistics][$currentjob][heal]} {@eval{$env[statistics][$currentjob][heal] + 1}};
  #VARIABLE {env[statistics][$currentjob][healhp]} {@eval{$env[statistics][$currentjob][healhp] + $healhp}};
  #VARIABLE {env[statistics][$currentjob][healmp]} {@eval{$env[statistics][]}}
}

#NOP {==============================================通用工具指令==============================================结束};

#NOP {==============================================特殊情况处理==============================================开始};

#ACTION {^你的内力不够，就这样飞跃断崖可能会有危险。} {
  fu dan;
} {1};

#NOP {==============================================特殊情况处理==============================================结束};

#CLASS commonmodule CLOSE;
defineweaponmap;
#SHOWME {<fac>@padRight{{公共}{12}}<fac> <cfa>模块加载完毕<cfa>};