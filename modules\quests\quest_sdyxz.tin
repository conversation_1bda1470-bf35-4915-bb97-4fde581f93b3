#nop {射雕英雄传;%1:后续指令};
#ALIAS goquest_sdyxz {
	#VARIABLE {questmodule} {射雕英雄};
  #SWITCH {$questlist[$questmodule][laststep]} {
    #CASE {0} {
			gotonpc {郭啸天} {sdyxz_askguo {%1}};
    };
    #DEFAULT {
      %1;
    };
  };
};
#ALIAS {sdyxz_askguo} {
	#CLASS questclass KILL;
	#CLASS questclass OPEN;
	#ACTION {^你向郭啸天打听有关『救援』的消息} {
		#ACTION {^郭啸天说道：「这位%*快去救我义弟啊，要不他顶不住了} {
      #CLASS questresponseclass KILL;
			#CLASS questclass KILL;
      #NOP {已经开始了};
			guard_call {牛家村} {杨家小屋} {jin bing} {
				gotodo {牛家村} {前院} {sdyxz_save {%1}}
			};
    };
		#ACTION {^郭啸天说道：「多谢这位%*出手救我义弟呀} {
      #CLASS questresponseclass KILL;
			#CLASS questclass KILL;
      #NOP {已经完成了};
			questupdate {$questmodule} {1};
			dohalt {%1};
    };
		#ACTION {^郭啸天说道：「今天先帮到这里吧，明天吧} {
      #CLASS questresponseclass KILL;
			#CLASS questclass KILL;
      #NOP {时间不够};
			questdelay {$questmodule} {0} {7200};
			dohalt {%1};
    };
		#ACTION {^郭啸天说道：「以你当前的经验恐怕还是无法帮忙，还是抓紧去练功去吧} {
      #CLASS questresponseclass KILL;
			#CLASS questclass KILL;
      #NOP {经验不够};
			questdelay {$questmodule} {} {7200};
			dohalt {%1};
    };
		#ACTION {^郭啸天说道：「昨日我和义弟救援一位被金兵追杀的道士，没想到今日却来有金兵来抓我们} {
      #CLASS questresponseclass KILL;
			#CLASS questclass KILL;
      #NOP {开始};
			guard_call {牛家村} {杨家小屋} {jin bing} {
				pfm_buff_normal;
				gotodo {牛家村} {前院} {sdyxz_save {%1}}
			};
    };
	};
	#CLASS questclass CLOSE;
	ask guo xiaotian about 救援;
};
#NOP {救杨铁心};
#ALIAS {sdyxz_save} {
	#CLASS questclass KILL;
	#CLASS questclass OPEN;
	#ACTION {^只听门内一个深沉浑厚的声音传来} {
		#CLASS questclass KILL;
		questfail {$questmodule};
		dohalt {%1};
	};
	#ACTION {^你不由感到十分好奇，你停了下来，静静倾听} {
		#VARIABLE {idle} {0};
	};
	#ACTION {^你听到杨铁心低说道：“官家不知为了何事，竟来污害良民。跟官府是辩不清楚的} {
		#VARIABLE {idle} {0};
	};
	#ACTION {^杨铁心怒气填膺，开门走出，大声喝道：“我就是杨铁心！你们干甚么} {
		#VARIABLE {idle} {0};
		kill jin bing;
	};
	#ACTION {^金兵「啪」的一声倒在地上，挣扎着抽动了几下就死了。} {
    kill jin bing;
  };
	#ACTION {^金兵哼了一声：“这点本事也敢管本大爷的事！” } {
		#CLASS questclass KILL;
		#NOP {失败了};
		questfail {$questmodule};
		dohalt {%1};
	};
	#ACTION {^%*眼看就是不敌，突然一股劲风袭来，金兵不由手上一松} {
		#CLASS questclass KILL;
		#NOP {失败了};
		questfail {$questmodule};
		dohalt {%1};
	};
	#ACTION {^这里没有这个人。} {
    guard_over;
    stopfight;
		dohalt {n};
  };
	#ACTION {^你于%*解开射雕英雄传风雪惊变篇} {
		#CLASS questclass KILL;
		questupdate {$questmodule} {1};
		dohalt {%1};
	};
	#CLASS questclass CLOSE;
	@@;
	openwimpy;
	startfight;
};