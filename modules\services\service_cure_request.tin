#LIST {emergencyrooms} {create} {868;871;872;875;878;881;884};
#NOP {解毒治疗内伤的请求,%1:要治疗的毒,%2:后续指令};
#ALIAS {cure_call} {
  #NOP {危险状态准备药物硬抗等待和治疗时间};
  #IF {@contains{{common[mergencydisease]}{%1}} > 0 && @carryqty{da huandan} < 2} {
    tbbuy {da huandan} {2} {cure_call {%1} {%2}}
  };
  #ELSE {
    #IF {"%1" == "怪蛇"} {
      fu dahuan dan;
    };
    cure_call_start {%1} {%2}
  };
};
#NOP {呼叫医生,%1:伤或者毒,%2:后续指令};
#ALIAS {cure_call_start} {
  #VARIABLE {checkresualt} {0};
  #VARIABLE {checkcount} {0};
  #VARIABLE {consultingroom} {$emergencyrooms[+@rnd{{1}{&emergencyrooms[]}}]};
  #CLASS serviceclass KILL;
  #CLASS serviceclass OPEN;
  #ACTION {^没有这个人。} {
    #VARIABLE {checkresualt} {3};
    #SHOWME {<faa>【%1】专科医师【$conf[nanny][doctor][%1]】不在线，请自行处理};
  };
  #ACTION {%*(%*)告诉你：cure_wait} {
    #VARIABLE {idle} {0};
    #VARIABLE {checkresualt} {2};
  };
  #ACTION {%*(%*)告诉你：cure_come} {
    #VARIABLE {checkresualt} {1};
    #VARIABLE {waiter} {
      {id} {@lower{%%2}}
      {name} {%%1}
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkresponse\"|你设定checkresponse为反馈信息}} {
    #MATH {checkcount} {$checkcount + 1};
    #DELAY {1} {
      #SWITCH {$checkresualt} {
        #CASE {1} {
          #CLASS serviceclass KILL;
          gotoroom {$consultingroom} {cure_request_wait {%1} {%2}}
        };
        #CASE {2} {
          #IF {@contains{{common[mergencydisease]}{%1}} > 0} {
            #IF {$hp[jing_per] < 30 || $hp[qi_per] < 30} {
              dohalt {
                fu dahuan dan;
                i;
                tell $conf[nanny][doctor][%1] cure_request %1 $consultingroom;
                hp;
                echo {checkresponse}; 
              };
            };
            #ELSE {
              #DELAY {3} {
                tell $conf[nanny][doctor][%1] cure_request %1 $consultingroom;
                hp;
                echo {checkresponse}; 
              };
            };
          };
          #ELSE {
            #CLASS serviceclass KILL;
            doqudu_self {%2}
          };
        };
        #CASE {3} {
          #CLASS serviceclass KILL;
          #VARIABLE {env[curets][%1]} {@now{}};
          %2;
        };
        #DEFAULT {
          #IF {$checkcount >= 3 } {
            #CLASS serviceclass KILL;
            #VARIABLE {env[curets][%1]} {@now{}};
            doqudu_self {%2}
          };
          #ELSE {
            echo {checkresponse}; 
          };
        };
      };
    };
  };
  #CLASS serviceclass CLOSE;
  tell $conf[nanny][doctor][%1] cure_request %1 $consultingroom;
  hp;
  echo {checkresponse}; 
};
#NOP {去地点等待医生问诊,等待的过程中需注意自身状态,危险时直接吃大还丹补满,%1:毒或伤,%2:后续指令};
#ALIAS {cure_request_wait} {
  #VARIABLE {startts} {@now{}};
  #VARIABLE {arrived} {0};
  #VARIABLE {okcount} {0};
  #VARIABLE {okflag} {0};
  #CLASS serviceclass KILL;
  #CLASS serviceclass OPEN;
  #ACTION {^$waiter[name]轻轻地拍了拍你的头} {
    #VARIABLE {arrived} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkresponse\"|你设定checkresponse为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {$hp[qi_per] < 30 || $hp[jing_per] < 30} {
      #IF {@carryqty{dahuan dan} > 0 || @carryqty{da huandan} > 0} {
        dohalt {
          fu dahuan dan;
          i;
          hp;
          echo {checkresponse}
        }
      };
      #ELSE {
        #CLASS serviceclass KILL;
        tbbuy {dahuan dan} {1} {gotoroom {$roomid} {cure_request_wait {%1}}}
      };
    };
    #ELSEIF {$arrived == 1} {
      #VARIABLE {okflag} {0};
      #VARIABLE {startts} {@now{}};
      echo {checkcure}
    };
    #ELSEIF {@elapsed{$startts} > 300} {
      #CLASS serviceclass KILL;
      #NOP {超时走了};
      logbuff {curemiss};
      tell $conf[nanny][doctor][%1] cure_cancel;
      #VARIABLE {env[curets][%1]} {@now{}};
      #NOP {疗毒失败后应重启并检查，此时已经设置了时间戳，会走自己疗毒流程};
      jobprepare
    };
    #ELSE {
      #DELAY {2} {
        hp;
        echo {checkresponse}
      };
    };
  };
  #ACTION {^$waiter[name]似乎不懂你的意思} {
    #VARIABLE {startts} {@now{}};
    cond;
    echo {checkcond}
  };
  #ACTION {^{设定环境变量：action \= \"checkcond\"|你设定checkcond为反馈信息}} {
    #IF {"$hp[condition][%1]" == ""} {
        ok $waiter[id]
    };
  };
  #NOP {结束治疗};
  #ACTION {^$waiter[name]痛快地对你说道：好吧} {
    #VARIABLE {okflag} {1};
  };
  #NOP {无法治疗};
  #ACTION {^$waiter[name]对着你摇了摇头} {
    #VARIABLE {okflag} {2};
    #VARIABLE {env[curets][%1]} {@now{}};
  };
  #ACTION {^{设定环境变量：action \= \"checkcure\"|你设定checkcure为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {@elapsed{$startts} > 300} {
      #CLASS serviceclass KILL;
      #NOP {超时走了};
      logbuff {curemiss};
      tell $conf[nanny][doctor][%1] cure_cancel;
      #VARIABLE {env[curets][%1]} {@now{}};
      #NOP {疗毒失败后应重启并检查，此时已经设置了时间戳，会走自己疗毒流程};
      jobprepare
    };
    #ELSEIF {$hp[qi_per] < 30 || $hp[jing_per] < 30} {
      #IF {@carryqty{dahuan dan} > 0 || @carryqty{da huandan} > 0} {
        fu dahuan dan;
        i;
      };
    };
    #IF {$okflag == 0} {
      #DELAY {2} {
        hp;
        echo {checkcure}
      };
    };
    #ELSEIF {$okflag == 1} {
      #CLASS serviceclass KILL;
      #VARIABLE {waiter} {};
      wear all;
      %2
    };
    #ELSE {
      #CLASS serviceclass KILL;
      #VARIABLE {waiter} {};
      tell $conf[nanny][doctor][%1] cure_cancel;
      logbuff {curecannot};
      #NOP {疗毒失败后应重启并检查，此时已经设置了时间戳，会走自己疗毒流程};
      jobprepare
    };
  };
  #CLASS serviceclass CLOSE;
  hp;
  echo {checkresponse}
};