#NOP {武功模块};
#CLASS kungfumodule KILL;
#CLASS kungfumodule OPEN;
#VARIABLE {ignore_levelup} {0};
#VARIABLE {lvchanged} {0};
#VARIABLE {kungfu} {};
#ACTION {^%*向你请教有关「%*」的疑问} {
	yun jing;
	hp;
	#IF {@rnd{{1}{10}} == 1} {
		pat $hp[spouse]
	};
};
#ACTION {^你的「%*」进步了！} {
	#VARIABLE {lvchanged} {1};
	#VARIABLE {checkcount} {0};
	#IF {$ignore_levelup != 1} {
		skills
	};
} {1};
#ACTION {^【你的技能表】：总共%u项技能} {
	#VARIABLE {skillscount} {@ctd{%1}};
	#VARIABLE {skillcapcount} {0};
	#CLASS skillcapclass OPEN;
	#ACTION {^│『职业技能 %u 种』} {
		#VARIABLE {tempcount} {@ctd{%%1}};
		#VARIABLE {craftskillcount} {0};
		#CLASS craftclass OPEN;
		#ACTION {^│%u(%u)%s→%u%s%d/%s%d%s%u(%u)%s→%u%s%d/%s%d│$} {
			#VARIABLE {kungfu[craft][%%%2][name]} {%%%1};
			#VARIABLE {kungfu[craft][%%%2][desc]} {@trim{%%%4}};
			#VARIABLE {kungfu[craft][%%%2][lv]} {%%%6};
			#VARIABLE {kungfu[craft][%%%2][point]} {%%%8};
			#VARIABLE {kungfu[craft][%%%2][max_point]} {@eval{(%%%6+1)**2}};
			#VARIABLE {kungfu[craft][%%%11][name]} {%%%10};
			#VARIABLE {kungfu[craft][%%%11][desc]} {@trim{%%%13}};
			#VARIABLE {kungfu[craft][%%%11][lv]} {%%%15};
			#VARIABLE {kungfu[craft][%%%11][point]} {%%%17};
			#VARIABLE {kungfu[craft][%%%11][max_point]} {@eval{(%%%15+1)**2}};
			#VARIABLE {craftskillcount} {@eval{$craftskillcount + 2}};
			#IF {$craftskillcount >= $tempcount} {
				#CLASS craftclass KILL;
			};
			#VARIABLE {skillcapcount} {@eval{$skillcapcount + 2}};
			#IF {$skillcapcount >= $skillscount} {
				#CLASS skillcapclass KILL;
			};
		}{1};
		#ACTION {^│%u(%u)%s→%u%s%d/%s%d} 
		{
			#VARIABLE {kungfu[craft][%%%2][name]} {%%%1};
			#VARIABLE {kungfu[craft][%%%2][desc]} {@trim{%%%4}};
			#VARIABLE {kungfu[craft][%%%2][lv]} {%%%6};
			#VARIABLE {kungfu[craft][%%%2][point]} {%%%8};
			#VARIABLE {kungfu[craft][%%%2][max_point]} {@eval{(%%%6+1)**2}};
			#VARIABLE {craftskillcount} {@eval{$craftskillcount + 1}};
			#IF {$craftskillcount >= $tempcount} {
				#CLASS craftclass KILL;
			};
			#VARIABLE {skillcapcount} {@eval{$skillcapcount + 1}};
			#IF {$skillcapcount >= $skillscount} {
				#CLASS skillcapclass KILL;
			};
		};
		#CLASS craftclass CLOSE;
	};
	#ACTION {^│『杂项技能 %u 种』} {
		#VARIABLE {tempcount} {@ctd{%%1}};
		#VARIABLE {knowskillcount} {0};
		#CLASS knowclass OPEN;
		#ACTION {^│{(\S+)\s*\((\S+)\)\s*→(\S+)\s+(\d+)/\s*(\d+)\s+(\S+)\s*\((\S+)\)\s*→(\S+)\s+(\d+)/\s*(\d+)}} {
			#VARIABLE {kungfu[know][%%%3][name]} {%%%2};
			#VARIABLE {kungfu[know][%%%3][desc]} {@trim{%%%4}};
			#VARIABLE {kungfu[know][%%%3][lv]} {%%%5};
			#VARIABLE {kungfu[know][%%%3][point]} {%%%6};
			#VARIABLE {kungfu[know][%%%3][max_point]} {@eval{(%%%5+1)**2}};
			#VARIABLE {kungfu[know][%%%8][name]} {%%%7};
			#VARIABLE {kungfu[know][%%%8][desc]} {@trim{%%%9}};
			#VARIABLE {kungfu[know][%%%8][lv]} {%%%10};
			#VARIABLE {kungfu[know][%%%8][point]} {%%%11};
			#VARIABLE {kungfu[know][%%%8][max_point]} {@eval{(%%%10+1)**2}};
			#VARIABLE {knowskillcount} {@eval{$knowskillcount + 2}};
			#IF {$knowskillcount >= $tempcount} {
				#CLASS knowclass KILL;
			};
			#VARIABLE {skillcapcount} {@eval{$skillcapcount + 2}};
			#IF {$skillcapcount >= $skillscount} {
				#CLASS skillcapclass KILL;
			};
		}{1};
		#ACTION {^│{(\S+)\s*\((\S+)\)\s*→(\S+)\s+(\d+)/\s*(\d+)\s+}│$} {
			#VARIABLE {kungfu[know][%%%3][name]} {%%%2};
			#VARIABLE {kungfu[know][%%%3][desc]} {@trim{%%%4}};
			#VARIABLE {kungfu[know][%%%3][lv]} {%%%5};
			#VARIABLE {kungfu[know][%%%3][point]} {%%%6};
			#VARIABLE {kungfu[know][%%%3][max_point]} {@eval{(%%%5+1)**2}};
			#VARIABLE {knowskillcount} {@eval{$knowskillcount + 1}};
			#IF {$knowskillcount >= $tempcount} {
				#CLASS knowclass KILL;
			};
			#VARIABLE {skillcapcount} {@eval{$skillcapcount + 1}};
			#IF {$skillcapcount >= $skillscount} {
				#CLASS skillcapclass KILL;
			};
		};
		#CLASS knowclass CLOSE;
	};
	#ACTION {^│『基本技能 %u 种』} {
		#VARIABLE {baseskillcount} {0};
		#VARIABLE {tempcount} {@ctd{%%1}};
		#CLASS baseclass OPEN;
		#ACTION {^│{(\S+)\s*\((\S+)\)\s*→(\S+)\s+(\d+)/\s*(\d+)\s+(\S+)\s*\((\S+)\)\s*→(\S+)\s+(\d+)/\s*(\d+)}} {
			#VARIABLE {kungfu[base][%%%3][name]} {%%%2};
			#VARIABLE {kungfu[base][%%%3][desc]} {@trim{%%%4}};
			#VARIABLE {kungfu[base][%%%3][lv]} {%%%5};
			#VARIABLE {kungfu[base][%%%3][point]} {%%%6};
			#VARIABLE {kungfu[base][%%%3][max_point]} {@eval{(%%%5+1)**2}};
			#VARIABLE {kungfu[base][%%%8][name]} {%%%7};
			#VARIABLE {kungfu[base][%%%8][desc]} {@trim{%%%9}};
			#VARIABLE {kungfu[base][%%%8][lv]} {%%%10};
			#VARIABLE {kungfu[base][%%%8][point]} {%%%11};
			#VARIABLE {kungfu[base][%%%8][max_point]} {@eval{(%%%10+1)**2}};
			#VARIABLE {baseskillcount} {@eval{$baseskillcount + 2}};
			#IF {$baseskillcount >= $tempcount} {
				#CLASS baseclass KILL;
			};
			#VARIABLE {skillcapcount} {@eval{$skillcapcount + 2}};
			#IF {$skillcapcount >= $skillscount} {
				#CLASS skillcapclass KILL;
			};
		}{1};
		#ACTION {^│{(\S+)\s*\((\S+)\)\s*→(\S+)\s+(\d+)/\s*(\d+)\s+}│$} {
			#VARIABLE {kungfu[base][%%%3][name]} {%%%2};
			#VARIABLE {kungfu[base][%%%3][desc]} {@trim{%%%4}};
			#VARIABLE {kungfu[base][%%%3][lv]} {%%%5};
			#VARIABLE {kungfu[base][%%%3][point]} {%%%6};
			#VARIABLE {kungfu[base][%%%3][max_point]} {@eval{(%%%5+1)**2}};
			#VARIABLE {baseskillcount} {@eval{$baseskillcount + 1}};
			#IF {$baseskillcount >= $tempcount} {
				#CLASS baseclass KILL;
			};
			#VARIABLE {skillcapcount} {@eval{$skillcapcount + 1}};
			#IF {$skillcapcount >= $skillscount} {
				#CLASS skillcapclass KILL;
			};
		};
		#CLASS baseclass CLOSE;
	};
	#ACTION {^│『特殊技能 %u 种』} {
		#VARIABLE {specskillcount} {0};
		#VARIABLE {tempcount} {@ctd{%%1}};
		#CLASS specclass OPEN;
		#ACTION {^│  □%u%!s(%u)%!s→%!s%u%!s%d/%!s%d%!D$} 
		{
			#VARIABLE {kungfu[spec][%%%2][name]} {%%%1};
			#VARIABLE {kungfu[spec][%%%2][desc]} {@trim{%%%3}};
			#VARIABLE {kungfu[spec][%%%2][lv]} {%%%4};
			#VARIABLE {kungfu[spec][%%%2][point]} {%%%5};
			#VARIABLE {kungfu[spec][%%%2][max_point]} {@eval{(%%%4+1)**2}};
			#VARIABLE {specskillcount} {@eval{$specskillcount + 1}};
			#IF {$specskillcount >= $tempcount} {
				#CLASS specclass KILL;
			};
			#VARIABLE {skillcapcount} {@eval{$skillcapcount + 1}};
			#IF {$skillcapcount >= $skillscount} {
				#CLASS skillcapclass KILL;
			};
		};
		#ACTION {^│    %u%s(%u)%s→%s%S%s%d/%s%d%D$} 
		{
			#VARIABLE {kungfu[spec][%%%3][name]} {%%%1};
			#VARIABLE {kungfu[spec][%%%3][desc]} {@trim{%%%6}};
			#VARIABLE {kungfu[spec][%%%3][lv]} {%%%8};
			#VARIABLE {kungfu[spec][%%%3][point]} {%%%10};
			#VARIABLE {kungfu[spec][%%%3][max_point]} {@eval{(%%%8+1)**2}};
			#VARIABLE {specskillcount} {@eval{$specskillcount + 1}};
			#IF {$specskillcount >= $tempcount} {
				#CLASS specclass KILL;
			};
			#VARIABLE {skillcapcount} {@eval{$skillcapcount + 1}};
			#IF {$skillcapcount >= $skillscount} {
				#CLASS skillcapclass KILL;
			};
		};
		#CLASS specclass CLOSE;
	};
	#CLASS skillcapclass CLOSE;
};
#NOP {获取激发技能};
#ACTION {^以下是你目前使用中的特殊技能。} {
	#CLASS jifaclass KILL;
	#CLASS jifaclass OPEN;
	#FOREACH {*kungfu[base][]} {bsk} {
		#VARIABLE {kungfu[base][$bsk][jifa]} {};
	};
	#ACTION {%u%!s(%u)%!s： %u%!s有效等级：%!s%*} {
		#IF {"%%3" != "无"} {
			#VARIABLE {kungfu[base][%%2][jifa]} {@getSkillFromName{%%3}};
			#VARIABLE {kungfu[base][%%2][effectlv]} {@trim{%%4}};
			#IF {"%%2" == "force"} {
				#VARIABLE {kungfu[force][@getSkillFromName{%%3}]} {%%3};
			};
		};
		#ELSE {
			#VARIABLE {kungfu[base][%%2][jifa]} {};
			#VARIABLE {kungfu[base][%%2][effectlv]} {0};
		};
	} {1};
	#ACTION {^>} {
		#CLASS jifaclass KILL;
		#UNVARIABLE {kungfu[force][]};
	};
	#CLASS jifaclass CLOSE;
};
#ACTION {^你现在没有使用任何特殊技能} {
	#FOREACH {*kungfu[base][]} {bsk} {
		#VARIABLE {kungfu[base][$bsk][jifa]} {};
	};
};
#ACTION {^取消全部技能准备} {
	#VARIABLE {kungfu[bei]} {};
};
#ACTION {^以下是你目前组合中的特殊技能：} {
	#CLASS beiclass KILL;
	#CLASS beiclass OPEN;
	#VARIABLE {kungfu[bei]} {};
	#ACTION {{拳|掌|指|手|爪|腿}法(%*)%!s%u} {
		#VARIABLE {kungfu[bei][%%2]} {@getSkillFromName{%%3}};
	} {1};
	#ACTION {^>} {
		#IF {"$kungfu[jobbei]" == ""} {
			#VARIABLE {kungfu[jobbei]} {$kungfu[bei]};
		};
		#CLASS beiclass KILL;
	};
	#CLASS beiclass CLOSE;
};
#NOP {获取特殊技能对应的基础技能};
#ACTION {^┌【%*功能表】} {
	#VARIABLE {tempskill} {};
	#CLASS verifyclass KILL;
	#CLASS verifyclass OPEN;
	#ACTION {^│特殊技能：} {
		#CLASS jifabaseclass OPEN;
		#ACTION {(%u)} {
			#VARIABLE {kungfu[spec][$tempskill][jifa][%%%1]} {%%%1};
		} {4};
		#ACTION {(%u)%!*(%u)} {
			#VARIABLE {kungfu[spec][$tempskill][jifa][%%%1]} {%%%1};
			#VARIABLE {kungfu[spec][$tempskill][jifa][%%%2]} {%%%2};
		} {3};
		#ACTION {(%u)%!*(%u)%!*(%u)} {
			#VARIABLE {kungfu[spec][$tempskill][jifa][%%%1]} {%%%1};
			#VARIABLE {kungfu[spec][$tempskill][jifa][%%%2]} {%%%2};
			#VARIABLE {kungfu[spec][$tempskill][jifa][%%%3]} {%%%3};
		} {2};
		#ACTION {(%u)%!*(%u)%!*(%u)%!*(%u)} {
			#VARIABLE {kungfu[spec][$tempskill][jifa][%%%1]} {%%%1};
			#VARIABLE {kungfu[spec][$tempskill][jifa][%%%2]} {%%%2};
			#VARIABLE {kungfu[spec][$tempskill][jifa][%%%3]} {%%%3};
			#VARIABLE {kungfu[spec][$tempskill][jifa][%%%4]} {%%%4};
		} {1};
		#ACTION {^├─────────────────────────────────┤} {
			#CLASS jifabaseclass KILL;
		};
		#CLASS jifabaseclass CLOSE;
	};
	#ACTION {^└─────────────────────────────────┘} {
		#CLASS jifabaseclass KILL;
		#CLASS verifyclass KILL;
	};
	#CLASS verifyclass CLOSE;
	#FOREACH {*kungfu[spec][]} {sk} {
		#IF {"$kungfu[spec][$sk][name]" == "%1"} {
			#VARIABLE {tempskill} {$sk};
			#BREAK;
		};
	};
};
#NOP {职业技能};
#ACTION {^ 你目前尚不会打造任何东西} {
	#VARIABLE {kungfu[woker]} {};
};
#ACTION {^【你已经学会的打造项目有】} {
	#CLASS workerclass KILL;
	#CLASS workerclass OPEN;
	#ACTION {^{\s+(\S+)\((\S+)\)\s+(\S+)\((\S+)\)\s+(\S+)\((\S+)\)\s+(\S+)\((\S+)\)}} {
		#VARIABLE {kungfu[woker][@lower{%%3}]} {%%2};
		#VARIABLE {kungfu[woker][@lower{%%5}]} {%%4};
		#VARIABLE {kungfu[woker][@lower{%%7}]} {%%6};
		#VARIABLE {kungfu[woker][@lower{%%9}]} {%%8};
	} {1};
	#ACTION {^{\s+(\S+)\((\S+)\)\s+(\S+)\((\S+)\)\s+(\S+)\((\S+)\)}} {
		#VARIABLE {kungfu[woker][@lower{%%3}]} {%%2};
		#VARIABLE {kungfu[woker][@lower{%%5}]} {%%4};
		#VARIABLE {kungfu[woker][@lower{%%7}]} {%%6};
	} {2};
	#ACTION {^{\s+(\S+)\((\S+)\)\s+(\S+)\((\S+)\)}} {
		#VARIABLE {kungfu[woker][@lower{%%3}]} {%%2};
		#VARIABLE {kungfu[woker][@lower{%%5}]} {%%4};
	} {3};
	#ACTION {^{\s+(\S+)\((\S+)\)}} {
		#VARIABLE {kungfu[woker][@lower{%%3}]} {%%2};
	} {4};
	#LINE ONESHOT #ACTION {^>} {
		#CLASS workerclass KILL
	} {1};
	#CLASS workerclass CLOSE;
	#VARIABLE {kungfu[woker]} {};
};
#NOP {准备所有技能};
#ALIAS {preparekungfu} {
	#FOREACH {*kungfu[base][]} {bsk} {
		#IF {"$common[baseexcludes][$ssk]" == "$bsk"} {
			#CONTINUE;
		};
		#IF {"$kungfu[base][$bsk][jifa]" != ""} {
			#CONTINUE;
		};
		#IF {"$bsk" == "force"} {
			#CONTINUE;
		};

		jifa $bsk @getBaseMaxMapSkill{$bsk}
	};
	#IF {"$conf[primaryforce]" != ""} {
		jifa force $conf[primaryforce]
	};
	#ELSE {
		jifa force @getBaseMaxMapSkill{force}
	};
	#IF {"$conf[pfm][bei]" != ""} {
		$conf[pfm][bei];
	};
	jifa
};
#NOP {获取指定基础技能对应的最大特殊技能,%1:基础技能};
#FUNCTION getBaseMaxMapSkill {
	#LOCAL {tsk} {};
	#LOCAL {tlv} {0};
	#FOREACH {*kungfu[spec][]} {ssk} {
		#IF {"$kungfu[spec][$ssk][jifa][%1]" == ""} {
			#CONTINUE;
		};
		#IF {"%1" == "force" && @contains{{conf[ignoreskills]}{$ssk}} != 0} {
			#CONTINUE;
		};
		#IF {$kungfu[spec][$ssk][lv] > $tlv} {
			#LOCAL {tsk} {$ssk};
			#LOCAL {tlv} {$kungfu[spec][$ssk][lv]};
		};
	};

	#RETURN {$tsk};
};
#NOP {准备技能,%1:后续指令};
#ALIAS {prepareskills} {
	#CLASS kungfuclass KILL;
	#CLASS kungfuclass OPEN;
	#ACTION {^{设定环境变量：action \= \"checkkungfu\"|你设定checkkungfu为反馈信息}} {
		#FOREACH {*kungfu[spec][]} {ssk} {
			verify $ssk;
		};
		#DELAY {1} {
			echo {checkbei};
		};
	};
	#ACTION {^{设定环境变量：action \= \"checkbei\"|你设定checkbei为反馈信息}} {
		#CLASS kungfuclass KILL;
		#NOP {处理特殊内功};
		#VARIABLE {kungfu[force]} {};
		#FOREACH {*kungfu[spec][]} {ssk} {
			#FOREACH {*kungfu[spec][$ssk][jifa][]} {bsk} {
				#IF {"$common[baseexcludes][$ssk]" == "$bsk"} {
					#CONTINUE;
				};
				#IF {"$bsk" == "force"} {
					#VARIABLE {kungfu[force][$ssk]} {$kungfu[spec][$ssk][name]};
				};
			};
		};
		preparekungfu;
		guide_setpfm {%1};
	};
	#CLASS kungfuclass CLOSE;
	skills;
	jifa;
	echo {checkkungfu}
};
#NOP {获取一个空手技能};
#FUNCTION getFistSkill {
	#LOCAL {beiskill} {};
	#LIST {weaonskills} {create} {$common[normalweapon][]};
	#FOREACH {*kungfu[spec][]} {ssk} {
		#FOREACH {*kungfu[spec][$ssk][jifa][]} {bsk} {
			#IF {"$bsk" == "parry" || "$bsk" == "force" || "$bsk" == "dodge"} {
				#CONTINUE;
			};
			#IF {@contains{{weaonskills}{$bsk}} > 0} {
				#CONTINUE;
			};
			#IF {"$kungfu[base][$bsk][jifa]" != ""} {
				#LOCAL {beiskill} {$bsk};
				#BREAK;
			};
		};
		#IF {"$beiskill" != ""} {
			#BREAK;
		};
	};
	#RETURN {$beiskill};
};
#NOP {获取一个基础技能对应的特殊技能,%1:基础技能};
#FUNCTION getMapSkill {
	#FOREACH {*kungfu[spec][]} {ssk} {
		#IF {@contains{{conf[ignoreskills]}{$ssk}} > 0} {
			#CONTINUE;
		};
		#FOREACH {*kungfu[spec][$ssk][jifa][]} {bsk} {
			#IF {"$bsk" == "%1"} {
				#RETURN {$ssk};
			};
		};
	};
	#RETURN {};
};
#NOP {根据中文名称获取技能英文名称};
#FUNCTION getSkillFromName {
	#FOREACH {*kungfu[spec][]} {sk} {
		#IF {"$kungfu[spec][$sk][name]" == "%1"} {
			#RETURN {$sk};
		};
	};
	#FOREACH {*kungfu[base][]} {sk} {
		#IF {"$kungfu[base][$sk][name]" == "%1"} {
			#RETURN {$sk};
		};
	};
	#FOREACH {*kungfu[know][]} {sk} {
		#IF {"$kungfu[know][$sk][name]" == "%1"} {
			#RETURN {$sk};
		};
	};
	#RETURN {};
};
#NOP {获取技能等级,%1:英文名称};
#FUNCTION getSkillLevel {
	#IF {"$kungfu[craft][%1][lv]" != ""} {
		#RETURN {$kungfu[craft][%1][lv]};
	};
	#IF {"$kungfu[know][%1][lv]" != ""} {
		#RETURN {$kungfu[know][%1][lv]};
	};
	#ELSEIF {"$kungfu[base][%1][lv]" != ""} {
		#RETURN {$kungfu[base][%1][lv]};
	};
	#ELSEIF {"$kungfu[spec][%1][lv]" != ""} {
		#RETURN {$kungfu[spec][%1][lv]};
	};
	#ELSE {
		#RETURN {0};
	};
};
#NOP {获取技能忽略标志,%1:英文名称};
#FUNCTION getSkillIgnore {
	#IF {"$kungfu[know][%1]" != ""} {
		#RETURN {$kungfu[know][%1][ignore]};
	};
	#ELSEIF {"$kungfu[base][%1]" != ""} {
		#RETURN {$kungfu[base][%1][ignore]};
	};
	#ELSEIF {"$kungfu[spec][%1]" != ""} {
		#RETURN {$kungfu[spec][%1][ignore]};
	};
	#ELSE {
		#RETURN {};
	};
};
#NOP {清除技能忽略标识,%1:要清除的技能名称,不传则清空所有};
#ALIAS {clearskillignore} {
	#IF {"$kungfu[know][%1]" != ""} {
		#VARIABLE {kungfu[know][%1][ignore]} {};
	};
	#ELSEIF {"$kungfu[base][%1]" != ""} {
		#VARIABLE {kungfu[base][%1][ignore]} {};
	};
	#ELSEIF {"$kungfu[spec][%1]" != ""} {
		#VARIABLE {kungfu[spec][%1][ignore]} {};
	};
	#ELSE {
		#FOREACH {*kungfu[spec][]} {sk} {
			#VARIABLE {kungfu[spec][$sk][ignore]} {};
		};
		#FOREACH {*kungfu[base][]} {sk} {
			#VARIABLE {kungfu[base][$sk][ignore]} {};
		};
		#FOREACH {*kungfu[know][]} {sk} {
			#VARIABLE {kungfu[know][$sk][ignore]} {};
		};
	};
};
#ALIAS {kungfuclear} {
	#CLASS bookclass KILL;
	#CLASS readclass KILL;
	#CLASS learnclass KILL;
	#CLASS lingwuclass KILL;
	#CLASS lianclass KILL;
};
#NOP {等待时尝试练功,优先force,dodge,%1:是否保存内力};
#ALIAS {waitlian} {
	#IF {$hp[neili] > $threshold_neili} {
		#VARIABLE {lianskill} {@getLianSkill{2}};
		#IF {"$lianskill" != "" && @getSkillLevel{$lianskill[spec]} >= 220} {
			#LOCAL {nowweapon} {$id[weapon]};
			#LOCAL {targetweapon} {$common[weaponmapping][$lianskill[base]][+1]};
			#LOCAL {weaponchanged} {0};
			#IF {"$lianskill[base]" != "force" && "$targetweapon" != "$nowweapon"} {
				#IF {"$nowweapon" != ""} {
					addcmd {unwield $nowweapon};
					#LOCAL {nowweapon} {};
				};
				#IF {"$targetweapon" != ""} {
					#IF {"$conf[armor][glove]" != ""} {
						addcmd {remove $conf[armor][glove]}; 
					};
					addcmd {wield $common[weaponmapping][$lianskill[base]][+1]};
					#LOCAL {nowweapon} {$common[weaponmapping][$lianskill[base]][+1]};
				};
			};
			#LOCAL {oldjifa} {$kungfu[base][$lianskill[base]][jifa]};
			#LOCAL {liantimes} {@eval{$hp[exp]/10000000}};
			#IF {$liantimes < 1} {
				#LOCAL {liantimes} {1}
			};
			#IF {$liantimes > 5} {
				#LOCAL {liantimes} {5}
			};
			#NOP {当空手技能已经bei无法重新jifa为其他特殊技能，必须先bei none};
			#LOCAL {changebei} {0};
			#IF {@contains{{common[fist]}{$lianskill[base]}} > 0 && "$kungfu[base][$lianskill[base]][jifa]" != "$lianskill[spec]" && "$kungfu[bei][$lianskill[base]]" != ""} {
				#LOCAL {changebei} {1};
			};
			#IF {$changebei == 1} {
				addcmd {bei none};
				addcmd {jifa $lianskill[base] $lianskill[spec]};
				addcmd {bei $lianskill[base]};
			};
			#ELSE {
				addcmd {jifa $lianskill[base] $lianskill[spec]};
			};

			#LOCAL {liantimes} {@getLianTimes{{$lianskill[base]}{$lianskill[spec]}{@eval{$hp[neili] - $threshold_neili}}}};

			addcmd {lian $lianskill[base] $liantimes};
			addcmd {yun jingli};
			addcmd {yun jing};

			#IF {$changebei == 1} {
				addcmd {bei none};
				#FOREACH {*kungfu[jobbei][]} {bsk} {
					addcmd {jifa $bsk $kungfu[jobbei][$bsk]};
					addcmd {bei $bsk};
				};
			};
			#IF {"$oldjifa" != "$lianskill[spec]"} {
				addcmd {jifa $lianskill[base] $oldjifa};
			};
			#IF {"$nowweapon" != "$conf[weapon][primary]"} {
				#IF {"$nowweapon" != ""} {
					addcmd {unwield $nowweapon};
				};
				#IF {"$conf[weapon][primary]" != ""} {
					addcmd {wield $conf[weapon][primary]};
				};
				#ELSEIF {"$conf[armor][glove]" != ""} {
					addcmd {wear $conf[armor][glove]};
				};
			};
			addcmd {i};
			addcmd {yun jingli};
			addcmd {hp};
			addcmd {skills};
			execute;
		};
		#ELSE {
			#IF {$hp[neili_max] >= 6000 && $hp[exp] < 100000000} {
				closesaving;
			};
		};
	};
};
#NOP {学习本草术理,%1:后续指令,%2:目标等级};
#ALIAS {fullmedicine} {
	#NOP {你以五十一两白银又五十文铜钱的价格从买卖提那里买下了一本寓意草。};
	#CLASS bookclass OPEN;
	#ACTION {^{设定环境变量：action \= \"checkmedicine\"|你设定checkmedicine为反馈信息}} {
		#CLASS bookclass KILL;
		#IF {"$kungfu[know][medicine]" == ""} {
			#LOCAL {templv} {0};
		};
		#ELSE {
			#LOCAL {templv} {$kungfu[know][medicine][lv]};
		};
		#LOCAL {targetlv} {121};
		#IF {"%2" != "" && @isNumber{%2}} {
			#LOCAL {targetlv} {%2};
		};
		#VARIABLE {nextdo} {%1};
		#NOP {脚本运行时极小概率会出现某种BUG，导致技能抓取问题。这里判断一下出现了就退出};
		#IF {$hp[exp] > 2000000 && ("$kungfu[know][medicine]" == "" || "$kungfu[base][medicine]" != "")} {
			#KILL ALL;
			#zap
		};
		#IF {$templv >= $targetlv} {
			#SHOWME {<faa>你已经出师了};
			%1
		};
		#ELSEIF {$templv < 32} {
			#IF {$targetlv > 32} {
				#VARIABLE {nextdo} {fullmedicine {%1} {%2}};
			};
			#IF {"$id[things][ji fang]" != ""} {
				goreadbook {ji fang} {$nextdo} {} {} {medecine} {32};
			};
			#ELSE {
				getbook_jifang {
					goreadbook {ji fang} {$nextdo} {} {} {medecine} {32};
				};
			};
		};
		#ELSEIF {$templv < 42} {
			#IF {$targetlv > 42} {
				#VARIABLE {nextdo} {fullmedicine {%1} {%2}};
			};
			#IF {"$id[things][yuyi cao]" != ""} {
				goreadbook {yuyi cao} {$nextdo} {} {} {medecine} {42};
			};
			#ELSE {
				getbook_yuyicao {
					goreadbook {yuyi cao} {$nextdo} {} {} {medecine} {42};
				};
			};
		};
		#ELSEIF {$templv < 52} {
			#IF {$targetlv > 52} {
				#VARIABLE {nextdo} {fullmedicine {%1} {%2}};
			};
			#IF {"$id[things][sangang shilue]" != ""} {
				goreadbook {sangang shilue} {$nextdo} {} {} {medecine} {52};
			};
			#ELSE {
				getbook_sangang {
					goreadbook {sangang shilue} {$nextdo} {} {} {medecine} {52};
				};
			};
		};
		#ELSEIF {$templv < 62} {
			#IF {$targetlv > 62} {
				#VARIABLE {nextdo} {fullmedicine {%1} {%2}};
			};
			#IF {"$id[things][kejin jijie]" != ""} {
				goreadbook {kejin jijie} {$nextdo} {} {} {medecine} {62};
			};
			#ELSE {
				#NOP {添加保姆支持,但是用完要还回去};
				library_call {kejin jijie} {
					goreadbook {kejin jijie} {$nextdo} {1} {} {medecine} {62};
				} {
					getbook_kejin {
						goreadbook {kejin jijie} {$nextdo} {} {} {medecine} {62};
					};
				};
			};
		};
		#ELSEIF {$templv < 72} {
			#IF {$targetlv > 72} {
				#VARIABLE {nextdo} {fullmedicine {%1} {%2}};
			};
			#IF {"$id[things][douzhen dinglun]" != ""} {
				goreadbook {douzhen dinglun} {$nextdo} {} {} {medecine} {72};
			};
			#ELSE {
				library_call {douzhen dinglun} {
					goreadbook {douzhen dinglun} {$nextdo} {1} {} {medecine} {72};
				} {
					getbook_douzhen {
						goreadbook {douzhen dinglun} {$nextdo} {1} {} {medecine} {72};
					};
				};
			};
		};
		#ELSEIF {$templv < 82} {
			#IF {$targetlv > 82} {
				#VARIABLE {nextdo} {fullmedicine {%1} {%2}};
			};
			#IF {"$id[things][boji xidoufang]" != ""} {
				goreadbook {boji xidoufang} {$nextdo} {} {} {medecine} {82};
			};
			#ELSE {
				library_call {boji xidoufang} {
					goreadbook {boji xidoufang} {$nextdo} {} {} {medecine} {82};
				} {
					getbook_boji {
						goreadbook {boji xidoufang} {$nextdo} {} {} {medecine} {82};
					};
				};
			};
		};
		#ELSEIF {$templv < 102} {
			#IF {$targetlv > 102} {
				#VARIABLE {nextdo} {fullmedicine {%1} {%2}};
			};
			#IF {"$id[things][bencao gangmu]" != ""} {
				goreadbook {bencao gangmu} {$nextdo} {} {} {medecine} {102};
			};
			#ELSE {
				getbook_bencaogangmu {
					goreadbook {bencao gangmu} {$nextdo} {} {} {medecine} {102};
				};
			};
		};
		#ELSEIF {$templv < 122} {
			#VARIABLE {nextdo} {fullmedicine {%1} {%2}};
			#IF {"$id[things][bencao jizhu]" != ""} {
				goreadbook {bencao jizhu} {$nextdo} {} {} {medecine} {122};
			};
			#ELSE {
				getbook_bencaojizhu {
					goreadbook {bencao jizhu} {$nextdo} {} {} {medecine} {122};
				};
			};
		};
		#ELSE {
			#SHOWME {<faa>你已经出师了};
			%1
		};
	};
	#CLASS bookclass CLOSE;
	hp;
	i;
	skills;
	echo {checkmedicine}
};
#NOP {肘后备急方};
#ALIAS {getbook_jifang} {
	gotodo {扬州城} {药铺} {getbook_jifang_start {%1}}
};
#ALIAS {getbook_jifang_start} {
	#VARIABLE {oom} {0};
	#CLASS bookclass KILl;
	#CLASS bookclass OPEN;
	#ACTION {^%*说道：「穷光蛋，一边呆着去！」} {
		#VARIABLE {oom} {1};
	};
	#ACTION {^{设定环境变量：action \= \"checkbook\"|你设定checkbook为反馈信息}} {
		#DELAY {2} {
			#IF {@carryqty{ji fang}} {
				#CLASS bookclass KILl;
				%1;
			};
			#ELSEIF {$oom == 1} {
				#CLASS bookclass KILl;
				gotodo {扬州城} {天阁斋} {
					qu 2 gold;
					getbook_jifang {%1};
				};
			};
			#ELSE {
				buy ji fang;
				i;
				echo {checkbook}
			};
		};
	};
	#CLASS bookclass CLOSE;
	buy ji fang;
	i;
	echo {checkbook}
};
#NOP {寓意草};
#ALIAS {getbook_yuyicao} {
	gotodo {星宿海} {商铺} {getbook_yuyicao_start {%1}}
};
#ALIAS {getbook_yuyicao_start} {
	#VARIABLE {oom} {0};
	#CLASS bookclass KILl;
	#CLASS bookclass OPEN;
	#ACTION {^%*说道：「穷光蛋，一边呆着去！」} {
		#VARIABLE {oom} {1};
	};
	#ACTION {^{设定环境变量：action \= \"checkbook\"|你设定checkbook为反馈信息}} {
		#DELAY {2} {
			#IF {@carryqty{yuyi cao}} {
				#CLASS bookclass KILl;
				%1;
			};
			#ELSEIF {$oom == 1} {
				#CLASS bookclass KILl;
				gotodo {星宿海} {万宝斋} {
					qu 2 gold;
					getbook_jifang {%1};
				};
			};
			#ELSE {
				buy yuyi cao;
				i;
				echo {checkbook}
			};
		};
	};
	#CLASS bookclass CLOSE;
	buy yuyi cao;
	i;
	echo {checkbook}
};
#NOP {三冈识略};
#ALIAS {getbook_sangang} {
	gotodo {明教} {药房} {getbook_sangang_start {%1}}
};
#ALIAS {getbook_sangang_start} {
	#VARIABLE {oom} {0};
	#CLASS bookclass KILl;
	#CLASS bookclass OPEN;
	#ACTION {^%*说道：「穷光蛋，一边呆着去！」} {
		#VARIABLE {oom} {1};
	};
	#ACTION {^{设定环境变量：action \= \"checkbook\"|你设定checkbook为反馈信息}} {
		#DELAY {2} {
			#IF {@carryqty{sangang shilue}} {
				#CLASS bookclass KILl;
				%1;
			};
			#ELSEIF {$oom == 1} {
				#CLASS bookclass KILl;
				gotodo {明教} {勒马斋} {
					qu 2 gold;
					getbook_jifang {%1};
				};
			};
			#ELSE {
				buy sangang shilue;
				i;
				echo {checkbook}
			};
		};
	};
	#CLASS bookclass CLOSE;
	buy sangang shilue;
	i;
	echo {checkbook}
};
#NOP {科金镜赋集解};
#ALIAS {getbook_kejin} {
	gotodo {扬州城} {药铺} {getbook_kejin_ask {%1}};
};
#ALIAS {getbook_kejin_ask} {
	#CLASS bookclass KILL;
	#CLASS bookclass OPEN;
	#ACTION {^药铺老板说道：「你是说那本[科金镜赋集解]吧，前几天还在这里卖的，不知道给哪个家伙} {
		dohalt {
			#IF {@carryqty{kao ya} == 0} {
				dohalt {
					gotodo {扬州城} {小吃店} {
						buy kao ya;
						i;
						echo {checkkaoya};
					}
				}
			};
			#ELSE {
				#CLASS bookclass KILL;
				getbook_kejin_start {%1};
			};
		}
	};
	#ACTION {^{设定环境变量：action \= \"checkkaoya\"|你设定checkkaoya为反馈信息}} {
		#DELAY {1} {
			#IF {@carryqty{kao ya} == 0} {
				buy kao ya;
				i;
				echo {checkkaoya};
			};
			#ELSE {
				#CLASS bookclass KILL;
				getbook_kejin_start {%1};
			};
		}
	};
	#CLASS bookclass CLOSE;
	drop kao ya;
	i;
	ask yaopu laoban about 医书;
};
#NOP {找空空儿};
#ALIAS {getbook_kejin_start} {
	#LIST {roomlist} {create} {1;405;406;407;408;409;410;411;413;415;416;417;418;419;420;421;422;423;424;425;427;428;429;430;431;433;435;436;437;438;439;440;441;442;443;444;445;446;447;448;449;450;451;452;453;454;455;456;457;458;459;460;461;462;463;464;465;466;467;468;469;470;471;472;473;474;475;478;479;480;481;483;484;492;493;494;495;496;497;498;500;501;502;503;504;505;506;507;508;537;538;539};
	#LOCAL {temproomid} {$roomlist[+1]};
	#LIST {roomlist} {delete} {1};
	#CLASS bookclass KILL;
	#CLASS bookclass OPEN;
	#ACTION {^这里没有 kong kong。} {
		#IF {&roomlist[] == 0} {
			#CLASS bookclass KILL;
			#SHOWME {<faa>找不到空空儿};
			getbook_kejin {%1};
		};
		#ELSE {
			#LOCAL {temproomid} {$roomlist[+1]};
			#LIST {roomlist} {delete} {1};
			runwait {gotodo {扬州城}{$temproomid}{follow kong kong}};
		};
	};
	#ACTION {^你决定跟随空空儿一起行动。} {
		dohalt {
			ask kong kong about 医书;
		};
	};
	#ACTION {^空空儿说道：「嘻嘻，你说那本书啊，是在我手里，不过。。我好象不记得放哪了?} {
		dohalt {
			give kao ya to kong kong;
			i;
			echo {checkbook}
		}
	};
	#ACTION {^{设定环境变量：action \= \"checkbook\"|你设定checkbook为反馈信息}} {
		#IF {@carryqty{kejin jijie} == 1} {
			#CLASS bookclass KILL;
			%1;
		};
		#ELSE {
			kill kong kong;
		};
	};
	#ACTION {^空空儿「啪」的一声倒在地上，挣扎着抽动了几下就死了} {
		#CLASS bookclass KILL;
		gotodo {扬州城} {小吃店} {startfull {getbook_kejin {%1}} {3}};
	};
	#CLASS bookclass CLOSE;
	gotodo {扬州城}{$temproomid}{follow kong kong};
};
#NOP {痘疹定论};
#ALIAS {getbook_douzhen} {
	gotonpc {采药道长} {getbook_douzhen_start {%1}};
};
#ALIAS {getbook_douzhen_start} {
	#CLASS bookclass KILL;
	#CLASS bookclass OPEN;
	#ACTION {^采药道长交给你一本痘疹定论} {
    #CLASS bookclass KILL;
		i;
		dohalt {%1};
	};
	#ACTION {^采药道长说道：「%*是否打探到了结果？」} {
    #CLASS bookclass KILL;
		#NOP {退出清除临时标志};
		#NOP {这里如整体将%1存入alias再次载入会有问题,这里仅捕捉%1中fullmedicine及其参数};
		#LOCAL {tparam} {%1};
		#REPLACE {tparam} {\x7B} {<};
		#REPLACE {tparam} {\x7D} {>};
		#REGEXP $tparam {fullmedicine <%*> <%*>} {
			setlogindo {fullmedicine} {&1} {&2};
			doquit;
		} {%1};
	};
	#CLASS bookclass CLOSE;
	ask caiyao daozhang about 只是;
};
#NOP {博集稀痘方};
#ALIAS {getbook_boji} {
	gotonpc {老翁} {getbook_boji_start {%1}};
};
#ALIAS {getbook_boji_start} {
	#CLASS bookclass KILL;
	#CLASS bookclass OPEN;
	#ACTION {^无名老翁说道：「昔年一位武当采药道长失足坠落后山悬崖，我正好采药路过} {
		#DELAY {2} {gotonpc {采药道长} {ask caiyao daozhang about 结果};};
	};
	#ACTION {^{采药道长交给你一本博集稀痘方。|采药道长对你失望极了}} {
		#CLASS bookclass KILL;
		i;
		dohalt {%1};
	};
	#CLASS bookclass CLOSE;
	ask lao weng about 搭救;
};
#NOP {本草纲目};
#ALIAS {getbook_bencaogangmu} {
	gotodo {明教} {1638} {getbook_bencaogangmu_start {%1}};
};
#ALIAS {getbook_bencaogangmu_start} {
	#VARIABLE {startts} {@now{}};
	#VARIABLE {okflag} {0};
	#CLASS bookclass KILL;
	#CLASS bookclass OPEN;
	#ACTION {^张中把手一伸拦住你的去路，你已拿过书了还要} {
		#VARIABLE {okflag} {1};
	} {1};
	#ACTION {^{设定环境变量：action \= \"checkzhang\"|你设定checkzhang为反馈信息}} {
		#IF {$okflag == 1} {
			#CLASS bookclass KILL;
			#NOP {这里如整体将%1存入alias再次载入会有问题,这里仅捕捉%1中fullmedicine及其参数};
			#LOCAL {tparam} {%1};
			#REPLACE {tparam} {\x7B} {<};
			#REPLACE {tparam} {\x7D} {>};
			#REGEXP $tparam {fullmedicine <%*> <%*>} {
				setlogindo {fullmedicine} {jobcheck} {&2};
				doquit;
			} {%1};
		};
		#ELSE {
			w;
			get bencao gangmu;
		};
	};
	#ACTION {^你附近没有这样东西。} {
		#VARIABLE {idle} {0};
		#IF {@elapsed{$startts} < 3600} {
			#DELAY {6} {
				get bencao gangmu;
			}
		};
		#ELSE {
			#CLASS bookclass KILL;
			%1;
		};
	};
	#ACTION {^你捡起一本} {
		#CLASS bookclass KILL;
		i;
		loc {%1};
	};
	#CLASS bookclass CLOSE;
	w;
	#DELAY {0.5} {
		echo {checkzhang};
	};
};
#NOP {本草经集注};
#ALIAS {getbook_bencaojizhu} {
	gotodo {明教} {1638} {getbook_bencaojizhu_start {%1}};
};
#ALIAS {getbook_bencaojizhu_start} {
	#VARIABLE {startts} {@now{}};
	#VARIABLE {okflag} {0};
	#CLASS bookclass KILL;
	#CLASS bookclass OPEN;
	#ACTION {^张中把手一伸拦住你的去路，你已拿过书了还要} {
		#VARIABLE {okflag} {1};
	} {1};
	#ACTION {^{设定环境变量：action \= \"checkzhang\"|你设定checkzhang为反馈信息}} {
		#IF {$okflag == 1} {
			#CLASS bookclass KILL;
			#NOP {这里如整体将%1存入alias再次载入会有问题,这里仅捕捉%1中fullmedicine及其参数};
			#LOCAL {tparam} {%1};
			#REPLACE {tparam} {\x7B} {<};
			#REPLACE {tparam} {\x7D} {>};
			#REGEXP $tparam {fullmedicine <%*> <%*>} {
				setlogindo {fullmedicine} {jobcheck} {&2};
				doquit;
			} {%1};
		};
		#ELSE {
			w;
			get bencao jizhu;
		};
	};
	#ACTION {^你附近没有这样东西。} {
		#VARIABLE {idle} {0};
		#IF {@elapsed{$startts} < 3600} {
			#DELAY {6} {
				get bencao jizhu;
			}
		};
		#ELSE {
			#CLASS bookclass KILL;
			%1;
		};
	};
	#ACTION {^你捡起一本} {
		#CLASS bookclass KILL;
		i;
		loc {%1};
	};
	#CLASS bookclass CLOSE;
	w;
	#DELAY {0.5} {
		echo {checkzhang};
	};
};
#NOP {经脉学,%1:后续操作,%2:目标等级};
#ALIAS {fulljingmai} {
	#CLASS bookclass OPEN;
	#ACTION {^{设定环境变量：action \= \"checkmedicine\"|你设定checkmedicine为反馈信息}} {
		#CLASS bookclass KILL;
		#LOCAL {templv} {@getSkillLevel{jingmai-xue}};
		#LOCAL {targetlv} {121};
		#IF {"%2" != ""} {
			#LOCAL {targetlv} {%2};
		};
		#IF {$templv >= $targetlv} {
			%1;
		};
		#VARIABLE {nextdo} {%1};
		#ELSEIF {$templv < 41} {
			#IF {$targetlv > 41} {
				#VARIABLE {nextdo} {fulljingmai {%1} {%2}};
			};
			#IF {"$id[things][jingmai book]" != ""} {
				godubook {book} {$nextdo} {} {} {jingmai-xue} {41};
			};
			#ELSE {
				getbook_jingmai1 {
					godubook {book} {$nextdo} {} {} {jingmai-xue} {41};
				};
			};
		};
		#ELSEIF {$templv < 81} {
			#IF {$targetlv > 81} {
				#VARIABLE {nextdo} {fulljingmai {%1} {%2}};
			};
			#IF {"$id[things][jingmai book]" != ""} {
				godubook {book} {$nextdo} {} {} {jingmai-xue} {81};
			};
			#ELSE {
				getbook_jingmai2 {
					godubook {book} {$nextdo} {} {} {jingmai-xue} {81};
				};
			};
		};
		#ELSEIF {$templv < 121} {
			#IF {$targetlv > 121} {
				#VARIABLE {nextdo} {fulljingmai {%1} {%2}};
			};
			#IF {"$id[things][jingmai book]" != ""} {
				godubook {book} {$nextdo} {} {} {jingmai-xue} {121};
			};
			#ELSE {
				getbook_jingmai3 {
					godubook {book} {$nextdo} {} {} {jingmai-xue} {121};
				};
			};
		};
		#ELSEIF {$templv < 141} {
			#IF {$targetlv > 141} {
				#VARIABLE {nextdo} {fulljingmai {%1} {%2}};
			};
			#IF {"$id[things][jingmai book]" != ""} {
				godubook {book} {$nextdo} {} {} {jingmai-xue} {141};
			};
			#ELSE {
				getbook_jingmai4 {
					godubook {book} {$nextdo} {} {} {jingmai-xue} {141};
				};
			};
		};
		#ELSEIF {$templv < 151} {
			#IF {$targetlv > 151} {
				#VARIABLE {nextdo} {fulljingmai {%1} {%2}};
			};
			#IF {"$id[things][jingmai book]" != ""} {
				godubook {book} {$nextdo} {} {} {jingmai-xue} {151};
			};
			#ELSE {
				getbook_jingmai5 {
					godubook {book} {$nextdo} {} {} {jingmai-xue} {151};
				};
			};
		};
		#ELSEIF {$templv < 161} {
			#IF {$targetlv > 161} {
				#VARIABLE {nextdo} {fulljingmai {%1} {%2}};
			};
			#IF {"$id[things][jingmai book]" != ""} {
				godubook {book} {$nextdo} {} {} {jingmai-xue} {161};
			};
			#ELSE {
				getbook_jingmai6 {
					godubook {book} {$nextdo} {} {} {jingmai-xue} {161};
				};
			};
		};
		#ELSEIF {$templv < 171} {
			#IF {$targetlv > 171} {
				#VARIABLE {nextdo} {fulljingmai {%1} {%2}};
			};
			#IF {"$id[things][jingmai book]" != ""} {
				godubook {book} {$nextdo} {} {} {jingmai-xue} {171};
			};
			#ELSE {
				getbook_jingmai7 {
					godubook {book} {$nextdo} {} {} {jingmai-xue} {171};
				};
			};
		};
		#ELSE {
			#SHOWME {<faa>你已经出师了};
			%1
		};
	};
	#CLASS bookclass CLOSE;
	hp;
	i;
	skills;
	echo {checkmedicine};
};
#NOP {初级经脉学,1~41};
#ALIAS {getbook_jingmai1} {
	#CLASS bookclass OPEN;
	#ACTION {^药铺伙计说道：「你想买的东西我这里没有。」} {
		#CLASS bookclass KILL;
		%1
	};
	#ACTION {^你以%*的价格从%*那里买下了} {
		#CLASS bookclass KILL;
		#DELAY {1} {
			godubook {book} {fulljingmai {%1}} {} {} {jingmai-xue};
		};
	};
	#CLASS bookclass CLOSE;
	gotodo {扬州城} {药铺} {buy jingmai book}
};
#NOP {进阶经脉学,42~81};
#ALIAS {getbook_jingmai2} {
	#CLASS bookclass OPEN;
	#ACTION {^薛慕华说道：「你想买的东西我这里没有。」} {
		#CLASS bookclass KILL;
		%1
	};
	#ACTION {^你以%*的价格从薛慕华那里买下了} {
		#CLASS bookclass KILL;
		#DELAY {1} {
			godubook {book} {fulljingmai {%1}} {} {} {jingmai-xue};
		};
	};
	#CLASS bookclass CLOSE;
	gotonpc {薛慕华} {buy jingmai book}
};

#NOP {高级经脉学,82~121};
#ALIAS {getbook_jingmai3} {
	#CLASS bookclass OPEN;
	#ACTION {^平一指说道：「你想买的东西我这里没有。」} {
		#CLASS bookclass KILL;
		%1
	};
	#ACTION {^平一指说道：「穷光蛋，一边呆着去！」} {
		#DELAY {0.5} {
			e;e;n;w;qu 15 gold;e;s;w;w;
			buy jingmai book
		};
	};
	#ACTION {^你以%*的价格从平一指那里买下了} {
		#CLASS bookclass KILL;
		#DELAY {1} {
			godubook {book} {fulljingmai {%1}} {} {} {jingmai-xue};
		}
	};
	#CLASS bookclass CLOSE;
	gotonpc {平一指} {buy jingmai book}
};

#NOP {针灸概论,122~141};
#NOP {针灸概论→薛幕华处ask xue about 武功然后teach xue ***教会薛幕华几种武功后ask xue about 学问};
#ALIAS {getbook_jingmai4} {
	#CLASS bookclass OPEN;
	#ACTION {^薛慕华说道：「听说阁下武功不错。。。」} {
		dohalt {ask xue about 武功}
	};
	#ACTION {^薛慕华说道：「多谢，我会好好报答的。请使用 teach xue <skill> 来指导我。」} {
		#LIST {tskills} {create} {*kungfu[spec][]};
		#VARIABLE {skindex} {1};
		#VARIABLE {teachmode} {0};
		dohalt {
			teach xue $tskills[+$skindex];
		};
	};
	#ACTION {^你向薛慕华仔细地解说} {
		#VARIABLE {teachmode} {1};
		teach xue $tskills[+$skindex];
	};

	#ACTION {^{你的这个技能太差了|薛神医的这个技能已经不能再进步了}} {
		#IF {$teachmode == 0} {
			#MATH {skindex} {$skindex + 1};
			#IF {$skindex <= &tskills[]} {
				teach xue $tskills[+$skindex];
			};
			#ELSE {
				#CLASS bookclass kill;
				#DELAY {1} {%1};
			};
		};
		#ELSE {
			#VARIABLE {teachmode} {0};
			dohalt {ask xue about 学问};
		};
	};
	#ACTION {^薛神医给你一本针灸概论。} {
		#CLASS bookclass KILL;
		#DELAY {1} {
			godubook {book} {fulljingmai {%1}} {} {} {jingmai-xue};
		}
	};
	#CLASS bookclass CLOSE;
	gotonpc {薛慕华} {ask xue about 武功}
};
#NOP {孙思邈千斤方,142~151};
#NOP {薛幕华处ask xue about 学问 ask xue about 救人 找到薛幕华指定的玩家【heal 玩家id】,只给房间名，不给城市名,蛋疼};
#ALIAS {getbook_jingmai5} {
	#CLASS bookclass OPEN;
	#ACTION {^薛慕华说道：「学医者要有任道之心，你是否愿意帮我去救个人？」} {
		dohalt {ask xue about 救人}
	};
	#ACTION {^薛慕华说道：「请找到%*(%*)。」} {
		#VARIABLE {playername} {%%1};
		#VARIABLE {playerid} {%%2};
	};
	#ACTION {^薛慕华说道：「你去%*找找看。」} {
		#VARIABLE {playeradd} {%%1};
		dohalt {gotoroom {$playeradd} {heal $playerid}};
	};
	#ACTION {^你手指微弹，将药水撒向} {
		dohalt {gotonpc {薛慕华}};
	};
	#ACTION {^薛幕华给了你一本孙思邈千斤方。} {
		#CLASS bookclass KILL;
		dohalt {
			godubook {book} {fulljingmai {%1}} {} {} {jingmai-xue};
		};
	};
	#CLASS bookclass CLOSE;
	gotonpc {薛慕华} {ask xue about 学问}
};
#NOP {黄帝内经,152~161};
#NOP {平一指处【ask ping about 经脉学】【ask ping about 求教】杀掉平一指指定的NPC后【qie corpse】【give ping head】};
#ALIAS {getbook_jingmai6} {
	#CLASS bookclass OPEN;
	#ACTION {^平一指说道：「怎么？是来求教的么？」} {
		dohalt {ask ping about 求教}
	};
	#ACTION {^平一指说道：「你去帮我杀了%u(%*)吧。」} {
		#VARIABLE {pingtargetname} {%%1};
	};
	#ACTION {^平一指说道：「你去%u找找看。」} {
		#VARIABLE {joblocation} {%%1};
		parsejoblocation {$joblocation} {dohalt {jobnextroom {look}}};
	};
	#LINE ONESHOT #ACTION {$pingtargetname(%*)} {
		#VARIABLE {pingtargetid} {@lower{%%1}};
	};

	#ACTION {^{设定环境变量：action \= \"pingtarget\"|你设定pingtarget为反馈信息}} {
		#VARIABLE {idle} {0};
		#IF {"$pingtargetid" == ""} {
			jobnextroom {look}
		};
		#ELSE {
			kill $pingtargetid
		};
	};
	#ACTION {^$pingtargetname「啪」的一声倒在地上，挣扎着抽动了几下就死了。} {
		wcwp;
		get silver from corpse 1;
		get ling pai from corpse 1;
		qie corpse 1;
	};
	#ACTION {^你扬起%*，对准$pingtargetname的尸体的脖子比了比，猛斩了下去！} {
		gotonpc {平一指} {give shouji to ping}
	};
	#ACTION {^平一指给了你一本黄帝内经。} {
		#CLASS bookclass KILL;
		#DELAY {1} {
			godubook {book} {fulljingmai {%1}} {} {} {jingmai-xue};
		};
	};
	#CLASS bookclass CLOSE;
	gotonpc {平一指} {ask ping about 经脉学}
};

#NOP {王叔和脉经,162~171};
#NOP {平一指处【ask ping about 求教】然后到南阳城断崖(pa ya)让气血到【1-4%】然后回到平一指处他就会给你了如果不给就【w;e】多走几回,走多也不给};
#ALIAS {getbook_jingmai7} {
	#CLASS bookclass OPEN;
	#ACTION {^平一指说道：「你的经脉学已经不错了，如果不是什么紧急情况，也够用了。」} {
		dohalt {gotodo {南阳城}{断崖}{checkpaya}};
	};
	#ACTION {^你气喘嘘嘘，感到无法爬上去，摔了下来！} {
		#variable idle 0;
		checkpaya
	};
	#ACTION {^平一指给了你一本王叔和脉经。} {
		#local getbook 1;
	};
	#alias checkping {
		#if {$getbook==1} {
			#unvariable getbook;
			#CLASS bookclass KILL;
			gotonpc {薛慕华} {xueheal {fulljingmai {171}}}
		};
		#ELSE 
		{
			e;w;#delay {3} {checkping}
		};
	};
	#alias checkpaya {
		hp;
		#delay {0.2} {
			#if {$hp[qi_per]>4} {pa ya};#else {gotonpc {平一指}{#variable getbook 0;checkping}}
		};
	};
	#CLASS bookclass CLOSE;
	gotonpc {平一指} {ask ping about 求教}
};
#NOP {子午针灸经,162~181};
#NOP {胡青牛处(到蝴蝶谷需要明教的ID带路)【ask hu about 经脉学】};
#ALIAS {getbook_jingmai8} 
{};
#NOP {带脉论,182~191};
#NOP {1.胡青牛处ask hu about 经脉学};
#NOP {2.高矮老者ask ai about 鲜于通;走法:先去华山寝室拿绳子，然后飞华山脚下，进去树林，然后经过空地，再进东南的山涧，输入指令bang shengzi然后输入climb up上去，看见山洞进去就是了};
#NOP {3.张无忌ask zhang about 鲜于通};
#NOP {4.胡青牛ask hu about 经脉学};
#ALIAS {getbook_jingmai9} 
{};
#NOP {华陀内昭图192~200};
#NOP {1.胡青牛处ask hu about 经脉学2.张无忌ask zhang about 胡青牛3.张无忌会给你一块铁焰令4.回到胡青牛处ask hu about 经脉学(杀掉半路上抢铁焰令的蒙古兵)};
#ALIAS {getbook_jingmai10} 
{};
#NOP {拿易经,%1:后续指令,%2:失败指令 };
#ALIAS {getbook_yijing} {
	#IF {"$hp[party]" != "桃花岛"} {
		%1
	};
	#ELSE {
		gotonpc {陆乘风} {getbook_yijing_start {%1} {%2}}
	};
};
#NOP {要易经,%1:后续指令,%2:失败指令};
#ALIAS {getbook_yijing_start} {
	#VARIABLE {missingflag} {0};
	#CLASS bookclass KILL;
	#CLASS bookclass OPEN;
	#ACTION {^这里没有这个人} {
		#VARIABLE {missingflag} {1};
	};
	#ACTION {^{设定环境变量：action \= \"checkbook\"|你设定checkbook为反馈信息}} {
		#CLASS bookclass KILL;
		#IF {$missingflag == 1} {
			#DELAY {6} {
				ask lu about 易经;
				i;
				echo {checkbook};
			};
		};
		#ELSE {
			dohalt {
				#IF {@carryqty{yi jing} == 1} {
					%1;
				};
				#ELSE {
					%2
				};
			}
		};
	};
	#CLASS bookclass CLOSE;
	ask lu about 易经;
	i;
	echo {checkbook};
};
#NOP {拿九宫八卦图,%1:后续指令,%2:失败指令};
#ALIAS {getbook_bagua} {
	#IF {"$hp[party]" != "桃花岛"} {
		%1
	};
	#ELSE {
		gotonpc {黄药师} {getbook_bagua_start {%1} {%2}}
	};
};
#NOP {拿九宫八卦图,%1:后续指令,%2:失败指令};
#ALIAS {getbook_bagua_start} {
	#CLASS bookclass KILL;
	#CLASS bookclass OPEN;
	#ACTION {^你向黄药师打听有关『奇门八卦』的消息} {
		#CLASS askresponseclass OPEN;
		#ACTION {^黄药师%*说道：「我把它放在书房了，你自己去找找看吧} {
			#CLASS askresponseclass KILL;
			dohalt {
				gotodo {桃花岛} {书房} {
					fan 书案;
					i;
					echo {checkbook};
				}
			};
		};
		#CLASS askresponseclass CLOSE;
	};
	#ACTION {^{设定环境变量：action \= \"checkbook\"|你设定checkbook为反馈信息}} {
		#CLASS bookclass KILL;
		#CLASS bookclass KILL;
		dohalt {
			#IF {@carryqty{jiugong tu} == 1} {
				%1;
			};
			#ELSE {
				%2
			};
		}
	};
	#CLASS bookclass CLOSE;
	ask huang about 奇门八卦;
};
#NOP {学习毒技，%1:后续指令};
#ALIAS {fullpoison} {
	#CLASS bookclass KILL;
	#CLASS bookclass OPEN;
	#ACTION {^{设定环境变量：action \= \"checkpoison\"|你设定checkpoison为反馈信息}} {
		#CLASS bookclass KILL;
		#IF {"$hp[shen]" == "正气"} {
			gofshen {50000} {fullpoison {%1}};
		};
		#ELSEIF {@getSkillLevel{poison} < 32} {
			#IF {@carryqty{yaoli jing} == 0} {
				getbook_yaolijing {
					godubook {jing} {fullpoison {%1}} {} {} {poison}
				}
			};
			#ELSE {
				godubook {jing} {fullpoison {%1}} {} {} {poison}
			};
		};
		#ELSEIF {@getSkillLevel{poison} < 152} {
			#IF {@carryqty{she jing} == 0} {
				library_call {she jing} {
					godubook {jing} {fullpoison {%1}} {1} {} {} {poison}
				} {
					getbook_shejing {
						godubook {jing} {fullpoison {%1}} {} {} {poison}
					}
				}
			};
			#ELSE {
				godubook {jing} {fullpoison {%1}} {} {} {poison}
			};
		};
		#ELSE {
			%1;
		};
	};
	#CLASS bookclass CLOSE;
	i;
	hp;
	skills;
	echo {checkpoison};
};
#ALIAS {getbook_yaolijing} {
	gotodo {明教} {书院} {
		getbook_yaolijing_get {%1};
	};
};
#ALIAS {getbook_yaolijing_get} {
	#CLASS bookclass KILL;
	#CLASS bookclass OPEN;
	#ACTION {^{设定环境变量：action \= \"checkbook\"|你设定checkbook为反馈信息}} {
		#IF {@carryqty{yaoli jing} == 0} {
			#DELAY {6} {
				get she jing;
				i;
				echo {checkbook};
			}
		};
		#ELSE {
			#CLASS bookclass KILL;
			%1;
		};
	};
	#CLASS bookclass CLOSE;
	get yaoli jing;
	i;
	echo {checkbook};
};
#NOP {拿蛇经,必须有蛤蟆功};
#ALIAS {getbook_shejing} {
	gotoroom {3428} {getbook_shejing_trigger {%1}}
};
#ALIAS {getbook_shejing_trigger} {
	#VARIABLE {startts} {0};
	#VARIABLE {checkok} {0};
	#VARIABLE {pushcount} {0};
	#VARIABLE {pushdo} {
		#VARIABLE {checkok} {0};
			look shikuai;
			push shikuai;
			echo {checkpush};
	};
	#CLASS bookclass KILL;
	#CLASS bookclass OPEN;
	#ACTION {^你刨开缝隙口的乱石子，一弯腰往缝隙里钻了进去} {
		dohalt {
			$pushdo;
		};
	};
	#ACTION {^你{将石块按下寸许|已经推动过了石块|要对谁做这个动作}} {
		#VARIABLE {checkok} {1};
	};
	#ACTION {^{设定环境变量：action \= \"checkpush\"|你设定checkpush为反馈信息}} {
		#VARIABLE {idle} {0};
		#IF {$checkok == 0} {
			#DELAY {1} {
				dohalt {
					$pushdo;
				};
			}
		};
		#ELSE {
			#VARIABLE {checkok} {0};
			#MATH {pushcount} {$pushcount + 1};
			dohalt {
				#IF {$pushcount == 1} {
					n;
					$pushdo;
				};
				#ELSEIF {$pushcount == 2} {
					w;
					$pushdo;
				};
				#ELSE {
					#VARIABLE {startts} {@now{}};
					out;
					loc {
						echo {checkdown};
					};
				};
			}
		};
	};
	#ACTION {^石柱忽然发出轧轧的声音，向一侧缓缓移开，向下露出一个黑洞洞的入口} {
		#VARIABLE {checkok} {1};
	};
	#ACTION {^{设定环境变量：action \= \"checkdown\"|你设定checkdown为反馈信息}} {
		#VARIABLE {idle} {0};
		#IF {$checkok == 0} {
			#DELAY {2} {
				#IF {@elapsed{$startts} > 120} {
					#CLASS bookclass KILL;
					getbook_shejing {%1};
				};
				#ELSE {
					echo {checkdown};
				};
			}
		};
		#ELSE {
			dohalt {
				#VARIABLE {startts} {@now{}};
				d;
				echo {checkmidao};
				loc {
					gotoroom {天井} {
						getbook_shejing_get {%1}
					};
				};
			}
		};
	};
	#ACTION {^{设定环境变量：action \= \"checkmidao\"|你设定checkmidao为反馈信息}} {
		#DELAY {1} {
			#IF {"$room" == "密道"} {
				loc {
					gotoroom {天井} {getbook_shejing_get {%1}};
				};
			};
			#ELSEIF {@elapsed{$startts} < 12} {
				d;
				echo {checkmidao};
			};
			#ELSE {
				#CLASS bookclass KILL;
				loc {
					getbook_shejing {%1};
				};
			};
		};
	};
	#CLASS bookclass CLOSE;
	dohalt {zuan fengxi};
};
#ALIAS {getbook_shejing_get} {
	#CLASS bookclass KILL;
	#CLASS bookclass OPEN;
	#ACTION {^{设定环境变量：action \= \"checkbook\"|你设定checkbook为反馈信息}} {
		#IF {@carryqty{she jing} == 0} {
			#DELAY {6} {
				get she jing;
				i;
				echo {checkbook};
			}
		};
		#ELSE {
			#CLASS bookclass KILL;
			%1;
		};
	};
	#CLASS bookclass CLOSE;
	get she jing;
	i;
	echo {checkbook};
};
#NOP {补奇门八卦,%1:后续指令};
#ALIAS {fullqmbg} {
	#IF {@getSkillLevel{qimen-bagua} < 31} {
		library_call {易经} {
			godubook {jing} {fullqmbg {%1}} {} {} {qimen-bagua};
		} {%1};
	};
	#ELSEIF {@getSkillLevel{qimen-bagua} < 51} {
		library_call {九宫八卦图谱} {
			godubook {tu} {fullqmbg {%1}} {} {} {qimen-bagua};
		} {%1};
	};
	#ELSEIF {@getSkillLevel{qimen-bagua} < 151} {
		learnbagua {%1}
	};
	#ELSE {
		%1;
	};
};
#NOP {读书%1:书,%2书读完后的操作,%3:是否需要还书,%4:如异常结束要进行的操作,%5:技能名称,%6:要到达的等级};
#ALIAS {goreadbook} {
	#IF {$hp[neili_max] < 5000} {
		gotodo {扬州城} {小吃店} {readbookyz {%1} {%2} {%3} {%4} {%5} {%6}}
	};
	#ELSE {
		gotodo {长安城} {鼓楼} {readbookca {%1} {%2} {%3} {%4} {%5} {%6}}
	};
};
#NOP {读书%1:书,%2书读完后的操作,%3:是否需要还书,%4:如异常结束要进行的操作,%5:技能名称,%6:要到达的等级};
#ALIAS {godubook} {
	#IF {$hp[neili_max] < 5000} {
		gotodo {扬州城} {小吃店} {dubookyz {%1} {%2} {%3} {%4} {%5} {%6}}
	};
	#ELSE {
		gotodo {长安城} {鼓楼} {dubookca {%1} {%2} {%3} {%4} {%5} {%6}}
	};
};
#NOP {洗澡读书%1:书,%2书读完后的操作,%3:是否需要还书,%4:如异常结束要进行的操作,%5:技能名称,%6:要到达的等级};
#ALIAS {readbookca} {
	dobookca {read} {%1} {%2} {%3} {%4} {%5} {%6};
};
#NOP {洗澡读书%1:书,%2书读完后的操作,%3:是否需要还书,%4:如异常结束要进行的操作,%5:技能名称,%6:要到达的等级};
#ALIAS {dubookca} {
	dobookca {du} {%1} {%2} {%3} {%4} {%5} {%6};
};
#NOP {睡觉读书%1:书,%2书读完后的操作,%3:是否需要还书,%4:如异常结束要进行的操作,%5:技能名称,%6:要到达的等级};
#ALIAS {readbookyz} {
	dobookyz {read} {%1} {%2} {%3} {%4} {%5} {%6};
};
#NOP {睡觉读书%1:书,%2书读完后的操作,%3:是否需要还书,%4:如异常结束要进行的操作,%5:技能名称,%6:要到达的等级};
#ALIAS {dubookyz} {
	dobookyz {du} {%1} {%2} {%3} {%4} {%5} {%6};
};
#NOP {扬州睡觉读书,%1:指令,%2:书籍,%3:后续指令,%4:是否需要还书,%5:如异常结束要进行的操作,%6:技能名称,%7:要到达的等级};
#ALIAS {dobookyz} {
	#VARIABLE {checkcount} {0};
	#VARIABLE {reading} {1};
	#VARIABLE {readdo} {
		#VARIABLE {reading} {0};
		execute {#10 %1 %2;yun jing;hp};
		echo {checkhp}
	};
	#VARIABLE {readover} {0};
	#CLASS readclass KILL;
	#CLASS readclass OPEN;
	#NOP {设上如果有全真连解谜的ce zi,读满后会有此提示};
	#ACTION {^你%*研{读|习}} {
		#VARIABLE {reading} {1};
	};
	#ACTION {^你要领悟什么} {
		#VARIABLE {readover} {1};
	};
	#ACTION {^这本书对于你来说已经太肤浅了} {
		#VARIABLE {readover} {1};
	};
	#ACTION {^你的实战经验不足，再怎么读也没用} {
		#VARIABLE {readover} {1};
	};
	#ACTION {^由于你的%*的火侯不够，不能再进行更高一层的提高} {
		#VARIABLE {readover} {1};
	};
	#ACTION {^你研读了一会儿，但是发现上面所说的对你而言都太浅了，没有学到任何东西。} {
		#VARIABLE {readover} {1};
	} {1};
	#ACTION {^你觉得这经书上所写的已经太浅了，不能学到什么东西。} {
		#VARIABLE {readover} {1};
	};
	#ACTION {^你的实战经验不足} {
		#VARIABLE {readover} {1};
	};
	#ACTION {^你的{内力|真气}不够} {
		#VARIABLE {reading} {1};
		#VARIABLE {readover} {2};
	};
	#ACTION {^你{内力|真气}不够} {
		#VARIABLE {reading} {1};
		#VARIABLE {readover} {2};
	};
	#ACTION {^你的潜能已经用完了，再怎么读也没用。} {
		#VARIABLE {reading} {1};
		#VARIABLE {readover} {3};
	};
	#ACTION {^怎么着，想白住我们宝昌客栈啊} {
		#DELAY {2} {
			execute {w;w;qu 5 silver;e;e;give 5 silver to xiao er;u;enter;sleep};
		};
	};
	#ACTION {^{设定环境变量：action \= \"checkhp\"|你设定checkhp为反馈信息}} {
		#VARIABLE {idle} {0};
		#MATH {checkcount} {$checkcount + 1};
		#IF {$hp[neili] < 400 && $hp[jing] < 100} {
			#VARIABLE {readover} {2};
		};
		#IF {"%6" != "" && "%7" != ""} {
			#IF {@getSkillLevel{%6} >= %7} {
				#VARIABLE {readover} {1};
			};
		};
		#IF {$checkcount >= 5} {
			#VARIABLE {checkcount} {0};
			skills;
		};
		#IF {$reading == 0} {
			#VARIABLE {readover} {1};
		};
		#VARIABLE {reading} {0};
		#DELAY {0.5} {
			#IF {"$caller" != ""} {
				#CLASS readclass KILL;
				checkrequest {gotodo {扬州城} {小吃店} {dobookyz {%1} {%2} {%3} {%4} {%5} {%6} {%7}}};
			};
			#ELSE {
				#SWITCH {$readover} {
					#CASE {1} {
						skills;
						echo {checkover};
					};
					#CASE {2} {
						execute {w;s;w;qu 5 silver;e;e;give 5 silver to xiao er;u;enter;sleep};
					};
					#CASE {3} {
						#CLASS readclass KILL;
						#DELAY {1} {
							qu_pot {gotodo {扬州城} {小吃店} {dobookyz {%1} {%2} {%3} {%4} {%5} {%6} {%7}}};
						};
					};
					#DEFAULT {
						#IF {$env[buff] == 0} {
							pfm_wuxing;
						};
						$readdo;
					}
				};
			};
		};
	};
	#ACTION {^{设定环境变量：action \= \"checkover\"|你设定checkover为反馈信息}} {
		#CLASS readclass KILL;
		#IF {"%4" != ""} {
			library_return {%2} {%3};
		};
		#ELSE {
			drop %2;
			i;
			%3
		};
	};
	#ACTION {^你一觉醒来} {
		#CLASS readclass KILL;
		execute {out;d;w;n;e};
		startfull {dobookyz {%1} {%2} {%3} {%4} {%5} {%6} {%7}} {3};
	};
	#CLASS readclass CLOSE;
	#NOP {因为读书经常指令溢出会导致发呆,这里需要清空发呆计数};
	#VARIABLE {idleroom} {
		{name} {$room}
		{count} {0}
	};
	wwxwp;
	pfm_wuxing;
	echo {checkhp};
};
#NOP {长按洗澡读书,%1:指令,%2:书籍,%3:后续指令,%4:是否需要还书,%5:如异常结束要进行的操作,%6:技能名称,%7:要到达的等级};
#ALIAS {dobookca} {
	#VARIABLE {checkcount} {0};
	#VARIABLE {reading} {1};
	#VARIABLE {readdo} {
		#VARIABLE {reading} {0};
		execute {#10 %1 %2;yun jing;hp};
		echo {checkhp}
	};
	#VARIABLE {readover} {0};
	#CLASS readclass KILL;
	#CLASS readclass OPEN;
	#NOP {设上如果有全真连解谜的ce zi,读满后会有此提示};
	#ACTION {^你%*研{读|习}} {
		#VARIABLE {reading} {1};
	};
	#ACTION {^你要领悟什么} {
		#VARIABLE {readover} {1};
	};
	#ACTION {^这本书对于你来说已经太肤浅了} {
		#VARIABLE {readover} {1};
	};
	#ACTION {^你的实战经验不足，再怎么读也没用} {
		#VARIABLE {readover} {1};
	};
	#ACTION {^由于你的%*的火侯不够，不能再进行更高一层的提高} {
		#VARIABLE {readover} {1};
	};
	#ACTION {^你研读了一会儿，但是发现上面所说的对你而言都太浅了，没有学到任何东西。} {
		#VARIABLE {readover} {1};
	} {1};
	#ACTION {^你觉得这经书上所写的已经太浅了，不能学到什么东西。} {
		#VARIABLE {readover} {1};
	};
	#ACTION {^你的实战经验不足} {
		#VARIABLE {readover} {1};
	};
	#ACTION {^你的{内力|真气}不够} {
		#VARIABLE {reading} {1};
		#VARIABLE {readover} {2};
	};
	#ACTION {^你{内力|真气}不够} {
		#VARIABLE {reading} {1};
		#VARIABLE {readover} {2};
	};
	#ACTION {^你的潜能已经用完了，再怎么读也没用。} {
		#VARIABLE {reading} {1};
		#VARIABLE {readover} {3};
	};
	#ACTION {^{设定环境变量：action \= \"checkhp\"|你设定checkhp为反馈信息}} {
		#VARIABLE {idle} {0};
		#MATH {checkcount} {$checkcount + 1};
		#IF {$hp[neili] < 400 && $hp[jing] < 100} {
			#VARIABLE {readover} {2};
		};
		#IF {"%6" != "" && "%7" != ""} {
			#IF {@getSkillLevel{%6} >= %7} {
				#VARIABLE {readover} {1};
			};
		};
		#IF {$checkcount >= 5} {
			#VARIABLE {checkcount} {0};
			skills;
		};
		#IF {$reading == 0} {
			#VARIABLE {readover} {1};
		};
		#VARIABLE {reading} {0};
		#DELAY {0.5} {
			#IF {"$caller" != ""} {
				#CLASS readclass KILL;
				checkrequest {gotodo {长安城} {鼓楼} {dobookca {%1} {%2} {%3} {%4} {%5} {%6} {%7}}};
			};
			#ELSE {
				#SWITCH {$readover} {
					#CASE {1} {
						skills;
						echo {checkover};
					};
					#CASE {2} {
						#CLASS readclass KILL;
						showerfull {gotodo {长安城} {鼓楼} {dobookca {%1} {%2} {%3} {%4} {%5} {%6} {%7}}}
					};
					#CASE {3} {
						#CLASS readclass KILL;
						#DELAY {1} {
							qu_pot {gotodo {长安城} {鼓楼} {dobookca {%1} {%2} {%3} {%4} {%5} {%6} {%7}}};
						};
					};
					#DEFAULT {
						#IF {$env[buff] == 0} {
							pfm_wuxing;
						};
						$readdo;
					}
				};
			};
		};
	};
	#ACTION {^{设定环境变量：action \= \"checkover\"|你设定checkover为反馈信息}} {
		#CLASS readclass KILL;
		#IF {"%4" != ""} {
			library_return {%2} {%3};
		};
		#ELSE {
			drop %2;
			i;
			%3
		};
	};
	#CLASS readclass CLOSE;
	#NOP {因为读书经常指令溢出会导致发呆,这里需要清空发呆计数};
	#VARIABLE {idleroom} {
		{name} {$room}
		{count} {0}
	};
	wwxwp;
	pfm_wuxing;
	echo {checkhp};
};
#NOP {学习,新手通过吃药快速学习,%1后续指令};
#ALIAS {golearn} {
	#IF {"$mapnpcs[$hp[master][name]]" == ""} {
		#SHOWME {<faa>请设置师傅资料};
	};
	#ELSEIF {"$hp[party]" == "古墓派" && "$hp[master][name]" == "自学"} {
		golearngumu {%1}
	};
	#ELSEIF {"@getMissingBaseWeapon{}" != ""} {
		findweapon {@getMissingBaseWeapon{}} {golearn {%1}}
	};
	#ELSEIF {$hp[pot] < 2000 && $env[oop] == 0} {
		qu_pot {golearn {%1}};
	};
	#ELSEIF {$env[guigu] == 0} {
		openguigu {golearn {%1}};
	};
	#ELSE {
		time;
		#IF {"$hp[party]" == "少林派" && $env[guilty] == 1} {
			sl_guilty {
				gotonpc {$hp[master][name]} {
					runwait {startlearn {%1}}
				};
			};
		};
		#ELSE {
			#NOP {小号吃药吧};
			#IF {$hp[neili_max] < 1500 && @carryqty{neixi wan} == 0 && $hp[balance] > 100} {
				buymedicine {neixi wan} {10} {golearn {%1}}
			};
			#ELSEIF {$hp[neili_max] >= 1500 && $hp[neili_max] < 4000 && @carryqty{chuanbei wan} == 0 && $hp[balance] > 100} {
				buymedicine {chuanbei wan} {10} {golearn {%1}}
			};
			#ELSE {
				gotonpc {$hp[master][name]} {runwait {startlearn {%1}}}
			};
		};
	};
};
#NOP {你现在的先天功修为只能用学(learn)的来增加熟练度};
#NOP {开始学习,%1后续指令};
#ALIAS {startlearn} {
	#VARIABLE {uwpflag} {0};
	#VARIABLE {mastermissing} {0};
	#VARIABLE {learnflag} {0};
	#VARIABLE {offsetgo} {};
	#VARIABLE {offsetback} {};
	#VARIABLE {learnskill} {@getLearnSkill{1}};
	#VARIABLE {learntimes} {10};
	#VARIABLE {lvupflag} {0};
	#CLASS learnclass KILL;
	#NOP {特殊位置偏移移动};
	#VARIABLE {learndo} {
		pfm_wuxing;
		execute {
			yun jing;
			xue $hp[master][id] $learnskill $learntimes;
			hp
		};
		echo {checkhp};
	};
	#CLASS learnclass OPEN;
	#ACTION {^{设定环境变量：action \= \"checkcost\"|你设定checkcost为反馈信息}} {
		#LOCAL {jing_cost} {@eval{($hp[jing_max] - $hp[jing])*150/100}};
		#VARIABLE {learntimes} {@eval{$hp[jing_max]/$jing_cost}};
		#VARIABLE {learntimes} {@eval{($learntimes/10)*10}};
		#IF {$learntimes < 10} {
			#VARIABLE {learntimes} {10};
		};
		#ELSEIF {$learntimes > 50} {
			#VARIABLE {learntimes} {50};
		};
		#IF {$hp[jing_max] < 300 && $learntimes > 10} {
			#VARIABLE {learntimes} {10};
		};
		#IF {$hp[jing_max] < 500 && $learntimes > 20} {
			#VARIABLE {learntimes} {20};
		};
		#IF {$hp[jing_max] < 800 && $learntimes > 30} {
			#VARIABLE {learntimes} {30};
		};
		#IF {$learntimes <= 0} {
			execute {
				hp;
				xue $hp[master][id] $learnskill;
				hp
			};
			echo {checkcost};
		};
		#ELSE {
			#VARIABLE {learndo} {
				pfm_wuxing;
				execute {
					yun jing;
					xue $hp[master][id] $learnskill $learntimes;
					hp
				};
				echo {checkhp};
			};
			$learndo
		};
	};
	#ACTION {^{设定环境变量：action \= \"checkmaster\"|你设定checkmaster为反馈信息}} {
		#VARIABLE {idle} {0};
		#IF {"$roomthings[$hp[master][name]]" == ""} {
			#CLASS learnclass KILL;
			#IF {$__DEBUG__ == 1} {
				#SHOWME {<ffa>师傅人没了};
			};
			#DELAY {4} {
				startlearn {%1}
			};
		};
		#ELSEIF {"$learnskill" == "douzhuan-xingyi"} {
			#CLASS learnclass KILL;
			fulldzxy {gomaster {startlearn {%1}}};
		};
		#ELSEIF {"$learnskill" == "jiuyang-shengong"} {
			#CLASS learnclass KILL;
			fulljiuyang {gomaster {startlearn {%1}}};
		};
		#ELSEIF {"$learnskill" == "qiankun-danuoyi"} {
			#CLASS learnclass KILL;
			fullqkdny {startlearn {%1}};
		};
		#ELSE {
			#LOCAL {tempcount} {&roomthings[$hp[master][name]][]};
			#VARIABLE {hp[master][id]} {$roomthings[$hp[master][name]][+$tempcount]};
			#LOCAL {skweapon} {@getBaseWeapon{$learnskill}};
			#IF {"$skweapon" != ""} {
				wwp {$skweapon}
			};
			#ELSE {
				uwwp
			};
			pfm_wuxing;
			execute {
				exp;
				yun jing;
				hp;
				xue $hp[master][id] $learnskill;
				hp
			};
			echo {checkcost};
		};
	};
	#ACTION {^{斗转星移只能通过领悟来提高|这项技能你|本草术理只能通过|你必须去学堂|也许是缺乏实战经验|你的内功火候不够|你没有出家|你对神龙药理学的领悟|你的基本功火候未到|玄铁剑法|学就只能学的这里了|你不能再学习|你没有学过七伤拳经|这套剑法我就教到这儿|你的碧海潮生功级别不够，无法学习奇门八卦阵|你的身法不够，无法领悟奇门八卦阵}} {
		#LOCAL {ignoreflag} {true};
		#IF {"%%1" == "这项技能你" || "%%1" == "你必须去学堂" || "%%1" == "玄铁剑法" || "%%1" == "学就只能学的这里了" || "%%1" == "你不能再学习" || "%%1" == "这套剑法我就教到这儿" || "%%1" == "你的碧海潮生功级别不够，无法学习奇门八卦阵" || "%%1" == "你的身法不够，无法领悟奇门八卦阵"} {
			#LOCAL {ignoreflag} {ever};
		};
		#IF {"$kungfu[know][$learnskill]" != ""} {
			#VARIABLE {kungfu[know][$learnskill][ignore]} {$ignoreflag};
		};
		#ELSEIF {"$kungfu[base][$learnskill]" != ""} {
			#VARIABLE {kungfu[base][$learnskill][ignore]} {$ignoreflag};
		};
		#ELSEIF {"$kungfu[spec][$learnskill]" != ""} {
			#VARIABLE {kungfu[spec][$learnskill][ignore]} {$ignoreflag};
		};
		#ELSE {
			#VARIABLE {kungfu[spec][$learnskill][ignore]} {$ignoreflag};
		};
		#VARIABLE {learnflag} {1};
		#IF {"%%1" == "你没有学过七伤拳经"} {
			#VARIABLE {learnflag} {3};
		};
	};
	#ACTION {^你要向谁求教？} {
		#VARIABLE {mastermissing} {1};
	};
	#ACTION {^空手方能练习} {
		#IF {$uwpflag == 1} {
			uwwp {$id[weapon] 2};
			#VARIABLE {uwpflag} {0};
		};
		#ELSE {
			#VARIABLE {uwpflag} {1};
			uwwp
		};
	};
	#ACTION {^学%*时手里不能拿武器} {
		#IF {$uwpflag == 1} {
			uwwp {$id[weapon] 2};
			#VARIABLE {uwpflag} {0};
		};
		#ELSE {
			#VARIABLE {uwpflag} {1};
			uwwp
		};
	};
	#ACTION {^练%*必须空手} {
		#IF {$uwpflag == 1} {
			uwwp {$id[weapon] 2};
			#VARIABLE {uwpflag} {0};
		};
		#ELSE {
			#VARIABLE {uwpflag} {1};
			uwwp
		};
	};
	#ACTION {^你手上的武器不能用来%*。} {
		wwp {@getBaseWeapon{$learnskill}}
	};
	#ACTION {^空手时无法练%*。} {
		wwp {@getBaseWeapon{$learnskill}}
	};
	#ACTION {^你屡犯僧家数戒，尘俗之心太重，无法修炼禅宗心法。} {
		#VARIABLE {mastermissing} {2};
	};
	#ACTION {^你的侠义正气太低了} {
		#VARIABLE {learnflag} {2};
	};
	#ACTION {^这套剑法我就教到这儿，以后就要靠你自己去领悟了} {
		@@
	};
	#ACTION {^你使用的武器不对} {
		#LOCAL {skweapon} {@getBaseWeapon{$learnskill}};
		#IF {"$skweapon" != ""} {
			wwp {$skweapon};
		};
	};
	#ACTION {^你觉得对太极拳理还不够理解，无法继续练习太极拳} {
		ask zhang sanfeng about 太极拳理
	};
	#ACTION {^{设定环境变量：action \= \"checkhp\"|你设定checkhp为反馈信息}} {
		#VARIABLE {idle} {0};
		#IF {$mastermissing == 1} {
			#CLASS learnclass KILL;
			closeguigu {guide_checkskill {%1}}
		};
		#ELSEIF {$mastermissing == 2} {
			#CLASS learnclass KILL;
			sl_guilty {golearn {%1}};
		};
		#ELSEIF {$learnflag == 1} {
			#CLASS learnclass KILL;
			#DELAY {0.5} {
				startlearn {%1}
			};
		};
		#ELSEIF {$learnflag == 2} {
			#CLASS learnclass KILL;
			gozshen {100000} {golearn {%1}}
		};
		#ELSEIF {$learnflag == 3} {
			#CLASS learnclass KILL;
			ask zhang wuji about 七伤拳经;
			dohalt {golearn {%1}};
		};
		#ELSE {
			dohalt {
				#IF {$hp[jing_per] < 80 && @carryqty{huoxue dan} > 0} {
					fu huoxue dan;
					i;
					yun jing
				};
				#IF {$hp[pot] < $learntimes} {
					#CLASS learnclass KILL;
					#IF {$env[oop] == 0} {
						qu_pot {golearn {%1}}
					};
					#ELSE {
						closeguigu {guide_checkskill {%1}};
					};
				};
				#ELSEIF {$hp[neili] < 100} {
					#IF {@carryqty{neixi wan} > 0} {
						fu neixi wan;
						i;
						hp;
						#DELAY {1} {
							echo {checkhp};
						};
					};
					#ELSEIF {$hp[neili_max] < 4000 && @carryqty{chuanbei wan} > 0} {
						fu chuanbei wan;
						execute {
							i;
							yun jing;
							hp;
							xue $hp[master][id] $learnskill;
							hp
						};
						echo {checkcost};
					};
					#ELSEIF {$hp[neili_max] < 4000 && $hp[balance] > 100} {
						#CLASS learnclass KILL;
						golearn {%1}
					};
					#ELSE {
						#CLASS learnclass KILL;
						learnsleep {%1}
					};
				};
				#ELSE {
					$learndo
				};
			}
		};
	};
	#ACTION {^你现在正忙着呢。} {
		#DELAY {1} {
			$learndo
		};
	};
	#CLASS learnclass CLOSE;
	#IF {"$learnskill" == ""} {
		#CLASS learnclass KILL;
		closeguigu {guide_checkskill {%1}}
	};
	#ELSEIF {"$hp[master][name]" == "裘千丈"} {
		guide_findqqz {
			id here;
			echo {checkmaster};
		};
	};
	#ELSE {
		id here;
		echo {checkmaster};
	};
};
#NOP {学习睡觉休息,%1:后续指令};
#ALIAS {learnsleep} {
	#VARIABLE {dazuostep} {$common[bedroom][$hp[master]][ops]};
	#CLASS learnclass KILL;
	#CLASS learnclass OPEN;
	#ACTION {^你一觉醒来} {
		#CLASS learnclass KILL;
		#IF {$hp[exp] < 165000} {
			#IF {$kungfu[base][force][effectlv] >= 150 @getSkillLevel{literate} < 122} {
				goliterate {122} {golearn {%1}}
			};
			#ELSE {
				golearn {%1}
			};
		};
		#ELSE {
			gotonpc {$hp[master][name]} {
				startfull {startlearn {%1}} {3};
			};
		};
	};
	#CLASS learnclass CLOSE;
	#LOCAL {bedroom} {$common[bedroom][$hp[master][name]][$hp[sex]]};
	#NOP {针对新手学习睡觉增加自动吃yuji wan的判定};
	#IF {"$bedroom" == ""} {
		#CLASS learnclass KILL;
		#SHOWME {<faa>未配置门派休息房间,请在common模块中进行设置};
		startfull {gotonpc {$hp[master][name]} {startlearn {%1}}} {3};
	};
	#ELSEIF {$hp[neili_max] <= 2000 && @eval{$hp[neili_limit_max] - $hp[neili_max]} > 100 && "$kungfu[base][force][jifa]" != ""} {
		#CLASS learnclass KILL;
		doeatyuji {golearn {%1}} {1}
	};
	#ELSEIF {$hp[neili_max] > 5000} {
		#CLASS learnclass KILL;
		showerfull {golearn {%1}}
	};
	#ELSE {
		checkfirstlearn {gotoroom {$bedroom[roomid]} {sleep}};
	};
};
#NOP {检查新学的技能，进行激发,%1:后续操作};
#ALIAS {checkfirstlearn} {
	#VARIABLE {firstskill} {};
	#CLASS checkfirstclass OPEN;
	#ACTION {^{设定环境变量：action \= \"checkjifa\"|你设定checkjifa为反馈信息}} {
		#CLASS checkfirstclass KILL;
		#FOREACH {*kungfu[spec][$firstskill][jifa][]} {bsk} {
			#IF {"$kungfu[base][$bsk][jifa]" == ""} {
				jifa $bsk $firstskill;
				#BREAK;
			};
		};
		#DELAY {0.5} {
			checkfirstlearn {%1}
		}
	};
	#CLASS checkfirstclass CLOSE;
	#FOREACH {*kungfu[base][]} {sk} {
		#IF {"$kungfu[base][$sk][jifa]" == ""} {
			jifa all;
			#BREAK;
		};
	};
	jifa;
	#CLASS checkfirstclass KILL;
	%1
};
#NOP {获取下一个需要学习的技能,%1:学习标识};
#NOP {如果不做区别,统一采用conf[allowdiff]参数进行判定,这里会导致学习逻辑异常};
#FUNCTION getLearnSkill {
	#IF {"$conf[newbie][party]" == ""} {
		#RETURN {@getLearnSkillNormal{%1}};
	};
	#ELSE {
		#RETURN {@getLearnSkillGuide{%1}};
	};
};
#NOP {普通模式};
#FUNCTION getLearnSkillNormal {
	#LOCAL {difflv} {1};
	#NOP {优先学习force和对应的特殊内功,其他随意};
	#LOCAL {tempskill} {$kungfu[base][force][jifa]};
	#IF {"$kungfu[spec][$tempskill][ignore]" != "ever" && @eval{$hp[max_lv] - $kungfu[spec][$tempskill][lv]} >= $difflv && @eval{$kungfu[spec][$tempskill][lv] - $kungfu[base][force][lv]} <= 10 && $kungfu[spec][$tempskill][lv] < 220} {
		#RETURN {$tempskill};
	};
	#ELSEIF {"$kungfu[base][force][ignore]" != "ever" && @eval{$hp[max_lv] - $kungfu[base][force][lv]} >= $difflv && $kungfu[base][force][lv] < 220} {
		#RETURN {force};
	};
	#ELSE {
		#NOP {先学知识};
		#FOREACH {*kungfu[know][]} {sk} {
			#IF {@contains{{nolearnskills}{$sk}} > 0} {
				#CONTINUE;
			};
			#IF {@contains{{conf[ignoreskills]}{$sk}} > 0} {
				#CONTINUE;
			};
			#IF {"$kungfu[know][$sk][ignore]" == "ever"} {
				#CONTINUE;
			};
			#RETURN {$sk};
		};
		#NOP {再学基本};
		#FOREACH {*kungfu[base][]} {sk} {
			#IF {$kungfu[base][$sk][lv] >= 220} {
				#CONTINUE;
			};
			#IF {@contains{{nolearnskills}{$sk}} > 0} {
				#CONTINUE;
			};
			#IF {@contains{{conf[ignoreskills]}{$sk}}} {
				#CONTINUE;
			};
			#IF {"$kungfu[base][$sk][ignore]" == "ever"} {
				#CONTINUE;
			};
			#IF {$kungfu[base][$sk][lv] >= $hp[max_lv]} {
				#CONTINUE;
			};
			#RETURN {$sk};
		};
		#NOP {先学特殊};
		#FOREACH {*kungfu[spec][]} {sk} {
			#IF {$kungfu[spec][$sk][lv] >= 220} {
				#CONTINUE;
			};
			#IF {@contains{{nolearnskills}{$sk}} > 0} {
				#CONTINUE;
			};
			#IF {@contains{{conf[ignoreskills]}{$sk}}} {
				#CONTINUE;
			};
			#IF {"$kungfu[spec][$sk][ignore]" == "ever"} {
				#CONTINUE;
			};
			#IF {$kungfu[spec][$sk][lv] >= $hp[max_lv]} {
				#CONTINUE;
			};
			#RETURN {$sk};
		};
	};
	#RETURN {};
};
#NOP {向导模式};
#FUNCTION getLearnSkillGuide {
	#NOP {向导模式按照向导中给出的技能顺序进行学习};
	#FOREACH {$guide[$hp[party]][masters][$hp[master][name]][favourites][]} {sk} {
		#IF {@contains{{nolearnskills}{$sk}} > 0} {
			#CONTINUE;
		};
		#IF {@contains{{conf[ignoreskills]}{$sk}} > 0} {
			#CONTINUE;
		};
		#IF {"$kungfu[know][$sk][ignore]" == "ever"} {
			#CONTINUE;
		};
		#IF {"$kungfu[base][$sk][ignore]" == "ever"} {
			#CONTINUE;
		};
		#IF {"$kungfu[spec][$sk][ignore]" == "ever"} {
			#CONTINUE;
		};
		#IF {@getSkillLevel{$sk} == 0} {
			#RETURN {$sk};
		};
		#IF {@getSkillLevel{$sk} >= 220} {
			#CONTINUE;
		};
		#IF {"$kungfu[know][$sk]" =="" && @getSkillLevel{$sk} >= $hp[max_lv]} {
			#CONTINUE;
		};
		#RETURN {$sk};
	};
	#RETURN {};
};
#NOP {去领悟,%1:后续指令,%2:中止条件,主要用于任务间隙领悟};
#ALIAS {golingwu} {
	#LOCAL {minpot} {2000};
	#IF {"%2" != ""} {
		#LOCAL {minpot} {@eval{$hp[pot_max] / 10}};
	};
	#IF {$hp[pot] < $minpot && $env[oop] == 0} {
		qu_pot {golingwu {%1} {%2}};
	};
	#ELSEIF {$env[guigu] == 0} {
		openguigu {golingwu {%1} {%2}}
	};
	#ELSE {
		lianfullneili {
			#IF {"$hp[party]" == "少林派" && $env[guilty] == 1} {
				sl_guilty {
					gotodo {嵩山少林} {达摩院后殿} {startlingwu {%1} {%2}}
				};
			};
			#ELSE {
				gotodo {嵩山少林} {达摩院后殿} {startlingwu {%1} {%2}}
			};
		};
	};
};
#NOP {开始领悟,%1:后续指令,%2:中止条件,%3:上一个领悟的技能};
#ALIAS {startlingwu} {
	#VARIABLE {lingwuflag} {0};
	#VARIABLE {lingwuskill} {@getLingwuSkill{1}};
	#VARIABLE {lingwudelay} {1};
	#VARIABLE {abortexpr} {%2};
	#VARIABLE {lingwudo} {
		execute {
			#10 lingwu $lingwuskill[base];
			yun jing;
			hp;
		};
		echo {checkhp};
	};
	#CLASS lingwuclass OPEN;
	#ACTION {^{设定环境变量：action \= \"checkcost\"|你设定checkcost为反馈信息}} {
		#NOP {领悟时如果jing小于最大精(%100情形)的一半时,领悟无效且耗精};
		#LOCAL {jing_cost} {@eval{$hp[jing_max] - $hp[jing]}};
		#LOCAL {jing_full} {@eval{$hp[jing_max] * 100 / $hp[jing_per]}};
		#NOP {计算最优的领悟次数和延时};
		#LOCAL {lingwutimes} {@eval{($hp[jing_max]-$jing_full/2)/$jing_cost}};
		#LOCAL {lingwutimes} {@eval{$lingwutimes+1}};
		#IF {$lingwutimes > 10} {
			#LOCAL {lingwutimes} {10};
		};
		#IF {$lingwutimes <= 0} {
			execute {
				yun jing;
				hp;
				lingwu $lingwuskill[base];
				hp
			};
			echo {checkcost};
		};
		#ELSE {
			#VARIABLE {lingwudelay} {@eval{$lingwutimes * 0.1}};
			#IF {$lingwudelay > 0.5} {
				#VARIABLE {lingwudelay} {0.5};
			};
			#VARIABLE {lingwudo} {
				execute {
					#$lingwutimes lingwu $lingwuskill[base];
					yun jing;
					hp;
				};
				echo {checkhp} {2};
			};
			ensure {yun jing} {checkhp}
		};
	};
	#ACTION {^由于实战经验不足} {
		#VARIABLE {lingwuflag} {1};
	};
	#ACTION {^你的%*造诣不够} {
		#VARIABLE {lingwuflag} {1};
	};
	#ACTION {^格式： lingwu <技能> [次数] } {
	};
	#ACTION {^{设定环境变量：action \= \"checkhp\"|你设定checkhp为反馈信息}} {
		resonate {checkhp};
		#VARIABLE {idle} {0};
		#DELAY {$lingwudelay} {
			#IF {"$caller" != ""} {
				#CLASS lingwuclass KILL;
				prepareforce;
				checkrequest {golingwu {%1} {%2}}
			};
			#ELSEIF {$lingwuflag == 1} {
				echo {checklingwu};
			};
			#ELSEIF {$hp[neili] < 400} {
				#CLASS lingwuclass KILL;
				s;
				time;
				loc {golingwu {%1} {%2}}
			};
			#ELSEIF {"$abortexpr[busy]" != "" && @getConditionTick{{$abortexpr[busy]}} <= 20} {
				#CLASS lingwuclass KILL;
				closeguigu {checkhp {%1}}
			};
			#ELSEIF {"$abortexpr[negtive]" != "" && @getNegtiveTick{{$abortexpr[negtive]}} <= 20} {
				#CLASS lingwuclass KILL;
				closeguigu {checkhp {%1}}
			};
			#ELSEIF {$hp[pot] < 10} {
				#CLASS lingwuclass KILL;
				#IF {"$abortexpr" != ""} {
					closeguigu {checkhp {%1}}
				};
				#ELSE {
					#DELAY {0.5} {
						loc {
							#IF {$env[oop] == 0} {
								qu_pot {golingwu {%1} {%2}}
							};
							#ELSE {
								closeguigu {checkrequest {checkhp {golian {%1} {1}}}};
							};
						}
					};
				}
			};
			#ELSE {
				#IF {$env[buff] == 0} {
					pfm_wuxing;
					dowuxingforce;
				};
				#IF {"%2" != ""} {cond};
				$lingwudo
			};
		}
	};
	#ACTION {^{设定环境变量：action \= \"checklingwu\"|你设定checklingwu为反馈信息}} {
		#CLASS lingwuclass KILL;
		#IF {"$caller" != ""} {
			checkrequest {golingwu {%1} {%2}} {prepareforce};
		};
		#ELSE {
			startlingwu {%1} {%2} {$lingwuskill[base]};
		};
	};
	#CLASS lingwuclass CLOSE;
	#IF {"$lingwuskill" == ""} {
		#CLASS lingwuclass KILL;
		preparekungfu;
		#NOP {这里根据传入的上一个领悟技能判定是否在补脱节很大的技能};
		#NOP {如果是脱节的或者新学的技能，可以考虑原地s打坐，而不是来回的关闭鬼谷和存潜能};
		#IF {"%3" != "" && @eval{$kungfu[base][%3][lv]} < @eval{$hp[max_lv]*4/5}} {
			#DELAY {0.5} {
				checkhp {golian {%1} {1}}
			}
		};
		#ELSE {
			#DELAY {0.5} {
				closeguigu {checkhp {golian {%1} {1}}}
			};
		};
	};
	#ELSE {
		wwxwp;
		pfm_wuxing;
		dowuxingforce;
		exp;
		#if {@contains{{common[fist]}{$lingwuskill[base]}} == 0}{
			jifa $lingwuskill[base] $lingwuskill[spec];
		};
		#ELSE {
			bei none;
			jifa $lingwuskill[base] $lingwuskill[spec];
			bei $lingwuskill[base];
		};
		yun jing;
		lingwu $lingwuskill[base];
		hp;
		echo {checkcost};
	};
};
#NOP {是否需要自动补技能了，在潜能用途auto时，只有当最后一个base技能脱节超过设定值且超过20级时才去领悟练习一次};
#FUNCTION isNeedAutoFullSkill {
	#FOREACH {*kungfu[spec][]} {ssk} {
		#IF {@contains{{conf[ignoreskills]}{$ssk}} > 0} {
			#CONTINUE;
		};
		#IF {@eval{$hp[max_lv] - $kungfu[spec][$ssk][lv]} >= 20} {
			#RETURN {1};
		};
	};

	#RETURN {0};
};
#NOP {是否需要自动busy领悟};
#FUNCTION isNeedBusyLingwu {
	#NOP {潜能用途配置};
	#IF {"$conf[potpurpose]" != "lingwu" && "$conf[potpurpose]" = "auto"} {
		#RETURN {0};
	};

	#NOP {是否达到领悟等级};
	#IF {$hp[max_lv] < 220} {
		#RETURN {0};
	};

	#IF {"@getLingwuSkill{}" == ""} {
		#RETURN {0};
	};

	#RETURN {1};
};
#NOP {获取下一个领悟的技能,%1:是否不使用用户配置技能等级差进行判定,非空};
#NOP {如果不做区别,统一采用conf[allowdiff]参数进行判定,这里会导致练习后无法跟着继续领悟};
#FUNCTION getLingwuSkill {
	#LOCAL {difflv} {1};
	#NOP {这里做个判断，如果特定标识潜能用途为none，则直接返回空};
	#IF {"$conf[potpurpose]" == "none"} {
		#RETURN {};
	};
	#IF {"%1" == "" && 1 == 2} {
		#LOCAL {difflv} {$conf[allowdiff][skill]};
		#NOP {添加经验换内力设置的临时预期等级处理};
		#IF {"$conf[autoexchange][state]" != "" && "$conf[autoexchange][expectlv]" != ""} {
			#LOCAL {difflv} {@eval{$hp[max_lv] - $conf[autoexchange][expectlv]}};
		};
	};
	#IF {"$conf[primaryforce]" != "" && $kungfu[spec][$conf[primaryforce]][lv] >= $kungfu[base][force][lv] && @eval{$hp[max_lv] - $kungfu[base][force][lv]} >= $difflv && $kungfu[base][force][lv] >= 200} {
		#RETURN {
			{base} {force}
			{spec} {$conf[primaryforce]}
		};
	};
	#IF {"$conf[wuxingforce]" != ""  && $kungfu[spec][$conf[wuxingforce]][lv] >= $kungfu[base][force][lv] && @eval{$hp[max_lv] - $kungfu[base][force][lv]} >= $difflv && $kungfu[base][force][lv] >= 200} {
		#RETURN {
			{base} {force}
			{spec} {$conf[wuxingforce]}
		};
	};
	#FOREACH {*kungfu[base][]} {bsk} {
		#IF {@contains{{conf[ignoreskills]}{$bsk}} > 0} {
			#CONTINUE;
		};
		#IF {"$bsk" == "force" && $kungfu[base][force][lv] < 200} {
			#CONTINUE;
		};
		#IF {@eval{$hp[max_lv] - $kungfu[base][$bsk][lv]} < $difflv} {
			#CONTINUE;
		};
		#NOP {至少有一个特殊技能等级大于基础技能等级};
		#FOREACH {*kungfu[spec][]} {ssk} {
			#IF {@contains{{conf[ignoreskills]}{$ssk}} > 0} {
				#CONTINUE;
			};
			#IF {"$kungfu[spec][$ssk][jifa][$bsk]" == ""} {
				#CONTINUE;
			};
			#IF {$kungfu[spec][$ssk][lv] >= $kungfu[base][$bsk][lv]} {
				#RETURN {
					{base} {$bsk}
					{spec} {$ssk}
				};
			};
		};
	};
	#RETURN {};
};
#NOP {最近一次练习用的房间};
#VARIABLE {lianroom} {0};
#NOP {临时挂起的技能，在getLianSkill中应用};
#VARIABLE {pendingskills} {};
#NOP {挂起某项技能，临时不再练习,%1:技能名称};
#ALIAS {suspendskill} {
	#VARIABLE {pendingskills[%1]} {$hp[max_lv]};
};
#NOP {删除已过期的挂起技能，升级后清除低级的挂起技能，应用于job.tin中};
#ALIAS {clearsuspendskills} {
	#LIST {tempskills} {create} {};
	#FOREACH {*pendingskills[]} {sk} {
		#IF {$pendingskills[$sk] < $hp[max_lv]} {
			#LIST {tempskills} {add} {$sk};
		};
	};

	#FOREACH {$tempskills[]} {sk} {
		#UNVARIABLE {pendingskills[$sk]};
	};
};
#NOP {去练习,%1:后续指令,%2:随机房间标识,如未指定则使用lianroom};
#ALIAS {golian} {
	#IF {"@getMissingBaseWeapon{}" != ""} {
		findweapon {@getMissingBaseWeapon{}} {golian {%1}}
	};
	#ELSE {
		#IF {"%2" == "" || $lianroom == 0} {
			#NOP {为防止聚集,随机房间};
			#LOCAL {pindex} {@rnd{{1}{&practicerooms[]}}};
			#VARIABLE {lianroom} {$practicerooms[+$pindex]};
		};
		gotodo {长安城} {$lianroom} {startlian {%1}};
	};
};
#NOP {获取练习次数,%1:基础技能,%2:特殊技能,%3:可以消耗的内力};
#FUNCTION getLianTimes {
	#NOP {计算当前内力允许练习的次数};
	#LOCAL {neilicost} {20};
	#IF {"$common[practicecost][%2]" != ""} {
		#LOCAL {neilicost} {$common[practicecost][%2]};
	};
	#LOCAL {neilitimes} {@eval{%3 / $neilicost}};

	#NOP {练习一次增加的熟练度};
	#LOCAL {addpoint} {@eval{$kungfu[base][%1][lv] / 5 + 1}};
	#IF {"%1" == "force"} {
		#LOCAL {addpoint} {@eval{$addpoint + $hp[int_xt] + @getSkillLevel{literate} / 10}}
	};

	#NOP {计算练满所需次数};
	#LOCAL {diffpoint} {@eval{$kungfu[spec][%2][max_point] - $kungfu[spec][%2][point]}};
	#IF {$kungfu[spec][%2][lv] < $hp[max_lv]} {
		#LOCAL {diffpoint} {@eval{$kungfu[spec][%2][lv] * $kungfu[spec][%2][lv] * $kungfu[spec][%2][lv]}};
	};
	#LOCAL {needtimes} {@eval{$diffpoint / $addpoint + 10}};

	#NOP {计算实际练习次数};
	#LOCAL {liantimes} {$needtimes};
	#IF {$neilitimes < $needtimes} {
		#LOCAL {liantimes} {$neilitimes};
	};
	#LOCAL {liantimes} {@eval{($neilitimes / 10) * 10}};
	#IF {$liantimes < 10} {
		#LOCAL {liantimes} {10};
	};

	#NOP {内功练习};
	#IF {$liantimes > $hp[pot]} {
		#LOCAL {liantimes} {$hp[pot]};
	};
	#RETURN {$liantimes};
};
#NOP {开始练习,%1:后续指令};
#ALIAS {startlian} {
	#VARIABLE {lvchanged} {0};
	#VARIABLE {checkcount} {0};
	#VARIABLE {lianflag} {0};
	#VARIABLE {wxjzpoison} {0};
	#VARIABLE {liantimes} {50};
	#VARIABLE {lianskill} {@getLianSkill{1}};
	#VARIABLE {bskweapon} {$common[weaponmapping][$lianskill[base]][+1]};
	#VARIABLE {liando} {
		execute {
			yun jingli;
			yun jing;
			yun qi;
			lian $lianskill[base];
			hp;
		};
		echo {checkhp};
	};
	#CLASS lianclass OPEN;
	#ACTION {^你突然感觉自己小腹上‘梁门’、‘太乙’两穴隐隐疼痛。} {
		#VARIABLE {wxjzpoison} {1};
	};
	#ACTION {^你突然感觉自己‘关元穴’上有点麻木，不禁一阵心惊} {
		#VARIABLE {wxjzpoison} {2};
	};
	#ACTION {^你突然感觉自己阳白、廉泉、风府三处穴道，如万针攒刺，痛不可当} {
		#VARIABLE {wxjzpoison} {3};
	};
	#ACTION {^由于实战经验不足} {
		#VARIABLE {lianflag} {1};
	};
	#ACTION {^你现在的修为不足以提高} {
		#VARIABLE {lianflag} {1};
	};
	#ACTION {^你现在的%*修为只能用学(learn)的来增加熟练度} {
		#VARIABLE {lianflag} {2};
		#VARIABLE {kungfu[spec][@getSkillFromName{%%1}][ignore]} {};
	};
	#ACTION {^%*只能通过学习来增加熟练度。} {
		#VARIABLE {kungfu[spec][@getSkillFromName{%%1}][ignore]} {ever};
	};
	#ACTION {^你的基本功火候未到} {
		#VARIABLE {lianflag} {1};
		#VARIABLE {kungfu[spec][$lianskill[spec]][baselimit]} {$kungfu[base][$lianskill[base]][lv]};
	};
	#ACTION {^斗转星移只能通过领悟来提高} {
		#VARIABLE {lianflag} {1};
	};
	#ACTION {^独孤九剑只能通过领悟来提高} {
		#VARIABLE {lianflag} {1};
	};
	#ACTION {^你的侠义正气太低了} {
		#VARIABLE {lianflag} {3};
	};
	#ACTION {^大理乃是明门正派，看来你的侠义正气还不够啊} {
		#VARIABLE {lianflag} {3};
	};
	#ACTION {^修习%*必须有%*配合。} {
		#VARIABLE {lianflag} {4};
	};
	#ACTION {^你的道德经不够，不能继续修习九阴真功} {
		#VARIABLE {lianflag} {255};
	};
	#ACTION {^练%*必须空手} {
		uwwp
	};
	#ACTION {^学%*必须空手} {
		uwwp
	};
	#ACTION {^空手方能练习%*} {
		uwwp
	};
	#ACTION {^空手时无法练%*} {
		wwp {$bskweapon}
	};
	#ACTION {^你必须使用%*来练%*} {
		wwp {$bskweapon}
	};
	#ACTION {^你没有使用的武器} {
		wwp {$bskweapon}
	};
	#ACTION {^你使用的武器不对} {
		#LOCAL {skweapon} {@getBaseWeapon{$lianskill[spec]}};
		#IF {"$skweapon" != ""} {
			wwp {$skweapon}
		};
	};
	#ACTION {^{设定环境变量：action \= \"checkhp\"|你设定checkhp为反馈信息}} {
		#VARIABLE {idle} {0};
		#IF {$lvchanged == 1} {
			#VARIABLE {lvchanged} {0};
			#VARIABLE {lianflag} {1};
		};
		#MATH {checkcount} {$checkcount + 1};
		#IF {$checkcount >= 3} {
			#VARIABLE {checkcount} {0};
			skills;
		};
		#IF {$wxjzpoison >= 3} {
			#CLASS lianclass KILL;
			#DELAY {1} {
				prepareforce;
				sl_wxff {golian {%1} {1}}
			};
		};
		#ELSEIF {$hp[neili] < 1000} {
			#CLASS lianclass KILL;
			#DELAY {1} {
				time;
				skills;
				checkrequest {checkquest {golian {%1} {1}} {prepareforce}} {prepareforce};
			};
		};
		#ELSE {
			#SWITCH {$lianflag} {
				#CASE {1} {
					echo {checklian};
				};
				#CASE {2} {
					#CLASS lianclass KILL;
					#DELAY {1} {
						prepareforce;
						golearn {%1};
					};
				};
				#CASE {3} {
					#CLASS lianclass KILL;
					#DELAY {1} {
						gozshen {20000} {golian {%1} {1}};
					};
				};
				#CASE {4} {
					jifa;
					echo {checklian};
				};
				#CASE {255} {
					#CLASS lianclass KILL;
					fulldaodejing {
						golian {%1} {1}
					} {
						suspendskill {jiuyin-zhengong};
						golian {%1} {1}
					};
				};
				#DEFAULT {
					#DELAY {0.5} {$liando}
				};
			};
		};
	};
	#ACTION {^{设定环境变量：action \= \"checklian\"|你设定checklian为反馈信息}} {
		#CLASS lianclass KILL;
		#IF {("$lianskill[base]" == "force" || "$lianskill[spec]" == "bingcan-duzhang") && $hp[pot] < $liantimes} {
			#DELAY {1} {
				qu_pot {golian {%1} {1}}
			};
		};
		#ELSE {
			yun jingli;
			checkrequest {checkquest {golian {%1} {1}} {prepareforce}} {prepareforce};
		};
	};
	#CLASS lianclass CLOSE;
	#IF {"$lianskill" == ""} {
		#CLASS lianclass KILL;
		preparekungfu;
		#DELAY {0.5} {
			#IF {$wxjzpoison > 0} {
				sl_wxff {golian {%1} {1}}
			};
			#ELSEIF {"@getLingwuSkill{1}" != ""} {
				golingwu {%1}
			};
			#ELSE {
				prepareforce;
				lianfullneili {guide_checkskill {%1}};
			};
		};
	};
	#ELSEIF {"$lianskill[spec]" == "douzhuan-xingyi"} {
		#CLASS lianclass KILL;
		fulldzxy {golian {%1} {1}};
	};
	#ELSEIF {"$lianskill[spec]" == "qiankun-danuoyi"} {
		#CLASS lianclass KILL;
		fullqkdny {golian {%1} {1}};
	};
	#ELSEIF {"$lianskill[spec]" == "dugu-jiujian" && $kungfu[spec][dugu-jiujian][lv] < 300} {
		#CLASS lianclass KILL;
		fulldgjj {golian {%1} {1}};
	};
	#ELSEIF {1 == 2 && "$lianskill[base]" == "force" && $kungfu[spec][$lianskill[spec]][lv] < @eval{$kungfu[base][force][lv] *3 / 4}} {
		#NOP {练特殊内功如果等级小于基础内功的一半吃大还丹练};
		#CLASS lianclass KILL;
		jifa force $lianskill[spec];
		#VARIABLE {kungfu[base][force][jifa]} {$lianskill[spec]};
		golianeat {force} {$lianskill[spec]} {@eval{$kungfu[base][force][lv] *3 / 4}} {golian {%1}};
	};
	#ELSE {
		addcmd {hp};
		addcmd {exp};
		#IF {"$bskweapon" == "" && "$lianskill[base]" != "force" && "$lianskill[base]" != "dodge" && "$lianskill[base]" != "parry"} {
			addcmd {bei none};
		};
		addcmd {jifa $lianskill[base] $lianskill[spec]};
		#VARIABLE {kungfu[base][$lianskill[base]][jifa]} {$lianskill[spec]};
		#IF {"$bskweapon" == "" && "$lianskill[base]" != "force" && "$lianskill[base]" != "dodge" && "$lianskill[base]" != "parry"} {
			addcmd {bei $lianskill[base]};
		};
		#NOP {依赖性判断};
		#IF {"$common[dependencies][$lianskill[spec]]" != "" && "$common[dependencies][$lianskill[spec]]" != "$kungfu[base][force][jifa]"} {
			addcmd {jifa force $common[dependencies][$lianskill[spec]]};
			#IF {"$kungfu[base][force][jifa]" != "$common[dependencies][$lianskill[spec]]"} {
				#VARIABLE {hp[neili]} {0};
			};
			#VARIABLE {kungfu[base][force][jifa]} {$common[dependencies][$lianskill[spec]]};
		};
		execute {};
		#VARIABLE {checkcount} {0};
		#IF {$hp[neili] < 1000} {
			lianfullneili {golian {%1} {1}};
		};
		#ELSE {
			#IF {"$bskweapon" != ""} {
				wwp {$bskweapon};
			};
			#ELSE {
				uwwp;
			};
			#NOP {在修炼force时如果内功有悟性效果则使用};
			#IF {"$lianskill[base]" == "force" && "$common[wuxingforce][$lianskill[spec]]" != ""} {
				yun $common[wuxingforce][$lianskill[spec]];
			};
			echo {checkhp};
		};
	};
};
#NOP {练习满内力，%1:后续指令};
#ALIAS {lianfullneili} {
	#CLASS lianclass KILL;
	#CLASS lianclass OPEN;
	#ACTION {^{设定环境变量：action \= \"checkhp\"|你设定checkhp为反馈信息}} {
		#CLASS lianclass KILL;
		#NOP {当内力大于5w或者当前激发的内功等级太低则直接洗澡(比如某些技能练习需要激发特定内功，而该内功不能或不需要full)};
		#IF {$hp[neili] < 1000} {
			#IF {$hp[neili_max] >= 5000 || $kungfu[spec][$kungfu[base][force][jifa]][lv] < @eval{$kungfu[base][force][lv] * 3 / 4}} {
				showerfull {%1};
			};
			#ELSE {
				startfull {%1} {3};
			};
		};
		#ELSE {
			%1
		};
	};
	#CLASS lianclass CLOSE;
	hp;
	echo {checkhp};
};
#NOP {获取下一个练习的技能,%1:标识参数,不传按照通用配置允许脱节等级判定,用于checkpot处};
#NOP {传1用于练技能处,最多练到最大熟练度一半,传2用于waitlian中练技能};
#NOP {练功优先级主内功->非依赖内功的技能->其他内功->依赖其他内功的技能};
#FUNCTION getLianSkill {
	#LOCAL {tempsk} {};
	#LOCAL {difflv} {1};
	#IF {"%1" == "" && 1 == 2} {
		#LOCAL {difflv} {$conf[allowdiff][skill]};
	};
	#LOCAL {ppoints} {0};
	#LOCAL {shenok} {1};
	#IF {"$conf[primaryforce]" != "" && "$common[baseexcludes][$conf[primaryforce]]" != "force"} {
		#IF {"%1" == "2"} {
			#LOCAL {ppoints} {$kungfu[spec][$conf[primaryforce]][max_point]};
			#IF {@isShenRequireOK{$conf[primaryforce]} == 0} {
				#LOCAL {shenok} {};
			};
		};
		#IF {$shenok == 1 && @isNeedLianForce{{$conf[primaryforce]}{$difflv}{$ppoints}} == 1} {
			#RETURN {
				{base} {force}
				{spec} {$conf[primaryforce]}
			};
		};
		#NOP {其他除内功以外的非依赖技能};
		#LOCAL {tempsk} {@getLianForceSkill{{$conf[primaryforce]}{%1}}};
		#IF {"$tempsk" != ""} {
			#RETURN {$tempsk};
		};
	};
	#NOP {如未设置主内功自动视为仅有一个内功或其他内功被忽略且未忽略技能没有其他内功的依赖性};
	#FOREACH {*kungfu[force][]} {ng} {
		#IF {"$ng" == "$conf[primaryforce]"} {
			#CONTINUE;
		};
		#NOP {等待练时不练习非当前jifa的内功};
		#IF {"%1" == "2" && "$ng" != "$kungfu[base][force][jifa]"} {
			#CONTINUE;
		};
		#LOCAL {shenok} {1};
		#IF {"%1" == "2"} {
			#LOCAL {ppoints} {$kungfu[spec][$ng][max_point]};
			#IF {@isShenRequireOK{$ng} == 0} {
				#LOCAL {shenok} {};
			};
		};

		#IF {$shenok == 1 && @isNeedLianForce{{$ng}{$difflv}{$ppoints}} == 1} {
			#RETURN {
				{base} {force}
				{spec} {$ng}
			};
		};

		#LOCAL {tempsk} {@getLianForceSkill{{$ng}{%1}}};
		#IF {"$tempsk" != ""} {
			#RETURN {$tempsk};
		};
	};
	#NOP {无限制获取};
	#RETURN {@getLianForceSkill{{}{%1}}};
};

#NOP {获取激发指定内功时可练习的技能,%1:内功,%2:练习标识};
#FUNCTION getLianForceSkill {
	#LOCAL {difflv} {1};
	#IF {"%2" == ""} {
		#LOCAL {difflv} {$conf[allowdiff][skill]};
	};
	#LOCAL {ppoints} {0};
	#FOREACH {*kungfu[spec][]} {ssk} {
		#NOP {排除内功};
		#IF {"$kungfu[force][$ssk]" != ""} {
			#CONTINUE;
		};
		#NOP {排除依赖};
		#IF {"$common[dependencies][$ssk]" != "" && "$common[dependencies][$ssk]" != "%1"} {
			#CONTINUE;
		};
		
		#IF {"%2" == "2"} {
			#NOP {对于部分武功是无法练红，等级相同不可练习};
			#IF {"$ssk" == "wudu-yanluobu"} {
				#LOCAL {ppoints} {1};
			};
			#ELSE {
				#LOCAL {ppoints} {$kungfu[spec][$ssk][max_point]};
			};
		};
		#NOP {对于乾坤大挪移和斗转星移，无法waitlian，这去除熟练度};
		#IF {"$ssk" == "qiankun-danuoyi" || "$ssk" == "douzhuan-xingyi"} {
			#LOCAL {ppoints} {0};
		};
		#IF {@isNeedLianNormal{{$ssk}{$difflv}{$ppoints}} == 0} {
			#CONTINUE;
		};
		#FOREACH {*kungfu[spec][$ssk][jifa][]} {bsk} {
			#IF {"$common[baseexcludes][$ssk]" == "$bsk"} {
				#CONTINUE;
			};
			#IF {"$bsk" == "parry" && "$ssk" != "douzhuan-xingyi" && "$ssk" != "qiankun-danuoyi"} {
				#CONTINUE;
			};
			#IF {$kungfu[spec][$ssk][lv] > $kungfu[base][$bsk][lv]} {
				#CONTINUE;
			};
			#IF {$kungfu[spec][$ssk][lv] < $kungfu[base][$bsk][lv]} {
				#RETURN {
					{base} {$bsk}
					{spec} {$ssk}
				};
			};
			#IF {$kungfu[spec][$ssk][point] >= $ppoints} {
				#CONTINUE;
			};
			#RETURN {
				{base} {$bsk}
				{spec} {$ssk}
			};
		};
	};
	#RETURN {};
};
#NOP {普通技能是否需要练习,%1:技能名称,%2:判定的脱节等级,%3:判定的熟练点数};
#FUNCTION isNeedLianNormal {
	#LOCAL {sklv} {@getSkillLevel{%1}};
	#LOCAL {tarlv} {%2};
	#IF {"%1" == "qiankun-danuoyi"} {
		#MATH {tarlv} {$tarlv + 1};
	};

	#IF {"%1" == "qiankun-danuoyi" && @getSkillLevel{jiuyang-shengong} <= @eval{$kungfu[spec][qiankun-danuoyi][lv]+10}} {
		#RETURN {0};
	};
	
	#IF {"%1" == "anran-zhang" && $hp[con] < 26} {
		#RETURN {0};
	};

	#NOP {是否被临时忽略};
	#IF {"$pendingskills[%1]" != ""} {
		#RETURN {0};
	};

	#NOP {是否已忽略};
	#IF {@contains{{conf[ignoreskills]}{%1}} > 0} {
		#RETURN {0};
	};

	#NOP {是否满足神要求};
	#IF {@isShenRequireOK{%1} == 0} {
		#RETURN {0};
	};

	#NOP {几个特别的技能处理};
	#IF {"%1" == "taiji-jian" && @eval{@getSkillLevel{%1} - @getSkillLevel{taiji-quan}} > 30} {
		#RETURN {0};
	};

	#NOP {是否满足等级要求};
	#IF {$hp[max_lv] > $sklv} {
		#RETURN {1};
	};

	#NOP {是否满足熟练度条件};
	#IF {$kungfu[spec][%1][point] < %3} {
		#RETURN {1};
	};

	#RETURN {0};
};
#NOP {内功技能是否需要练习,%1:技能名称,%2:判定的脱节等级,%3:判定的熟练点数};
#FUNCTION isNeedLianForce {
	#LOCAL {sklv} {@getSkillLevel{%1}};
	#LOCAL {tarlv} {%2};
	
	#NOP {是否被临时忽略};
	#IF {"$pendingskills[%1]" != ""} {
		#RETURN {0};
	};

	#NOP {是否已忽略};
	#IF {@contains{{conf[ignoreskills]}{%1}} > 0} {
		#RETURN {0};
	};

	#NOP {潜能不够};
	#IF {$hp[pot] < 10} {
		#RETURN {0};
	};

	#NOP {是否满足等级要求，特殊内功不能高于基础内功10级};
	#IF {@eval{$sklv - $kungfu[base][force][lv]} >= 10} {
		#RETURN {0};
	};

	#NOP {九阴真功最高可练习的等级为道德经等级-10};
	#IF {"%1" == "jiuyin-zhengong" && $kungfu[spec][%1][lv] > @eval{$kungfu[know][daode-jing][lv] - 10}} {
		#RETURN {0};
	};

	#NOP {普通练习%3=0，不高于内功等级};
	#IF {%3 == 0 && $sklv >= $kungfu[base][force][lv]} {
		#RETURN {0};
	};

	#NOP {waitlian请求时%3!0,需要练红，技能不能高于最大等级切不能高于force 10级};
	#IF {$kungfu[spec][%1][point] >= %3 && $sklv >= $hp[max_lv]} {
		#RETURN {0};
	};

	#RETURN {1};
};
#NOP {神要求是否满足,%1:技能名称};
#FUNCTION isShenRequireOK {
	#LOCAL {shenreq} {$common[shenrequires][%1]};
	#IF {"$shenreq" == ""} {
		#RETURN {1};
	};

	#IF {@abs{$shenreq} > $hp[shen_num]} {
		#RETURN {0};
	};

	#IF {$shenreq > 0 && "$hp[shen]" == "戾气"} {
		#RETURN {0};
	};

	#IF {$shenreq < 0 && "$hp[shen]" == "正气"} {
		#RETURN {0};
	};

	#RETURN {1};
};
#NOP {修复技能神需求，%1:技能，%2:后续指令};
#ALIAS {fixshen} {
	#IF {$common[shenrequires][%1] > 0} {
		gozshen {$common[shenrequires][%1]} {%2};
	};
	#ELSE {
		gofshen {@abs{$common[shenrequires][%1]}} {%2};
	};
};
#NOP {吃大还丹练武功，%1:基础技能，%2:特殊技能，%3:目标等级，%4:后续指令};
#ALIAS {golianeat} {
	#IF {@isShenRequireOK{%2} == 0} {
		fixshen {%2} {golianeat {%1} {%2} {%3} {%4}}
	};
	#ELSE {
		gotodo {扬州城} {当铺} {golianeat_start {%1} {%2} {%3} {%4}};
	};
};
#NOP {吃大还丹练武功};
#ALIAS {golianeat_start} {
	#VARIABLE {targetlv} {$kungfu[base][%1][lv]};
	#IF {"%3" != ""} {
		#VARIABLE {targetlv} {%3};
	};
	#VARIABLE {liando} {
		yun jing;
		yun qi;
		yun jingli;
		lian %1;
		hp;
		echo {checkhp};
	};
	#CLASS lianclass KILL;
	#CLASS lianclass OPEN;
	#ACTION {^由于实战经验不足} {
		#IF {$lvchanged == 0} {
			#VARIABLE {lvchanged} {1};
		};
	};
	#ACTION {^{设定环境变量：action \= \"checkhp\"|你设定checkhp为反馈信息}} {
		#DELAY {0.5} {
			#IF {$lvchanged == 1} {
				#VARIABLE {lvchanged} {0};
				echo {checklian};
			};
			#ELSEIF {$hp[pot] < 50} {
				#CLASS lianclass KILL;
				qu_pot {golianeat {%1} {%2} {%3} {%4}} {100000};
			};
			#ELSEIF {$hp[neili] < 500} {
				#IF {@carryqty{da huandan} > 0} {
					fu dahuan dan;
					i;
					$liando;
				};
				#ELSE {
					duihuan da huandan;
					i;
					echo {checkduihuan};
				};
			};
			#ELSE {
				$liando;
			};
		};
	};
	#ACTION {^{设定环境变量：action \= \"checkfu\"|你设定checkfu为反馈信息}} {
		#IF {$okflag == 1} {
			yun jing;
			yun qi;
			yun jingli;
			lian %1;
			hp;
			echo {checkhp};
		};
		#ELSE {
			#VARIABLE {okflag} {0};
			duihuan da huandan;
			echo {checkduihuan};
		};
	};
	#ACTION {^{设定环境变量：action \= \"checkduihuan\"|你设定checkduihuan为反馈信息}} {
		#IF {@carryqty{da huandan} == 0} {
			#DELAY {1} {
				duihuan da huandan;
				i;
				echo {checkduihuan};
			}
		};
		#ELSE {
			dohalt {
				fu dahuan dan;
				i;
				hp;
				echo {checkhp};
			};
		};
	};
	#ACTION {^{设定环境变量：action \= \"checklian\"|你设定checklian为反馈信息}} {
		#IF {$kungfu[spec][%2][lv] >= $targetlv || $kungfu[spec][%2][lv] >= $kungfu[base][%1][lv]} {
			#CLASS lianclass KILL;
			%4;
		};
		#ELSE {
			$liando;
		};
	};
	#CLASS lianclass CLOSE;
	jifa %1 %2;
	i;
	echo {checkhp};
};
#NOP {学习读书写字,%1:要到达的等级,%2后续指令};
#ALIAS {goliterate} {
	#LOCAL {targetlv} {@eval{%1}};
	#IF {$targetlv == 0} {
		#LOCAL {targetlv} {300};
	};
	#IF {@getSkillLevel{literate} >= %1} {
		%2
	};
	#ELSEIF {$hp[pot] < 2000} {
		qu_pot {goliterate {%1} {%2}};
	};
	#ELSEIF {$env[guigu] == 0} {
		openguigu {goliterate {%1} {%2}};
	};
	#ELSE {
		gotonpc {顾炎武} {
			startliterate {%1} {%2}
		};
	};
};
#NOP {练完准备内功和空手};
#ALIAS {prepareforce} {
	#IF {"$conf[primaryforce]" != ""} {
		jifa force $conf[primaryforce];
		#IF {"$conf[primaryforce]" != "$kungfu[base][force][jifa]"} {
			#VARIABLE {hp[neili]} {0};
		};
		#VARIABLE {kungfu[base][force][jifa]} {$conf[primaryforce]};
	};
	#IF {"$conf[pfm][bei]" != ""} {
		$conf[pfm][bei];
	};
	jifa;
};
#ALIAS {preparewuxingforce} {
	#IF {"$conf[wuxingforce]" != ""} {
		jifa force $conf[wuxingforce];
		#IF {"$conf[wuxingforce]" != "$kungfu[base][force][jifa]"} {
			#VARIABLE {hp[neili]} {0};
		};
		#VARIABLE {kungfu[base][force][jifa]} {$conf[wuxingforce]};
		hp;
		jifa;
	};
};
#ALIAS {dowuxingforce} {
	#IF {"$conf[wuxingforce]" != "" && "$common[wuxingforce][$conf[wuxingforce]]" != ""} {
		yun $common[wuxingforce][$conf[wuxingforce]];
	};
};
#NOP {开始学习读书写字,%1:要到达的等级,%2:后续指令};
#ALIAS {startliterate} {
	#VARIABLE {outofmoney} {0};
	#VARIABLE {learnover} {0};
	#CLASS learnclass KILL;
	#VARIABLE {targetlv} {@eval{$hp[int_xt] * 10}};
	#IF {"%1" != ""} {
		#VARIABLE {targetlv} {%1};
	};
	#VARIABLE {learntimes} {10};
	#IF {$hp[jing_max] < 300} {
		#VARIABLE {learntimes} {10};
	};
	#ELSEIF {$hp[jing] < 600} {
		#VARIABLE {learntimes} {20};
	};
	#ELSEIF {$hp[jing] < 900} {
		#VARIABLE {learntimes} {30};
	};
	#ELSEIF {$hp[jing] < 1200} {
		#VARIABLE {learntimes} {40};
	};
	#ELSE {
		#VARIABLE {learntimes} {50};
	};
	#VARIABLE {learndo} {
		yun jing;
		xue gu literate $learntimes;
		hp;
		echo {checkhp};
	};
	#CLASS learnclass KILL;
	#CLASS learnclass OPEN;
	#ACTION {^顾炎武对着你端详了一番道：“ 你因先天所制，已无法再进修更高深的学问了} {
		#VARIABLE {learnover} {1};
	};
	#ACTION {^{设定环境变量：action \= \"checkcost\"|你设定checkcost为反馈信息}} {
		#LOCAL {jing_cost} {@eval{($hp[jing_max] - $hp[jing])*150/100}};
		#VARIABLE {learntimes} {@eval{$hp[jing_max]/$jing_cost}};
		#VARIABLE {learntimes} {@eval{($learntimes/10)*10}};
		#IF {$learnover == 1} {
			#CLASS learnclass KILL;
			closeguigu {%2}
		};
		#ELSEIF {$learntimes < 10} {
			#VARIABLE {learntimes} {10};
		};
		#ELSEIF {$learntimes > 50} {
			#VARIABLE {learntimes} {50};
		};
		#VARIABLE {learndo} {
			execute {
				yun jing;
				xue gu literate $learntimes;
				hp
			};
			echo {checkhp};
		};
		$learndo
	};
	#ACTION {^{设定环境变量：action \= \"checkhp\"|你设定checkhp为反馈信息}} {
		#VARIABLE {idle} {0};
		dohalt {
			#IF {$kungfu[know][literate][lv] >= $targetlv || $learnover == 1} {
				#CLASS learnclass KILL;
				#SHOWME {<faa>已达成目的};
				closeguigu {%2}
			};
			#ELSEIF {$hp[pot] < $learntimes} {
				#CLASS learnclass KILL;
				goliterate {%1} {%2}
			};
			#ELSEIF {$hp[neili] < 100} {
				#IF {$hp[neili_max] > 5000} {
					#CLASS learnclass KILL;
					showerfull {goliterate {%1} {%2}};
				};
				#ELSE {
					#VARIABLE {medicine} {chuanbei wan};
					#IF {$hp[neili_max] < 2000} {
						#VARIABLE {medicine} {neixi wan};
					};
					#IF {@carryqty{$medicine} > 0} {
						fu $medicine;
						i;
						$learndo
					};
					#ELSE {
						#CLASS learnclass KILL;
						buymedicine {$medicine} {5} {
							goliterate {%1} {%2}
						}
					};
				};
			};
			#ELSE {
				#IF {$env[buff]} {
					pfm_wuxing;
				};
				$learndo
			};
		}
	};
	#ACTION {^你现在正忙着呢。} {
		#DELAY {1} {
			$learndo
		};
	};
	#ACTION {^{这项技能你的程度|这项技能你恐怕}} {
		#IF {"$kungfu[know][$learnskill]" != ""} {
			#VARIABLE {kungfu[know][$learnskill][ignore]} {true};
		};
		#IF {"$kungfu[base][$learnskill]" != ""} {
			#VARIABLE {kungfu[base][$learnskill][ignore]} {true};
		};
		#IF {"$kungfu[spec][$learnskill]" != ""} {
			#VARIABLE {kungfu[spec][$learnskill][ignore]} {true};
		};
		#VARIABLE {learnskill} {@getLearnSkill{1}};
		#IF {"$learnskill" != ""} {
			#VARIABLE {learndo} {
				execute {
					yun jing;
					xue gu $learnskill $learntimes;
					hp
				};
				echo {checkhp};
			};
		};
	};
	#ACTION {^你没有存那么多的钱} {
		#VARIABLE {outofmoney} {1};
	};
	#ACTION {^你现在学习一次所需要的费用%*} {
		#IF {$outofmoney == 0} {
			execute {s;w;#3 n;w;qu 100 gold;i;e;#3 s;e;n};
			$learndo;
		};
		#ELSE {
			#CLASS learnclass KILL;
			#NOP {<faa>没钱了};
			closeguigu {%2}
		};
	};

	#ACTION {^你一觉醒来} {
		execute {
			yun jing;
			yun qi;
			hp;
			out;d;w;#3 s;e;n;
		};
		#IF {$kungfu[base][force][effectlv] > 150} {
			dzn
		};
		#ELSE {
			#UNTICKER {idleticker};
			dazuo @eval{$hp[qi]-10};
		};
	};
	#ACTION {^{你没有那么多的气|你现在精不够}} {
		#DELAY {5} {
			execute {
				yun jing;
				yun qi;
				hp;
			};
			#IF {$kungfu[base][force][effectlv] > 150} {
				dzn
			};
			#ELSE {
				dazuo @eval{$hp[qi]-10};
			};
		}	
	};
	#ACTION {^{$dazuo_over}} {
		hp;
		#IF {$kungfu[base][force][effectlv] > 150} {
			#IF {$hp[neili] > $threshold_neili} {
				pfm_wuxing;
				$learndo
			};
			#ELSE {
				yun qi;
				dzn
			};
		};
		#ELSE {
			$learndo
		};
  };
	#CLASS learnclass CLOSE;
	pfm_wuxing;
	execute {
		hp;
		xue gu literate;
		hp
	};
	echo {checkcost};
};
#NOP {古墓自学,%1:后续指令};
#ALIAS {golearngumu} {
	#IF {@carrqty{fire} == 0} {
		buyfire {golearngumu {%1}}
	};
	#ELSE {
		gotodo {终南山} {后堂} {startlearngumu {%1}}
	};
};
#NOP {古墓新手开始学习,%1:后续指令};
#ALIAS {startlearngumu} {
	#VARIABLE {gmskill} {@getLearnSkillGumu{}};
	#VARIABLE {gmpathto} {};
	#VARIABLE {gmpathback} {};
	#VARIABLE {learndo} {
		execute {
			#10 $gmskill[action];
			yun jing;
			yun jingli;
			hp
		};
		echo {checkhp};
	};

	#CLASS learnclass OPEN;
	#ACTION {^你一觉醒来} {
		execute {
			yun jing;
			yun qi;
			hp;
			open door;
			n
		};
		#IF {$hp[neili_max] > 2000} {
			dzn
		};
		#ELSE {
			#UNTICKER {idleticker};
			echo {checkdazuo};
		};
	};
	#ACTION {^{设定环境变量：action \= \"checkdazuo\"|你设定checkdazuo为反馈信息}} {
		dazuo @eval{$hp[qi]-10};
	};
	#ACTION {^{你没有那么多的气|你现在精不够}} {
		#DELAY {5} {
			execute {
				yun jing;
				yun qi;
				hp
			};
			#IF {$hp[neili_max] > 2000} {
				dzn
			};
			#ELSE {
				echo {checkdazuo};
			};
		}	
	};
	#ACTION {^你至少需要} {
		#CLASS learnclass KILL;
		#DELAY {2} {
			yun qi;
			checkdazuopoint {startlearngumu {%1}}
		};
	};
	#ACTION {^{$dazuo_over}} {
		hp;
		#IF {$hp[neili_max] > 2000} {
			#IF {$hp[neili] > $threshold_neili} {
				startlearngumu {%1}
			};
			#ELSE {
				yun qi;
				dzn
			};
		};
		#ELSE {
			startlearngumu {%1}
		};
  };
	#ACTION {^{你心随弦走，融入琴境，伴随琴声，高声吟道|你的玉女心经精进一层|你的玉女心经已有相当火厚|石壁所述尽在你心|你对着地上研习一会|你已经完全读懂|你使劲地摇晃大树，发现大树|你对石壁上所述剑法已全然掌握|你拳力已刚猛无比|你掌力雄浑|你不用在这里|你已学会|你已经学会}} {
		#VARIABLE {gmskill} {}
	};
	#ACTION {^{设定环境变量：action \= \"checkhp\"|你设定checkhp为反馈信息}} {
		#VARIABLE {idle} {0};
		#DELAY {1} {
			#IF {"$gmskill" == ""} {
				#CLASS learnclass KILL;
				skills;
				$gmpathback;
				loc {
					golearngumu {%1}
				}
			};
			#ELSEIF {$hp[pot] < 10} {
				#CLASS learnclass KILL;
				$gmpathback;
				loc {
					gotodo {终南山} {神秘物体} {
						qn_qu 500;
						golearngumu {%1}
					}
				}
			};
			#ELSEIF {$hp[neili] < 100 && $hp[jing] < 100} {
				jifa all;
				jifa;
				execute {
					$gmpathback;
					open door;
					s;
					sleep
				};
			};
			#ELSE {
				$learndo
			};
		};
	};
	#CLASS learnclass CLOSE;
	#NOP {古墓自学当force和yunu-xinjing都大于50后,通过学习补litereate到122,顺便吃yuji wan};
	#IF {"$gmskill" == ""} {
		#CLASS learnclass KILL;
		i;
		jifa all;
		jifa;
		goliangumu {%1};
	};
	#ELSEIF {@getSkillLevel{force} > 50 && @getSkillLevel{yunu-xinjing} > 50} {
		#IF {@getSkillLevel{literate} < 122} {
			#CLASS learnclass KILL;
			doeatyuji {goliterate {122} {%1}}
		};
		#ELSEIF {$hp[neili_max] < 2000 && @eval{$hp[neili_limit_max] - $hp[neili_max]} >= 500 && "$kungfu[base][force][jifa]" != ""} {
			#CLASS learnclass KILL;
			doeatyuji {%1}
		};
		#ELSE {
			pfm_wuxing;
			#VARIABLE {gmpathto} {$gmskill[pathto]};
			#VARIABLE {gmpathback} {$gmskill[pathback]};
			execute {$gmpathto};
			#IF {"$gmskill[skill]" == "yunu-xinjing" && $kungfu[spec][yunu-xinjing][lv] > 51} {
				nuo qin
			};
			$learndo
		};
	};
	#ELSE {
		pfm_wuxing;
		#VARIABLE {gmpathto} {$gmskill[pathto]};
		#VARIABLE {gmpathback} {$gmskill[pathback]};
		execute {$gmpathto};
		#IF {$gmskill[location][room] == "琴室"} {
			nuo qin
		};
		$learndo
	};
};
#NOP {配偶间学习，%1:技能，%2:等级，%3:后续指令};
#ALIAS {learnspouse} {
	qu_pot {openguigu {gotodo {扬州城} {月老亭} {learnspouse_start {%1} {%2} {%3}}}}
};
#NOP {配偶间学习，%1:技能，%2:等级};
#ALIAS {learnspouse_start} {
	#VARIABLE {learnflag} {0};
	#VARIABLE {targetlv} {220};
	#IF {"%2" != ""} {
		#VARIABLE {targetlv} {@eval{%2}};
	};
	#VARIABLE {learndo} {};
	#CLASS learnclass KILL;
	#CLASS learnclass OPEN;
	#ACTION {^{设定环境变量：action \= \"checkcost\"|你设定checkcost为反馈信息}} {
		#LOCAL {jing_cost} {@eval{($hp[jing_max] - $hp[jing])*150/100}};
		#VARIABLE {learntimes} {@eval{$hp[jing_max]/$jing_cost}};
		#VARIABLE {learntimes} {@eval{($learntimes/10)*10}};
		#IF {$learntimes < 10} {
			#VARIABLE {learntimes} {10};
		};
		#ELSEIF {$learntimes > 50} {
			#VARIABLE {learntimes} {50};
		};
		#IF {$hp[jing_max] < 300 && $learntimes > 10} {
			#VARIABLE {learntimes} {10};
		};
		#IF {$hp[jing_max] < 500 && $learntimes > 20} {
			#VARIABLE {learntimes} {20};
		};
		#IF {$hp[jing_max] < 800 && $learntimes > 30} {
			#VARIABLE {learntimes} {30};
		};
		#IF {$learntimes <= 0} {
			execute {
				hp;
				xue $hp[spouse] %1;
				hp
			};
			echo {checkcost};
		};
		#ELSE {
			#VARIABLE {learndo} {
				pfm_wuxing;
				execute {
					yun jing;
					xue $hp[spouse] %1 $learntimes;
					hp
				};
				echo {checkhp};
			};
			$learndo
		};
	};
	#ACTION {你今天太累了，结果什么也没有学到} {
		#VARIABLE {learnflag} {2};
	};
	#ACTION {^你没有这么多潜能来学习，没有办法再成长了} {
		#VARIABLE {learnflag} {1};
	};
	#ACTION {^{设定环境变量：action \= \"checkhp\"|你设定checkhp为反馈信息}} {
		dohalt {
			#IF {@getSkillLevel{%1} >= $targetlv} {
				#CLASS learnclass KILL;
				#SHOWME {<faa>学习完毕！};
				closeguigu {%3};
			};
			#ELSEIF {$learnflag == 1} {
				#CLASS learnclass KILL;
				learnspouse {%1} {%2}
			};
			#ELSEIF {$learnflag == 2 && $hp[neili] > 500} {
				#CLASS learnclass KILL;
				#NOP {等级增长后精不够了，重新计算};
				dohalt {
					learnspouse_start {%1} {%2} {%3}
				}
			};
			#ELSEIF {$hp[neili] < 500} {
				#CLASS learnclass KILL;
				showerfull {learnspouse {%1} {%2}}
			};
			#ELSE {
				$learndo
			};
		};
	};
	#CLASS learnclass CLOSE;
	#IF {"$hp[spouse]" == ""} {
		#CLASS learnclass KILL;
		#SHOWME {<faa>你还是个光棍!};
	};
	#ELSE {
		pfm_wuxing;
		execute {
			exp;
			yun jing;
			hp;
			xue $hp[spouse] %1;
			hp
		};
		echo {checkcost}
	};
};
#NOP {夫妻互学服务};
#ALIAS {servicespouse} {
	gotodo {扬州城} {月老亭} {servicespouse_start}
};
#ALIAS {servicespouse_start} {
	#CLASS learnclass KILL;
	#CLASS learnclass OPEN;
	#ACTION {^{设定环境变量：action \= \"checkhp\"|你设定checkhp为反馈信息}} {
		#VARIABLE {idle} {0};
		#DELAY {6} {
			smile;
			hp;
			echo {checkhp}
		}
	};
	#CLASS learnclass CLOSE;
	echo {checkhp}
};
#NOP {古墓新手练技能};
#ALIAS {goliangumu} {
	gotodo {终南山} {后堂} {startliangumu {%1}}
};
#NOP {练技能,%1:后续指令};
#ALIAS {startliangumu} {
	#VARIABLE {lianskill} {};
	#VARIABLE {liando} {};
	#CLASS lianclass OPEN;
	#ACTION {^你一觉醒来} {
		#CLASS lianclass KILL;
		execute {
			yun jing;
			yun qi;
			hp;
			open door;
			n
		};
		startfull {startliangumu {%1}} {3};
	};
	#ACTION {^{设定环境变量：action \= \"checkhp\"|你设定checkhp为反馈信息}} {
		#VARIABLE {idle} {0};
		#IF {$kungfu[spec][$kungfu[base][$lianskill][jifa]][lv] >= $kungfu[base][$lianskill][lv] || $kungfu[spec][$kungfu[base][$lianskill][jifa]][lv] >= 121} {
			#CLASS lianclass KILL;
			startliangumu {%1}
		};
		#ELSEIF {$hp[neili] < 100} {
			open door;
			s;
			sleep
		};
		#ELSE {
			$liando
		};
	};
	#CLASS lianclass CLOSE;
	#IF {$kungfu[spec][yunu-shenfa][lv] < $kungfu[base][dodge][lv] && $kungfu[spec][yunu-shenfa][lv] < 121} {
		#VARIABLE {lianskill} {dodge};
	};
	#ELSE {
		#IF {"$hp[sex]" == "m" && $kungfu[spec][quanzhen-jianfa][lv] < $kungfu[base][sword][lv] && $kungfu[spec][quanzhen-jianfa][lv] < 121} {
			#VARIABLE {lianskill} {sword};
		};
		#ELSEIF {"$hp[sex]" == "f" && $kungfu[spec][yunu-jianfa][lv] < $kungfu[base][sword][lv] && $kungfu[spec][yunu-jianfa][lv] < 121} {
			#VARIABLE {lianskill} {sword};
		};
	};
	#IF {"$lianskill" == ""} {
		#SHOWME {<faa>可以出古墓了};
		guide_checkskill {%1};
	};
	#ELSE {
		#VARIABLE {liando} {
			lian $lianskill;
			yun jingli;
			hp;
			#DELAY {0.5} {
				echo {checkhp};
			}
		};
		#IF {"$lianskill" == "sword"} {
			#IF {"$id[weapon]" == ""} {
				e;n;get jian;wield jian;i;s;w
			};
		};
		echo {checkhp};
	};
};
#NOP {获取古墓新手要学习的技能};
#NOP {内功->51,读书识字->101,内功->101,其他到101};
#FUNCTION getLearnSkillGumu {
	#VARIABLE {gmskill} {};
	#IF {@getSkillLevel{yunu-xinjing} < 51} {
		#VARIABLE {gmskill} {
			{pathto}{open door;s}
			{pathback}{open door;n}
			{skill}{force}
			{action}{zuo bed}
		}
	};
	#ELSEIF {@getSkillLevel{force} < 51} {
		#VARIABLE {gmskill} {
			{pathto}{w;w;n}
			{pathback}{s;e;e}
			{skill}{force}
			{action}{yanxi wall}
		}
	};
	#ELSEIF {@getSkillLevel{yunu-xinjing} < 121 && @getSkillLevel{yunu-xinjing} < $hp[max_lv]} {
		#VARIABLE {gmskill} {
			{pathto}{e;nuo qin}
			{pathback}{w}
			{skill}{yunu-xinjing}
			{action}{yanxi table}
		}
	};
	#ELSEIF {@getSkillLevel{force} < 121 && @getSkillLevel{force} < $hp[max_lv]} {
		#VARIABLE {gmskill} {
			{pathto}{e}
			{pathback}{w}
			{skill}{force}
			{action}{tan qin}
		}
	};
	#ELSEIF {@getSkillLevel{dodge} < 51} {
		#VARIABLE {gmskill} {
			{pathto}{w;open door;s}
			{pathback}{open door;n;e}
			{skill}{dodge}
			{action}{zhuo maque}
		}
	};
	#ELSEIF {@getSkillLevel{dodge} < 101} {
		#VARIABLE {gmskill} {
			{pathto}{w;open door;s;enter}
			{pathback}{out;open door;n;e}
			{skill}{dodge}
			{action}{zhuo maque}
		}
	};
	#ELSEIF {@getSkillLevel{parry} < 51} {
		#VARIABLE {gmskill} {
			{pathto}{w;w;n}
			{pathback}{s;e;e}
			{skill}{parry}
			{action}{yanxi ground}
		}
	};
	#ELSEIF {@getSkillLevel{parry} < 101} {
		#VARIABLE {gmskill} {
			{pathto}{w;w;out;s}
			{pathback}{n;enter;e;e}
			{skill}{parry}
			{action}{yao tree}
		}
	};
	#ELSEIF {@getSkillLevel{sword} < 51} {
		#VARIABLE {gmskill} {
			{pathto}{w;n}
			{pathback}{s;e}
			{skill}{sword}
			{action}{xiulian eastwall}
		}
	};
	#ELSEIF {@getSkillLevel{sword} < 101} {
		#VARIABLE {gmskill} {
			{pathto}{w;n;tui eastwall;enter}
			{pathback}{tui westwall;out;s;e}
			{skill}{sword}
			{action}{xiulian westwall}
		}
	};
	#ELSEIF {"$kungfu[spec][yunu-shenfa]" == ""} {
		#VARIABLE {gmskill} {
			{pathto}{w;w;n}
			{pathback}{s;e;e}
			{skill}{yunu-shenfa}
			{action}{yanxi top}
		}
	};
	#ELSE {
		#IF {"$hp[sex]" == "m"} {
			#IF {"$kungfu[spec][quanzhen-jianfa]" == ""} {
				#VARIABLE {gmskill} {
					{pathto}{w;n}
					{pathback}{s;e}
					{skill}{quanzhen-jianfa}
					{action}{yanxi top}
				}
			};
		};
		#ELSE {
			#IF {"$kungfu[spec][yunu-jianfa]" == ""} {
				#VARIABLE {gmskill} {
					{pathto}{w;n;tui eastwall;enter}
					{pathback}{tui westwall;out;s;e}
					{skill}{yunu-jianfa}
					{action}{yanxi top}
				}
			};
		};
	};
	#RETURN {$gmskill};
};
#NOP {是否存在正在学习或者练习需要正气的内功};
#FUNCTION {hasZhengqiForce} {
	#FOREACH {*common[shenrequires][]} {sk} {
		#IF {@contains{{conf[ignoreskills]}{$sk}} > 0} {
			#CONTINUE;
		};
		#IF {@eval{$kungfu[spec][$sk][lv]} == 0} {
			#CONTINUE;
		};

		#RETURN {1};
	};

	#RETURN {0};
}
#NOP {古墓双修,%1:双修的配合者};
#ALIAS {goshuangxiu} {
	gotodo {终南山} {红花丛} {startshuangxiu {%1}}
};
#ALIAS {startshuangxiu} {
	#VARIABLE {askresult} {0};
	#VARIABLE {hxtarget} {};
	#VARIABLE {shuangxiuflag} {0};
	#VARIABLE {checkcount} {0};
	#CLASS learnclass OPEN;
	#ACTION {^{没有这个人|这个人断线了}} {
		#VARIABLE {askresult} {1};
	};

	#ACTION {^你的潜能消耗光了，暂时无法合修} {
		execute {s;e;s;u;qn_qu 1000;d;n;n;w;e;s;e;hp};
		#IF {"%1" != ""} {
			hexiu $hxtarget;
			echo {checkshuangxiu};
		};
	};
	#ACTION {^红花丛中，你端坐于%*面前，欲与其和修玉女心经。} {
	};
	#ACTION {^红花丛中，%*端坐于你面前，欲与你和修玉女心经。} {
		#IF {"%1" == ""} {
			#IF {$hp[neili] < 200} {
				w;
				dzn
			};
			#ELSE {
				execute {
					yun jing;
					yun qi;
					hp;
					hexiu $hxtarget
				};
			};
		};
	};
	#ACTION {^%!*(%*)告诉你：shuangxiu} {
		#VARIABLE {hxtarget} {@lower{%%1}};
	};
	#ACTION {^良久后} {
		#VARIABLE {idle} {0};
		#VARIABLE {checkcount} {0};
		hp;
		#IF {"%1" != ""} {
			dohalt {
				#IF {$hp[neili] < 200} {
					w;
					dzn
				};
				#ELSE {
					execute {
						yun jing;
						yun qi;
						hp;
						hexiu $hxtarget
					};
					echo {checkshuangxiu};
				};
			}
		};
	};
	#ACTION {^{$dazuo_over}} {
    hp;
		#IF {$hp[neili] > $threshold_neili} {
			e;
			hexiu $hxtarget;
			echo {checkshuangxiu};
		};
		#ELSE {
			dzn
		};
  };
	#ACTION {^{设定环境变量：action \= \"checkshuangxiu\"|你设定checkshuangxiu为反馈信息}} {
		#IF {$askresult == 1} {
			#DELAY {5} {
				#VARIABLE {askresult} {0};
				tell $hxtarget shuangxiu;
				hexiu $hxtarget;
				echo {checkshuangxiu};
			};
		};
	};
	#CLASS learnclass CLOSE;
	remove all;
	#IF {"%1" != ""} {
		#VARIABLE {hxtarget} {%1};
		tell $hxtarget shuangxiu;
		hexiu $hxtarget;
		echo {checkshuangxiu};
	};
};
#NOP {读禅宗心法,%1:后续指令};
#ALIAS {learnbuddhism} {
	gotodo {星宿海}	{莫高窟} {learnbuddhism_start {%1}}
}; 
#ALIAS {learnbuddhism_start} {
	#VARIABLE {learndo} {
		execute {
			#10 du shu;
			hp
		};
		echo {checkhp};
	};
	#CLASS learnclass OPEN;
	#ACTION {^你附近没有这样东西} {
		#VARIABLE {idle} {0};
		#DELAY {5} {
			get shu
		};
	};
	#ACTION {^你捡起一本%*} {
		$learndo
	};

	#ACTION {^{$dazuo_over}} {
		#VARIABLE {idle} {0};
    #IF {$hp[neili] > $threshold_neili} {
			yun qi;
			$learndo
		};
		#ELSE {
			dzn
		};
  };
	#ACTION {^{设定环境变量：action \= \"checkhp\"|你设定checkhp为反馈信息}} {
		#VARIABLE {idle} {0};
		#DELAY {0.5} {
			yun jing;
			#IF {$kungfu[know][buddhism][lv] >= 30} {
				#CLASS learnclass KILL;
				drop shu;
				%1
			};
			#ELSEIF {$hp[neili] < 400} {
				dzn
			};
			#ELSE {
				$learndo
			};
		};
	};
	#CLASS learnclass CLOSE;
	get shu
};
#NOP {大草原草场练基本,%1:基本技能,%2:后续指令};
#ALIAS {duigancao} {
	#LIST {supportbs} {create} {stick;hook;staff;brush;axe;whip;throwing;blade;spear};
	#IF {@contains{{supportbs}{%1}} == 0} {
		#SHOWME {<faa>不支持的基础技能};
		%2;
	};
	#ELSE {
		#LOCAL {wp} {@getDefineWeapon{%1}};
		#IF {"$wp" == ""} {
			#SHOWME {<faa>未配置的基础武器};
			%2;
		};
		#ELSEIF {@carryqty{$wp} == 0} {
			findweapon {$wp} {duigancao {%1} {%2}};
		};
		#ELSE {
			gotodo {回疆} {草场} {duigancao_start {%1} {%2}};
		};
	};
};
#ALIAS {duigancao_start} {
	#VARIABLE {okflag} {0};
	#CLASS kungfuclass KILL;
	#CLASS kungfuclass OPEN;
	#ACTION {^你使劲地叉着干草，但却发现没有什么大的用处} {
		#VARIABLE {okflag} {1};
	};
	#ACTION {^{设定环境变量：action \= \"checkhp\"|你设定checkhp为反馈信息}} {
		#VARIABLE {idle} {0};
		#DELAY {1} {
			#IF {$okflag == 1} {
				#CLASS kungfuclass KILL;
				%2;
			};
			#ELSEIF {$hp[neili] < 500} {
				#CLASS kungfuclass KILL;
				startfull {duigancao_start {%1} {%2}} {3};
			};
			#ELSE {
				execute {
					yun qi;
					yun jing;
					yun jingli;
					#10 dui gancao;
					hp
				};
				echo {checkhp};
			};
		};
	};
	#CLASS kungfuclass CLOSE;
	wwp {@getDefineWeapon{%1}};
	hp;
	echo {checkhp};
};
#NOP {你把五个手指插入骷髅头上的五个指孔};
#NOP {周芷若claw 0~31};
#ALIAS {learnclaw} {
	gotonpc {周芷若} {learnclaw_start {%1}}
};
#ALIAS {learnclaw_start} {
	#VARIABLE {insertflag} {0};
	#VARIABLE {learndo} {
		#VARIABLE {insertflag} {0};
		execute {#5 insert kulou tou};
		echo {checkclaw}
	};
	#CLASS learnclass KILL;
	#CLASS learnclass OPEN;
	#ACTION {^你把五个手指插入骷髅头上的五个指孔} {
		#VARIABLE {insertflag} {1};
	};
	#ACTION {^{设定环境变量：action \= \"checkclaw\"|你设定checkclaw为反馈信息}} {
		#VARIABLE {idle} {0};
		#IF {@getSkillLevel{claw} >= 31} {
			#CLASS learnclass KILL;
			#DELAY {1} {%1};
		};
		#IF {$insertflag == 0} {
			#DELAY {6} {
				$learndo
			};
		};
		#ELSE {
			#DELAY {0.5} {
				$learndo
			};
		};
	};
	#CLASS learnclass CLOSE;
	$learndo
};
#NOP {少林基础武功领悟,%1:基础武功,%2:后续指令};
#ALIAS {learnbase} {
	#SWITCH {"%1"} {
		#CASE {"hand"} {
			#IF {@getSkillLevel{%1} < 31} {
				#SHOWME {<faa>读桃花岛《兰花拂穴手图解》或明教《鹰爪手法》到31级};
			};
			#ELSEIF {@getSkillLevel{buddhism} < 31} {
				learnbuddhism {learnbase {%1} {%2}};
			};
			#ELSE {
				gotodo {嵩山少林} {圣僧塔} {learnbase_start {%1} {%2}}
			};
		};
		#CASE {"cuff"} {
			#IF {@getSkillLevel{%1} < 31} {
				#SHOWME {<faa>读明教《七伤拳谱》到31级};
			};
			#ELSEIF {@getSkillLevel{buddhism} < 31} {
				learnbuddhism {learnbase {%1} {%2}};
			};
			#ELSE {
				gotodo {嵩山少林} {圣僧塔} {learnbase_start {%1} {%2}}
			};
		};
		#CASE {"strike"} {
			#IF {@getSkillLevel{%1} < 31} {
				#SHOWME {<faa>读桃花岛《落英神剑掌图解》或天龙寺《掌法诀要》到31级};
			};
			#ELSE {
				gotodo {嵩山少林} {圣僧塔} {learnbase_start {%1} {%2}}
			};
		};
		#CASE {"claw"} {
			#IF {@getSkillLevel{%1} < 31} {
				learnclaw {learnbase {claw} {%2}};
			};
			#ELSE {
				gotodo {嵩山少林} {圣僧塔} {learnbase_start {%1} {%2}}
			};
		};
		#DEFAULT {
			#SHOWME {<faa>尚未支持的技能};
			%2;
		};
	};
};
#ALIAS {learnbase_start} {
	#VARIABLE {learndo} {};
	#VARIABLE {enterdo} {};
	#VARIABLE {okflag} {0};
	#CLASS learnclass KILL;
	#CLASS learnclass OPEN;

	#ACTION {^{你在虚空中，感觉大师座下打开了一个小门|只听“砰”的一声，你已被送进了法台|你气运丹田，嘿的一声向灵牌推出一掌|你在台边一借力，身子凌空直上}} {
		#VARIABLE {okflag} {1};
	};
	#ACTION {^{你已尽数领悟透了案上书法的含义|佛像姿势虽繁但你已尽数领悟于心|你用力过猛，大绳从中断绝|你掌力沉雄，铜缸内净水为你掌力所激}} {
		#VARIABLE {okflag} {1};
	};
	#ACTION {^{设定环境变量：action \= \"checkflag\"|你设定checkflag为反馈信息}} {
		#VARIABLE {idle} {0};
		#IF {$okflag == 1} {
			#VARIABLE {okflag} {0};
			enter;
			echo {checkhp};
		};
		#ELSE {
			#DELAY {1} {
				$enterdo;
			};
		};
	};
	#ACTION {^{$dazuo_over}} {
    #IF {$hp[neili] > $threshold_neili} {
			echo {checkhp};
    };
    #ELSE {
      dzn
    };
  };
	#ACTION {^{设定环境变量：action \= \"checkhp\"|你设定checkhp为反馈信息}} {
		#VARIABLE {idle} {0};
		#DELAY {1} {
			#IF {$okflag == 1} {
				#CLASS learnclass KILL;
				out;
				fangqi buddhism;
				dohalt {
					loc {%2};
				};
			};
			#ELSEIF {$hp[neili] < 400} {
				yun jing;
				dzn
			};
			#ELSE {
				execute {
					yun jing;
					#10 $learndo;
					hp
				};
				echo {checkhp};
			};
		}
	};
	#CLASS learnclass CLOSE;
	#SWITCH {"%1"} {
		#CASE {"hand"} {
			#VARIABLE {learndo} {ningwang foxiang};
			#VARIABLE {enterdo} {
				canchan zuo;
				echo {checkflag};
			};
			say 若得不驰散，深入实相不;
			$enterdo;
		};
		#CASE {"cuff"} {
			#VARIABLE {learndo} {du lingan};
			#VARIABLE {enterdo} {
				sheshen;
				echo {checkflag};
			};
			say 今日大欢喜，舍却危脆身;
			$enterdo;
		};
		#CASE {"strike"} {
			#VARIABLE {learndo} {strike shui};
			#VARIABLE {enterdo} {
				fushi pai;
				chuzhang pai;
				echo {checkflag};
			};
			$enterdo;
		};
		#CASE {"claw"} {
			#VARIABLE {learndo} {claw sheng};
			#VARIABLE {enterdo} {
				shenru;
				echo {checkflag};
			};
			say 若得不驰散，深入实相不;
			$enterdo;
		};
		#DEFAULT {
			#CLASS learnclass KILL;
			%2
		};
	};
};
#NOP {开始在塔林修炼手法,完毕后视情况放弃禅宗心法};
#ALIAS {learnhand_start} {
	#VARIABLE {overflag} {0};
	#VARIABLE {learndo} {
		execute {
			yun jing;
			#10 ningwang foxiang;
			hp
		};
		echo {checkhp};
	};
	#CLASS learnclass OPEN;

	#ACTION {^佛像姿势虽繁但你已尽数领悟于心} {
		#VARIABLE {overflag} {1};
	};
	#ACTION {^{$dazuo_over}} {
		#VARIABLE {idle} {0};
    #IF {$hp[neili] > $threshold_neili} {
			yun qi;
			$learndo
		};
		#ELSE {
			dzn
		};
  };
	#ACTION {^{设定环境变量：action \= \"checkhp\"|你设定checkhp为反馈信息}} {
		#VARIABLE {idle} {0};
		#DELAY {0.5} {
			#IF {$overflag == 1} {
				#IF {$kungfu[know][buddhism][lv] < 50} {
					fangqi buddhism
				};
				dohalt {%1}
			};
			#ELSEIF {$hp[neili] < 400} {
				dzn
			};
			#ELSE {
				$learndo
			};
		};
	};
	#CLASS learnclass CLOSE;
	$learndo
};
#NOP {奇门八卦读石碑,至少需要51,%1:后续指令};
#ALIAS {learnbagua} {
	gotodo {桃花岛} {山顶} {
		learnbagua_start {restoregift {%1}};
	};
};
#NOP {奇门八卦读石碑,至少需要51,%1:后续指令};
#ALIAS {learnbagua_start} {
	#VARIABLE {okflag} {0};
	#CLASS learnclass KILL;
	#CLASS learnclass OPEN;

	#ACTION {^你仔细地读了一遍碑上的文字，发现已经太浅显了，无法学到新的知识。} {
		#VARIABLE {okflag} {1};
		skills
	};
	#ACTION {^{设定环境变量：action \= \"checkhp\"|你设定checkhp为反馈信息}} {
		#VARIABLE {idle} {0};
		#DELAY {0.5} {
			#IF {$okflag == 1} {
				#CLASS learnclass KILL;
				loc {%1}
			};
			#ELSEIF {$hp[neili] < 300} {
				#CLASS learnclass KILL;
				startfull {learnbagua_start {%1}} {3}
			};
			#ELSE {
				execute {
					#10 du shibei;
					yun jing;
					hp
				};
				echo {checkhp};
			};
		}
	};
	#CLASS learnclass CLOSE;
	pfm_wuxing;
	hp;
	echo {checkhp};
};
#NOP {补知识技能,%1:后续指令,因为medicine需要退出所以最后补};
#ALIAS {fullknow} {
	fullqmbg {fulljingmai {fullmedicine {%1} {121}} {121}}	
};
#NOP {学习蛤蟆功,%1:后续指令};
#ALIAS {learnhmg} {
	#NOP {西域大戈壁要判断下水};
	#IF {$hp[water] < 80} {
		gofood {learnhmg {%1}}
	};
	#ELSE {
		gotodo {白驼山} {乱石堆} {learnhmg_start {%1}}
	};
};
#NOP {开始补蛤蟆功,%1:后续指令};
#ALIAS {learnhmg_start} {
	#VARIABLE {okflag} {0};
	#CLASS learnclass KILL;
	#CLASS learnclass OPEN;
	#ACTION {^你一掌向巨石推去，只听轰的一声，巨石被你掌力震成两半} {
		#VARIABLE {okflag} {1};
	};
	#ACTION {^你用手指推了推巨石，脸上露出一丝难堪的神色} {
		#VARIABLE {okflag} {1};
	};
	#ACTION {^{设定环境变量：action \= \"checkhp\"|你设定checkhp为反馈信息}} {
		#VARIABLE {idle} {0};
		#IF {$okflag == 1} {
			#CLASS learnclass KILL;
			%1
		};
		#ELSEIF {$hp[neili] < 200} {
			#CLASS learnclass KILL;
			startfull {learnhmg_start {%1}} {3}
		};
		#ELSE {
			#DELAY {1} {
				execute {
					#10 strike stone;
					yun jing;
					yun jingli;
					hp
				};
				echo {checkhp};
			};
		};
	};
	#CLASS learnclass CLOSE;
	adjustgift {20} {20} {10} {30} {echo {checkhp}};
};
#NOP {读九阴下,%1:技能,%2:后续指令};
#ALIAS {readjydown} {
	#IF {@carryqty{jiuyin zhenjing} == 0} {
		findjydown {readjydown {%1}}
	};
	#ELSE {
		gotodo {扬州城} {小吃店} {readjydown_start {%1} {%2}}
	};
};
#NOP {读九阴下,%1:技能,%2:后续指令};
#ALIAS {readjydown_start} {
	#VARIABLE {missing} {0};
	#VARIABLE {readover} {0};
	#VARIABLE {readdo} {
		execute {
			#10 read %1;
			yun jing;
			hp
		};
		echo {checkhp};
	};
	#CLASS learnclass KILL;
	#CLASS learnclass OPEN;
	#ACTION {^你一时想不起九阴真经下卷有什么用处，就随手把它丢掉了} {
		#VARIABLE {missing} {1};
	};
	#ACTION {^你觉得这经书上所写的已经太浅了，不能学到什么东西。} {
		#VARIABLE {readover} {1};
	};
	#ACTION {^你的内力不够，无法领会这个技能} {
		#VARIABLE {readover} {3};
	};
	#ACTION {^你的潜能已经用完了，再怎么读也没用。} {
		#VARIABLE {readover} {4};
	};
	#ACTION {^{设定环境变量：action \= \"checkhp\"|你设定checkhp为反馈信息}} {
		#VARIABLE {idle} {0};
		#DELAY {1} {
			#IF {$missing == 1} {
				#CLASS readclass KILL;
				findjydown {readjydown {%1} {%2}}
			};
			#ELSEIF {$readover == 1} {
				#CLASS readclass KILL;
				skills;
				%3
			};
			#ELSEIF {$readover == 3} {
				execute {w;s;w;qu 5 silver;e;e;give 5 silver to xiao er;u;enter;sleep};
			};
			#ELSEIF {$readover == 4} {
				#CLASS readclass KILL;
				#DELAY {1} {
					qu_pot {gotodo {扬州城} {小吃店} {readjydown_start {%1} {%2}}};
				};
			};
			#ELSE {
				#IF {$hp[neili] < 100 && $hp[jing] < 100} {
					execute {w;s;w;qu 5 silver;e;e;give 5 silver to xiao er;u;enter;sleep};
				};
				#ELSE {
					$readdo
				};
			};
		}
	};
	#ACTION {^你一觉醒来} {
		#VARIABLE {readover} {0};
		execute {
			out;d;w;n;e;
			yun jing;
		};
		dzn
	};
	#ACTION {^{$dazuo_over}} {
		#IF {$hp[neili] > $threshold_neili} {
			pfm_wuxing;
			$readdo
		};
		#ELSE {
			dzn
		};
  };
	#CLASS learnclass CLOSE;
	pfm_wuxing;
	$readdo;
};
#ALIAS {findjydown} {
	gotodo {兰州城} {石洞} {findjydown_start {%1}}
};
#ALIAS {findjydown_start} {
	#CLASS learnclass KILL;
	#CLASS learnclass OPEN;
	#ACTION {^这里没有这个人。} {
		#VARIABLE {idle} {0};
		#DELAY {5} {
			kill mei chaofeng
		};
	};
	#ACTION {^梅超风「啪」的一声倒在地上，挣扎着抽动了几下就死了} {
    follow chen xuanfeng
  };
	#ACTION {^这里没有 chen xuanfeng} {
    kill mei chaofeng
  };
	#ACTION {^你决定跟随陈玄风一起行动} {
    kill chen xuanfeng
  };
	#ACTION {^陈玄风「啪」的一声倒在地上，挣扎着抽动了几下就死了} {
    #CLASS learnclass KILL;
		i;
		#DELAY {2} {
			loc {%1}
		};
  };
	#CLASS learnclass CLOSE;
	kill mei chaofeng
};
#NOP {蛤蟆功初始化学习技能,解完hmg2后自动补技能,直到用户接手重新配置用户文件};
#NOP {staff->cuff(如果有)->lingshe-zhangfa->lingshe-quanfa->hamagong};
#ALIAS {hmg_guide} {
	#NOP {无用的武功先忽略};
	#IF {@contains{{conf[ignoreskills]}{hamabu}} == 0} {
		#LIST {conf[ignoreskills]} {add} {hamabu};
	};
	#IF {@contains{{conf[ignoreskills]}{lingshe-quanfa}} == 0} {
		#LIST {conf[ignoreskills]} {add} {lingshe-quanfa};
	};
	#IF {@contains{{conf[ignoreskills]}{lingshe-zhangfa}} == 0} {
		#LIST {conf[ignoreskills]} {add} {lingshe-zhangfa};
	};
	#IF {@contains{{conf[ignoreskills]}{hamagong}} == 0} {
		#LIST {conf[ignoreskills]} {add} {hamagong};
	};
	#IF {@getSkillLevel{literate} < @eval{$hp[int_xt] * 10}} {
		#IF {$hp[balance] < 6000} {
			funds_call {hmg_guide} {6000}
		};
		#ELSE {
			goliterate {@eval{$hp[int_xt] * 10}} {hmg_guide}
		};
	};
	#ELSEIF {@getSkillLevel{staff} < 101} {
		duigancao {staff} {hmg_guide};
	};
	#ELSEIF {@getSkillLevel{cuff} > 31 && @getSkillLevel{cuff} < 101} {
		learnbase {cuff} {hmg_guide};
	};
	#ELSEIF {@getSkillLevel{qimen-bagua} < 31} {
		library_call {易经} {
			godubook {jing} {hmg_guide} {} {} {qimen-bagua};
		};
	};
	#ELSEIF {@getSkillLevel{qimen-bagua} < 51} {
		library_call {九宫八卦图谱} {
			godubook {tu} {hmg_guide} {} {} {qimen-bagua};
		};
	};
	#ELSEIF {@getSkillLevel{poison} < 151} {
		fullpoison {hmg_guide};
	};
	#ELSEIF {@getSkillLevel{jingmai-xue} < 121} {
		fulljingmai {hmg_guide} {121};
	};
	#ELSEIF {@getSkillLevel{medicine} < 122} {
		fullmedicine {hmg_guide} {121};
	};
	#ELSE {
		#NOP {开始正常补技能,优先用原来的内功补技能,最后补蛤蟆功};
		#IF {@getSkillLevel{cuff} >= 101} {
			#LIST {conf[ignoreskills]} {delete} {@contains{{conf[ignoreskills]}{lingshe-quanfa}}};
		};
		#LIST {conf[ignoreskills]} {delete} {@contains{{conf[ignoreskills]}{lingshe-zhangfa}}};
		#IF {"$conf[newbie][party]" != ""} {
			#LIST {guide[$conf[newbie][party]][masters][$hp[master][name]][favourites]} {add} {lingshe-quanfa};
			#LIST {guide[$conf[newbie][party]][masters][$hp[master][name]][favourites]} {add} {lingshe-zhangfa};
		};
		golian {jobgo};
	};
};
#NOP {练玄铁剑,%1:后续指令};
#ALIAS {fullxtj} {
	#IF {"$hp[party]" != "古墓派" || @getSkillLevel{xuantie-jianfa} > 210} {
		#SHOWME {<faa>别闹了。};
		%1;
	};
	#ELSEIF {@getSkillLevel{xuantie-jianfa} == 0} {
		gotonpc {杨过} {fullxtj_ask {fullxtj_check {%1}}}
	};
	#ELSE {
		fullxtj_check {%1};
	};
};
#NOP {玄铁剑问杨过};
#ALIAS {fullxtj_ask} {
	#CLASS learnclass KILL;
	#CLASS learnclass OPEN;
	#ACTION {杨过说道：「{我不是已经告诉你玄铁剑法的运气之道了么|屏气具息，凝气守中，意守丹田}} {
		dohalt {
			ask yang guo about 武功;
		};
	};
	#ACTION {^你向杨过打听有关『武功』的消息} {
		dohalt {
			ask yang guo about 剑法
		};
	};
	#ACTION {^你向杨过打听有关『剑法』的消息} {
		#CLASS learnclass KILL;
		dohalt {
			prepareskills {%1};
		};
	};
	#CLASS learnclass CLOSE;
	ask yang about 玄铁剑法;
};
#NOP {玄铁剑检查武器,先拿chang jian};
#ALIAS {fullxtj_check} {
	#IF {@getSkillLevel{xuantie-jianfa} <= 120} {
		#IF {@carryqty{xuantie jian} == 0} {
			findweapon_xtj {fullxtj_check {%1}};
		};
		#ELSE {
			gotodo {襄阳城} {山洪中} {fullxtj_start {%1}}
		};
	};
	#ELSEIF {@getSkillLevel{xuantie-jianfa} <= 160} {
		#IF {@carryqty{chang jian} == 0} {
			findweapon_cj {fullxtj_check {%1}};
		};
		#ELSE {
			gotodo {襄阳城} {山洪中} {fullxtj_start {%1}}
		};
	};
	#ELSEIF {@getSkillLevel{xuantie-jianfa} <= 210} {
		#IF {@carryqty{mu jian} == 0} {
			findweapon_mj {fullxtj_check {%1}};
		};
		#ELSE {
			gotodo {襄阳城} {山洪中} {fullxtj_start {%1}}
		};
	};
	#ELSE {
		%1
	};
};
#NOP {玄铁剑练剑};
#ALIAS {fullxtj_start} {
	#VARIABLE {learnflag} {0};
	#VARIABLE {learndo} {
		#VARIABLE {learnflag} {0};
		execute {
			#10 ji flood;
			yun jingli;
			hp
		};
		echo {checkhp};
	};
	#CLASS learnclass KILL;
	#CLASS learnclass OPEN;
	#ACTION {^{设定环境变量：action \= \"checkweapon\"|你设定checkweapon为反馈信息}} {
		#IF {@getSkillLevel{xuantie-jianfa} <= 120} {
			#IF {@carryqty{xuantie jian} == 0} {
				#CLASS learnclass KILL;
				fullxtj {%1};
			};
			#ELSE {
				wwp {xuantie jian};
				$learndo;
			};
		};
		#ELSEIF {@getSkillLevel{xuantie-jianfa} <= 160} {
			#IF {@carryqty{chang jian} == 0} {
				#CLASS learnclass KILL;
				fullxtj {%1};
			};
			#ELSE {
				drop xuantie jian;
				wwp {chang jian};
				$learndo;
			};
		};
		#ELSEIF {@getSkillLevel{xuantie-jianfa} <= 210} {
			#IF {@carryqty{mu jian} == 0} {
				fullxtj {%1};
			};
			#ELSE {
				drop chang jian;
				wwp {mu jian};
				$learndo;
			};
		};
	};
	#ACTION {^你对着山洪拳打脚踢了一阵，感到劲疲力尽。} {
		#VARIABLE {learnflag} {1};
	};
	#ACTION {^凭你现在的玄铁剑法等级，不用玄铁剑怎能领悟呢} {
		#VARIABLE {learnflag} {1};
	};
	#ACTION {^你想进一步提高玄铁剑法的修为，要换一把剑了} {
		#VARIABLE {learnflag} {1};
	};
	#ACTION {^你想进一步提高玄铁剑法的修为，要进一步去领悟了。} {
		#VARIABLE {learnflag} {1};
	};
	#ACTION {^你已经无法再在这里加深你的玄铁剑法的修为了} {
		#VARIABLE {learnflag} {2};
	};
	#ACTION {^{设定环境变量：action \= \"checkhp\"|你设定checkhp为反馈信息}} {
		#VARIABLE {idle} {0};
		#DELAY {1} {
			#IF {$learnflag == 1} {
				#CLASS learnclass KILL;
				fullxtj_start {%1};
			};
			#ELSEIF {$learnflag == 2} {
				#CLASS learnclass KILL;
				drop xuantie jian;
				drop chang jian;
				drop mu jian;
				%1;
			};
			#ELSEIF {$hp[neili] < 1000} {
				#CLASS learnclass KILL;
				startfull {fullxtj_start {%1}} {3};
			};
			#ELSE {
				$learndo;
			};
		};
	};
	#CLASS learnclass CLOSE;
	i;
	echo {checkweapon};
};
#NOP {慕容的斗转星移;%1:后续指令};
#ALIAS {fulldzxy} {
	#IF {@getSkillLevel{douzhuan-xingyi} < 51} {
		gotonpc {慕容复} {fulldzxy_book {%1}};
	};
	#ELSE {
		gotonpc {慕容复} {fulldzxy_askfu {%1}};
	};
};
#NOP {慕容复要书};
#ALIAS {fulldzxy_book} {
	#CLASS learnclass KILL;
	#CLASS learnclass OPEN;
	#ACTION {^{设定环境变量：action \= \"checkbook\"|你设定checkbook为反馈信息}} {
		#VARIABLE {idle} {0};
		dohalt {
			#IF {@carryqty{douzhuan xingyi} == 0} {
				ask murong fu about 领悟;
				i;
				echo {checkbook};
			};
			#ELSE {
				#CLASS learnclass KILL;
				godubook {book} {%1} {} {} {douzhuan-xingyi};
			};
		}
	};
	#CLASS learnclass CLOSE;
	ask murong fu about 领悟;
	i;
	echo {checkbook};
};
#NOP {慕容复直接问，相当于在观星台};
#ALIAS {fulldzxy_askfu} {
	#VARIABLE {okflag} {0};
	#CLASS learnclass KILL;
	#CLASS learnclass OPEN;
	#ACTION {^你听了慕容复的指点，领悟到斗转星移的精髓所在} {
		#VARIABLE {okflag} {1};
	};
	#ACTION {^慕容复%*说道：「你的实战经验不够} {
		#VARIABLE {okflag} {1};
	};
	#ACTION {^你潜能不够} {
		#VARIABLE {okflag} {2};
	};
	#ACTION {^{设定环境变量：action \= \"checktaojiao\"|你设定checktaojiao为反馈信息}} {
		#VARIABLE {idle} {0};
		#DELAY {2} {
			#IF {$okflag == 0} {
				taojiao douzhuan-xingyi;
				skills;
				echo {checktaojiao};
			};
			#ELSEIF {$okflag == 1} {
				#CLASS learnclass KILL;
				#VARIABLE {kungfu[spec][douzhuan-xingyi][ignore]} {true};
				%1;
			};
			#ELSE {
				#CLASS learnclass KILL;
				qu_pot {gotonpc {慕容复} {dzxy_askfu {%1}}};
			};
		};
	};
	#CLASS learnclass CLOSE;
	taojiao douzhuan-xingyi;
	skills;
	echo {checktaojiao};
};
#NOP {领悟独孤九剑，%1:后续操作};
#ALIAS {fulldgjj} {
	gotoroom {3725} {fulldgjj_start {%1}}
};
#ALIAS {fulldgjj_start} {
	#VARIABLE {overflag} {0};
	#CLASS learnclass KILL;
	#CLASS learnclass OPEN;
	#ACTION {^你的基本剑法造诣不够} {
		#VARIABLE {overflag} {2};
	};
	#ACTION {^你现在的潜能不足以领悟独孤九剑} {
		#VARIABLE {overflag} {1};
	};
	#ACTION {^{设定环境变量：action \= \"checkhp\"|你设定checkhp为反馈信息}} {
		#DELAY {0.2} {
			dohalt {
				#IF {$overflag == 2 || @getSkillLevel{dugu-jiujian} >= 300} {
					#CLASS learnclass KILL;
					%1
				};
				#ELSEIF {$overflag == 1} {
					#CLASS learnclass KILL;
					qu_pot {fulldgjj {%1}}
				};
				#ELSEIF {$hp[neili] < 1000} {
					#CLASS learnclass KILL;
					startfull {fulldgjj_start {%1}} {3}
				};
				#ELSE {
					yun jing;
					hp;
					lingwu dugu-jiujian;
					echo {checkhp}
				};
			};
		};
	};
	#CLASS learnclass CLOSE;
	pfm_wuxing;
	echo {checkhp}
};
#NOP {读九阳神功，%1:后续指令你};
#ALIAS {fulljiuyang} {
	gotoroom {3748} {fulljiuyang_qu {%1}}
};
#ALIAS {fulljiuyang_qu} {
	#CLASS learnclass KILL;
	#CLASS learnclass OPEN;
	#ACTION {^你大吃一惊，藏在山壁中的九阳真经已然不见，不知被谁拿走了} {
		#CLASS learnclass KILL;
		#VARIABLE {kungfu[spec][jiuyang-shengong][ignore]} {true};
		#DELAY {1} {
			%1
		};
	};
	#ACTION {^你将泥土挖开，将藏在里面的九阳真经取了出来} {
		#CLASS learnclass KILL;
		#DELAY {1} {
			fulljiuyang_read {%1};
		};
	};
	#CLASS learnclass CLOSE;
	qu jiuyang zhenjing;
};
#ALIAS {fulljiuyang_read} {
	#VARIABLE {readover} {0};
	#CLASS learnclass KILL;
	#CLASS learnclass OPEN;
	
	#ACTION {^由于你的{内功|圣火神功}的火侯不够，不能再获得更高一层的提高} {
		#VARIABLE {readover} {1};
	};
	#ACTION {^你的实战经验不足，再怎么读也没用} {
		#VARIABLE {readover} {1};
	};
	#ACTION {^你内力不够，无法钻研这么高深的武功} {
		#VARIABLE {readover} {2};
	};
	#ACTION {^这最后一册书上的内容过于深奥，光靠研读而不身体力行是无法再获得进步的} {
		#VARIABLE {readover} {2};
	};
	#ACTION {^{设定环境变量：action \= \"checkhp\"|你设定checkhp为反馈信息}} {
		#VARIABLE {idle} {0};
		#DELAY {1} {
			#IF {$readover == 1 || @eval{$kungfu[spec][jiuyang-shengong][lv]} >= 220} {
				#CLASS learnclass KILL;
				cang jiuyang zhenjing;
				%1
			};
			#ELSEIF {$readover == 2 || $hp[jing] < 100 || $hp[neili] < 1000} {
				#CLASS learnclass KILL;
				startfull {fulljiuyang_read {%1}} {3}
			};
			#ELSE {
				execute {
					#10 read jiuyang zhenjing;
					yun jing;
					hp
				};
				echo {checkhp}
			};
		}
	};
	#CLASS learnclass CLOSE;
	echo {checkhp};
};
#NOP {明教的乾坤大挪移;%1:后续指令};
#ALIAS fullqkdny {
	gotonpc {张无忌} {fullqkdny_start {%1}}
};
#ALIAS fullqkdny_start {
	#CLASS learnclass KILL;
	#CLASS learnclass OPEN;
	#ACTION {^你运起九阳神功，原本艰涩难懂的招式在九阳真气的运转下轻而易举的使用了出来} {
	};
	#ACTION {^张无忌说道：「你的乾坤大挪移等级不够，还不能从我这里学到什么} {
		#VARIABLE {kungfu[spec][qiankun-danuoyi][ignore]} {ever};
	};
	#ACTION {^{设定环境变量：action \= \"checkover\"|你设定checkover为反馈信息}} {
		#CLASS learnclass KILL;
		dohalt {%1}
	};
	#CLASS learnclass CLOSE;
	taojiao qiankun-danuoyi;
	skills;
	echo {checkover}
};
#NOP {初始练习金刀黑剑};
#ALIAS {fulljdhj} {
	#IF {@getSkillLevel{sword} < 100} {
		duigancao {sword} {fulljdhj {%1}}
	};
	#ELSEIF {@getSkillLevel{blade} < 100} {
		duigancao {blade} {fulljdhj {%1}}
	};
	#ELSE {
		prepareskills {%1}
	};
};
#NOP {练习无相劫指，%1:后续指令};
#ALIAS {fullwxjz} {
	#IF {@getSkillLevel{wuxiang-zhi} < 200} {
		gotonpc {无相禅师} {fullwxjz_start {%1}}
	};
	#ELSE {
		%1
	};
};
#ALIAS {fullwxjz_start} {
	#CLASS learnclass KILL;
	#CLASS learnclass OPEN;
	#ACTION {^{设定环境变量：action \= \"checkhp\"|你设定checkhp为反馈信息}} {
		#VARIABLE {idle} {0};
		#DELAY {1} {
			#IF {@getSkillLevel{wuxiang-zhi} >= 200} {
				#CLASS learnclass KILL;
				%1;
			};
			#ELSEIF {$hp[neili] < 1000} {
				#CLASS learnclass KILL;
				startfull {fullwxjz_start {%1}} {3}
			};
			#ELSE {
				#10 bo 木屑;
				yun jingli;
				hp;
				echo {checkhp}
			};
		};
	};
	#CLASS learnclass CLOSE;
	echo {checkhp}
};
#NOP {问苗人凤冷泉神功};
#ALIAS {fulllqsg} {
	gotonpc {苗人凤} {fulllqsg_start {%1}}
};
#ALIAS {fulllqsg_start} {
	#CLASS learnclass KILL;
	#CLASS learnclass OPEN;
	#ACTION {^你向苗人凤请教有关冷泉神功的奥妙} {
		#IF {$hp[pot] < 50} {
			#CLASS learnclass KILL;
			dohalt {qu_pot {fulllqsg {%1}}}
		};
		#ELSEIF {$hp[neili] < 100} {
			#CLASS learnclass KILL;
			dohalt {
				s;
				loc {startfull {fulllqsg {%1}}}
			}
		};
		#ELSE {
			dohalt {
				yun jing;
				hp;
				ask miao renfeng about 冷泉神功
			}
		};
	};
	#CLASS learnclass CLOSE;
	hp;
	ask miao renfeng about 冷泉神功
};
#NOP {补交易技能用于钓鱼,必须cp10,不然涨不动,%1:要去买卖的城市,%2:杀兵的房间索引,为空默认为1};
#NOP {流程就是去长安杀10件军服,然后去指定的城市卖掉,再买回来,如果军服没了,回去收集};
#LIST {jufurooms} {create} {997;864;893;932;961;953;943;971};
#ALIAS {fulltrade} {
	#VARIABLE {rooomindex} {1};
	#IF {"%2" != ""} {
		#VARIABLE {rooomindex} {@eval{%2}};
	};
	#IF {$roomindex > &jufurooms[]} {
		#VARIABLE {rooomindex} {1};
	};
	#CLASS learnclass KILL;
	#CLASS learnclass OPEN;
	#ACTION {^{设定环境变量：action \= \"checkjufu\"|你设定checkjufu为反馈信息}} {
		#CLASS learnclass KILL;
		#IF {@carryqty{junfu} < 10} {
			fulltrade_getjunfu {%1} {$rooomindex};
		};
		#ELSE {
			gotodo {%1} {当铺} {fulltrade_sell_start {%1}};
		};
	};
	#CLASS learnclass CLOSE;
	i;
	echo {checkjufu};
};
#NOP {收集军服,%1:买卖的城市,%2:房间索引};
#ALIAS {fulltrade_getjunfu} {
	gotoroom {$jufurooms[+%2]} {fulltrade_getjunfu_start {%1} {%2}};
};
#NOP {杀官兵收集军服,%1:买卖的城市,%2:房间索引};
#ALIAS {fulltrade_getjunfu_start} {
	#CLASS learnclass KILL;
	#CLASS learnclass OPEN;
	#ACTION {^这里没有这个人。} {
		#CLASS learnclass KILL;
		#DELAY {1} {
			fulltrade {%1} {@eval{%2 + 1}};
		};
	};
	#ACTION {^{官兵|值勤兵}「啪」的一声倒在地上，挣扎着抽动了几下就死了} {
		dohalt {
			get junfu from corpse;
			kill bing;
		};
	};
	#CLASS learnclass CLOSE;
	execute {
		unset wimpy;
		#2 get junfu;
		#2 get junfu from corpse;
		#2 get junfu from hai gu;
		kill bing
	};
};
#NOP {开始买卖,%1:传递进来的城市};
#ALIAS {fulltrade_sell_start} {
	#VARIABLE {sellout} {0};
	#CLASS learnclass KILL;
	#CLASS learnclass OPEN;
	#ACTION {^%*说道：「junfu？ 你想买的东西我这里没有。」} {
		#VARIABLE {sellout} {1};
	};
	#ACTION {^{设定环境变量：action \= \"checksell\"|你设定checksell为反馈信息}} {
		#VARIABLE {idle} {0};
		#DELAY {1} {
			#IF {@carryqty{gold} < 10} {
				#CLASS learnclass KILL;
				#SWITCH {"$city"} {
					#CASE {"佛山镇"} {
						gotodo {大理城} {大理钱庄} {balanceex {100} {50} {gotodo {%1} {当铺} {fulltrade_sell_start {%1}}}};
					};
					#CASE {"大理城"} {
						gotodo {大理城} {大理钱庄} {balanceex {100} {50} {gotodo {%1} {当铺} {fulltrade_sell_start {%1}}}};
					};
					#CASE {"扬州城"} {
						gotodo {扬州城} {天阁斋} {balanceex {100} {50} {gotodo {%1} {当铺} {fulltrade_sell_start {%1}}}};
					};
					#CASE {"福州城"} {
						gotodo {福州城} {通宝斋} {balanceex {100} {50} {gotodo {%1} {当铺} {fulltrade_sell_start {%1}}}};
					};
					#CASE {"襄阳城"} {
						gotodo {襄阳城} {宝龙斋} {balanceex {100} {50} {gotodo {%1} {当铺} {fulltrade_sell_start {%1}}}};
					};
					#CASE {"长安城"} {
						gotodo {长安城} {威信钱庄} {balanceex {100} {50} {gotodo {%1} {当铺} {fulltrade_sell_start {%1}}}};
					};
				};
			};
			#ELSEIF {@carryqty{junfu} > 0} {
				sell junfu;
				i;
				echo {checksell};
			};
			#ELSE {
				#VARIABLE {sellout} {0};
				echo {checkbuy};
			};
		};
	};
	#ACTION {^{设定环境变量：action \= \"checkbuy\"|你设定checkbuy为反馈信息}} {
		#VARIABLE {idle} {0};
		#DELAY {1} {
		#IF {@carryqty{junfu} >= 10} {
				#IF {"$caller" != ""} {
					checkrequest {fulltrade {%1}};
				};
				#ELSE {
					echo {checksell};
				};
			};
			#ELSEIF {$sellout == 0} {
				buy junfu;
				i;
				echo {checkbuy};
			};
			#ELSEIF {@carryqty{junfu} < 3} {
				#CLASS learnclass KILL;
				fulltrade {%1};
			};
			#ELSE {
				#IF {"$caller" != ""} {
					checkrequest {fulltrade {%1}};
				};
				#ELSE {
					echo {checksell};
				};
			};
		};
	};
	#CLASS learnclass CLOSE;
	echo {checksell};
};
#NOP {学习五行阵,%1:后续指令};
#ALIAS {fullwxz} {
	gotonpc {温方山} {fullwxz_start {%1}};	
};
#ALIAS {fullwxz_start} {
	#CLASS learnclass KILL;
	#CLASS learnclass OPEN;
	#ACTION {^你听了温方山的指点，对五行阵法的奥妙似乎有些心得} {
		#IF {"$caller" != ""} {
			#CLASS learnclass KILL;
			dohalt {
				checkrequest {fullwxz {%1}};
			};
		};
		#ELSEIF {$hp[neili] < 500} {
			#CLASS learnclass KILL;
			dohalt {
				startfull {fullwxz_start {%1}} {3};
			};
		};
		#ELSE {
			dohalt {
				yun jing;
				hp;
				ask wen fangshan about 五行阵;
			};
		};
	};
	#ACTION {^温方山说道：「今天我已经教你很多了，你还是改天再来吧。」} {
		#CLASS learnclass KILL;
		dohalt {
			qu_pot {fullwxz {%1}};
		};
	};
	#CLASS learnclass CLOSE;
	ask wen fangshan about 五行阵;	
};
#NOP {学习神照经,%1:后续指令};
#ALIAS {fullszj} {
	#IF {$hp[pot] < 2000} {
		qu_pot {gotonpc {丁典} {fullszj_start {%1}}};
	};
	#ELSE {
		gotonpc {丁典} {fullszj_start {%1}};
	};
};
#ALIAS {fullszj_start} {
	#CLASS learnclass KILL;
	#CLASS learnclass OPEN;
	#ACTION {^你听了丁典的指点，对神照经的奥妙似乎有些心得。} {
		dohalt {
			#IF {@getSkillLevel{shenzhao-jing} >= 120} {
				#CLASS learnclass KILL;
				%1;
			};
			#ELSE {
				ask ding dian about 神照经;
			};
		};
	};
	#ACTION {^你听了丁典的指点，可是还是不能理解。} {
		#CLASS learnclass KILL;
		dohalt {
			qu_pot {gotonpc {丁典} {fullszj_start {%1}}};
		};
	};
	#CLASS learnclass CLOSE;
	ask ding dian about 神照经
};
#NOP 读辟邪剑谱;
#ALIAS {getjiasha} {
	#ACTION {^眼看那袈裟从身旁飘过，你伸手一抓，差了数尺，没能抓到} {
		hook jia sha;
	} {5};

	#ACTION {^你右手搭在崖上，左脚拚命向外一勾，只觉脚尖似乎碰到了袈裟，立即缩回，当真幸运得紧，竟将那袈裟勾到了，没落入天声峡下的万仞深渊之中。} 
	{
		dohalt {#unticker {resetidle};move east;jump window;gotodo {华山} {书院} {readjiasha}};
	};

	gotodo {华山} {男休息室} {jump window;move west;listen;#ticker {resetidle}{#variable idle 0;#cr}{1}};
};

#alias fullpxj
{
	#if {@carryqty{zijin jiasha}>0} {readjiasha};
	#else {getjiasha}
};

#alias readjiasha
{
	#VARIABLE {readdo} {#10 read jiasha;yun jing;hp;echo {checkhp}};
	#VARIABLE {readover} {0};
	#CLASS readclass OPEN;
	#ACTION {^你研读了一会儿，但是发现上面所说的对你而言都太浅了，没有学到任何东西。} {
		#VARIABLE {readover} {1};
	};

	#action {^什么?} {#VARIABLE {readover} {2}};

	#ACTION {^你的真气不够。} {
		#VARIABLE {readover} {3}
	};
	#ACTION {^{设定环境变量：action \= \"checkhp\"|你设定checkhp为反馈信息}} {
		#VARIABLE {idle} {0};
		#DELAY {0.5} {
			#IF {$readover == 1} {
				#CLASS readclass KILL;
				#nop drop %2;
				i;
				skills;
				%3
			};

			#ELSEIF {$readover == 2} {
				getjiasha
			};

			#ELSEIF {$readover == 3} {
				w;s;sleep
			};

			#ELSE {$readdo};
		}
	};
	#ACTION {^你一觉醒来} {
		#VARIABLE {readover} {0};
		n;e;
		yun jing;
		dzn
	};
	#ACTION {^{$dazuo_over}} {
		#IF {$hp[neili] > $threshold_neili} {
			pfm_wuxing;
			$readdo
		};
		#ELSE {
			dzn
		};
	};

	#CLASS readclass CLOSE;
	pfm_wuxing;
	$readdo;
};

#NOP 修炼千蛛万毒手;
#ALIAS {qzwds} 
{
	gotodo {星宿海}{星宿海}{uwwp;xiuliando}
};

#ALIAS {xiuliando} 
{
	#class xiuliando kill;
	#class xiuliando open;
	#ACTION {^盒中两只拇指大小的蜘蛛蠕蠕而动} {uwwp;xiulian};
	#ACTION {^再过一会，你鼻尖上渗出细细的一粒粒汗珠} {#delay {5} {look jin he}};
	#ACTION {^盒中蜘蛛已然死去，里面空空如也。} {dohalt {gotodo {星宿海}{星宿海}{zhua zhizhu}}};
	#ACTION {^你盘膝坐下，行了一会儿内功，将双手两根食指伸进盒中，只见盒中的一对花} {#variable idle 0};
	#ACTION {^你满脸庄严肃穆之容} {#variable idle 0} {5};
	#ACTION {^你还没打开盒盖，修炼什么啊？} {open he;look jin he};
	#ACTION {^你刚刚修炼千蛛万毒手完毕，还是先歇息一会吧。} {#variable idle 0;#delay {5} {xiulian}};
	#ACTION {^斓，鲜明夺目的蜘蛛，你心下大喜，急忙将蜘蛛抓入盒内。} {dohalt {qzwds}};
	#ACTION {^但过了良久，草丛中没有任何异状，你失望极了。} {#variable idle 0;#delay {5} {zhua zhizhu}};
	#ACTION {^你的真气不够，恐难抵挡蜘蛛的毒性。} {startfull {xiuliando}};
	#ACTION {^你手持武器，怎么修炼千蛛万毒手啊？} {uwwp;xiulian};
	#class xiuliando close;
	look jin he;
};

#ALIAS {duihuanjingpa_start} {
	#CLASS commonclass OPEN;
	#ACTION {^{设定环境变量：action \= \"checkjingpa\"|你设定checkjingpa为反馈信息}} {
		#CLASS commonclass KILL;
		#DELAY {0.5} {
			#IF {@carryqty{jingpa}>0} {
				dohalt {%1};
			};
			#ELSE {
				duihuan jingpa;
				i;
				echo {checkjingpa};
			};
		};
	};
	#CLASS commonclass CLOSE;
	duihuan jingpa;
	i;
	echo {checkjingpa};
};
#NOP {学习职业技能,%1:职业技能,duanzao或zhizao,%2:后续指令};
#ALIAS {learncraft} {
	#IF {@eval{$kungfu[craft][%1][lv]} < 221} {
		#IF {"%1" == "duanzao"} {
			learnduanzao {%2};
		};
		#ELSEIF {"%1" == "zhizao"} {
			learnzhizao {%2};
		};
		#ELSE {
			#SHOWME {<faa>未支持的职业技能};
			%2
		};
	};
	#ELSE {
		lingwucraft {%1} {%2};
	};
};
#NOP {学习锻造,%1:后续指令};
#ALIAS {learnduanzao} {
	openguigu {gotodo {扬州城} {兵器铺} {learnduanzao_start {%1}}};
};
#ALIAS {learnduanzao_start} {
	#VARIABLE {okflag} {0};
	#CLASS learnclass KILL;
	#CLASS learnclass OPEN;
	#ACTION {^你没有这么多潜能来学习} {
		#VARIABLE {okflag} {1};
	};
	#ACTION {^你现在的学费是} {
		#VARIABLE {okflag} {2};
	};
	#ACTION {^{设定环境变量：action \= \"checkhp\"|你设定checkhp为反馈信息}} {
		#VARIABLE {idle} {0};
		#IF {$okflag == 1} {
			#CLASS learnclass KILL;
			hp;
			qu_pot {learnduanzao {%1}}
		};
		#ELSEIF {$okflag == 2} {
			#CLASS learnclass KILL;
			i;
			dohalt {
				localwithdraw {500} {} {learnduanzao {%1}}
			}
		};
		#ELSEIF {$hp[neili] < 1000} {
			#CLASS learnclass KILL;
			startfull {learnduanzao_start {%1}}
		};
		#ELSE {
			dohalt {
				pfm_wuxing;
				yun jing;
				xue shi duanzao 50;
				hp;
				echo {checkhp}
			};
		};
	};
	#CLASS learnclass CLOSE;
	hp;
	echo {checkhp}
};
#NOP {学习织造,%1:后续指令};
#ALIAS {learnzhizao} {
	openguigu {gotodo {大理城} {裁缝店} {learnzhizao_start {%1}}};
};
#ALIAS {learnzhizao_start} {
	#VARIABLE {okflag} {0};
	#CLASS learnclass KILL;
	#CLASS learnclass OPEN;
	#ACTION {^你没有这么多潜能来学习} {
		#VARIABLE {okflag} {1};
	};
	#ACTION {^你现在的学费是} {
		#VARIABLE {okflag} {2};
	};
	#ACTION {^{设定环境变量：action \= \"checkhp\"|你设定checkhp为反馈信息}} {
		#VARIABLE {idle} {0};
		#IF {$okflag == 1} {
			#CLASS learnclass KILL;
			hp;
			qu_pot {learnzhizao {%1}}
		};
		#ELSEIF {$okflag == 2} {
			#CLASS learnclass KILL;
			i;
			localwithdraw {500} {} {learnzhizao {%1}}
		};
		#ELSEIF {$hp[neili] < 1000} {
			#CLASS learnclass KILL;
			startfull {learnzhizao_start {%1}}
		};
		#ELSE {
			dohalt {
				pfm_wuxing;
				yun jing;
				xue caifeng zhizao 50;
				hp;
				echo {checkhp}
			};
		};
	};
	#CLASS learnclass CLOSE;
	hp;
	echo {checkhp}
};
#NOP {领悟职业技能,%1:职业技能,%2:后续指令};
#ALIAS {lingwucraft} {
	#IF {"%1" == "duanzao" || "%1" == "zhizao"} {
		openguigu {gotonpc {成高道长} {lingwucraft_start {%1} {%2}}};
	};
	#ELSE {
		#SHOWME {<faa>未支持的职业技能};
		%2
	};
};
#ALIAS {lingwucraft_start} {
	#VARIABLE {okflag} {0};
	#CLASS learnclass KILL;
	#CLASS learnclass OPEN;
	#ACTION {^成高道长漫不经心的} {
		#NOP {没潜能};
		#VARIABLE {okflag} {2};
	};
	#ACTION {^成高道长赶紧捂住腰包，对你哀求道} {
		#NOP {没钱};
		#VARIABLE {okflag} {3};
	};
	#ACTION {^成高道长突然叹息道：「你真是一方顽石，顽石不可化也。我看你是很难开窍了} {
		#NOP {需要给qqlly};
		#VARIABLE {okflag} {4};
	};
	#ACTION {^你和成高道长一起交流着} {
		#NOP {正常学习};
		#VARIABLE {okflag} {1};
	};
	#ACTION {^{设定环境变量：action \= \"checkhp\"|你设定checkhp为反馈信息}} {
		#VARIABLE {idle} {0};
		dohalt {
			pfm_wuxing;
			#IF {$okflag == 1} {
				#IF {$hp[neili] < 1000} {
					#CLASS learnclass KILL;
					startfull {lingwucraft_start {%1} {%2}}
				};
				#ELSE {
					yun jing;
					ask chenggao about %1;
					hp;
					echo {checkhp};
				};
			};
			#ELSEIF {$okflag == 2} {
				#CLASS learnclass KILL;
				hp;
				closeguigu {qu_pot {lingwucraft {%1} {%2}}}
			};
			#ELSEIF {$okflag == 3} {
				#CLASS learnclass KILL;
				i;
				closeguigu {localwithdraw {500} {} {lingwucraft {%1} {%2}}}
			};
			#ELSEIF {$okflag == 4} {
				#CLASS learnclass KILL;
				#IF {@carryqty{qiqiaolinglong yu} > 0} {
					lingwu %1 with yu;
					dohalt {lingwucraft_start {%1} {%2}};
				};
				#ELSE {
					#CLASS learnclass KILL;
					qusomething {qiqiaolinglong yu} {lingwucraft {%1} {%2}} {%2};
				};
			};
			#ELSE {
				#CLASS learnclass KILL;
				#NOP {学习完了吧};
				%2
			};
		};
	};
	#CLASS learnclass CLOSE;
	i;
	pfm_wuxing;
	ask chenggao about %1;
	hp;
	echo {checkhp};
};
#ALIAS {fullningxue} {
	#CLASS learnclass KILL;
	#CLASS learnclass OPEN;
	#ACTION {^{设定环境变量：action \= \"checkningxue\"|你设定checkningxue为反馈信息}} {
		#CLASS learnclass KILL;
		#IF {@getSkillLevel{ningxue-shenzhua} < 150} {
			#IF {@carryqty{ningxue shenzhuapu} == 0} {
				getbook_ningxue {%1}
			};
			#ELSE {
				goreadbook {book} {fullningxue {%1}} {} {} {ningxue-shenzhua} {150};
			};
		};
		#ELSEIF {@getSkillLevel{claw} < 101} {
			learnbase {claw} {%1};
		};
		#ELSE {
			%1
		};
	};
	i;
	skills;
	echo {checkningxue}
};
#ALIAS {getbook_ningxue} {
	#LIST {jobroomlist} {create} {76;77;78;79;80;81;82;83;84;85;88;89;90;91;92;93;94;95;96;97;98;99;100;101;102;103;104;108;1853;1854;1855;1856};
	follow none;
	jobnextroom {getbook_ningxue_askchen {%1}} {getbook_ningxue {%1}};
};
#ALIAS {getbook_ningxue_askchen} {
	#CLASS bookclass KILL;
	#CLASS bookclass OPEN;
	#ACTION {^这里没有 chen jinnan} {
		runwait {jobnextroom {getbook_ningxue_askchen {%1}} {getbook_ningxue {%1}}};
	};
	#ACTION {^你决定跟随陈近南一起行动。} {
		dohalt {ask chen jinnan about 凝血神爪};
	};
	#ACTION {^陈近南给了你一本凝血神爪谱。} {
		#CLASS bookclass KILL;
		dohalt {fullningxue {%1}}
	};
	#CLASS bookclass CLOSE;
	follow chen jinnan;
};
#NOP {是否需要读道德经};
#FUNCTION isNeedFullDaodejing {
  #IF {@eval{$kungfu[spec][jiuyin-zhengong][lv]} <= 220} {
    #RETURN {0};
  };
  #IF {"$kungfu[base][force][jifa]" != "jiuyin-zhengong"} {
    #RETURN {0};
  };
  #NOP {如果道德经等级和经验等级<经验等级+10+允许脱节等级};
  #IF {@eval{$hp[max_lv] - $kungfu[know][daode-jing][lv]} <= 10} {
    #RETURN {0};
  };
	#IF {@elapsed{$env[jybookts]} < 600} {
		#RETURN {0};
	};

  #RETURN {1};
};
#NOP {读九阴道德经,%1:后续指令};
#ALIAS {fulldaodejing} {
	getjyfullbook {goreadbook {daode-jing} {%1} {} {} {daode-jing} {$hp[max_lv]}} {%1}
};
#NOP {补九阴全技能,daode-jing->jiuyin-zhengong->xuanyin-jian->dafumo-quan->jiuyin-shenzhang,%1:后续指令};
#ALIAS {fulljiuyin} {
	#VARIABLE {targetskill} {};
	#VARIABLE {targetbook} {};
	#CLASS learnclass KILL;
	#CLASS learnclass OPEN;
	#ACTION {^{设定环境变量：action \= \"checkbook\"|你设定checkbook为反馈信息}} {
		#CLASS learnclass KILL;
		#IF {@getSkillLevel{daode-jing} < 220} {
			#VARIABLE {targetskill} {daode-jing};
			#VARIABLE {targetbook} {九阴真经上卷};
		};
		#ELSEIF {@getSkillLevel{jiuyin-zhengong} < 200} {
			#VARIABLE {targetskill} {jiuyin-zhengong};
			#VARIABLE {targetbook} {九阴真经上卷};
		};
		#ELSEIF {@getSkillLevel{jiuyin-shenfa} < 150} {
			#VARIABLE {targetskill} {jiuyin-shenfa};
			#VARIABLE {targetbook} {九阴真经上卷};
		};
		#ELSEIF {@getSkillLevel{xuanyin-jian} < 150} {
			#VARIABLE {targetskill} {xuanyin-jian};
			#VARIABLE {targetbook} {九阴真经上卷};
		};
		#ELSEIF {@getSkillLevel{jiuyin-zhengong} < 220} {
			#VARIABLE {targetskill} {jiuyin-zhengong};
			#VARIABLE {targetbook} {九阴真经};
		};
		#ELSEIF {@getSkillLevel{jiuyin-shenzhang} < 150} {
			#VARIABLE {targetskill} {jiuyin-shenzhang};
			#VARIABLE {targetbook} {九阴真经};
		};
		#ELSEIF {@getSkillLevel{dafumo-quan} < 150} {
			#VARIABLE {targetskill} {dafumo-quan};
			#VARIABLE {targetbook} {九阴真经};
		};
		#ELSEIF {@getSkillLevel{jiuyin-shenzhua} < 150} {
			#VARIABLE {targetskill} {jiuyin-shenzhua};
			#VARIABLE {targetbook} {九阴真经};
		};
		#ELSEIF {@getSkillLevel{yinlong-bian} < 150} {
			#VARIABLE {targetskill} {yinlong-bian};
			#VARIABLE {targetbook} {九阴真经};
		};
		#IF {"$targetskill" == ""} {
			%1;
		};
		#ELSE {
			#IF {"$id[things][jiuyin zhenjing][name]" == "$targetbook"} {
				goreadbook {$targetskill} {fulljiuyin {%1}}}  {} {} {$targetskill;
			};
			#ELSE {
				#IF {"$id[things][jiuyin zhenjing]" != ""} {
					drop jiuyin zhenjing;
				};
				#IF {"$targetbook" == "九阴真经上卷"} {
					getjyupbook {fulljiuyin {%1}};
				};
				#ELSE {
					getjyfullbook {fulljiuyin {%1}};
				};
			};
		};
	};
	#CLASS learnclass CLOSE;
	i;
	echo {checkbook};
};
#NOP {拿九阴上册,%1:后续指令};
#ALIAS {getjyupbook} {
	gotodo {桃花岛} {岩洞} {getjyupbook_start {%1}}
};
#ALIAS {getjyupbook_start} {
	#VARIABLE {okflag} {0};
	#VARIABLE {checkcount} {0};
	#CLASS learnclass KILL;
	#CLASS learnclass OPEN;
	#ACTION {^你向周伯通打听有关『九阴真经』的消息。} {
		#CLASS learnclass KILL;
		i;
		dohalt {%1}
	};
	#ACTION {^这里没有这个人} {
		#VARIABLE {idle} {0};
		#DELAY {2} {
			ask zhou botong about 九阴真经
		};
	};
	#CLASS learnclass CLOSE;
	ask zhou botong about 九阴真经
};
#NOP {拿九阴下册,%1:拿到的指令,%2:没拿到的指令};
#ALIAS {getjydownbook} {
	gotodo {兰州城} {石洞} {getjydownbook_start {%1}}
};
#ALIAS {getjydownbook_start} {
	#CLASS learnclass KILL;
	#CLASS learnclass OPEN;
	#ACTION {^这里没有这个人。} {
		follow chen xuanfeng
	};
	#ACTION {^这里没有 chen xuanfeng。} {
		#CLASS learnclass OPEN;
		%1
	};
	#ACTION {^你决定跟随陈玄风一起行动} {
    kill chen xuanfeng
  };
	#ACTION {^梅超风「啪」的一声倒在地上，挣扎着抽动了几下就死了} {
    follow none;
		follow chen xuanfeng;
  };
	#ACTION {^陈玄风「啪」的一声倒在地上，挣扎着抽动了几下就死了} {
		#CLASS learnclass KILL;
		i;
		dohalt {loc {%1}}
  };

	#CLASS learnclass CLOSE;
	follow none;
	kill mei chaofeng
};
#NOP {拿九阴全,%1:后续指令,%2:失败指令};
#ALIAS {getjyfullbook} {
	#CLASS bookclass KILL;
	#CLASS bookclass OPEN;
	#ACTION {^{设定环境变量：action \= \"checkdownbook\"|你设定checkdownbook为反馈信息}} {
		#IF {@instr{{$id[things][jiuyin zhenjing][name]}{九阴真经下卷}} == 0} {
			#CLASS bookclass KILL;
			#VARIABLE {env[jybookts]} {@now{}};
			%2
		};
		#ELSE {
			getjyupbook {echo {checkupbook}}
		};
	};
	#ACTION {^{设定环境变量：action \= \"checkupbook\"|你设定checkupbook为反馈信息}} {
		#CLASS bookclass KILL;
		#IF {@instr{{$id[things][jiuyin zhenjing][name]}{九阴真经上卷}} == 0} {
			#VARIABLE {env[jybookts]} {@now{}};
			%2
		};
		#ELSE {
			he;
			i;
			%1
		};
	};
	#CLASS bookclass CLOSE;
	#IF {"$id[things][jiuyin zhenjing][name]" == "九阴真经"} {
		#CLASS bookclass KILL;
		%1
	};
	#ELSEIF {@instr{{$id[things][jiuyin zhenjing][name]}{九阴真经下卷}} == 0} {
		getjydownbook {echo {checkdownbook}}
	};
	#ELSEIF {@instr{{$id[things][jiuyin zhenjing][name]}{九阴真经上卷}} == 0} {
		getjyupbook {echo {checkupbook}}
	};
	#ELSE {
		#CLASS bookclass KILL;
		he;
		i;
		%1
	};
};
#NOP {学习古墓九阴，%1:后续指令，这里先检查基础技能默认修习鞭法和爪法};
#ALIAS {fullgmjy} {
	#IF {@getSkillLevel{whip} < 101} {
		duigancao {whip} {fullgmjy {%1}}
	};
	#ELSEIF {@getSkillLevel{claw} < 101} {
		learnbase {claw} {fullgmjy {%1}};
	};
	#ELSE {
		gotoroom {3542} {fullgmjy_start {%1}}
	};
};
#ALIAS {fullgmjy_start} {
	#VARIABLE {okflag} {0};
	#VARIABLE {yandulist} {
		{daode-jing} {60}
		{jiuyin-zhengong} {201}
		{jiuyin-shenfa} {10}
		{jiuyin-shenzhua} {140}
		{yinlong-bian} {10}
	};
	#VARIABLE {jyskill} {*yandulist[+1]};
	#CLASS learnclass KILL;
	#CLASS learnclass OPEN;
	#ACTION {^你的潜能已经用完了，再怎么读也没用} {
		#VARIABLE {okflag} {1};
	};
	#ACTION {^你的内力不够，无法领会这个技能} {
		#VARIABLE {okflag} {2};
	};
	#ACTION {^你{觉得这洞壁上所刻|觉得洞壁所刻}} {
		#VARIABLE {okflag} {3};
	};
	#ACTION {^{设定环境变量：action \= \"checkhp\"|你设定checkhp为反馈信息}} {
		#VARIABLE {idle} {0};
		#DELAY {1} {
			#IF {$okflag == 1} {
				#CLASS learnclass KILL;
				qu_pot {fullgmjy {%1}}
			};
			#ELSEIF {$okflag == 2} {
				startfull {fullgmjy_start {%1}} {3}
			};
			#ELSEIF {$okflag == 3 || @getSkillLevel{$jyskill} >= $yandulist[$jyskill]} {
				#UNVARIABLE {yandulist[$jyskill]};
				#IF {"$yandulist" == ""} {
					prepareskills {{%1}}
				};
				#ELSE {
					#VARIABLE {jyskill} {*yandulist[+1]};
					#10 yandu $jyskill;
					yun jing;
					hp;
					echo {checkhp}
				};
			};
			#ELSE {
				#10 yandu $jyskill;
				yun jing;
				hp;
				echo {checkhp}
			};
		};
	};
	#CLASS learnclass CLOSE;
	#10 yandu $jyskill;
	yun jing;
	hp;
	echo {checkhp}
};
#NOP {百牛山呼吸};
#ALIAS {huxiforce} {
	setlogindo {huxiforce};
	gotodo {昆仑山} {白牛山} {huxiforce_start}
};
#ALIAS {huxiforce_start} {
	#NOP {门派内功基本是要求200才能练};
	#VARIABLE {hxng} {$kungfu[base][force][jifa]};
	#VARIABLE {kungfu[spec][$hxng][lv]} {0};
	#VARIABLE {wxcmd} {
		yun jing;
		yun qi;
		huxi
	};
	#IF {"$common[wuxingforce][$kungfu[base][force][jifa]]" != ""} {
		#VARIABLE {wxcmd} {
			yun jing;
			yun qi;
			yun $common[wuxingforce][$kungfu[base][force][jifa]];
			huxi
		};
	};
	#CLASS learnclass kill;
	#CLASS learnclass open;
	#ACTION {^你的内功已经有一定基础了，在这里呼吸已经没什么作用了} {
		skills;
		echo {checkforce}
	};
	#ACTION {^{设定环境变量：action \= \"checkforce\"|你设定checkforce为反馈信息}} {
		fangqi force @eval{$kungfu[base][force][lv] - 100};
		#SEND {y};
		dohalt {wo stone};
	};
	#ACTION {^你深深吸了几口气，只觉得寒气冲进五脏六腑，体内的真气几乎提不起来} {
		dohalt {wo stone};
	};
	#ACTION {^你已经躺在青石上了} {
		dohalt {
			execute {$wxcmd};
		};
	};
	#ACTION {^你侧身躺于青石上，意守丹田，口眼轻闭，双腿自然微曲，全身放松} {
		dohalt {
			execute {$wxcmd};
		};
	};
	#ACTION {^寒风凛冽，你刚运过功，身子正十分虚弱，先好好休息一下吧} {
		#DELAY {1} {
			execute {$wxcmd};
		};
	};
	#ACTION {^你集聚体内真气，深深吸进几口气，缓缓呼出，只觉得全身透彻清凉，心定似水，仿佛已物我浑然一体} {
		#VARIABLE {idle} {0};
		hp;
		#IF {@getSkillLevel{$hxng} >= 200} {
			#CLASS learnclass kill;
			saveequipment {golian {@@}};
		};
		#IF {$hp[neili] < 1000} {
			#IF {@carryqty{dahuan dan} > 0 || @carryqty{da huandan} > 0} {
				fu dahuan dan;
				i;
				hp;
				#DELAY {2} {
					execute {$wxcmd};
				};
			};
			#ELSE {
				#CLASS learnclass kill;
				tbbuy {dahuan dan} {1} {huxiforce};
			};
		};
		#ELSE {
			#DELAY {2} {
				execute {$wxcmd};
			};
		};
	};
	#CLASS learnclass close;
	wo stone;
};
#NOP {洗澡};
#ALIAS {fullbeauty} {
	gotoroom {1372} {fullbeauty_start {%1}}
};
#ALIAS {fullbeauty_start} {
	#VARIABLE {okflag} {0};
	#VARIABLE {outdo} {};
	#CLASS learnclass KILL;
	#CLASS learnclass CLOSE;
	#ACTION {^突然一阵清风吹来，你哆嗦了一下，连忙爬出洗象池} {
		hp;
		#DELAY {2} {
			#IF {$hp[neili] < 500} {
				#CLASS learnclass KILL;
				#VARIABLE {outdo} {startfull {fullbeauty_start {%1}}};
			};
			#ELSE {
				#LOCAL {fts} {@getAvailableFestival{}};
				#IF {$fts > 0} {
					#CLASS learnclass KILL;
					loc {getgift {$common[festivals][$fts]} {fullbeauty {%1}}}
				};
				#ELSE {
					#VARIABLE {okflag} {0};
					yun jing;
					yun qi;
					yun jingli;
					xi pool;
					echo {checkxi}
				};
			};
		}
	};
	#ACTION {^你扑通一声跳进了池水} {
		#VARIABLE {okflag} {1};
	};
	#ACTION {^{设定环境变量：action \= \"checkxi\"|你设定checkxi为反馈信息}} {
		#VARIABLE {idle} {0};
		#IF {$okflag == 0} {
			#DELAY {1} {
				yun jing;
				yun qi;
				yun jingli;
				xi pool;
				hp;
				echo {checkxi}
			};
		};
		#ELSE {
			#VARIABLE {idle} {-120};
		};
	};
	#ACTION {^{设定环境变量：action \= \"checkout\"|你设定checkout为反馈信息}} {
		#VARIABLE {idle} {0};
		#IF {&roomexits[] == 1} {
			#DELAY {1} {
				s;
				look;
				echo {checkout}
			}
		};
		#ELSE {
			#CLASS learnclass KILL;
			$outdo
		};
	};
	#CLASS learnclass CLOSE;
	drop all;
	xi pool;
	echo {checkxi}
};
#CLASS kungfumodule CLOSE;
#SHOWME {<fac>@padRight{{技能}{12}}<fac> <cfa>模块加载完毕<cfa>};