#NOP {昆仑拜师指南};
#NOP {各级别师傅的拜师限制和需要关注的技能};
#VARIABLE {guide[昆仑派][masters]} {
  {sequence} {
    {1} {高则成}
    {2} {西华子}
    {3} {何太冲}
    {4} {何足道}
  }
  {高则成} {
    {id} {gao zecheng}
    {conditions} {
      {gift} {
        {str} {20}
        {con} {20}
        {dex} {10}
        {int} {30}
      }
    }
    {favourites} {
      {1} {xuantian-wuji}
      {2} {force}
    }
    {beforedo} {}
  }
  {西华子} {
    {id} {xi huazi}
    {conditions} {
      {skills} {
        {xuantian-wuji} {80}
        {force} {85}
      }
    }
    {favourites} {
      {1} {xuantian-wuji}
      {2} {force}
    }
    {beforedo} {}
  }
  {何太冲} {
    {id} {he taichong}
    {conditions} {
      {skills} {
        {xuantian-wuji} {100}
      }
    }
    {favourites} {
      {1} {force}
      {2} {xuantian-wuji}
      {3} {dodge}
      {4} {taxue-wuhen}
      {5} {sword}
      {6} {liangyi-jian}
      {7} {cuff}
      {8} {zhentian-quan}
      {9} {parry}
    }
    {beforedo} {}
  }
  {何足道} {
    {id} {he zudao}
    {conditions} {
      {skills} {
        {xuantian-wuji} {130}
        {zhentian-quan} {80}
      }
    }
    {favourites} {
      {1} {force}
      {2} {xuantian-wuji}
      {3} {dodge}
      {4} {taxue-wuhen}
      {5} {sword}
      {6} {xunlei-jian}
      {7} {strike}
      {8} {kunlun-zhang}
      {9} {parry}
      {10} {art}
    }
    {beforedo} {guide_fullart {#}}
  }
};
#NOP {按照指定技能的各个等级(>=0)决定任务使用的pfm及其要做的任务};
#VARIABLE {guide[昆仑派][pfms]} {
  {100} {
    {pfmskill} {liangyi-jian}
    {jobs} {华山;送信}
    {weapon} {
      {primary} {lanyu duzhen};
      {secondary} {yinshe sword}
    }
    {wuxing} {}
    {normalbuff} {}
    {fightbuff} {}
    {busy} {}
    {attack} {
      jiali max;
      perform liangyi-jian.feihua;
      jiali 10;
    }
  }
  {130} {
    {pfmskill} {xunlei-jian}
    {jobs} {武当;送信2}
    {weapon} {
      {primary} {yinshe sword};
      {secondary} {lanyu duzhen}
    }
    {wuxing} {}
    {normalbuff} {}
    {fightbuff} {}
    {busy} {}
    {attack} {
      jiali max;
      perform xunlei-jian.podi;
      jiali 10
    }
  }
  {200} {
    {pfmskill} {xunlei-jian}
    {jobs} {武当;送信2}
    {weapon} {
      {primary} {yinshe sword};
      {secondary} {lanyu duzhen}
    }
    {wuxing} {}
    {normalbuff} {}
    {fightbuff} {yun wuji}
    {busy} {}
    {attack} {
      jiali max;
      perform xunlei-jian.podi;
      jiali 10
    }
  }
};
#NOP {门派武器武功需要的武器};
#VARIABLE {guide[昆仑派][weapons]} {
  {liangyi-jian} {sword}
  {xunlei-jian} {sword}
};
#NOP {补琴棋书画,%1:后续指令};
#ALIAS {guide_fullart} {
  gotodo {昆仑山} {苦寒楼二层} {guide_fullart_duanmo {%1}}  
};
#NOP {获取琴棋,%1:后续指令};
#ALIAS {guide_fullart_duanmo} {
  #VARIABLE {artitem} {};
  #CLASS guideclass OPEN;
  #ACTION {^你得到一张古筝} {
    #VARIABLE {artitem} {qin};
  };
  #ACTION {^你得到一副围棋} {
    #VARIABLE {artitem} {qi};
  };
  #ACTION {^设定环境变量：action \= \"checkduanmo\"} {
    #VARIABLE {idle} {0};
    dohalt {
      #IF {"$artitem" != ""} {
        #CLASS guideclass KILL;
        gotodo {昆仑山} {花园} {guide_fullart_start {$artitem} {%1}}
      };
      #ELSE {
        duanmo wall;
        set action checkduanmo
      };
    };
  };
  #CLASS guideclass CLOSE;
  duanmo wall;
  set action checkduanmo
};
#NOP {补art,%1:物品,%2:后续指令};
#ALIAS {guide_fullart_start} {
  #VARIABLE {tempdo} {
    #10 play %1;
    yun qi;yun jing;
    hp;
    set action checkhp
  };
  #CLASS guideclass OPEN;
  #ACTION {^你的「琴棋书画」进步了} {
    skills
  };
  #ACTION {^你摆出一张{棋盘|古筝}} {
  
  };
  #ACTION {^你一觉醒来} {
    s;#3 w;dzn 
  };
  #ACTION {^{$dazuo_over}} {
    #IF {$hp[neili] > $threshold_neili} {
      $tempdo
    };
    #ELSE {
      dzn
    };
  };
  #ACTION {^设定环境变量：action \= \"checkhp\"} {
    #VARIABLE {idle} {0};
    #DELAY {1} {
      #IF {@getSkillLevel{art} >= 120} {
        #CLASS guideclass KILL;
        drop %1;
        gotonpc {何足道} {%2}
      };
      #ELSEIF {$hp[neili] < 100} {
        #3 e;n;sleep
      };
      #ELSE {
        $tempdo
      };
    }
  };
  #CLASS guideclass CLOSE;
  $tempdo
};
