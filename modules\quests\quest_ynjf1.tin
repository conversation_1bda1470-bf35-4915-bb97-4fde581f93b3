#NOP {越女剑法,%1:后续指令};
#ALIAS {goquest_ynjf1} {
  #VARIABLE {questmodule} {越女剑法一};
  gotonpc {韩小莹} {ynjf_askhan {%1}}
};
#ALIAS {ynjf_askhan} {
  #VARIABLE {questmodule} {越女剑法一};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你向韩小莹打听有关『越女剑』的消息。} {
    #CLASS questresponseclass OPEN;
    #ACTION {^韩小莹说道：「你如此容貌，恐怕难以学会越女剑法。」} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      #IF {$hp[tontbao] < 1000} {
        questfail {$questmodule};
        dohalt {%1}
      };
      #ELSE {
        dohalt {
          fullper {goquest_ynjf {%1}};
        };
      };
      
    };
    #ACTION {^韩小莹说道：「{你还是去增长武学经验吧。|你武学经验太差了，恐怕难以学会越女剑法。}} {
      #NOP {最低经验不够};
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questdelay {$questmodule} {} {7200};
      dohalt {
        %1
      };
    };
    #ACTION {^看起来韩小莹在%*秒之内不准备理你。} {
      #NOP {时间不够};
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questdelay {$questmodule} {0} {7200};
      dohalt {
        %1
      };
    };
  };
  #ACTION {^韩小莹说道：「你已经学会越女剑了，去找我家前辈学习吧！」} {
    #CLASS questclass KILL;
    questsuccess {$questmodule};
    dohalt {%1};
  };
  #ACTION {^你百思不得其解，耗尽精神，皱了皱眉头，看来只能下次再来学习了！} {
    #CLASS questclass KILL;
    questfail {$questmodule};
    dohalt {%1};
  };
  #ACTION {^机缘巧合下，你终于学得了越女剑法的精髓，切记行走江湖，多行义事！} {
    #CLASS questclass KILL;
    questsuccess {$questmodule};
    #SEND {shout 哈哈哈，我开了$questmodule，羡慕嫉妒恨吧！！！};
    dohalt {%1};
  };
  #CLASS questclass CLOSE;
  pray pearl;
  ask han xiaoying about 越女剑
};