#NOP {七窍玲珑任务模块};
#NOP {队伍检索到的未定城市的房间按照如下城市进行顺序访问};
#LIST {common[citypriority]} {create} {华山;襄阳城;大理城;成都城;扬州城;武当山};
#NOP {寻找玩家需要忽略的房间名称};
#LIST {common[cylignorerooms]} {create} {碎石路;松树林;菜地;大松林;西湖边;山路};
#NOP {此站一些基本确定的房间};
#VARIABLE {common[definitelyrooms]} {
  {前厅} {2260;634}
  {杂货铺} {579;442;882}
  {马房} {1183}
  {驿站} {1184}
  {封禅台} {2715}
};
#NOP {ls区域与自身区域映射};
#VARIABLE {common[lscity]} {
  {大理城} {大理城}
  {大理城东} {大理城}
  {大理城南} {大理城}
  {大理王府} {大理城}
  {大理皇宫} {大理城}
  {大理城西} {大理城}
  {柳宗镇} {襄阳城}
  {大草原} {回疆}
  {伊犁城} {星宿海}
  {襄阳郊外} {襄阳城}
};
#NOP {用户无法回复时特定区域指定若干房间进行搜索};
#VARIABLE {common[qqllcity]} {
  {武当山} {2398;2417;2418;2418}
  {华山} {2260;2268;2267}
  {大理城} {1106;1183;1182;1184}
  {襄阳城} {579;580;616}
  {长乐帮} {521;522;523;531;524} 
  {扬州城} {473;442;431;405}
  {大雪山} {1788;1789;1794}
  {嵩山} {2714;2715}
};
#ALIAS {jobgo_qqll} {
  checkrequest {
    checkteam {
      gotodo {终南山} {日月岩} {
        startfull {
          gotodo {终南山} {金莲阁} {jobmuster_qqll};
        };
      };
    };
  };
};
#NOP {队伍集合接收任务};
#ALIAS {jobmuster_qqll} {
  #IF {$conf[team][leader] == 1} {
    teammuster_leader {$conf[team][partner]} {jobask_qqll};
  };
  #ELSE {
    teammuster_member {$conf[team][partner]} {uwwp};
  };
};
#ALIAS {setass} {
  #VARIABLE {jobnpc_assist_id} {%1};
  #VARIABLE {jobnpc_assist_name} {%2};
  #VARIABLE {startts} {@now{}};
};
#ALIAS {jobask_qqll} {
  #VARIABLE {startts} {0};
  #VARIABLE {askresult} {0};
  #VARIABLE {jobnpc_assist_id} {};
  #VARIABLE {jobnpc_assist_name} {};
  #VARIABLE {jobnpc_assist_location} {};
  #CLASS jobrequestclass KILL;
  #CLASS jobrequestclass OPEN;
  #ACTION {^你向马钰打听有关『七眼石』的消息。} {
    #CLASS jobresponseclass KILL;
    #CLASS jobresponseclass OPEN;
    #ACTION {^马钰说道：「我一直在寻找能够帮我解开先师深意之能工巧匠。」} {
      #CLASS jobresponseclass KILL;
      dohalt {
        cond;
        ask ma yu about job;
      }
    };
    #CLASS jobresponseclass CLOSE;
  };
  #ACTION {^你向马钰打听有关『job』的消息。} {
    #VARIABLE {idle} {0};
    #CLASS jobresponseclass KILL;
    #CLASS jobresponseclass OPEN;
    #ACTION {^马钰说道：「前路茫茫} {
      #CLASS jobresponseclass KILL;
      dohalt {
        ask ma yu about job;
      }
    };
    #ACTION {^马钰说道：「咦？怎么好象人不全啊} {
      #CLASS jobresponseclass KILL;
      #CLASS jobresponseclass OPEN;
      dohalt {jobmuster_qqll};
    };
    #ACTION {^马钰说道：「你不是已经接了任务了么} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {jobfangqi_qqll};
    };
    #ACTION {^马钰说道：「你们现在这么忙} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      teamorder_standby;
      dohalt {
        su;
        startfull {nd;jobask_qqll} {1}
      }
    };
    #ACTION {^马钰说道：「现在没有事情需要麻烦你们} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {
        teamtodo {终南山} {日月岩} {jobcheck};
        jobcheck;
      };
    };
    #ACTION {^马钰在你的耳边悄声说道：此人神龙见首不见尾，上次%*(%*)曾在%*见过} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      #VARIABLE {startts} {@now{}};
      #VARIABLE {jobnpc_assist_id} {@lower{%%%2}};
      #VARIABLE {jobnpc_assist_name} {%%%1};
      #VARIABLE {jobnpc_assist_location} {%%%3};
      #VARIABLE {startts} {@now{}};
      #LIST {jobroomlist} {clear} {};
      dohalt {jobtrace_qqll};
    };
    #CLASS jobresponseclass CLOSE;
  };
  #CLASS jobrequestclass CLOSE;
  cond;
  ask ma yu about 七眼石;
};
#NOP {获取真正的房间名称};
#FUNCTION getExactRoom {
  #LOCAL {tempstr} {%1};
  #REPLACE {tempstr} {,} {;};
  #LIST {segments} {create} {$tempstr};
  #RETURN {$segments[+1]};
};
#NOP {获取城市所在区块};
#FUNCTION getCityLand {
  #IF {@contains{{common[mainland]}{%1}} > 0} {
    #RETURN {mainland};
  };
  #ELSEIF {@contains{{common[westland]}{%1}} > 0} {
    #RETURN {westland};
  };
  #ELSEIF {@contains{{common[southland]}{%1}} > 0} {
    #RETURN {southland};
  };
  #ELSEIF {@contains{{common[northland]}{%1}} > 0} {
    #RETURN {northland};
  };
  #ELSE {
    #RETURN {otherland};
  };
};
#NOP {追踪玩家,%1-是否瞎蒙模式};
#ALIAS {jobtrace_qqll} {
  #VARIABLE {okflag} {0};
  #VARIABLE {responsed} {0};
  #VARIABLE {checkcount} {0};
  #VARIABLE {temptracecity} {};
  #VARIABLE {temptraceroom} {};
  #VARIABLE {traceroom} {};
  #VARIABLE {tracecity} {};
  #VARIABLE {traceroom} {};
  #VARIABLE {traceroomlist} {};
  #CLASS jobdoclass KILL;
  #CLASS jobdoclass OPEN;
  #NOP {粽众(Sanjue)回答你：我在【大理城-道旁田野】，任务：【sx1】，目的地：【玉虚观-道旁田野】};
  #ACTION {^%*(%*)回答你：当前位于:%* %*} {
    #NOP {只响应无需过河的主大陆};
    #VARIABLE {responsed} {1};
    #VARIABLE {temptracecity} {%%3};
    #VARIABLE {temptraceroom} {@getExactRoom{%%4}};
    #SHOWME {<faa>city = $temptracecity,room = $temptraceroom};
    #NOP {高山脚下不去，一般只是路过抓不到};
    #IF {"$jobnpc_assist_id" == "@lower{%%2}" && "$temptraceroom" != "高山脚下" && ("$temptracecity" != "$tracecity" || "$temptraceroom" != "$traceroom") && @contains{{common[cylignorerooms]}{$temptraceroom}} == 0} {
      #VARIABLE {tracecity} {$temptracecity};
      #VARIABLE {traceroom} {$temptraceroom};
      #SHOWME {<ffa>tracecity = $tracecity,traceroom = $traceroom};
      jobparse_qqll;
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkresponse\"|你设定checkresponse为反馈信息}} {
    teamorder_standby;
    #VARIABLE {echots} {0};
    #VARIABLE {idle} {0};
    #MATH {checkcount} {$checkcount + 1};
    #IF {@elapsed{$startts} > 180} {
      #NOP {超时放弃};
      #CLASS jobdoclass KILL;
      jobfangqi_qqll;
    };
    #ELSEIF {$responsed == 1} {
      #NOP {更新目的地};
      #IF {$okflag == 1 && &traceroomlist[] > 0} {
        #CLASS jobdoclass KILL;
        #VARIABLE {jobroomlist} {$traceroomlist};
        jobsort_qqll;
      };
      #ELSE {
        #VARIABLE {checkcount} {0};
        #VARIABLE {responsed} {0};
        #VARIABLE {okflag} {0};
        tell $jobnpc_assist_id where are you;
        #DELAY {4} {
          echo {checkresponse};
        };
      };
    };
    #ELSEIF {$checkcount >= 5 && $responsed == 0} {
      #NOP {持续查询无响应,放弃任务};
      #CLASS jobdoclass KILL;
      #IF {"$common[qqllcity][$jobnpc_assist_location]" != ""} {
        #SHOWME {<afa>定点区域 $jobnpc_assist_location 访问};
        #LIST {jobroomlist} {create} {$common[qqllcity][$jobnpc_assist_location]};
        jobfindassist_qqll_once
      };
      #ELSE {
        #SHOWME {<faa>玩家无响应，放弃任务};
        jobfangqi_qqll;
      };
    };
    #ELSE {
      #MATH {checkcount} {$checkcount + 1};
      #DELAY {2} {
        echo {checkresponse};
      };
    };
  };
  #CLASS jobdoclass CLOSE;
  #NOP {每隔四秒请求一次最新的位置};
  #IF {"%1" != ""} {
    #SHOWME {<faa>没有蒙到};
    jobfangqi_qqll
  };
  #ELSE {
    #SHOWME {<faa>开始追踪};
    tell $jobnpc_assist_id where are you;
    loc {
      echo {checkresponse};
    };
  };
};
#NOP {解析玩家地址};
#ALIAS {jobparse_qqll} {
  #VARIABLE {hasexactroom} {0};
  #VARIABLE {traceroomlist} {};
  #VARIABLE {exactroomlist} {};
  #NOP {部分特定房间特殊处理};
  #IF {"$traceroom" == "山路"} {
    #NOP {某集团等待送信杀手地};
    #LIST {traceroomlist} {add} {1196};
  };
  #ELSEIF {"$common[definitelyrooms][$traceroom]" != ""} {
    #SHOWME {<afa>已知确定的房间};
    #LIST {traceroomlist} {create} {$common[definitelyrooms][$traceroom]};
  };
  #ELSE {
    #VARIABLE {temptraceroomlist} {@findRooms{{}{$traceroom}}};
    #FOREACH {$temptraceroomlist[]} {r} {
      #NOP {受限房间不去};
      #IF {"@getRoomInfo{{$r}{ROOMDATA}}" != ""} {
        #CONTINUE;
      };
      #LOCAL {targetcity} {@getRoomInfo{{$r}{ROOMAREA}}};
      #IF {@contains{{common[mainland]}{$targetcity}} == 0} {
        #SHOWME {<faa>$r:不在主大陆};
        #CONTINUE;
      };
      #NOP {某些常见的房间与特定城市的组合排除};
      #IF {"$traceroom" == "前厅" && "$targetcity" == "终南山"} {
        #SHOWME {<faa>$r:排除常见地址};
        #CONTINUE;
      };
      #NOP {需要过河和迷宫的房间不去};
      #LOCAL {temproompath} {@getWalkPath{{$roomid}{$r}}};
      #IF {(@instr{{$temproompath}{river_}} == 1 || @instr{{$temproompath}{matrix_}} == 1) && @instr{{$temproompath}{river_baoxiang}} == 0} {
        #SHOWME {<faa>$r:排除需过河房间};
        #CONTINUE;
      };
      #SHOWME {<aff>"@getRoomInfo{{$r}{ROOMAREA}}" == "$tracecity"};
      #IF {"@getRoomInfo{{$r}{ROOMAREA}}" == "$tracecity"} {
        #SHOWME {<afa>$r:全匹配房间};
        #VARIABLE {hasexactroom} {1};
        #LIST {exactroomlist} {add} {$r};
        #CONTINUE;
      };
      #LIST {traceroomlist} {add} {$r};
    };
  };
  #NOP {如果有全匹配的则，则取前两个};
  #VARIABLE {okflag} {1};
  #IF {$hasexactroom == 1} {
    #IF {&exactroomlist[] == 1 && &traceroomlist[] > 0} {
      #LIST {exactroomlist} {add} {$traceroomlist[+1]};
    };
    #VARIABLE {traceroomlist} {$exactroomlist};
  };
  #ELSEIF {&traceroomlist[] == 0 || &traceroomlist[] > 3} {
    #VARIABLE {okflag} {0};
  };
  printvar traceroomlist;
  #SHOWME {<ffa>追踪房间数:&traceroomlist[],okflag = $okflag};
};
#NOP {对玩家可能存在房间进行排序};
#ALIAS {jobsort_qqll} {
  #VARIABLE {tempsortlist} {};
  #VARIABLE {temproomlist} {};
  #FOREACH {$jobroomlist[]} {r} {
    #LOCAL {roomcity} {@getRoomInfo{{$r}{ROOMAREA}}};
    #LOCAL {sortindex} {@contains{{common[citypriority]}{$roomcity}}};
    #IF {$sortindex == 0} {
      #LOCAL {sortindex} {255};
    };
    #VARIABLE {temproomlist[$r]} {$sortindex};
    #VARIABLE {tempsortlist[$sortindex]} {$r};
  };
  #LIST {jobroomlist} {clear};
  #FOREACH {*tempsortlist[]} {d} {
    #FOREACH {*temproomlist[]} {r} {
      #IF {$temproomlist[$r] == $d} {
        #LIST {jobroomlist} {add} {$r}
      };
    };
  };
  jobfindassist_qqll;
};
#NOP {组队去找协助的玩家};
#ALIAS {jobfindassist_qqll} {
  #CLASS jobdoclass KILL;
  #NOP {跟随时无法渡过汉水,武当山与其他城市需要重新集合};
  #LOCAL {targetcity} {@getRoomInfo{{$jobroomlist[+1]}{ROOMAREA}}};
  #IF {("$city" == "武当山" && "$targetcity" != "武当山") || ("$city" != "武当山" && "$targetcity" == "武当山") || @contains{{common[mainland]}{$targetcity}} == 0} {
    teammuster {$targetcity} {$cities[$targetcity][cityroomid]} {jobfindassist_qqll};
  };
  #ELSE {
    jobnextroom {jobinquiry_qqll} {jobtrace_qqll};
  };
};
#NOP {组队去找协助的玩家，仅尝试一次};
#ALIAS {jobfindassist_qqll_once} {
  #CLASS jobdoclass KILL;
  #NOP {跟随时无法渡过汉水,武当山与其他城市需要重新集合};
  #LOCAL {targetcity} {@getRoomInfo{{$jobroomlist[+1]}{ROOMAREA}}};
  #IF {("$city" == "武当山" && "$targetcity" != "武当山") || ("$city" != "武当山" && "$targetcity" == "武当山") || @contains{{common[mainland]}{$targetcity}} == 0} {
    teammuster {$targetcity} {$cities[$targetcity][cityroomid]} {jobfindassist_qqll_once};
  };
  #ELSE {
    jobnextroom {jobinquiry_qqll {1}} {jobfangqi_qqll};
  };
};
#NOP {询问玩家，%1-找不到就放弃};
#ALIAS {jobinquiry_qqll} {
  #VARIABLE {askresult} {0};
  #VARIABLE {checkcount} {0};
  #VARIABLE {joblocation} {};
  #CLASS jobcheckclass KILL;
  #CLASS jobcheckclass OPEN;
  #ACTION {^你要对谁做这个动作？} {
    #CLASS jobcheckclass KILL;
    teamorder_standby;
    #IF {"%1" == ""} {
      runwait {jobnextroom {jobinquiry_qqll} {jobtrace_qqll}};
    };
    #ELSE {
      runwait {jobnextroom {jobinquiry_qqll} {jobfangqi_qqll}};
    };
  };
  #ACTION {^你双手抱拳，对$jobnpc_assist_name作了个揖道} {
    #CLASS jobdoclass KILL;
    #VARIABLE {askresult} {0};
    ask $jobnpc_assist_id about 淳于蓝;
    echo {checkask};
  };
  #ACTION {^这里没有这个人。} {
    #NOP {问的人跑了,重新找};
    #VARIABLE {askresult} {255};
  };
  #ACTION {^你的搭档去哪里了} {
    #VARIABLE {askresult} {254};
  };
  #ACTION {^你向$jobnpc_assist_name打听有关『淳于蓝』的消息。} {
    #CLASS jobresponseclass KILL;
    #CLASS jobresponseclass OPEN;
    #MATH {checkcount} {$checkcount + 1};
    #ACTION {^$jobnpc_assist_name在你的耳边悄声说道：不过我没看清楚他去哪里了} {
      #VARIABLE {askresult} {2};
    };
    #ACTION {^$jobnpc_assist_name在你的耳边悄声说道：不过他现在已经去%*了} {
      #VARIABLE {askresult} {1};
      #VARIABLE {joblocation} {$jobnpc_assist_location%%%1};
    };
    #CLASS jobresponseclass CLOSE;
  };
  #ACTION {^{设定环境变量：action \= \"checkask\"|你设定checkask为反馈信息}} {
    #VARIABLE {echots} {0};
    #VARIABLE {idle} {0};
    #CLASS jobresponseclass KILL;
    dohalt {
      #SWITCH {$askresult} {
        #CASE {1} {
          #CLASS jobdoclass KILL;
          #CLASS jobcheckclass KILL;
          bye $jobnpc_assist_id;
          #DELAY {1} {jobdo_qqll}
        };
        #CASE {3} {
          #CLASS jobdoclass KILL;
          #CLASS jobcheckclass KILL;
          #DELAY {1} {jobfangqi_qqll}
        };
        #CASE {254} {
          #CLASS jobdoclass KILL;
          #CLASS jobcheckclass KILL;
          #NOP {重新集合};
          teammuster {$city} {$roomid} {jobinquiry_qqll};
        };
        #CASE {255} {
          #CLASS jobdoclass KILL;
          #CLASS jobcheckclass KILL;
          #NOP {重新追踪};
          #DELAY {1} {jobtrace_qqll}
        };
        #DEFAULT {
          #DELAY {0.5} {
            #VARIABLE {askresult} {0};
            ask $jobnpc_assist_id about 淳于蓝;
            echo {checkask};
          }
        };
      };
    };
  };
  #CLASS jobcheckclass CLOSE;
  time;
  hi $jobnpc_assist_id;
};
#ALIAS {jobdo_qqll} {
  #VARIABLE {killercount} {0};
  #VARIABLE {jobnpc_contact} {0};
  #LIST {wanderpath} {clear};
  #CLASS jobdoclass KILL;
  #CLASS jobdoclass OPEN;
  #ACTION {^你觉得行囊中的顽石越来越沉，令你举步维艰} {
    #VARIABLE {jobnpc_contact} {1};
    stopwalk;
    jobfight_qqll
  };
  #CLASS jobdoclass CLOSE;
  #NOP {去城市房间杀};
  wwp;
  pfm_buff_normal;
  createpfm {@getFightPerform{}} {1};
  gotodo {$city} {$cities[$city][cityroomid]} {jobfight_qqll};
};
#NOP {左右行走触发杀手};
#ALIAS {jobfight_qqll} {
  #VARIABLE {jobnpc_gaoshou} {none};
  #VARIABLE {jobnpc_gaoshou_id} {};
  #VARIABLE {checkcount} {0};
  #VARIABLE {pathindex} {1};
  #CLASS jobfightclass KILL;
  #CLASS jobfightclass OPEN;
  #ACTION {^一个蒙面杀手从路边跳了出来} {
    #CLASS jobkillercapclass KILL;
    #CLASS jobkillercapclass OPEN;
    #ACTION {^%*决定跟随你一起行动} {
      #CLASS jobkillercapclass KILL;
      #VARIABLE {jobnpc_gaoshou} {%%%1};
    };
    #CLASS jobkillercapclass CLOSE;
  };
  #ACTION {$jobnpc_gaoshou(%*)} {
    #IF {"$jobnpc_gaoshou" != ""} {
      #VARIABLE {jobnpc_gaoshou_id} {@lower{%%1}};
    };
  };
  #ACTION {^这里不准战斗} {
    #NOP {无法战斗的房间,行走一步,将本次的rollingroom排除,重新初始化};
    #CLASS jobfightclass KILL;
    #VARIABLE {exclueroom} {$roomid};
    #IF {$pathindex > 1} {#VARIABLE {exclueroom} {$rollingroom};};
    #MATH {pathindex} {$pathindex + 1};
    loc {
      jobfight_qqll {$exclueroom};
    };
  };
  #ACTION {^$jobnpc_gaoshou转身几个起落就不见了} {
    #VARIABLE {checkcount} {0};
    #VARIABLE {jobnpc_contact} {0};
    #VARIABLE {jobnpc_gaoshou} {none};
    #VARIABLE {jobnpc_gaoshou_id} {};
    #MATH {killercount} {$killercount + 1};
    teamorder_standby;
    teamorder_stopfight;
    stopfight;
    #VARIABLE {idle} {-120};
    dohalt {
      get gold;
      yun jingli;
      yun jing;
      yun qi;
      forceheal {echo {checkover}};
    }
  };
  #ACTION {^{设定环境变量：action \= \"checkover\"|你设定checkover为反馈信息}} {
    #IF {"$roomid" != "$cities[$city][cityroomid]"} {
      #CLASS jobfightclass KILL;
      loc {gotodo {$city} {$cities[$city][cityroomid]} {jobfight_qqll}}
    };
    #ELSE {
      echo {checkwander}
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkwander\"|你设定checkwander为反馈信息}} {
    resonate {checkwander};
    #VARIABLE {idle} {0};
    #MATH {checkcount} {$checkcount + 1};
    #VARIABLE {echots} {0};
    #IF {"$jobnpc_gaoshou" != "none"} {
      ensure {look} {checkkiller};
    };
    #ELSEIF {$checkcount > 180} {
      #CLASS jobfightclass KILL;
      #SHOWME {<faa>巡逻次数：$checkcount，已处理杀手：$killercount};
      jobfangqi_qqll;
    };
    #ELSEIF {$killercount >= 5 || ($checkcount > 120 && $killercount >= 3)} {
      #CLASS jobfightclass KILL;
      loc {parsejoblocation {$joblocation} {jobgotocyl_qqll} {jobfangqi_qqll}};
    };
    #ELSE {
      #IF {&wanderpath[] == 0} {
        jobsetwander_qqll
      };
      #IF {@eval{$checkcount % 20} == 0} {
        teamorder_standby;
      };
      $wanderpath[+$pathindex];
      #MATH {pathindex} {$pathindex + 1};
      #IF {$pathindex > 2} {#VARIABLE {pathindex} {1};};
      #DELAY {0.2} {
        echo {checkwander} {2};
      };
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkkiller\"|你设定checkkiller为反馈信息}} {
    resonate {checkkiller};
    #VARIABLE {echots} {0};
    #VARIABLE {idle} {0};
    #MATH {checkcount} {$checkcount + 1};
    #IF {"$jobnpc_gaoshou_id" != ""} {
      teamorder_startfight {$jobnpc_gaoshou_id};
    };
    #ELSEIF {"$roomid" != "$cities[$city][cityroomid]"} {
      #CLASS jobfightclass KILL;
      loc {gotodo {$city} {$cities[$city][cityroomid]} {jobfight_qqll {1}}}
    };
  };
  #CLASS jobfightclass CLOSE;
  echo {checkwander} {2}
};
#NOP {初始化巡逻路径};
#ALIAS {jobsetwander_qqll} {
  #LOCAL {pathto} {};
  #LIST {wanderpath} {clear} {};
  #VARIABLE {rollingroom} {@getSafeExit{{$roomid}{%1}}};
  #IF {$rollingroom == -1 || $rollingroom == $roomid} {
    #LOCAL {pathto} {$roomexits[+@rnd{{1}{&roomexits[]}}]};
  };
  #ELSE {
    #LOCAL {pathto} {@getWalkPath{{$roomid}{$rollingroom}}};
  };
  #SHOWME {$pathto};
  #LIST {wanderpath} {add} {$pathto};
  #LIST {wanderpath} {add} {@reverseDir{$pathto}};
};
#ALIAS {jobgotocyl_qqll} {
  #IF {&jobroomlist[] == 0} {
    jobfangqi_qqll
  };
  #ELSE {
    #LOCAL {targetcity} {@getRoomInfo{{$jobroomlist[+1]}{ROOMAREA}}};
    teammuster {$targetcity} {$cities[$targetcity][cityroomid]} {jobfindcyl_qqll} {uwwp};
  };
};
#NOP {组队去找淳于蓝};
#ALIAS {jobfindcyl_qqll} {
  teamorder_standby;
  #LOCAL {targetcity} {@getRoomInfo{{$jobroomlist[+1]}{ROOMAREA}}};
  #NOP {这里需要注意，寻找淳于蓝时需确认下一个目标房间是否需要过迷宫和河流};
  #LOCAL {targetpath} {@getWalkPath{{$roomid}{$jobroomlist[+1]}}};
  #IF {@instr{{$targetpath}{river_}} == 0 && @instr{{$targetpath}{matrix_}} == 0} {
    jobnextroom {checkcyl};
  };
  #ELSE {
    teammuster {$targetcity} {$jobroomlist[+1]} {jobnextroom {checkcyl}} {uwwp};
  };
};
#ALIAS {checkcyl} {
  #VARIABLE {askresult} {0};
  #VARIABLE {checkcount} {0};
  #VARIABLE {tryagin} {0};
  #CLASS jobcheckclass KILL;
  #CLASS jobcheckclass OPEN;
  #ACTION {^你要对谁做这个动作？} {
    #IF {$tryagin == 0} {
      #VARIABLE {tryagin} {1};
      #LOCAL {saferoomid} {@getSafeExit{$roomid}};
      #LOCAL {safepath} {@getWalkPath{{$roomid}{$saferoomid}}};
      $safepath;
      @reverseDir{$safepath};
      hi chunyu lan
    };
    #ELSE {
      #CLASS jobcheckclass KILL;
      jobfindcyl_qqll;
    };
  };
  #ACTION {^你双手抱拳，对淳于蓝作了个揖道：这位壮士请了！} {
    #VARIABLE {askresult} {0};
    dohalt {
      ask chunyu lan about 七眼石;
    }
  };
  #ACTION {^你的搭档去哪里了} {
    #VARIABLE {askresult} {254};
  };
  #ACTION {^你向淳于蓝打听有关『七眼石』的消息。} {
    #IF {$askresult == 254} {
      #CLASS jobcheckclass KILL;
      teammuster {$city} {$roomid} {checkcyl};
    };
    #ELSE {
      dohalt {
        give qiyan shi to chunyu lan;
        echo {checkresult};
      }
    };
  };
  #ACTION {^淳于蓝给你一块} {
    #VARIABLE {askresult} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkresult\"|你设定checkresult为反馈信息}} {
    #VARIABLE {echots} {0};
    #VARIABLE {idle} {0};
    #IF {$askresult == 0 && $checkcount < 10} {
      #MATH {checkcount} {$checkcount + 1};
      #DELAY {2} {
        echo {checkresult};
      };
    };
    #ELSE {
      #CLASS jobcheckclass KILL;
      #NOP {各自回去,队长完成任务};
      dohalt {
        #IF {@contains{{common[mainland]}{$city}} > 0} {
          jobfinish_qqll;
        };
        #ELSE {
          teammuster {终南山} {金莲阁} {jobfinish_qqll_ask}
        };
      };
    };
  };
  #CLASS jobcheckclass CLOSE;
  hi chunyu lan;
};
#ALIAS {jobfinish_qqll} {
  #CLASS jobkillercapclass KILL;
  #CLASS jobfightclass KILL;
  #CLASS jobcheckclass KILL;
  #CLASS jobdoclass KILL;
  gotodo {终南山} {金莲阁} {jobfinish_qqll_ask};
};
#ALIAS {jobfinish_qqll_ask} {
  #VARIABLE {askresult} {0};
  #CLASS jobrequestclass KILL;
  #CLASS jobrequestclass OPEN;
  #ACTION {^你的搭档去哪里了} {
    #VARIABLE {askresult} {254};
    #CLASS jobresponseclass KILL;
    #CLASS jobrequestclass KILL;
    dohalt {teammuster {终南山} {金莲阁} {jobfinish_qqll_ask}};
  };
  #ACTION {^你向马钰打听有关『finish』的消息} {
    #CLASS jobresponseclass KILL;
    #CLASS jobresponseclass OPEN;
    #ACTION {^你还没领任务呢，怎么完成} {
      #VARIABLE {askresult} {2};
      dohalt {
        teamorder_todo {终南山} {金莲阁} {jobprepare};
        jobprepare;
      };
    };
    #ACTION {^你还没有完成任务呢} {
      #CLASS jobresponseclass KILL;
      #CLASS jobresponseclass OPEN;
      dohalt {jobfangqi_qqll};
    };
    #ACTION {^任务完成，你被奖励了} {
      #VARIABLE {askresult} {1};
      #VARIABLE {lastjob} {武当};
      #CLASS rewardclass KILL;
      #CLASS rewardclass OPEN;
      #ACTION {^%*点实战经验} {
        #VARIABLE {jobreward_exp} {%%%%1};
      };
      #ACTION {^%*点潜能} {
        #CLASS rewardclass KILL;
        #CLASS jobresponseclass KILL;
        #CLASS jobrequestclass KILL;
        #VARIABLE {jobreward_pot} {%%%%1};
        joblog {成功完成，获得$jobreward_exp点经验$jobreward_pot点潜能，耗时@elapsed{$jobstart_ts}秒。};
        jobclear_qqll;
        dohalt {
          teamorder_todo {终南山} {金莲阁} {jobprepare};
          jobprepare;
        };
      };
      #CLASS rewardclass CLOSE;
    };
    #CLASS jobresponseclass CLOSE; 
  };
  #CLASS jobrequestclass CLOSE;
  ask ma yu about finish;
};
#NOP {放弃任务};
#ALIAS {jobfangqi_qqll} {
  #CLASS jobkillercapclass KILL;
  #CLASS jobfightclass KILL;
  #CLASS jobcheckclass KILL;
  #CLASS jobdoclass KILL;
  follow none;
  teamorder_todo {终南山} {日月岩} {look};
  #DELAY {1} {
    gotodo {终南山} {金莲阁} {jobfangqi_qqll_ask};
  };
};
#ALIAS {jobfangqi_qqll_ask} {
  #CLASS jobrequestclass KILL;
  #CLASS jobrequestclass OPEN;
  #ACTION {^你向马钰打听有关『fangqi』的消息。} {
    #CLASS jobresponseclass KILL;
    #CLASS jobresponseclass OPEN;
    #ACTION {^马钰说道：「放弃？没领任务你放弃什么} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      follow none;
      dohalt {
        teamorder_todo {终南山} {日月岩} {jobcheck};
        jobcheck;
      };
    };
    #ACTION {^马钰说道：「找你帮忙看来是我的错。} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      #NOP {放弃后有三分钟的公共busy,只能等};
      follow none;
      dohalt {
        teamorder_todo {终南山} {日月岩} {jobcheck};
        jobcheck;
      };
    };
    #CLASS jobresponseclass CLOSE;
  };
  #CLASS jobrequestclass CLOSE;
  ask ma yu about fangqi;
};
#ALIAS {jobclear_qqll} {
  #VARIABLE {startts} {0};
  #VARIABLE {jobnpc_assist_id} {};
  #VARIABLE {jobnpc_assist_name} {};
  #VARIABLE {jobnpc_assist_location} {};
  #VARIABLE {joblocation} {};
  #LIST {jobroomlist} {clear};
  #VARIABLE {jobfight_ts} {0};
  #VARIABLE {jobnpc_gaoshou} {};
  #VARIABLE {jobnpc_gaoshou_id} {};
  #VARIABLE {killercount} {0};
  #VARIABLE {jobstart_ts} {0};
};