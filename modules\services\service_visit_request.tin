#NOP {遍历访问模块};
#NOP {呼叫访问请求,%1:要访问的房间,%2:后续指令,%3:呼叫失败的指令};
#ALIAS {visit_call} {
  visit_call_start {%1} {%2} {%3}
};
#NOP {遍历访问模块,%1:要访问的房间,%2:后续指令};
#ALIAS {visit_call_start} {
  #CLASS serviceclass KILL;
  #CLASS serviceclass OPEN;
  #ACTION {^没有这个人。} {
    #CLASS serviceclass KILL;
    #SHOWME {<faa>保姆$conf[nanny][visit]人不在};
    #IF {"%3" == ""} {
      %2
    };
    #ELSE {
      %3
    };
  };
  #ACTION {%*(%*)告诉你：visit_wait} {
    #CLASS serviceclass KILL;
    #VARIABLE {idle} {0};
    #IF {"%3" == ""} {
      %2
    };
    #ELSE {
      %3
    };
  };
  #ACTION {%*(%*)告诉你：visit_come} {
    %2;
  };
  #CLASS serviceclass CLOSE;
  #IF {"$conf[nanny][visitor]" == ""} {
    #SHOWME {<faa>未配置visitor保姆,请自行处理};
    %3
  };
  #ELSE {
    #SEND {tell $conf[nanny][visitor] visit_request_%1}
  };
};
#NOP {取消开门,%1:后续指令};
#ALIAS {visit_cancel} {
  #IF {"$conf[nanny][visitor]" != ""} {
    tell $conf[nanny][visitor] visit_cancel
  };
};