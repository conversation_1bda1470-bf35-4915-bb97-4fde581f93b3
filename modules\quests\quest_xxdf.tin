#NOP {吸星大法,%1:后续指令};
#ALIAS {goquest_xxdf} {
  #VARIABLE {questmodule} {吸星大法};
  xxdf_checkitem {%1};
};
#ALIAS {xxdf_checkitem} {
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkitem\"|你设定checkitem为反馈信息}} {
    #CLASS questclass KILL;
    #IF {@carryqty{wuyue lingqi} == 0} {
      xxdf_lingqi {xxdf_checkitem {%1}}
    };
    #ELSEIF {"$env[meizhuang]" == "YES"} {
      #NOP {解过，可以直接去};
      xxdf_visit {%1} {1}
    };
    #ELSEIF {@carryqty{shuaiyi tie} == 0} {
      xxdf_syt {xxdf_checkitem {%1}}
    };
    #ELSEIF {@carryqty{guangling san} == 0} {
      xxdf_gls {xxdf_checkitem {%1}}
    };
    #ELSEIF {@carryqty{ouxue pu} == 0} {
      xxdf_oxp {xxdf_checkitem {%1}}
    };
    #ELSEIF {@carryqty{xinglv tu} == 0} {
      xxdf_xlt {xxdf_checkitem {%1}}
    };
    #ELSE {
      xxdf_visit {%1}
    };
  };
  #CLASS questclass CLOSE;
  i;
  echo {checkitem};
};
#NOP {拿五岳令旗};
#ALIAS {xxdf_lingqi} {
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你向左冷禅打听有关『cancel』的消息} {
    dohalt {
      ask zuo lengchan about job;
      i;
      echo {checklingqi}
    }
  };
  #ACTION {^{设定环境变量：action \= \"checklingqi\"|你设定checklingqi为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {@carryqty{wuyue lingqi} == 0} {
      #NOP {如果拿不到，那应该是出问题了};
      dohalt {doquit};
    };
    #ELSE {
      #CLASS questclass KILL;
      dohalt {%1}
    };
  };
  #CLASS questclass CLOSE;
  gotonpc {左冷禅} {ask zuo lengchan about cancel}
};
#NOP {拿率意贴};
#ALIAS {xxdf_syt} {
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^这里没有这个人} {
    #VARIABLE {idle} {0};
    #DELAY {6} {
      kill deng bagong
    };
  };
  #ACTION {^邓八公「啪」的一声倒在地上，挣扎着抽动了几下就死了} {
    dohalt {
      get shuaiyi tie from corpse;
      i;
      echo {checkshuaiyitie};
    }
  };
  #ACTION {^{设定环境变量：action \= \"checkshuaiyitie\"|你设定checkshuaiyitie为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {@carryqty{shuaiyi tie} == 0} {
      #DELAY {4} {
        kill deng bagong
      };
    };
    #ELSE {
      #CLASS questclass KILL;
      dohalt {%1}
    };
  };
  #CLASS questclass CLOSE;
  gotonpc {邓八公} {echo {checkshuaiyitie}}
};
#NOP {拿广陵散};
#ALIAS {xxdf_gls} {
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checktieqiao\"|你设定checktieqiao为反馈信息}} {
    #IF {@carryqty{tie qiao} == 0} {
      #DELAY {2} {
        buy tie qiao;
        i;
        echo {checktieqiao}
      };
    };
    #ELSE {
      gotodo {襄阳城} {蔡邕墓} {
        wa mu;
        i;
        echo {checkguanglingsan}
      }
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkguanglingsan\"|你设定checkguanglingsan为反馈信息}} {
    #CLASS questclass KILL;
    #IF {@carryqty{guangling san} == 0} {
      #DELAY {2} {
        xxdf_gls {%1};
      };
    };
    #ELSE {
      dohalt {%1}
    };
  };
  #CLASS questclass CLOSE;
  gotodo {峨嵋山} {草棚} {echo {checktieqiao}}
};
#NOP {拿呕血谱};
#ALIAS {xxdf_oxp} {
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkoxp\"|你设定checkoxp为反馈信息}} {
    #VARIABLE {idle} {0};
    #CLASS questclass KILL;
    #IF {"$id[things][ouxue pu]" != ""} {
      #DELAY {1} {
        xxdf_xlt {%1}
      };
    };
    #ELSE {
      #DELAY {6} {
        look shelf;
        i;
        echo {checkoxp}
      };
    };
  };
  #CLASS questclass CLOSE;
  gotoroom {1039} {
    look shelf;
    i;
    echo {checkoxp}
  };
};
#NOP {拿溪山行旅图};
#ALIAS {xxdf_xlt} {
  #VARIABLE {okflag} {0};
  #VARIABLE {matrixdo} {
    execute {
      move wan;
      #3 zhuan tiewan zuo;
      #3 zhuan tiewan right;
    };
    echo {checkdest}
  };
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你向顾炎武打听有关『上官剑南』的消息。} {
    dohalt {
      gotodo {牛家村} {密室} {open xiang;open jiaceng;echo {checkxlt}};
    };
  };
  #ACTION {^你随手打开一轴画卷，怎么可能？竟然是据说失传已久的北宋范宽的真迹“溪山行旅图”！} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkxlt\"|你设定checkxlt为反馈信息}} {
    #IF {$okflag == 0} {
      #DELAY {0.2} {
        fan painting;
        echo {checkxlt}
      };
    };
    #ELSE {
      #CLASS questclass KILL;
      xxdf_visit {%1}
    };
  };
  #CLASS questclass CLOSE;
  gotonpc {顾炎武} {ask gu yanwu about 上官剑南};
};
#NOP {访问梅庄，%1:后续指令，%2:是否已经进来过};
#ALIAS {xxdf_visit} {
  #VARIABLE {okflag} {0};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你给黄钟公一本广陵散} {
    #VARIABLE {env[xxdf]} {};
    unset env_xxdf;
    #DELAY {1} {gotonpc {黑白子} {give zi ouxue pu}};
  };
  #ACTION {^你给黑白子一本呕血谱} {
    #DELAY {1} {gotonpc {秃笔翁} {give weng shuaiyi tie}};
  };
  #ACTION {^你给秃笔翁一本率意帖} {
    #DELAY {1} {gotonpc {丹青生} {give sheng xinglv tu}};
  };
  #ACTION {^你给丹青生一本溪山行旅图} {
    ask sheng about 秘密;
  };
  #ACTION {^你向丹青生打听有关『秘密』的消息。} {
    dohalt {gotodo {梅庄} {酒室} {zuan table}}
  };
  #ACTION {^你没事瞎钻什么桌子} {
    #CLASS questclass KILL;
    questfail {$questmodule};
    unset env_xxdf;
    unset env_meizhuang;
    #VARIABLE {env[xxdf]} {};
    #VARIABLE {env[meizhuang]} {};
    doquit
  };
  #ACTION {^你费尽九牛二虎之力，发现自己到了一个很神秘的地道里} {
    stopfight;
    #DELAY {1} {
      loc {
        gotoroom {3836} {ask ren woxing about 比剑};
      };
    };
  };
  #ACTION {^突然任我行发出一声巨吼，震的耳朵一阵阵发麻，一个不注意} {
    #VARIABLE {idle} {-60};
    #LINE ONESHOT #ACTION {^一股暖流发自丹田流向全身，慢慢地你又恢复了知觉} {
      pray pearl;
      yun jing;
      yun qi;
      yun jingli;
      hp;
      dohalt {
        move man;
        echo {checkxxdf};
      };
    } {1}
  };
  #ACTION {^你成功将铁笼中的文字拓下一本吸星大法拓本} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkxxdf\"|你设定checkxxdf为反馈信息}} {
    #CLASS questclass KILL;
    unset env_xxdf;
    #VARIABLE {env[xxdf]} {};
    #IF {$okflag == 0} {
      questfail {$questmodule};
      loc {doquit}
    };
    #ELSE {
      questsuccess {$questmodule};
      executecmd {xxdf_full {%1}};
    };
  };
  #CLASS questclass CLOSE;
  #IF {"%2" == ""} {
    gotonpc {黄钟公} {give gong guangling san}
  };
  #ELSE {
    gotonpc {丹青生} {ask sheng about 秘密}
  };
};
#ALIAS {xxdf_full} {
  #VARIABLE {overflag} {0};
  #VARIABLE {readdo} {
		#VARIABLE {reading} {0};
		execute {#10 read miji;yun jing;hp};
		echo {checkhp}
	};
  #CLASS readclass KILL;
	#CLASS readclass OPEN;
  #ACTION {^你已经不能从这本书里得到任何指点了} {
    #VARIABLE {overflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkhp\"|你设定checkhp为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {$overflag == 1} {
      #CLASS readclass KILL;
      #DELAY {1} {
        push door;
        prepareskills {loc {%1}};
      };
    };
    #ELSEIF {$hp[neili] < 500} {
      #CLASS readclass KILL;
      startfull {xxdf_full {%1}} {3};
    };
    #ELSE {
      #DELAY {0.8} {$readdo};
    };
  };
  #CLASS readclass CLOSE;
  echo {checkhp};
};