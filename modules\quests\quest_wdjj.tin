#NOP {武当剑诀,%1:后续指令};
#ALIAS {goquest_wdjj} {
  #VARIABLE {questmodule} {武当剑诀};
  #IF {@carryqty{yao chu} == 0} {
    getyaochu {goquest_wdjj {%1}}
  };
  #ELSEIF {@carryqty{mao tan} == 0} {
    getmaotan {goquest_wdjj {%1}}
  };
  #ELSE {
    gotonpc {采药道长} {
      give yao chu to caiyao daozhang;
      #DELAY {1} {loc {gotodo {武当山} {石壁岩洞} {wdjj_lingwu {%1}}}}
    };
  };
};
#ALIAS {wdjj_lingwu} {
  #VARIABLE {okflag} {0};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你静心研习石壁上的小子，大有感触，对「太极」的领会又更深入了一个层次} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^石壁顶部的几句太极口诀依稀可见} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^你近来已经看了好几遍了，早已熟记于心} {
    #VARIABLE {okflag} {2};
  };
  #ACTION {^{设定环境变量：action \= \"checklingwu\"|你设定checklingwu为反馈信息}} {
    #IF {$okflag == 1} {
      questsuccess {$questmodule}
    };
    #ELSE {
      questfail {$questmodule}
    };
    doquit
  };
  #CLASS questclass CLOSE;
  pray pearl;
  look wall;
  read wall;
  echo {checklingwu}
};