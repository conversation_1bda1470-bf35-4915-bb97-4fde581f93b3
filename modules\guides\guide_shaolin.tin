#NOP {少林派拜师指南};
#NOP {各级别师傅的拜师限制和需要关注的技能};
#VARIABLE {guide[少林派][masters]} {
  {sequence} {
    {1} {清法比丘}
    {2} {清为比丘}
    {3} {道正禅师}
    {4} {慧修尊者}
    {5} {澄知}
    {6} {玄苦大师}
    {7} {玄慈大师}
    {8} {渡难}
    {9} {渡厄}
    {10} {无名老僧}
  }
  {清法比丘} {
    {id} {qingfa biqiu}
    {conditions} {
      {gift} {
        {str} {20}
        {con} {20}
        {dex} {10}
        {int} {30}
      }
    }
    {favourites} {
      {1} {buddhism}
      {2} {force}
      {3} {yijin-jing}
      {4} {dodge}
      {5} {shaolin-shenfa}
      {6} {cuff}
      {7} {luohan-quan}
      {8} {parry}
    }
    {afterdo} {guide_sl_tidu {#}}
  }
  {清为比丘} {
    {id} {qingwei biqiu}
    {conditions} {
      {skills} {
        {luohan-quan} {50}
        {parry} {30}
      }
    }
    {favourites} {
      {1} {buddhism}
      {2} {force}
      {3} {yijin-jing}
      {4} {dodge}
      {5} {shaolin-shenfa}
      {6} {cuff}
      {7} {luohan-quan}
      {8} {parry}
    }
  }
  {道正禅师} {
    {id} {daozheng chanshi}
    {conditions} {
      {skills} {
        {luohan-quan} {50}
      }
    }
    {favourites} {
      {1} {buddhism}
      {2} {force}
      {3} {yijin-jing}
      {4} {dodge}
      {5} {shaolin-shenfa}
      {6} {cuff}
      {7} {luohan-quan}
      {8} {parry}
    }
    {beforedo} {goliterate {122} {guide_sl_fight {#}}}
  }
  {慧修尊者} {
    {id} {huixiu zunzhe}
    {conditions} {
      {skills} {
        {luohan-quan} {55}
      }
    }
    {favourites} {
      {1} {buddhism}
      {2} {force}
      {3} {yijin-jing}
      {4} {dodge}
      {5} {shaolin-shenfa}
      {6} {cuff}
      {7} {jingang-quan}
      {8} {parry}
    }
    {beforedo} {guide_sl_fight {#}}
  }
  {澄知} {
    {id} {chengzhi luohan}
    {conditions} {
      {skills} {
        {jingang-quan} {75}
      }
    }
    {favourites} {
      {1} {buddhism}
      {2} {force}
      {3} {yijin-jing}
      {4} {dodge}
      {5} {shaolin-shenfa}
      {6} {cuff}
      {7} {jingang-quan}
      {8} {parry}
    }
    {beforedo} {guide_sl_fight {#}}
  }
  {玄苦大师} {
    {id} {xuanku dashi}
    {conditions} {
      {skills} {
        {jingang-quan} {100}
      }
    }
    {favourites} {
      {1} {buddhism}
      {2} {force}
      {3} {yijin-jing}
      {4} {dodge}
      {5} {shaolin-shenfa}
      {6} {parry}
    }
    {beforedo} {guide_sl_lhz {#}}
  }
  {玄慈大师} {
    {id} {xuanci dashi}
    {conditions} {
      {skills} {
        {parry} {120}
      }
    }
    {favourites} {
      {1} {buddhism}
      {2} {force}
      {3} {yijin-jing}
      {4} {dodge}
      {5} {shaolin-shenfa}
      {6} {finger}
      {7} {nianhua-zhi}
      {8} {strike}
      {9} {sanhua-zhang}
      {10} {claw}
      {11} {longzhua-gong}
      {12} {parry}
    }
  }
  {渡难} {
    {id} {du nan}
    {conditions} {
      {skills} {
        {parry} {160}
      }
    }
    {favourites} {
      {1} {buddhism}
      {2} {force}
      {3} {yijin-jing}
      {4} {dodge}
      {5} {shaolin-shenfa}
      {6} {parry}
    }
    {beforedo} {guide_sl_fmq {#}}
  }
  {渡厄} {
    {id} {du e}
    {conditions} {
      {skills} {
        {longzhua-gong} {180}
      }
    }
    {favourites} {
      {1} {buddhism}
      {2} {force}
      {3} {yijin-jing}
      {4} {dodge}
      {5} {shaolin-shenfa}
      {6} {leg}
      {7} {ruying-suixingtui}
      {8} {hand}
      {9} {qianye-shou}
      {10} {parry}
    }
  }
  {无名老僧} {
    {id} {wuming laoseng}
    {conditions} {
      {gift} {
        {str} {20}
        {con} {20}
        {dex} {10}
        {int} {30}
      }
      {skills} {
        {qianye-shou} {180}
      }
    }
    {favourites} {
      {1} {buddhism}
      {2} {force}
      {3} {yijin-jing}
      {4} {dodge}
      {5} {shaolin-shenfa}
      {6} {strike}
      {7} {sanhua-zhang}
      {8} {finger}
      {9} {nianhua-zhi}
      {10} {leg}
      {11} {ruying-suixingtui}
      {12} {hand}
      {13} {qianye-shou}
      {14} {claw}
      {15} {longzhua-gong}
      {16} {yizhi-chan}
      {17} {cuff}
      {18} {jingang-quan}
      {19} {banruo-zhang}
      {20} {parry}
    }
    {beforedo} {guide_sl_wm}
  }
};
#NOP {按照指定技能的各个等级(>=0)决定任务使用的pfm及其要做的任务};
#VARIABLE {guide[少林派][pfms]} {
  {110} {
    {pfmskill} {jingang-quan}
    {jobs} {送信;华山}
    {beforedo} {
      bei none;
      jifa cuff jingang-quan;
      jifa parry jingang-quan;
      bei cuff
    }
    {fightbuff} {}
    {attack} {
      jiali max;
      perform jingang-quan.fumo;
    }
  }
  {120} {
    {pfmskill} {sanhua-zhang}
    {jobs} {武当;送信2}
    {beforedo} {
      bei none;
      jifa strike sanhua-zhang;
      jifa finger nianhua-zhi;
      bei strike;
      bei finger
    }
    {fightbuff} {}
    {busy} {}
    {attack} {
      jiali max;
      perform sanhua-zhang.sanhua;
      jiali 10;
    }
  }
  {200} {
    {pfmskill} {sanhua-zhang}
    {jobs} {武当;送信2}
    {beforedo} {
      bei none;
      jifa strike sanhua-zhang;
      jifa finger nianhua-zhi;
      bei strike;
      bei finger
    }
    {normalbuff} {yun jingang}
    {fightbuff} {}
    {busy} {}
    {attack} {
      jiali max;
      perform sanhua-zhang.sanhua;
      jiali 10;
      yun jingang;
    }
  }
  {300} {
    {pfmskill} {ruying-suixingtui}
    {jobs} {武当;送信2}
    {beforedo} {
      bei none;
      jifa leg ruying-suixingtui;
      jifa hand qianye-shou;
      jifa parry ruying-suixingtui;
      bei leg;
      bei hand
    }
    {normalbuff} {yun jingang}
    {fightbuff} {}
    {busy} {}
    {attack} {
      jiali max;
      perform ruying-suixingtui.ruying;
      jiali 10;
    }
  }
};
#NOP {剃度};
#ALIAS {guide_sl_tidu} {
  #CLASS guideclass KILL;
  #CLASS guideclass OPEN;
  #ACTION {^你向清法比丘打听有关『剃度』的消息} {
    dohalt {
      kneel;
      guide_sl_mrx {%1};
    };
  };
  #CLASS guideclass CLOSE;
  gotonpc {清法比丘} {ask qingfa biqiu about 剃度};
};
#NOP {木人巷};
#ALIAS {guide_sl_mrx} {
  #CLASS guideclass KILL;
  #CLASS guideclass OPEN;
  #ACTION {^澄观说道：「木人巷乃我少林最为凶险的去处之一，其中遍布手持利器的木人} {
    dohalt {
      yes;
    };
  };
  #ACTION {^澄观伸出手在你后背一推，将你推进门内！} {
    yun qi;
    n;
    echo {checkstep};
  };
  #ACTION {^{设定环境变量：action \= \"checkstep\"|你设定checkstep为反馈信息}} {
    #IF {"$room" == "木人巷"} {
      #DELAY {1} {
        yun qi;
        n;
        echo {checkstep};
      };
    };
    #ELSE {
      #CLASS guideclass kill;
      loc {%1};
    };
  };
  #CLASS guideclass CLOSE;
  gotodo {嵩山少林} {罗汉堂一部} {ask chengguan luohan about 木人巷};
};
#NOP {升辈比武};
#ALIAS {guide_sl_fight} {
  #IF {@carryqty{chantui yao} < 2} {
    buymedicine {chantui yao} {2} {guide_sl_fight_do {%1}};
  };
  #ELSE {
    guide_sl_fight_do {%1};
  };
};
#ALIAS {guide_sl_fight_do} {
  #VARIABLE {master_name} {};
  #VARIABLE {master_id} {};
  #VARIABLE {master_roomid} {$roomid};
  #VARIABLE {fight_flag} {0};
  #VARIABLE {fight_ok} {0};
  #CLASS guideclass KILL;
  #CLASS guideclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkskills\"|你设定checkskills为反馈信息}} {
    #VARIABLE {fqskills} {};
    #FOREACH {*kungfu[know][]} {sk} {
      #IF {$kungfu[know][$sk][lv] < 30} {
        #VARIABLE {fqskills[$sk]} {know};
      };
    };
    #FOREACH {*kungfu[base][]} {sk} {
      #IF {$kungfu[base][$sk][lv] < 30} {
        #VARIABLE {fqskills[$sk]} {base};
      };
    };
    #FOREACH {*kungfu[spec][]} {sk} {
      #IF {$kungfu[spec][$sk][lv] < 30} {
        #VARIABLE {fqskills[$sk]} {spec};
      };
    };
    #IF {&fqskills[] > 0} {
      fangqi *fqskills[+1];
      #UNVARIABLE {kungfu[$fqskills[+1]][*fqskills[+1]]};
      dohalt {
        skills;
        echo {checkskills};
      };
    };
    #ELSE {
      ask qingshan biqiu about 达摩令;
      echo {checkling};
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkling\"|你设定checkling为反馈信息}} {
    #VARIABLE {idle} {0};
    dohalt {
      #IF {@carryqty{damo ling} == 0} {
        bei none;
        #IF {"$kungfu[spec][jingang-quan]" != ""} {
          jifa cuff jingang-quan;
        };
        #ELSE {
          jifa cuff luohan-quan;
        };
        bei cuff;
        gomaster {
          id here;
          echo {checkmaster}
        };
      };
      #ELSE {
        ask qingshan biqiu about 达摩令;
        echo {checkling};
      };
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkmaster\"|你设定checkmaster为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {"$roomthings[$hp[master][name]]" == ""} {
			#DELAY {6} {echo {checkmaster}};
		};
		#ELSE {
      #VARIABLE {master_id} {$roomthings[$hp[master][name]][+1]};
      give damo ling to $master_id;
    };
  };
  #ACTION {^你给$hp[master][name]一面达摩令。} {
    closewimpy;
    #VARIABLE {fight_flag} {1};
    fight $master_id;
    echo {checkfight};
  };
  #ACTION {^$hp[master][name]说道：「唉呀，我现在身体感觉不太好，你等一会儿再来吧} {
    #VARIABLE {fight_flag} {0};
  };
  #ACTION {^{设定环境变量：action \= \"checkfight\"|你设定checkfight为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {$fight_flag == 1} {
      echo {checkhp};
    };
    #ELSE {
      #VARIABLE {fight_flag} {1};
      #DELAY {6} {
        fight $master_id;
        echo {checkfight};
      };
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkhp\"|你设定checkhp为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {$hp[neili] < 200} {
      fu neixi wan;
      fu chuanbei wan;
    };
    #IF {$hp[qi_per] < 80} {
      fu chantui yao;
    };
    #IF {$hp[qi_max] - $hp[qi] > 200} {
      yun qi;
    };
    #IF {$fight_ok == 1} {
      i;
      echo {checkletter};
    };
    #ELSEIF {$fight_ok == 2} {
      yun qi;
      hp;
      #VARIABLE {fight_flag} {1};
      fight $master_id;
      echo {checkfight};
    };
    #ELSE {
      #DELAY {2} {
        hp;
        i;
        echo {checkhp};
      };
    };
  };
  #ACTION {^$hp[master][name]说道：「%*还得多加练习，方能在少林诸多弟子中出人头地} {
    #VARIABLE {fight_ok} {2};
  };
  #ACTION {^{设定环境变量：action \= \"checkheal\"|你设定checkheal为反馈信息}} {
    #VARIABLE {idle} {0};
    
  };
  #ACTION {^$hp[master][name]交给你一封推荐信} {
    #VARIABLE {fight_ok} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkletter\"|你设定checkletter为反馈信息}} {
    #CLASS guideclass KILL;
    #LOCAL {tjx} {};
    #FOREACH {*id[things][]} {t} {
      #IF {"$id[things][$t][name]" == "推荐信"} {
        #LOCAL {tjx} {$t};
      };
    };
    #NOP {获取参数中师傅的名称};
    #LOCAL {tparam} {%1};
    #REPLACE {tparam} {\x7B} {<};
    #REPLACE {tparam} {\x7D} {>};
    #REGEXP $tparam {<%*>} {
      gotonpc {&1} {
        give $tjx to $guide[$conf[newbie][party]][masters][&1][id];
        %1;
      }
    } {%1};
  };
  #CLASS guideclass CLOSE;
  prepareskills {
    gotonpc {清善比丘} {
      skills;
      echo {checkskills};
    };
  };
};
#NOP {升澄过阵};
#ALIAS {guide_sl_lhz} {
  #VARIABLE {master_name} {};
  #CLASS guideclass KILL;
  #CLASS guideclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkskills\"|你设定checkskills为反馈信息}} {
    #VARIABLE {fqskills} {};
    #FOREACH {*kungfu[know][]} {sk} {
      #IF {$kungfu[know][$sk][lv] < 80} {
        #VARIABLE {fqskills[$sk]} {know};
      };
    };
    #FOREACH {*kungfu[base][]} {sk} {
      #IF {$kungfu[base][$sk][lv] < 80} {
        #VARIABLE {fqskills[$sk]} {base};
      };
    };
    #FOREACH {*kungfu[spec][]} {sk} {
      #IF {$kungfu[spec][$sk][lv] < 80} {
        #VARIABLE {fqskills[$sk]} {spec};
      };
    };
    #IF {&fqskills[] > 0} {
      fangqi *fqskills[+1];
      #UNVARIABLE {kungfu[$fqskills[+1]][*fqskills[+1]]};
      dohalt {
        skills;
        echo {checkskills};
      };
    };
    #ELSE {
      #CLASS guideclass KILL;
      gotonpc {玄苦大师} {joinsl_askluohan {xuanku dashi} {
        guide_sl_lhz_over {gotonpc {$master_name} {%1}};
      }};
    };
  };
  #CLASS guideclass CLOSE;
  #LOCAL {tparam} {%1};
  #REPLACE {tparam} {\x7B} {<};
  #REPLACE {tparam} {\x7D} {>};
  #REGEXP $tparam {<%*>} {#VARIABLE {master_name} {&1}} {%1};
  skills;
  echo {checkskills};
};
#ALIAS {guide_sl_lhz_over} {
  #CLASS guideclass KILL;
  #CLASS guideclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkdashi\"|你设定checkdashi为反馈信息}} {
    #IF {"$roomthings[玄难大师]" != "" || "$roomthings[玄苦大师]" != ""} {
      #DELAY {6} {
        id here;
        echo {checkdashi};
      };
    };
    #ELSE {
      #CLASS guideclass KILL;
      %1;
    };
  };
  #CLASS guideclass CLOSE;
  id here;
  echo {checkdashi};
};
#NOP {升玄过伏魔圈};
#ALIAS {guide_sl_fmq} {
  #VARIABLE {master_name} {};
  #CLASS guideclass KILL;
  #CLASS guideclass OPEN;
  #ACTION {^阿弥陀佛，小沙弥不在，你过段时间再来吧。} {
    #VARIABLE {idle} {0};
    #DELAY {6} {ask xuanbei dashi about 三长老};
  };
  #ACTION {^小沙弥对着玄悲大师说道：渡难长老闻听此讯马上就到。} {
    #VARIABLE {idle} {0};
  };
  #ACTION {^你的眼前一花，一个瘦小的身影闪了进来} {
    bai du nan;
    nod du nan;
  };
  #ACTION {^渡难说道：「从今以后你的法名叫做%*恭喜你荣升为少林派玄字辈圣僧之一！} {
    #CLASS guideclass KILL;
    drop puti zi;
    yun qi;
    hp;
    score;
    #DELAY {2} {
      gotonpc {$master_name} {%1};
    };
  };
  #CLASS guideclass CLOSE;
  #LOCAL {tparam} {%1};
  #REPLACE {tparam} {\x7B} {<};
  #REPLACE {tparam} {\x7D} {>};
  #REGEXP $tparam {<%*>} {#VARIABLE {master_name} {&1}} {%1};
  fangqi trade;
  dohalt {
    gotonpc {玄悲大师} {ask xuanbei dashi about 三长老};
  }
};
#NOP {拜无名};
#ALIAS {guide_sl_wm} {
  #NOP {读书识字先学到200吧};
  funds_call {goliterate {200} {guide_sl_jingshu}} {2000}
};
#ALIAS {guide_sl_jingshu} {
  #VARIABLE {okflag} {0};
  #CLASS guideclass KILL;
  #CLASS guideclass OPEN;
  #ACTION {^你向慕容博打听有关『无名老僧』的消息。} {
    #VARIABLE {idle} {};
    dohalt {ask murong bo about 心愿};
  };
  #ACTION {^你向慕容博打听有关『心愿』的消息。} {
    #VARIABLE {idle} {};
    dohalt {
      gotodo {泰山} {石经峪} {ask seng ren about name};
    };
  };
  #ACTION {^你向游方和尚问道：敢问老师父尊姓大名？} {
    #VARIABLE {idle} {};
    dohalt {ask seng ren about here};
  };
  #ACTION {^你向游方和尚问道：这位老师父，贫僧初到贵宝地，不知这里有些什么风土人情} {
    #VARIABLE {idle} {};
    dohalt {ask seng ren about 禅理};
  };
  #ACTION {^你向游方和尚打听有关『禅理』的消息} {
    #VARIABLE {idle} {};
    dohalt {
      execute {
        yun jing;
        #5 mosong stone
      };
      #DELAY {1} {echo {checkjing}};
    };
  };
  #ACTION {^你对着石坪上的「金刚经」琢磨了一回儿，似乎对一些禅理仍有些疑问} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkjing\"|你设定checkjing为反馈信息}} {
    #VARIABLE {idle} {};
    #IF {$okflag == 0} {
      execute {
        yun jing;
        #5 mosong stone
      };
      #DELAY {1} {echo {checkjing}};
    };
    #ELSE {
      ask seng ren about 禅理;
    };
  };
  #ACTION {^游方和尚说道：「从前有个老婆婆，卧在树下休息，忽有大熊要来吃她。老婆婆绕树奔逃} {
    #SEND {say 救人危难，奋不顾身，虽受牵累，终无所悔。};
  };
  #ACTION {^老僧袍袖似乎动了一下，然后微微一笑说道} {
    gotonpc {慕容博} {ask murong bo about 经书};
  };
  #ACTION {^老僧然后微微一笑说道，本有一物相赠} {
    #VARIABLE {idle} {0};
    #DELAY {6} {ask seng ren about 禅理};
  };
  #ACTION {^你向慕容博打听有关『经书』的消息。} {
    #CLASS guideclass KILL;
    #VARIABLE {idle} {};
    dohalt {
      loc {
        give fanwen jing to wuming laoseng;
        guide_baishi {无名老僧}};
    };
  };
  #CLASS guideclass CLOSE;
  gotonpc {慕容博} {ask murong bo about 无名老僧};
};