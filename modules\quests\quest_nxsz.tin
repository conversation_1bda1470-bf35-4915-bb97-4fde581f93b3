#nop  凝血神爪;
#alias goquest_nxsz {
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #VARIABLE questmodule 凝血神爪;
  #ACTION {^你决定跟随陈近南一起行动。} {
    dohalt {
        pray pearl;
        ask chen jinnan about 凝血神爪
    };
  };
  #ACTION {^陈近南说道：「%*你现在状态不太适合学习凝血神爪。」} {        
    #CLASS questclass KILL;
    questfail {$questmodule};
    dohalt {%1};
  };
  #ACTION {^陈近南说道：「我不是和你说了，你暂时无法学会凝血神爪么} {
    questdelay {$questmodule} {0} {7200};
    dohalt {%1};
  };
  #ACTION {^^陈近南说道：「现在你的修为还不够再次尝试} {
    questdelay {$questmodule} {} {7200};
    dohalt {%1};
  };
  #ACTION {^陈近南说道：「嗯，不错，你资质不错是块学武的料，这本「凝血神爪谱」你就拿去研究下吧。」} {        
    #CLASS questclass KILL;
    questsuccess {$questmodule};
    dohalt {%1}
  };

  #ACTION {^这里没有 chen jinnan} {
    #IF {&roomlist[] == 0} {
      #SHOWME {<faa>找不到陈近南};
      #CLASS questclass KILL;
      questdelay {$questmodule} {0} {3600};
      dohalt {%1};
    };
    #ELSE {
      #LOCAL {temproomid} {$roomlist[+1]};
      #LIST {roomlist} {delete} {1};
      runwait {gotodo {福州城}{$temproomid}{follow chen jinnan}};
    };
  };

  #CLASS questclass CLOSE;
  #LIST {roomlist} {create} {76;77;78;79;80;81;82;83;84;85;88;89;90;91;92;93;94;95;96;97;98;99;100;101;102;103;104;108;1853;1854;1855;1856};
  #LOCAL {temproomid} {$roomlist[+1]};
  #LIST {roomlist} {delete} {1};
  gotodo {福州城}{$temproomid}{follow chen jinnan};
};