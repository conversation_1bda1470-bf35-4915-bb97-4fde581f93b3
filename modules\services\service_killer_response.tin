#NOP {杀手模块};
#ALIAS {initkillerservice} {
  #CLASS servicekillerclass KILL;
  #CLASS servicekillerclass OPEN;
  #ACTION {^%*(%*)告诉你：killer_request_%*_%*} {
    killer_accept {@lower{%%2}} {%%1} {%%3} {%%4}
  };
  #ACTION {^! %*(%*)告诉你：killer_request_%*_%*} {
    killer_accept {@lower{%%2}} {%%1} {%%3} {%%4}
  };
  #ACTION {^%*(%*)告诉你：killer_cancel} {
    #VARIABLE {caller} {};
  };
  #ACTION {^! %*(%*)告诉你：killer_cancel} {
    #VARIABLE {caller} {};
  };
  #CLASS servicekillerclass CLOSE;
};
#NOP {注册杀人请求,%1:id,%2:name,%3:要杀的人,%4:标识};
#ALIAS {killer_accept} {
  #NOP {超过五分钟重置};
  #IF {"$caller" != "" && @elapsed{$caller[timestamp]} > 300} {
    #VARIABLE {caller} {};
  };
  #NOP {更新请求};
  #IF {"$caller" != "" && "$caller[id]" != "%1"} {
    tell %1 killer_wait
  };
  #ELSE {
    #VARIABLE {caller} {
      {request} {killer}
      {timestamp} {@now{}}
      {id} {%1}
      {name} {%2}
      {target} {%3}
      {extend} {%4}
    };
    tell %1 killer_come
  };
};
#NOP {响应杀人请求,%1:后续指令};
#ALIAS {killer_response} {
  #IF {"$caller[target]" == ""} {
    %1;
  };
  #ELSEIF {"$mapnpcs[$caller[target]]" == ""} {
    %1;
  };
  #ELSE {
    wwp;
    #IF {"$caller[extend]" == ""} {
      gotonpc {$caller[target]} {killer_response_start {%1}}
    };
    #ELSE {
      gotonpc {$caller[target]} {killer_response_start2 {%1}}
    };
  };
};
#NOP {开始杀人,%1:后续指令};
#ALIAS {killer_response_start} {
  #CLASS serviceclass KILL;
  #CLASS serviceclass OPEN;
  #ACTION {^苗家庄早已经封闭，无法进去。} {
    #CLASS serviceclass KILL;
    #DELAY {2} {
      %1
    };
  };
  #ACTION {^{设定环境变量：action \= \"checktarget\"|你设定checktarget为反馈信息}} {
		#IF {"$roomthings[$caller[target]]" == ""} {
			#CLASS serviceclass KILL;
      #VARIABLE {caller} {};
			runwait {%1}
		};
		#ELSE {
			kill $roomthings[$caller[target]][+1]
		};
	};
  #ACTION {^这里没有这个人。} {
    #CLASS serviceclass KILL;
    #VARIABLE {caller} {};
    runwait {%1}
  };
  #ACTION {^$caller[target]「啪」的一声倒在地上，挣扎着抽动了几下就死了。} {
    #CLASS serviceclass KILL;
    #VARIABLE {caller} {};
    dohalt {
      get silver from corpse;
      get gold from corpse;
      %1
    };
  };
  #CLASS serviceclass CLOSE;
  id here;
  echo {checktarget};
};
#NOP {开始杀人,持续杀直到取消,%1:后续指令};
#ALIAS {killer_response_start2} {
  #CLASS serviceclass KILL;
  #CLASS serviceclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checktarget\"|你设定checktarget为反馈信息}} {
    #IF {"$caller" == ""} {
      #CLASS serviceclass KILL;
      %1
    };
    #ELSEIF {"$roomthings[$caller[target]]" == ""} {
      #DELAY {1} {
        id here;
        echo {checktarget};
      };
    };
		#ELSE {
			kill $roomthings[$caller[target]][+1] 1;
      kill $roomthings[$caller[target]][+1] 2;
      kill $roomthings[$caller[target]][+1] 3;
      #DELAY {2} {
        echo {checktarget};
      };
		};
	};
  #CLASS serviceclass CLOSE;
  set wimpy 100;
  tell $caller[id] killer_onposition;
  id here;
  echo {checktarget};
};
initkillerservice