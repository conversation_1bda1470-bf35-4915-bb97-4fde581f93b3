# 项目说明

本项目是为 MUD 游戏“书剑”编写的 [TinTin++](https://tintin.sourceforge.io/) 脚本和资源集合。

## 功能

* **任务自动化**: 自动执行各种游戏任务。
* **工作自动化**: 自动完成各种工作。
* **角色管理**: 管理多个游戏角色。
* **模块化**: 脚本被组织成模块，易于管理和扩展。
* **用户界面**: 提供一个用户界面，方便与脚本交互。
* **地图**: 包含游戏地图，并提供寻路功能。

## 目录结构

* **books**: 包含解密指南、任务信息和其他文档。
* **chars**: 包含用于启动角色的 `.bat` 文件和角色配置文件。
* **log**: 用于存储游戏日志。
* **modules**: 包含核心脚本，按功能组织到子目录中。
* **others**: 包含字体和其他资源。

## 使用方法

1. **安装 TinTin++**: 从 [TinTin++ 官方网站](https://tintin.sourceforge.io/)下载并安装 TinTin++。
2. **配置 TinTin++**: 将 TinTin++ 的可执行文件路径添加到您的系统环境变量中。
3. **启动角色**: 运行 `chars` 目录中的 `.bat` 文件以启动游戏角色。

## 注意事项

* 本项目仍在开发中，可能存在一些错误。
* 请仔细阅读 `books` 目录中的文档，以了解如何使用脚本。
* 如果您有任何问题，请随时提出。
