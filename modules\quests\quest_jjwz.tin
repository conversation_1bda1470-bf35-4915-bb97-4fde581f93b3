#NOP {独孤九剑无招无式,%1:后续指令};
#ALIAS {goquest_jjwz} {
  #VARIABLE {questmodule} {九剑无招};
  #IF {$env[askfeng] == 0} {
    gotodo {华山} {夹山壁} {jjwz_askwuz<PERSON> {%1}}
  };
  #ELSE {
    jjwz_jianzhong {%1}
  };
};
#ALIAS {jjwz_askwuzhao} {
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你向风清扬打听有关『无招无式』的消息。} {
    #CLASS questresponseclass KILL;
    #CLASS questresponseclass OPEN;
    #ACTION {^风清扬说道：「你不是已经领悟了无招无式？来消遣老头子我} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questsuccess {$questmodule};
      dohalt {%1}
    };
    #ACTION {^风清扬说道：「{独孤九剑|活学活使}} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      #VARIABLE {env[askfeng]} {1};
      dohalt {jjwz_jianzhong {%1}}
    };
    #CLASS questresponseclass CLOSE;
  };
  ask feng qingyang about 无招无式
};
#NOP {去剑冢};
#ALIAS {jjwz_jianzhong} {
  #IF {@carryqty{fire} == 0} {
    buyfire {jjwz_jianzhong {%1}}
  };
  #ELSEIF {@carryqty{xiao shuzhi} == 0} {
    findweapon_xsz {jjwz_jianzhong {%1}}
  };
  #ELSE {
    gotodo {襄阳城} {剑冢} {jjwz_bajian {%1}}
  };
};
#ALIAS {jjwz_bajian} {
  #VARIABLE {okflag} {0};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^恭喜} {
    #VARIABLE {okflag} {1};
	};
  #ACTION {^{设定环境变量：action \= \"checkwuzhao\"|你设定checkwuzhao为反馈信息}} {
    #CLASS questclass KILL;
    #IF {$okflag == 1} {
      questsuccess {$questmodule};
    };
    #ELSE {
      questfail {$questmodule};
    };
    dohalt {%1}
  };
  #CLASS questclass CLOSE;
  pray pearl;
  ti mujian;
  echo {checkwuzhao}
};