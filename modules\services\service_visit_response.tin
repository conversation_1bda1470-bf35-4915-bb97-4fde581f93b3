#NOP {遍历访问};
#ALIAS {initvisitservice} {
  #CLASS servicevisitclass KILL;
  #CLASS servicevisitclass OPEN;
  #ACTION {^%*(%*)告诉你：visit_request_%*} {
    visit_accept {@lower{%%2}} {%%1} {%%3}
  };
  #ACTION {^! %*(%*)告诉你：visit_request_%*} {
    visit_accept {@lower{%%2}} {%%1} {%%3}
  };
  #ACTION {^%*(%*)告诉你：visit_cancel} {
    #VARIABLE {caller} {};
  };
  #ACTION {^! %*(%*)告诉你：visit_cancel} {
    #VARIABLE {caller} {};
  };
  #CLASS servicevisitclass CLOSE;
};
#NOP {遍历访问，%1:id,%2:name,%3:目标房间};
#ALIAS {visit_accept} {
  #NOP {超过五分钟重置};
  #IF {"$caller" != "" && @elapsed{$caller[timestamp]} > 300} {
    #VARIABLE {caller} {};
  };
  #VARIABLE {caller} {};
  #NOP {更新请求};
  #IF {"$caller" != "" && "$caller[request]" != "visit"} {
    tell %1 visit_wait
  };
  #ELSE {
    #VARIABLE {caller[request]} {visit};
    #VARIABLE {caller[timestamp]} {@now{}};
    #VARIABLE {caller[id]} {global};
    #VARIABLE {caller[name]} {全球流};
    #LOCAL {_rooms} {%3};
    #REPLACE {_rooms} {|} {;};
    #LIST {caller[rooms]} {create} {$_rooms};
    tell %1 visit_come
  };
};
#NOP {响应开门请求,%1:后续指令};
#ALIAS {visit_response} {
  #IF {&caller[rooms][] == 0} {
    #VARIABLE {caller} {};
    %1
  };
  #ELSE {
    gotoroom {$caller[rooms][+1]} {visit_checkroom {%1}}
  };
};
#ALIAS {visit_checkroom} {
  #LIST {caller[rooms]} {delete} {1};
  say $hp[name]到此一游！;
  runwait {visit_response {%1}}
};
initvisitservice