#NOP {==============================================账号配置==============================================开始};
#NOP {会话ID和账号一致即可};
#VARIABLE {user_session} {pksa};
#NOP {服务器端口，比如5555 9999 7777 3333}
#VARIABLE {user_port} {3333};
#NOP {账号，创建账号时有效};
#VARIABLE {user_char} {pksa};
#NOP {性别(m或f)，创建账号时有效};
#VARIABLE {user_gender} {m};
#NOP {名称，创建账号时有效};
#VARIABLE {user_name} {小纯洁};
#NOP {昵称};
#VARIABLE {user_nick} {$HIW$蛤蟆$NOR$·$HIC$九阴$NOR$·$HIR$天选之人};
#NOP {密码};
#VARIABLE {user_password} {ADMIN0917LP};
#NOP {角色配置文件名称，必须与实际文件名称一致};
#VARIABLE {user_profile} {char_pksa};
#NOP {killmsg};
#VARIABLE {user_killmsg} {$HIW$堂下$HIC$\$n$HIW$因何状告$HIR$本官$NOR$！};
#NOP {==============================================账号配置==============================================结束};

#NOP {仔细阅读注释内容，下面所述变量中以#LIST开头的后面大括号内支持多项，各项间用;隔开};
#NOP {下面配置内容带“示例”字样需手动删除};
#NOP {==============================================武器配置==============================================开始};
#NOP {primary:主武器,丢失时会启用备用武器，当主武器为打造武器(不在脚本内置的可购买武器范围内)时会持续使用备用武器20分钟然后退出并重新登录};
#NOP {seconday:备用武器，打造武器或可购买武器};
#NOP {chop:砍头或砍树用武器，不支持mujian，mu jian，推荐用changjan};
#NOP {wuxing:增加智力的武器，学习或领悟时会装备该武器};
#NOP {userweapons:身上携带的打造武器类型sword,blade等,每个基本技能可有多个武器，使用;隔开};
#VARIABLE {conf[weapon]} {
	{primary} {}
	{secondary} {}
	{chop} {changjian}
	{wuxing} {}
	{userweapons} {
		{sword} {}
		{staff} {}
	}
};
#NOP {==============================================武器配置==============================================结束};

#NOP {==============================================护甲配置==============================================开始};
#NOP {护甲配置,armor:护甲,coat:大裳(女),mantle:披风(男),belt:腰带,glove:手套,cap:帽子,boat:鞋子};
#VARIABLE {conf[armor]} {
	{armor} {}
	{coat} {}
	{mantle} {}
	{belt} {}
	{glove} {}
	{cap} {}
	{boot} {}
};
#NOP {==============================================护甲配置==============================================结束};

#NOP {==============================================任务配置==============================================开始};
#NOP {开启急速模式，开启后不再按区域行走，直接走最短路径};
#VARIABLE {conf[extrememode]} {1};

#NOP {接任务时要到达的内力阈值(最大内力的百分比80~180)};
#VARIABLE {conf[neilithreshold]} {120};

#NOP {允许最低的气血上限，低于该值会尝试疗伤，否则继续任务。};
#VARIABLE {conf[healthreshold]} {80};


#NOP {角色登录后进行的活动，job:任务};
#VARIABLE {conf[role]} {job};

#NOP {限制访问的受限区域，此处配置的区域不接收任务，具体参考common[limitedzone]内容};
#LIST {conf[limitedzone]} {create} {};

#NOP {任务列表, 武馆,巡城,长乐帮,丐帮,华山,嵩山,送信,天地会,颂摩崖,守卫襄阳,武当,雪山,做菜,护送,护镖,官府,巡逻,教和尚,守墓,七窍玲珑,钓鱼};
#NOP {要做的任务列表,二段任务在后面带2，如送信2};
#LIST {conf[joblist]} {create} {武当;送信2};

#NOP {送信1任务是否需要等待杀手,0:不等,1:等待};
#VARIABLE {conf[waitkiller]} {0};

#NOP {需要放弃的武当或送信NPC武功列表};
#LIST {conf[fangqiskill]} {create} {};

#NOP {需要放弃的武当或送信实力描述，如极其厉害和已入化境};
#LIST {conf[fangqidesc]} {create} {};

#NOP {需要放弃的雪山大内技能，具体为保镖的门派和武器连起来};
#LIST {conf[fangqiguard]} {create} {};

#NOP {是否进行其他扩展任务，在条件满足时优先去做扩展任务，一般就是指官府};
#LIST {conf[extendjob]} {create} {};

#NOP {官府任务允许的经验差};
#NOP {这里设置的内容是自身经验的倍数};
#VARIABLE {conf[guanfulv]} {2};

#NOP {组队任务设置,队员角色文件也要配置此处信息，当前组队支持qqll任务};
#NOP {leader:自己是否是队长,0:不是,1:是};
#NOP {partner:队友ID,只支持一个队友};
#VARIABLE {conf[team]} {
	{leader} {0}
	{partner} {}
};
#NOP {==============================================任务配置==============================================结束};

#NOP {==============================================携带物品和解毒配置==============================================结束};
#NOP {身上携带金钱};
#NOP {keep:身上正常要保留的数量};
#NOP {warning:预警数量，小于这个数字会去取钱};
#NOP {deposit:超载数量，大于这个数字会去存钱};
#NOP {如未配置默认携带2gold，50silver，预警数量为1gold 10silver，超载数量为5倍};
#VARIABLE {conf[money]} {
	{gold} {
		{carry} {2}
		{warning} {0}
		{deposit} {10}
	}
	{silver} {
		{carry} {50}
		{warning} {10}
		{deposit} {200}
	}
};
#NOP {药品贩子,仅用于正气丹};
#VARIABLE {conf[drugdealer]} {
	{id} {}
	{room} {424}
};
#NOP {身上携带的药物数量，keep:补货时补满的数量，warning:数量小于或等于时进行补货};
#VARIABLE {conf[medicine]} {
	{chantui yao} {
		{carry} {0}
		{warning} {0}
	}
	{huoxue dan} {
		{carry} {0}
		{warning} {0}
	}
	{chuanbei wan} {
		{carry} {0}
		{warning} {0}
	}
	{dahuan dan} {
		{carry} {1}
		{warning} {0}
	}
	{da huandan} {
		{carry} {0}
		{warning} {0}
	}
	{zhengqi dan} {
		{carry} {0}
		{warning} {0}
	}
	{xieqi wan} {
		{carry} {0}
		{warning} {0}
	}
	{tianqi} {
		{carry} {0}
		{warning} {0}
	}
};

#NOP {解毒的配置,吃药或者硬抗,drug:吃药,anti:去药铺硬抗，大于处理方式的数字(秒)则使用该处理方式};
#VARIABLE {conf[detoxify]} {
	{星宿掌} {
		{drug} {1200}
		{anti} {120}
	}
	{腐尸} {
		{drug} {1200}
		{anti} {120}
	}
	{火} {
		{drug} {2400}
	}
	{蓝砂手} {
		{drug} {2400}
	}
	{寒} {
		{drug} {2400}
	}
};
#NOP {==============================================携带物品和解毒配置==============================================开始};

#NOP {==============================================学习和领悟配置=========================================开始};
#NOP {skill:允许的脱节等级，0:不full技能，一般设置为1，不要设置的太高。经验等级与技能等级差>=该配置时去学习或领悟};
#NOP {neili:内力脱节点数，内力第一上限与最大内力差>=该配置值时去打坐。紫檀站6000前会一直吃yuji wan};
#NOP {jingli:精力脱节点数，精力上限与最大精力差值>=该配置值时去吐纳};
#VARIABLE {conf[allowdiff]} {
	{skill} {0}
	{neili} {100000}
	{jingli} {30000}
};

#NOP {是否自动吃yuji wan补内力。启用该设置后上面的allowdiff[neili]失效};
#NOP {state:非空自动补};
#NOP {threshold:最低通宝数量};
#VARIABLE {conf[autoyuji]} {
	{state} {}
	{threshold} {40000}
};

#NOP {经验换内力配置，默认100M后有效，启用该设置后上面的allowdiff[neili]失效};
#NOP {state:是否启动,非空启用};
#NOP {threshold:内力差阈值，最小1000。};
#VARIABLE {conf[autoexchange]} {
	{state} {0}
	{threshold} {1300}
};

#NOP {潜能用途,none:不去学习或领悟,learn:学习,lingwu:领悟};
#NOP {当技能>=220时learn会自动转为lingwu，向导模式时需设置为learn};
#VARIABLE {conf[potpurpose]} {none};

#NOP {忽略的技能列表，列表内武功不参与学习、领悟和练习};
#LIST {conf[ignoreskills]} {create} {hujia-daofa;kongming-quan;lingbo-weibu;miaojia-jianfa;pixie-jian;taizu-quan;yitian-tulong};

#NOP {任务用主内功};
#NOP {当角色拥有多个特殊内功时(不含在忽略列表的技能)，需要指定一个任务用的主内功，否则激发的内功是不确定的};
#VARIABLE {conf[primaryforce]} {shenzhao-jing};

#NOP {增加悟性的内功};
#NOP {当角色拥有多个特殊内功时(不含在忽略列表的技能)且有内功可增加悟性，可配置此项，在领悟前会激发该内功};
#VARIABLE {conf[wuxingforce]} {};
#NOP {==============================================学习和领悟配置=========================================结束};

#NOP {==============================================解谜配置==============================================开始};
#NOP {新使用这个脚本的角色需要标识一下自身已经完成的解谜，如果未标识还是会尝试去解谜，结果不确定。};
#NOP {对于一般的解谜输入questsuccess 名称 可设置为该解谜成功，对于分步骤的解谜输入questupdate 数字 数字大于等于该解谜步骤数即可};
#NOP {脚本使用内置的alias来保存角色各解谜的状态，最好先删除一些无用的alias};
#NOP {分步的解谜有天龙八部、雪山飞狐、连城诀、射雕英雄传、神照经，具体参考quest.tin文件};
#NOP {是否开启自动解谜};
#VARIABLE {conf[autoquest]} {1};

#NOP {参与的解谜，该参数比下面的ingorequests优先级高，仅参与这里已配置的解谜};
#LIST {conf[subcribequests]} {create} {空明拳;左右互搏;越女剑法一};

#NOP {不参与的解谜，所有未在该处配置的且脚本支持的解谜均会参与};
#NOP {情怀站太极宗师不是ask而是yun yinyun，所以默认太极宗师在这里屏蔽，武当派不要删除太极宗师};
#LIST {conf[ignorequests]} {create} {千蛛万毒手;躺尸剑法;金蛇剑法;太极宗师};
#NOP {==============================================解谜配置==============================================结束};

#NOP {==============================================服务配置==============================================开始};
#NOP {角色提供的服务项目，与保姆配置相对应，提供服务的账号会响应保姆的呼叫请求};
#NOP {funds:响应启动资金请求，配合向导模式可以实现从零开始无人值守};
#NOP {guard:响应护卫请求，当前支持雪山飞狐复仇篇黑衣人，宝藏篇高手和射雕英雄传金兵};
#NOP {killer:响应杀人请求，可呼叫服务号杀死某个NPC，NPC必须已在地图中支持};
#NOP {library:响应书籍请求，当前支持易经、九宫八卦图谱(服务号必须为桃花岛)，其他书籍服务号会检查杂货铺存货，请服务号事先准备好并存储在杂货铺};
#NOP {当前已支持medicine的kejin jijie,douzhen dinglun和boji xidoufang的自动还书};
#NOP {zhanbu:响应占卜请求，占卜需桃花岛账号且需要有一个助手才行};
#NOP {assist:占卜助手};
#LIST {conf[services]} {create} {};

#NOP {保姆配置，设置各种服务提供者的ID};
#NOP {banker:启动资金，与funds服务对应。巡城完成后会请求资金并学习literate至122级};
#NOP {killer:杀手，与killer服务对应，当前并无实际应用};
#NOP {library:图书馆，与library服务对应，当前学习medicine和奇门八卦时会向服务号请求相关书籍};
#NOP {guard:护卫，与guard服务对应，当前在解谜雪山飞狐复仇篇、宝藏篇和射雕英雄传杀金兵时会请求guard服务};
#NOP {zhanbu:占卜，与zhanbu服务对应，当前在解谜三无三不手时会请求占卜陆无双位置};
#VARIABLE {conf[nanny]} {
	{banker} {}
	{recycle} {}
	{zhanbu} {}
	{killer} {}
	{library} {}
	{doorman} {}
	{supplier} {}
	{visitor} {}
	{guard} {}
	{buffer} {}
	{doctor} {
		{星宿掌} {}
		{冰魄银针} {}
		{寒冰绵掌} {}
		{蔓陀萝花} {}
		{腐尸} {}
		{火} {}
		{寒} {}
	}
};
#NOP {==============================================服务配置==============================================结束};

#NOP {==============================================向导配置==============================================开始};
#NOP {新手模式要拜的师门，账号巡城完毕后会自动加入该门派，其后续何时拜师、学习什么技能、做什么任务、使用什么技能均由向导配置脚本决定};
#NOP {具体请参考guides下的门派向导脚本};
#NOP {当前脚本已支持的门派有古墓派、昆仑派、姑苏慕容、全真教、少林派、桃花岛、铁掌帮和星宿派};
#VARIABLE {conf[newbie]} {
	{party} {普通百姓}
};
#NOP {==============================================向导配置==============================================结束};

#NOP {==============================================战斗配置==============================================开始};
#NOP {被峨嵋幽冥后是否继续战斗，0:跑路，1:继续战斗};
#VARIABLE {conf[fight][youming]} {1};
#NOP {==============================================战斗配置==============================================结束};

#NOP {==============================================技能配置==============================================开始};
#NOP {向导模式时优先使用门派向导脚本中配置的技能};
#NOP {==============================================通用配置==============================================开始};
#NOP {空手武技准备，技能准备时会执行此处指令};
#VARIABLE {conf[pfm][bei]} {
	bei none;
	jifa cuff taizu-quan;
	jifa parry taizu-quan;
	bei cuff
};

#NOP {增加悟性的指令，在读书、学习和领悟时会执行，如玉女心经可设置为yun xinjing};
#VARIABLE {conf[pfm][wuxing]} {
};

#NOP {非战斗增幅指令，在接收到任务去做任务前会执行，主要是一些可以在非战斗状态使用的perform，如桃花岛五转、铁掌帮飘等};
#VARIABLE {conf[pfm][normalbuff]} {
	
};

#NOP {战斗增幅指令，需要在战斗中使用的增幅类perform，上面的normalbuff指令最好也配置在此处，因为增幅类状态可随时消失。};
#NOP {战斗增幅指令会与下面的攻击类perform指令合并到wimpycmd使用的autopfm alias中，参考fight.tin脚本};
#VARIABLE {conf[pfm][fightbuff]} {
	
};

#NOP {通用攻击类perform指令，会与上面的战斗增幅指令合并到wimpycmd使用的autopfm alias中};
#VARIABLE {conf[pfm][attack]} {
	jiali 1;
	perform cuff.qianjun #p;	
};
#NOP {==============================================通用配置==============================================结束};

#NOP {==============================================预设配置==============================================开始};
#NOP {你可以在此设置若干个攻击perform组合指令，然后应用在下面定义的空手或者特定任务perform中};
#NOP {示例中p0，p1即为预定义的perform代码，do:使用该perform时的预备指令,cmd:该perform的实际执行指令};
#NOP {如改pfm是使用武器的，需在weapon字段中指定武器,auto-指使用$conf[weapon][primary]};
#VARIABLE {conf[pfm][alias]} {
	{p0} {
		{do} {			
			bei none;
			jifa cuff taizu-quan;
			bei cuff;
		}
		{cmd} {
			jiali 1;			
			perform cuff.qianjun;
			}
	}
	{psanhuan} {
		{do} {
		}
		{weapon} {auto}
		{cmd} {
			jiali max;
			yun yinyun;
			perform sword.raozhi #s;
			perform sword.sanhuan #p;
		}
	}
	{pshoot} {
		{do} {
		}
		{weapon} {tianshe zhang}
		{cmd} {
			jiali max;
			perform lingshe-zhangfa.shoot #p
		}
	}
	{pqxwxj} {
		{do} {
			jiali 0
		}
		{weapon} {auto}
		{cmd} {
			jiali max;
			perform xuanyin-jian.xuanyin #p;
			jiali 0
		}
	}
};
#NOP {==============================================预设配置==============================================结束};

#NOP {==============================================场景配置==============================================开始};
#NOP {配置各种场景下使用的perform，配置内容为上面预设好的perform代码，未配置时统一使用通用攻击perform};
#NOP {空手，武器掉落时如未配置则会尝试捡武器};
#VARIABLE {conf[pfm][scene][unarmed]} {}

#NOP {嵩山请人};
#VARIABLE {conf[pfm][scene][ss]} {};

#NOP {送信1};
#VARIABLE {conf[pfm][scene][sx1]} {p0};

#NOP {送信2};
#VARIABLE {conf[pfm][scene][sx2]} {p0};

#NOP {华山1};
#VARIABLE {conf[pfm][scene][hs1]} {p0};

#NOP {华山2};
#VARIABLE {conf[pfm][scene][hs2]} {p0};

#NOP {武当};
#VARIABLE {conf[pfm][scene][wd]} {p0};

#NOP {雪山};
#VARIABLE {conf[pfm][scene][xs]} {};

#NOP {丐帮杀人};
#VARIABLE {conf[pfm][scene][gb]} {};

#NOP {丐帮颂摩崖};
#VARIABLE {conf[pfm][scene][smy]} {psanhuan};

#NOP {守卫襄阳};
#VARIABLE {conf[pfm][scene][swxy]} {psanhuan};

#NOP {七窍玲珑};
#VARIABLE {conf[pfm][scene][qqll]} {};
#NOP {==============================================场景配置==============================================结束};

#NOP {==============================================特定NPC配置==============================================开始};
#NOP {指定雪山大内使用的技能，配置字段为大内门派+武器(参考前面大内放弃配置)，配置内容为前面alias预设的perform代码，};
#VARIABLE {conf[pfm][npc][xs]} {
	{大理天龙寺} {pshoot}
	{少林长鞭} {pshoot}
	{峨嵋长剑} {pshoot}
	{峨嵋钢刀} {pshoot}
	{明教} {pshoot}
	{明教一块铁令} {pshoot}
};

#NOP {其他任务特殊技能对应的pfm};
#VARIABLE {conf[pfm][npc][skill]} {
	{原始剑法} {}
	{古拙掌法} {}
	{七弦无形剑} {}
	{降龙十八掌} {}
	{打狗棒法} {}
};

#NOP {送信2NPC技能使用的pfm，兼容期间覆盖上方的配置};
#VARIABLE {conf[pfm][skill][sx2]} {
	{原始剑法} {}
	{古拙掌法} {}
	{七弦无形剑} {}
	{降龙十八掌} {}
	{打狗棒法} {}
};
#NOP {==============================================特定NPC配置==============================================结束};

#NOP {==============================================切换配置==============================================结束};
#NOP {定义可通过触发器调整的perform，应用场景为无法通过合并指令来完成的情况，如五轮大转会一直放};
#NOP {字段为要触发的内容，值为前面定义的预设perform代码，触发内容不能有通配符};
#VARIABLE {conf[pfm][trigger]} {
	{结果%*被你杖头怪蛇一口喷中} {
		{condition} {雪山}
		{perform} {}
	}
	{你余势未了，突然长身往前疾扑，左臂伸了两伸，已将顾坚风左足踝上三寸的“ 悬钟穴” 、右足内踝上七寸的“ 中都穴” 先后点中} {
		{condition} {雪山}
		{perform} {}
	}
};
#NOP {==============================================切换配置==============================================结束};
#NOP {==============================================技能配置==============================================结束};

#SHOWME {<faa>角色配置加载完毕};