#NOP {发起启动资金支持,%1:后续指令,%2:数额,默认600};
#ALIAS {funds_call} {
  yz {funds_call_start {%1} {%2}}
};
#NOP {开始呼叫,先到位置再呼叫,%1:后续指令,%2:数额};
#ALIAS {funds_call_start} {
  #CLASS serviceclass KILL;
  #CLASS serviceclass OPEN;
  #ACTION {^没有这个人。} {
    #CLASS serviceclass KILL;
    #SHOWME {<faa>保姆$conf[nanny][banker]人不在};
    %1
  };
  #ACTION {%*(%*)告诉你：funds_wait} {
    #VARIABLE {idle} {0};
    #CLASS serviceclass KILL;
    #DELAY {5} {funds_call {%1} {%2}};
  };
  #ACTION {%*(%*)告诉你：funds_come} {
    #CLASS serviceclass KILL;
    #VARIABLE {waiter} {
      {id} {@lower{%%2}}
      {name} {%%1}
    };
    yz {funds_request_wait {%1} {%2}}
  };
  #CLASS serviceclass CLOSE;
  #IF {"$conf[nanny][banker]" == ""} {
    #SHOWME {<faa>未配置banker保姆,请自行处理};
    %1
  };
  #ELSE {
    tell $conf[nanny][banker] funds_request
  };
};
#NOP {去银行等待碰头,%1:后续指令,%2:数额};
#ALIAS {funds_request_wait} {
  #VARIABLE {startts} {@now{}};
  #VARIABLE {okflag} {0};
  #VARIABLE {arrived} {0};
  #VARIABLE {credit} {600};
  #IF {"%2" != ""} {
    #VARIABLE {credit} {%2};
  };
  #CLASS serviceclass KILL;
  #CLASS serviceclass OPEN;
  #ACTION {^$waiter[name]轻轻地拍了拍你的头} {
    #VARIABLE {arrived} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkresponse\"|你设定checkresponse为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {@elapsed{$startts} > 600} {
      #CLASS serviceclass KILL;
      loc {
        funds_call {%1};
      };
    };
    #ELSEIF {$arrived == 0} {
      #DELAY {5} {
        echo {checkresponse};
      };
    };
  };
  #ACTION {^$waiter[name]给你一百锭黄金} {
    #VARIABLE {okflag} {0};
    cun 100 gold;
    score;
    echo {checkcun};
  };
  #ACTION {你拿出一百锭黄金} {
    #VARIABLE {okflag} {1};
    #MATH {credit} {$credit - 100};
  };
  #ACTION {^您目前已有存款%*小号可难保管了} {
    #VARIABLE {okflag} {2};
  };
  #ACTION {^{设定环境变量：action \= \"checkcun\"|你设定checkcun为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {$okflag == 0} {
      #DELAY {2} {
        #VARIABLE {okflag} {0};
        cun 100 gold;
        score;
        echo {checkcun};
      };
    };
    #ELSEIF {$okflag == 2} {
      #CLASS serviceclass KILL;
      bye $waiter[id];
      #VARIABLE {waiter} {};
      %1;
    };
    #ELSEIF {$credit > 0} {
      jump $waiter[id];
    };
    #ELSE {
      #CLASS serviceclass KILL;
      bye $waiter[id];
      #VARIABLE {waiter} {};
      %1;
    };
  };
  #CLASS serviceclass CLOSE;
  echo {checkresponse};
};
#NOP {取消资金请求,%1:后续指令};
#ALIAS {funds_cancel} {
  #IF {"$conf[nanny][banker]" != ""} {
    tell $conf[nanny][banker] funds_cancel
  };
};