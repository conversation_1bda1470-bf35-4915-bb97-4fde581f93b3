#CLASS {qinghuaidao} {kill}
#CLASS {qinghuaidao} {open}

/* 
 * 情怀岛副本脚本（由haizhan.lua转换而来）
 * 该脚本实现了情怀岛副本的自动化功能
 */

#ALIAS {otherquest_battleship} {
    #IF {$battleship == 1} {
        #VAR killer_master {};
        #VAR killer_master_number 0;
        #UNTRIGGER {battleship_13};
        #UNTRIGGER {battleship_14};
        #UNTRIGGER {battleship_15};
        #UNTRIGGER {battleship_16};

        #TRIGGER {^\s*你被秘密地带到了神秘的小海滩！} {
            #VAR now_time {@time_in_seconds{} + 24 * 60 * 60};
            #VAR battleship_start_time $now_time;
            #ALIAS {挑战情怀岛} {$now_time};
            #ECHO {<解密>:%t <玩家:${char_id:-none}>：挑战情怀岛副本开始} {cyan};
            #UNTRIGGER {otherquest_battleship_1};
        } {otherquest_battleship_1};

        #IF {$battleship_root == 1} {
            location;
        } {
            #VAR battleship_start_time_1 {@time_in_seconds{}};
            #ALIAS {情怀岛开始时间} {$battleship_start_time_1};
        };

        i;
        
        #IF {$battleship_da_huandan == 1} {
            #DELAY {1} {
                #TRIGGER {^\s+(\S+)盒大还丹\(盒\)\(Da huandan\)$} {
                    #VAR da_huandan %1;
                    #IF {$da_huandan < $battleship_da_huandan_number} {
                        #DELAY {1} {
                            duihuan da huandan;
                            i;
                        };
                    } {
                        #UNTRIGGER {battleship_11};
                        #DELAY {1} {
                            #DELAY {3} {
                                #DELAY {1} {
                                    qinghuaidao;
                                    jump;
                                    set wimpy 100;
                                    #DELAY {3} {
                                        battleship_skills_1;
                                        #DELAY {3} {
                                            close_fight;
                                            Open_battleship_triggers;
                                        };
                                    };
                                };
                            };
                        };
                    };
                } {battleship_11};
                
                #IF {$da_huandan < $battleship_da_huandan_number} {
                    duihuan da huandan;
                    i;
                } {
                    #UNTRIGGER {battleship_11};
                    #DELAY {1} {
                        #DELAY {2} {
                            #DELAY {1} {
                                qinghuaidao;
                                jump;
                                set wimpy 100;
                                #DELAY {3} {
                                    battleship_skills_1;
                                    #DELAY {3} {
                                        close_fight;
                                        Open_battleship_triggers;
                                    };
                                };
                            };
                        };
                    };
                };
            };
        } {
            #DELAY {1} {
                close_fight;
                qinghuaidao;
                jump;
                set wimpy 100;
                #DELAY {3} {
                    battleship_skills_1;
                    #DELAY {3} {
                        Open_battleship_triggers;
                    };
                };
            };
        };

        #TRIGGER {^\s*舰底传来"咚咚"地声音，应该是海盗破坏了情怀舰，} {
            #KILL {timer};
            #KILL {timer2};
            #UNTRIGGER {battleship_3};
            #DELAY {1} {
                down;
            };
        } {battleship_1};

        #TRIGGER {^\s*这里没有这个人。} {
            climb;
        } {battleship_2};

        #TRIGGER {^\s*情怀战舰的牢固度：(.*)，该值低于500战舰有倾} {
            #VAR battleship_number %1;
            #IF {$battleship_number < 800} {
                #KILL {timer};
                #UNTRIGGER {battleship_6};
                #VAR idle 0;
                #TICKER {timer2} {
                    repair;
                } {2};
            } {
                #TRIGGER {battleship_6} {ON};
                #KILL {timer2};
            };
        } {battleship_3};

        #TRIGGER {^\s*海盗 *(.*)} {
            kill pirate 1;
            kill pirate 2;
        } {battleship_4};

        #TRIGGER {^\s*你爬上了情怀战舰，浑身湿漉漉的！} {
            #TRIGGER {battleship_3} {ON};
            location;
        } {battleship_5};

        #TRIGGER {^\s*情怀岛坐标是：(.*), (.*)，当前坐标是：(.*), (.*)。} {
            #VAR battleship_zuobiao1 %1;
            #VAR battleship_zuobiao2 %2;
            #VAR battleship_zuobiao3 %3;
            #VAR battleship_zuobiao4 %4;
            
            #REPLACE {battleship_zuobiao1} {\(} {};
            #REPLACE {battleship_zuobiao2} {\)} {};
            #REPLACE {battleship_zuobiao3} {\(} {};
            #REPLACE {battleship_zuobiao4} {\)} {};
            
            #IF {$battleship_zuobiao3 < $battleship_zuobiao1} {
                #VAR battleship_fangxiang {right};
            } {
                #IF {$battleship_zuobiao3 > $battleship_zuobiao1} {
                    #VAR battleship_fangxiang {left};
                } {
                    #IF {$battleship_zuobiao4 < $battleship_zuobiao2} {
                        #VAR battleship_fangxiang {up};
                    } {
                        #VAR battleship_fangxiang {down};
                    };
                };
            };
            
            #TICKER {timer} {
                order $battleship_fangxiang;
            } {2};
            
            #VAR idle 0;
        } {battleship_6};

        #TRIGGER {^\s*你下定决心，誓死夺回情怀岛，纵身一跃，跳上了情怀舰！} {
            #TICKER {timer1} {
                location;
            } {3};
        } {battleship_7};

        #TRIGGER {^\s*HP\:(.+)} {
            #REGEX {%1} {^(.%d*)/(%d-)/(%d-)%%/(.%d*)/(%d-)/%d-/(.%d*)/(%d-)/(%d-)%%/(.%d*)/(%d-)/%+(%d-)/(.%d-)/%d-/%d-/(%d-)%.%d%d%%/(%d-)/(%d-)/(%d-)%.%d%d%%/(%d-)/%d$} {
                #VAR jing &1;
                #VAR maxjing &2;
                #VAR hurtjing &3;
                #VAR jingli &4;
                #VAR maxjingli &5;
                #VAR qi &6;
                #VAR maxqi &7;
                #VAR hurtqi &8;
                #VAR neili &9;
                #VAR maxneili &10;
                #VAR jiali &11;
                #VAR shen &12;
                #VAR food &13;
                #VAR pot &14;
                #VAR maxpot &15;
                #VAR water &16;
                #VAR exp &17;
                
                #IF {$hurtqi < 80 || $neili < 5000} {
                    fu dan;
                };
            };
        } {battleship_8};

        #TRIGGER {^\s*你得到了(.*)枚情怀币} {
            #ECHO {<解密>:★∴} {green};
            #ECHO {<解密>:    ．．．．▍▍．..．█▍ ☆ ★∵  ..../} {green};
            #ECHO {<解密>:     ◥█▅▅██▅▅██▅▅▅▅▅███◤} {green};
            #ECHO {<解密>:     ．◥███████████████◤} {green};
            #ECHO {<解密>: ～～～～◥█████████████◤～～～～} {green};
            
            #VAR qinghuaimeng_tongbao %1;
            #ECHO {<解密>:%t <玩家:${char_id:-none}>：完成情怀岛副本，获得$qinghuaimeng_tongbao情怀币。} {yellow};
            #ECHO {<解密>:%t <玩家:${char_id:-none}>：挑战情怀岛副本结束} {white};
            
            #VAR time_s {@time_in_seconds{} - $battleship_start_time_1};
            #FORMAT {time_formatted} {%t} {$time_s};
            #ECHO {<解密>:%t <玩家:${char_id:-none}>：挑战情怀岛副本用时$time_formatted。};
            
            Close_battleship_triggers;
            changejob;
        } {battleship_9};

        #TRIGGER {^\s*(乘坐救生小艇你一路回到了长江|幸好你战前准备充分，乘坐救)} {
            #ECHO {<解密>:%t <玩家:${char_id:-none}>：情怀岛副本遭遇不可抗力中途撤退！} {red};
            changejob;
            #KILL {timer};
            #KILL {timer1};
            #KILL {timer2};
            Close_battleship_triggers;
        } {battleship_10};

        #TRIGGER {^\s*经过长时间的海上旅行，战舰} {
            #DELAY {1} {
                out;
            };
            Close_battleship_triggers;
            #TRIGGER {battleship_8} {ON};
            #TRIGGER {battleship_9} {ON};
            #TRIGGER {battleship_12} {ON};
            #TRIGGER {battleship_13} {ON};
            #TRIGGER {battleship_14} {ON};
            #TRIGGER {battleship_15} {ON};
            #TRIGGER {battleship_16} {ON};
            #TRIGGER {battleship_17} {ON};
            #TRIGGER {battleship_18} {ON};
            #KILL {timer};
            #KILL {timer1};
            #KILL {timer2};
        } {battleship_12};

        #TRIGGER {^\s(.*)海盗头目\s+(\S+)\((.*)\)} {
            #VAR killer_name %2;
            #VAR killer_id %3;
            #FORMAT killer_id {%l} {$killer_id};
            #VAR pfm_id $killer_id;
            #VAR killer_master 1;
            look $killer_id;
            
            #TRIGGER {^\s*这位海盗头目来自(.*)。} {
                #VAR attleship_party %1;
                #IF {"$attleship_fangqi_party" != ""} {
                    #IF {"$attleship_fangqi_party" == "*$attleship_party*"} {
                        #DELAY {1} {
                            #ECHO {<解密>:%t <玩家:${char_id:-none}>：挑战情怀岛，遇到放弃宗师，结束} {red};
                            Close_battleship_triggers;
                            #KILL {timer};
                            #KILL {timer1};
                            #KILL {timer2};
                            leave;
                            changejob;
                        };
                    } {
                        #DELAY {1} {
                            #IF {"$yun_pfm" != ""} {
                                $yun_pfm;
                                #DELAY {3} {
                                    yun qi;
                                    yun jing;
                                    yun jingli;
                                    #DELAY {1} {
                                        battleship_skills_2;
                                        #DELAY {1} {
                                            kill $killer_id;
                                        };
                                    };
                                };
                            };
                        };
                    };
                };
            } {battleship_20};
            
            #VAR idle 0;
        } {battleship_13};

        #TRIGGER {^\s*(\S+)(?:「啪」的一声倒在地上，挣扎着抽动了几下就死了。|一个闪身就不见了。)} {
            #IF {$killer_master > 0} {
                #REGEX {%1} {$killer_name} {
                    #ALIAS {action} {准备挑战};
                };
            } {
                kill pirate;
            };
            
            #IF {$killer_master_number == 1} {
                alias;
                leave;
            };
        } {battleship_14};

        #TRIGGER {^\s*你把\s+"action"\s+设定为\s+"准备挑战} {
            #TRIGGER {otherquest_qinghuaimeng_8} {ON};
            #VAR killer_master_number 1;
            #IF {$killer_master == 1} {
                look;
            };
        } {battleship_15};

        #TRIGGER {^\s*情怀岛$} {
            #DELAY {1} {
                battleship_skills_2;
                look;
            };
        } {battleship_16};

        #TRIGGER {^\s*你「啪」的一声倒在地上，挣扎着抽动了几下就死了。} {
            #ECHO {<解密>:%t <玩家:${char_id:-none}>：挑战情怀岛，死亡。};
            Close_battleship_triggers;
            #KILL {timer};
            #KILL {timer1};
            #KILL {timer2};
        } {battleship_17};

        #TRIGGER {^\s*血海夺岛成功,你成功获得(.*)} {
            #VAR challenge_log %1;
            #ECHO {<解密>:%t <玩家:${char_id:-none}>：情怀岛挑战成功，获得【$challenge_log】} {yellow};
        } {battleship_18};

        #TRIGGER {^\s*请明天再来执行血海夺岛任务吧} {
            #VAR now_time {@time_in_seconds{} + 24 * 60 * 60};
            #ALIAS {挑战情怀岛} {$now_time};
            #ECHO {<解密>:%t <玩家:${char_id:-none}>：情怀岛副本间隔未满24小时} {red};
            changejob;
        } {battleship_19};
    };
};

#ALIAS {battleship_skills_1} {
    #ALIAS {bei_skills} {$battleship_skills_1};
    #ALIAS {pfm} {$battleship_pfm_1};
    set wimpycmd hp ${char_id}\pfm;
    bei_skills;
};

#ALIAS {battleship_skills_2} {
    #ALIAS {bei_skills} {$battleship_skills_2};
    #ALIAS {pfm} {$battleship_pfm_2};
    set wimpycmd hp ${char_id}\pfm;
    bei_skills;
};

#ALIAS {Close_battleship_triggers} {
    #FOREACH {battleship_1;battleship_2;battleship_3;battleship_4;battleship_5;battleship_6;battleship_7;battleship_8;battleship_9;battleship_10;battleship_11;battleship_12;battleship_13;battleship_14;battleship_15;battleship_16;battleship_17;battleship_18;battleship_19;battleship_20} {trigger} {
        #UNTRIGGER {$trigger};
    };
};

#ALIAS {close_battleship_triggers} {
    Close_battleship_triggers;
};

#ALIAS {Open_battleship_triggers} {
    #FOREACH {battleship_1;battleship_2;battleship_3;battleship_4;battleship_5;battleship_6;battleship_7;battleship_8;battleship_9;battleship_10;battleship_11;battleship_12;battleship_13;battleship_14;battleship_15;battleship_16;battleship_17;battleship_18;battleship_19;battleship_20} {trigger} {
        #TRIGGER {$trigger} {ON};
    };
};

#ALIAS {open_battleship_triggers} {
    Open_battleship_triggers;
};

/* 添加辅助函数，模拟Lua中的time()函数 */
#FUNCTION {time_in_seconds} {
    #FORMAT result {%T};
    #RETURN $result;
};

#CLASS {qinghuaidao} {close}
