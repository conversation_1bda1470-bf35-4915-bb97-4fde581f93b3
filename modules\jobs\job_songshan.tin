#NOP {嵩山任务模块};
#NOP {需要放弃的杀人npc};
#LIST {conf[fangqinpc]} {create} {忽必烈;薛慕华};
#LIST {conf[fangqicomdesc]} {create} {一般高手;武林高手;深不可测;空前绝后;天神般无所伦比};
#ALIAS {jobgo_songshan} {
  checkrequest {
    #IF {"$hp[shen]" != "戾气" || $hp[shen_num] < 10000} {
      gofshen {10000} {jobgo_songshan}
    };
    #ELSE {
      gotodo {嵩山} {配天作镇坊} {startfull {jobask_songshan}}
    };
  };
};
#ALIAS {jobask_songshan} {
  gotodo {嵩山} {封禅台} {jobask_songshan_ask}
};
#ALIAS {jobask_songshan_ask} {
  #NOP {询问结果,0:成功,1:放弃,2:busy,3:空挡,4:继续任务,5:其他选项};
  #VARIABLE {askresult} {0};
  #CLASS jobrequestclass OPEN;
  #ACTION {^你向左冷禅打听有关『job』的消息。} {
    #VARIABLE {idle} {0};
    #CLASS jobresponseclass OPEN;
    #ACTION {^左冷禅说道：「对了，%*和我交情不错} {
      #VARIABLE {jobnpc_songshan} {%%%1};
    };
    #ACTION {^左冷禅说道：「你就代表我去%*邀请} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      #VARIABLE {joblocation} {%%%1};
      dohalt {parsejoblocation {$joblocation} {jobdo_songshan_invite} {jobfangqi_songshan}};
    };
    #ACTION {^左冷禅说道：「你听好了，有弟子回报%*这人对我五岳并派之举深表不满} {
      #VARIABLE {jobnpc_songshan} {%%%1};
    };
    #ACTION {^左冷禅说道：「嘿嘿，%!*在%*一带} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      #VARIABLE {joblocation} {%%%1};
      dohalt {
        #IF {@contains{{conf[fangqinpc]}{$jobnpc_songshan}} > 0} {
          jobfangqi_songshan
        };
        #ELSE {
          parsejoblocation {$joblocation} {jobdo_songshan_kill} {jobfangqi_songshan}
        };
      };
    };
    #ACTION {^左冷禅说道：「不是让你去} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {jobfangqi_songshan};
    };
    #ACTION {^左冷禅说道：「嗯，我现在忙，你别打扰。」} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {
        waitlian;
        #IF {$hp[busy][公共] > 20} {
          gotodo {嵩山} {配天作镇坊} {startfull {jobask_songshan} {2}}
        };
        #ELSE {
          gotodo {嵩山} {配天作镇坊} {startfull {jobask_songshan} {1}}
        };
      };
    };
    #ACTION {^左冷禅说道：「嗯，我现在正在思考并派大计，你别打扰。」} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {jobprepare};
    };
    #ACTION {^左冷禅说道：「%*我看你手脚散漫} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {gofshen {10000} {jobgo_songshan}};
    };
    #CLASS jobresponseclass CLOSE;
  };
  #CLASS jobrequestclass CLOSE;
  jobclear_songshan;
  execute {
    jobtimes;
    cond;
    time;
    ask zuo lengchan about job
  };
};
#ALIAS {jobdo_songshan_invite} {
  #VARIABLE {jobstart_ts} {@now{}};
  joblog {去邀请位于【$joblocation】的【$jobnpc_songshan】。};
  createpfm {@getFightPerform{{}{嵩山请}}} {1};
  jobnextroom {jobcheck_songshan {jobfight_songshan_invite}}
};
#ALIAS {jobdo_songshan_kill} {
  #VARIABLE {jobstart_ts} {@now{}};
  joblog {去杀死位于【$joblocation】的【$jobnpc_songshan】。};
  createpfm {@getFightPerform{{}{嵩山杀}}} {1};
  jobnextroom {jobcheck_songshan {jobfight_songshan_kill}}
};
#ALIAS {jobcheck_songshan} {
  #VARIABLE {jobnpc_songshan_id} {none};
  #CLASS jobcheckclass OPEN;
  #ACTION {$jobnpc_songshan(%*)} {
    #VARIABLE {jobnpc_songshan_id} {@lower{%%1}};
  };
  #ACTION {^这里没有 $jobnpc_songshan_id} {

  };
  #ACTION {^{设定环境变量：action \= \"checknpc\"|你设定checknpc为反馈信息}} {
    #VARIABLE {echots} {0};
    #VARIABLE {idle} {0};
    #CLASS jobcheckclass KILL;
    #IF {"$jobnpc_songshan_id" == "none"} {
      jobnextroom {jobcheck_songshan {%1}} {jobfangqi_songshan}
    };
    #ELSE {
      %1
    };
  };
  #CLASS jobcheckclass CLOSE;
  look;
  echo {checknpc};
};
#ALIAS {jobfight_songshan_invite} {
  #VARIABLE {jobfight_ts} {@now{}};
  #CLASS jobfightclass KILL;
  #CLASS jobfightclass OPEN;
  #ACTION {^你以本身修为判断$jobnpc_songshan的江湖历练大约是%*的级数} {
    #IF {@contains{{conf[fangqicomdesc]}{%%1}} > 0} {
      #CLASS jobfightclass KILL;
      jobfangqi_songshan
    };
    #ELSE {
      qing $jobnpc_songshan_id
    };
  };
  #ACTION {^$jobnpc_songshan{轻蔑|一脸|转过头来|嘿嘿一声}} {
    #VARIABLE {idle} {0};
    dohalt {
      execute {
        yun jing;
        yun jingli;
        qing $jobnpc_songshan_id
      };
    };
  };
  #ACTION {^你{脸色微变|向后一纵}} {
    dohalt {
      jobfangqi_songshan
    };
  };   
  #ACTION {^$jobnpc_songshan{脸色微变|向后一纵|向后退了几步}} {
    dohalt {
      execute {
        yun jing;
        yun jingli;
        qing $jobnpc_songshan_id
      };
    };
  };
  #ACTION {^你%*：承让} {
    dohalt {
      qing $jobnpc_songshan_id
    };
  };
  #ACTION {^$jobnpc_songshan_id%*：承让} {
    dohalt {
      jobfangqi_songshan
    };
  };
  #ACTION {^$jobnpc_songshan「啪」的一声倒在地上，挣扎着抽动了几下就死了。} {
    #CLASS jobfightclass KILL;
    dohalt {
      jobfangqi_songshan
    };
  };
  #ACTION {^没看见$jobnpc_songshan正忙着吗？没空理你} {
    #DELAY {0.5} {
      dohalt {
        execute {
          yun jing;
          yun jingli;
          qing $jobnpc_songshan_id
        };
      };
    };
  };
  #ACTION {^$jobnpc_songshan脸色苍白，只看了} {
    #DELAY {0.5} {
      dohalt {
        qing $jobnpc_songshan_id
      };
    };
  };
  #ACTION {^jobnpc_songshan扫了你一眼道} {
    startfight;
    autopfm;
  };
  #ACTION {^$jobnpc_songshan决定跟随你一起行动} {
    #CLASS jobfightclass KILL;
    joblog {成功邀请到【$jobnpc_songshan】，耗时@elapsed{$jobfight_ts}秒。};
    stopfight;
    dohalt {
      wwp;
      jobfinish_songshan
    };
  };
  #CLASS jobfightclass CLOSE;
  checkdanger {
    wwp;
    pfm_buff_normal;
    createpfm {$conf[pfm][scene][ss]};
    compare $jobnpc_songshan_id
  };
};
#ALIAS {jobfight_songshan_kill} {
  #VARIABLE {jobfight_ts} {@now{}};
  #VARIABLE {checkcount} {0};
  #VARIABLE {cuthead} {0};
  #CLASS jobfightclass OPEN;
  #ACTION {^$jobnpc_songshan「啪」的一声倒在地上，挣扎着抽动了几下就死了} {
    joblog {成功杀死【$jobnpc_songshan】，耗时@elapsed{$jobfight_ts}秒。};
    dohalt {
      stopfight;
      wcwp;
      qie corpse;
      echo {checkhead};
    };
  };
  #ACTION {^你扬起%*，对准$jobnpc_songshan的尸体的脖子比了比，猛斩了下去！} {
    #VARIABLE {cuthead} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkhead\"|你设定checkhead为反馈信息}} {
    #VARIABLE {echots} {0};
    #VARIABLE {idle} {0};
    #MATH {checkcount} {$checkcount + 1};
    #DELAY {2} {
      #IF {$cuthead == 1} {
        #CLASS jobfightclass KILL;
        dohalt {
          wwp;
          jobfinish_songshan {head}
        }
      };
      #ELSEIF {$checkcount > 3} {
        #CLASS jobfightclass KILL;
        jobfangqi_songshan;
      };
      #ELSE {
        wcwp;
        qie corpse;
        echo {checkhead};
      };
    }
  };
  #CLASS jobfightclass CLOSE;
  checkdanger {
    wwp;
    pfm_buff_normal;
    startfight;
    kill $jobnpc_songshan_id;
    autopfm
  };
};
#ALIAS {jobfinish_songshan} {
  gotodo {嵩山} {封禅台} {jobfinish_songshan_ask {%1}}
};
#ALIAS {jobfinish_songshan_ask} {
  #VARIABLE {rewardflag} {0};
  #VARIABLE {jobreward_exp} {0};
  #VARIABLE {jobreward_pot} {0};
  #VARIABLE {missingflag} {0};
  #VARIABLE {missingcount} {0};
  #VARIABLE {checkcount} {0};
  #CLASS jobfinishclass KILL;
  #CLASS jobfinishclass OPEN;
  #ACTION {^恭喜你！你成功的完成了嵩山任务！你被奖励了} {
    #VARIABLE {rewardflag} {1};
    #CLASS rewardclass KILL;
    #CLASS rewardclass OPEN;
    #ACTION {^%*点经验!} {
      #VARIABLE {jobreward_exp} {%%%1};
    };
    #ACTION {^%*点潜能!} {
      #CLASS rewardclass KILL;
      #VARIABLE {jobreward_pot} {%%%1};
    };
    #CLASS rewardclass CLOSE;
  };
  #ACTION {^{设定环境变量：action \= \"checkreward\"|你设定checkreward为反馈信息}} {
    #VARIABLE {echots} {0};
    #IF {$rewardflag == 0} {
      #MATH {checkcount} {$checkcount + 1};
      #IF {$checkcount > 4} {
        #IF {$missingflag == 0} {
          #VARIABLE {checkcount} {0};
          #MATH {missingcount} {$missingcount + 1};
          #IF {$missingcount > 3} {
            #CLASS jobfinishclass KILL;
            jobclear_songshan;
            jobfangqi_songshan;
          };
          #ELSE {
            sd;
            nu;
            echo {checkreward};
          };
        };
        #ELSE {
          #CLASS jobfinishclass KILL;
          jobfangqi_songshan;
        };
      };
      #ELSE {
        #DELAY {2} {echo {checkreward}};
      };
    };
    #ELSE {
      #CLASS jobfinishclass KILL;
      joblog {成功完成，获得$jobreward_exp点经验$jobreward_pot点潜能，耗时@elapsed{$jobstart_ts}秒。};
      jobclear_songshan;
      dohalt {jobprepare}
    };
  };
  #CLASS jobfinishclass CLOSE;
  #IF {"%1" != ""} {
    give %1 to zuo lengchan
  };
  echo {checkreward};
};
#ALIAS {jobfangqi_songshan} {
  gotodo {嵩山} {封禅台} {jobfangqi_songshan_ask}
};
#ALIAS {jobfangqi_songshan_ask} {
  #CLASS jobrequestclass KILL;
  #CLASS jobrequestclass OPEN;
  #ACTION {^你向左冷禅打听有关『放弃』的消息} {
    #CLASS jobresponseclass KILL;
    #CLASS jobresponseclass OPEN;
    #ACTION {^左冷禅说道：「既然你干不了也没关系，再去刻苦练功吧} {
      #CLASS jobresponseclass KILL;
      joblog {未能完成任务，耗时@elapsed{$jobstart_ts}秒};
      #NOP {因为嵩山任务放弃CD特别长，如果做雪山任务看是否能直接去等雪山};
      dohalt {jobprepare};
    };
    #ACTION {^左冷禅说道：「你没有领任务，和我嚷嚷什么？」} {
      #CLASS jobresponseclass KILL;
      dohalt {jobprepare}
    };
    #CLASS jobresponseclass CLOSE;
  };
  #CLASS jobrequestclass CLOSE;
  ask zuo lengchan about 放弃;
};
#ALIAS {jobclear_songshan} {
  #VARIABLE {jobnpc_songshan} {};
  #VARIABLE {jobnpc_songshan_id} {};
  #VARIABLE {joblocation} {};
  #LIST {jobroomlist} {clear};
  #VARIABLE {jobfight_ts} {0};
  #VARIABLE {jobnpc_count} {0};
  #VARIABLE {jobstart_ts} {0};
};