#nop {连城诀;%1:后续指令};
#ALIAS goquest_lcj {
	#VARIABLE {questmodule} {连城诀};
  #SWITCH {$questlist[$questmodule][laststep]} {
    #CASE {0} {
			lcj_chcklvjuhua {%1};
    };
    #CASE {1} {
      gotodo {襄阳城} {武馆大门} {lcj_enterwg {%1}};
    };
    #DEFAULT {
      %1;
    };
  };
};
#ALIAS {lcj_chcklvjuhua} {
	#CLASS questclass KILL;
	#CLASS questclass OPEN;
	#ACTION {^{设定环境变量：action \= \"checkflower\"|你设定checkflower为反馈信息}} {
		#CLASS questclass KILL;
		#IF {@carryqty{lv juhua} == 0} {
			gotodo {扬州城} {个园} {lcj_getyejuhua {%1}}
		};
		#ELSE {
			gotonpc {丁典} {lcj_askding {%1}};
		};
	};
	#CLASS questclass CLOSE;
	i;
	echo {checkflower};
};
#NOP {捡野菊花};
#ALIAS {lcj_getyejuhua} {
	#CLASS questclass KILL;
	#CLASS questclass OPEN;
	#ACTION {^{设定环境变量：action \= \"checkflower\"|你设定checkflower为反馈信息}} {
		#CLASS questclass KILL;
		#IF {@carryqty{ye juhua} == 0} {
			questdelay {$questmodule} {0} {1200};
			drop flower;
			%1;
		};
		#ELSE {
			gotodo {扬州城} {小盘古} {lcj_giveyejuhua {%1}};
		};
	};
	#CLASS questclass CLOSE;
	get flower;
	i;
	echo {checkflower};
};
#NOP {给菊友野菊花};
#ALIAS {lcj_giveyejuhua} {
	#CLASS questclass KILL;
	#CLASS questclass OPEN;
	#ACTION {^这里没有这个人} {
		#VARIABLE {idle} {0};
		#DELAY {6} {
			give ye juhua to ju you;
		};
	};
	#ACTION {^你身上没有这样东西} {
		#CLASS questclass KILL;
		questdelay {$questmodule} {0} {1200};
		%1;
	};
	#ACTION {^菊友说道：「想必你也没见过绿菊花罢。我家小姐闺房后面种了有，你可以去看看} {
		#CLASS questclass KILL;
		gotonpc {凌霜华} {lcj_asklsh {%1}};
	};
	#CLASS questclass CLOSE;
	give ye juhua to ju you;
};
#NOP {问凌霜华};
#ALIAS {lcj_asklsh} {
	#CLASS questclass KILL;
	#CLASS questclass OPEN;
	#ACTION {^凌霜华说道：「{啊！你是丁大哥的朋友啊|我不是告诉你了吗}} {
		#CLASS questclass KILL;
		dohalt {
			gotoroom {3642} {lcj_getlvjuhua {%1}}
		};
	};
	#CLASS questclass CLOSE;
	ask ling shuanghua about 丁典;
};
#ALIAS {lcj_getlvjuhua} {
	#CLASS questclass KILL;
	#CLASS questclass OPEN;
	#ACTION {^{设定环境变量：action \= \"checkflower\"|你设定checkflower为反馈信息}} {
		#CLASS questclass KILL;
		#IF {@carryqty{lv juhua} == 0} {
			#DELAY {6} {
				get flower;
				i;
				echo {checkflower};
			};
		};
		#ELSE {
			gotonpc {丁典} {lcj_askding {%1}};
		};
	};
	#CLASS questclass CLOSE;
	get flower;
	i;
	echo {checkflower};
};
#ALIAS {lcj_askding} {
	#CLASS questclass KILL;
	#CLASS questclass OPEN;
	#ACTION {^这里没有这个人} {
		#VARIABLE {idle} {0};
		#DELAY {6} {
			give lu juhua to ding dian;
		};
	};
	#ACTION {^你给丁典一朵绿菊花} {
		dohalt {
			ask ding dian about 狄云;
		};
	};
	#ACTION {^丁典说道%*我已经托人给霜华捎话了} {
		#CLASS questclass KILL;
		#NOP {等吧};
		questdelay {$questmodule} {0} {3600};
		dohalt {
			%1
		};
	};
	#ACTION {^丁典说道：「我也不知道狄兄弟现在在哪里} {
		#CLASS questclass KILL;
		#NOP {雪谷篇完成了};
		questupdate {$questmodule} {1};
		dohalt {
			%1;
		};
	};
	#ACTION {^丁典说道：「{今天很忙了|寻找狄兄弟也不用急在一时}} {
		#CLASS questclass KILL;
		#NOP {时间不足};
		questdelay {$questmodule} {7200} {0};
		dohalt {
			%1;
		};
	};
	#ACTION {^丁典说道：「以%*目前的经验就算去找} {
		#CLASS questclass KILL;
		#NOP {经验不足};
		questdelay {$questmodule} {0} {100000};
		dohalt {
			%1;
		};
	};
	#ACTION {^丁典说道：「%*已经去雪山寻找狄兄弟了} {
		#CLASS questclass KILL;
		#NOP {有人在做};
		questdelay {$questmodule} {7200} {0};
		dohalt {
			%1;
		};
	};
	#ACTION {^丁典说道：「当年我的确认识一个叫狄云的好兄弟，只是出狱后听说被雪山派血刀老祖抓走了} {
		#CLASS questclass KILL;
		dohalt {
			gotonpc {宝象} {
				ask bao xiang about 狄云;
				dohalt {
					gotonpc {血刀老祖} {
						ask xuedao laozu about 狄云;
						dohalt {
							gotonpc {狄云} {lcj_askdiyun {%1}};
						};
					};
				};
			};
		};
	};
	#CLASS questclass CLOSE;
	give lv juhua to ding dian;
};
#ALIAS {lcj_askdiyun} {
	#CLASS questclass KILL;
	#CLASS questclass OPEN;
	#ACTION {^你定眼一看，竟然是南四奇老二“中平枪”花铁干} {
		kill hua tiegan;
		startfight;
	};
	#ACTION {^花铁干嘿嘿叫了几声，嚷道：我先离开了} {
		stopfight;
		dohalt {
			ask di yun about 戚长发;
		};
	};
	#ACTION {^狄云说道：「我也不知道师傅现在在哪里！只是当年一场祸变，我至今也是莫名其妙} {
		dohalt {
			ask di yun about 祸变;
		}	
	};
	#ACTION {^狄云说道：「我也不知道师傅现在在哪里！而且什么时候能够离开这里，也是问题} {
		#CLASS questclass KILL;
		questupdate {$questmodule} {1};
		dohalt {
			%1;
		};
	};
	#CLASS questclass CLOSE;
	dohalt {
		give wucan yi to di yun;
	};
};
#ALIAS {lcj_enterwg} {
	#VARIABLE {okflag} {0};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^门卫疑惑地看着你道：你刚才怎么进去又出来了} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^门卫上前把手一伸：%*已经去武馆探密去了，你再等等} {
    #VARIABLE {okflag} {2};
  };
  #ACTION {^门卫上前把手一伸：不管怎样，今天很忙了} {
    #VARIABLE {okflag} {3};
  };
  #ACTION {^门卫上前把手一伸：以%*目前的经验就算进去，也做不了什么，还是抓紧练功吧} {
    #VARIABLE {okflag} {3};
  };
  #ACTION {^{设定环境变量：action \= \"checkresult\"|你设定checkresult为反馈信息}} {
    #CLASS questclass KILL;
    #SWITCH {$okflag} {
      #CASE {0} {
        loc {lcj_wgdo {%1}};
      };
      #CASE {1} {
        questfail {$questmodule};
				%1;
      };
      #CASE {2} {
				questdelay {$questmodule} {0} {3600};
				%1;
			};
      #CASE {3} {
				questdelay {$questmodule} {} {7200};
				%1;
			};
    };
  };
  #CLASS questclass CLOSE;
  enter;
  echo {checkresult};
};
#ALIAS {lcj_wgdo} {
	#VARIABLE {okflag} {0};
	#CLASS questclass KILL;
	#CLASS questclass OPEN;
	#ACTION {^万震山说道：「恩，当年他师傅妄想刺杀我，而狄云更是和贱女桃红苟合，送他去监狱已经是优待他了。」} {
		#VARIABLE {idle} {0};
		dohalt {ask wan about 戚长发};
	};
	#ACTION {^万震山说道：「当年戚长发妄想刺杀我，幸亏我发现得及时。这些年，也不知道他哪里去了。」} {
		#VARIABLE {idle} {0};
		dohalt {gotodo {武馆} {物品房} {ask wu about 镐头}};
	};
	#ACTION {^吴坎交给你一把镐头。} {
		#VARIABLE {idle} {0};
		dohalt {gotodo {武馆} {破祠堂} {ask qifu about 砌墙}};
	};
	#ACTION {^中年乞妇说道：「当年肯定是老爷亏心事做多了，又是杀害戚老头，又是陷害狄什么云。」} {
		#VARIABLE {idle} {0};
		dohalt {zuan dong}
	};
	#ACTION {^你{已经发现这里的秘密了|竟然发现墙壁竟然内有中空}} {
		#VARIABLE {okflag} {1};
	};
	#ACTION {^你萎在一个角落里，竟然找到一个黑乎乎的洞口。} {
		#VARIABLE {idle} {0};
		dohalt {
			wa qiang;
			echo {checkqiang}
		};
	};
	#ACTION {^{设定环境变量：action \= \"checkqiang\"|你设定checkqiang为反馈信息}} {
		#VARIABLE {idle} {0};
		dohalt {
			#IF {$okflag == 0} {
				wa qiang;
				echo {checkqiang}
			};
			#ELSE {
				zuan dong;
				dohalt {
					gotodo {武馆} {睡房} {ask qi about 狄云};
				}
			};
		};
	};
	#ACTION {^戚芳说道：「恩，当年的事情发生的确太突然了，其实我也觉得狄云不象那种人。」} {
		#VARIABLE {idle} {0};
		dohalt {ask qi about 唐诗选辑}
	};
	#ACTION {^戚芳说道：「恩，好像有这么个名字。可是我有点忘记在哪里了。」} {
		#VARIABLE {idle} {0};
		dohalt {ask qi about 鞋样}
	};
	#ACTION {^戚芳说道：「我好像顺手隔在书房里。只是也不知道现在狄云怎么样了。」} {
		#VARIABLE {idle} {0};
		dohalt {
			gotodo {武馆} {书房} {search book}
		}
	};
	#ACTION {^你急忙收拾好书籍，偷偷离开武馆。} {
		#VARIABLE {idle}{0};
		loc {
			dohalt {
				gotodo {扬州城} {小吃店} {
					buy jiudai;
					i;
					echo {checkjiudai};
				};
			};
		};
	};
	#ACTION {^{设定环境变量：action \= \"checkjiudai\"|你设定checkjiudai为反馈信息}} {
		#DELAY {1} {
			#IF {@carryqty{jiu dai} == 0} {
				buy jiudai;
				i;
				echo {checkjiudai};
			};
			#ELSE {
				gotodo {扬州城}{茶馆}{fill jiudai};
			};
		};
	};
	#ACTION {^你将牛皮酒袋装满。} {
		#VARIABLE {idle} {0};
		dohalt {
			gotodo {扬州城} {406} {jinpao xuanji in jiudai};
		};
	};
	#ACTION {^这里{正巧|竟然}有一个} {
		#VARIABLE {idle} {0};
		#DELAY {2} {
			jinpao xuanji in jiudai
		};
	};
	#ACTION {^一会的功夫，唐诗选辑上竟然显示一些数字出来。} {
		#VARIABLE {idle} {0};
		dohalt {
			drop jiudai;
			gotonpc {狄云} {dohalt {give di xuanji}}
		}
	};
	#ACTION {^狄云在你的耳边悄声说道：当年凡是与宝藏有关的人只怕都被间接害死了。} {
		#CLASS questclass KILL;
		questupdate {$questmodule} {2};
		dohalt {
			%1;
		};
	}; 
	#CLASS questclass CLOSE;
	#IF {"$city" == "武馆"} {
		gotodo {武馆} {冬暖阁} {ask wan about 狄云}
	};
	#ELSE {
		#CLASS questclass KILL;
		questdelay {$questmodule} {} {7200};
		%1;
	};
};