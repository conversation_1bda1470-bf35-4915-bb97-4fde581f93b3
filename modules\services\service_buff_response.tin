#NOP {初始化启动资金响应};
#ALIAS {initbuffservice} {
  #CLASS servicebuffclass KILL;
  #CLASS servicebuffclass OPEN;
  #ACTION {^%*(%*)告诉你：buff_request} {
    buff_accept {@lower{%%2}} {%%1}
  };
  #ACTION {^! %*(%*)告诉你：buff_request} {
    buff_accept {@lower{%%2}} {%%1}
  };
  #ACTION {^%*(%*)告诉你：buff_cancel} {
    #VARIABLE {caller} {};
  };
  #ACTION {^! %*(%*)告诉你：buff_cancel} {
    #VARIABLE {caller} {};
  };
  #CLASS servicebuffclass CLOSE;
};
#NOP {注册启动资金请求,%1:id,%2:name};
#ALIAS {buff_accept} {
  #NOP {超过五分钟重置};
  #IF {"$caller" != "" && @elapsed{$caller[timestamp]} > 300} {
    #VARIABLE {caller} {};
  };
  #NOP {更新请求};
  #IF {"$caller" != "" && "$caller[id]" != "%1"} {
    tell %1 buff_wait
  };
  #ELSE {
    #VARIABLE {caller} {
      {request} {buff}
      {timestamp} {@now{}}
      {id} {%1}
      {name} {%2}
    };
    tell %1 buff_come
  };
};
#NOP {去扬州城墓室响应,%1:后续指令};
#ALIAS {buff_response} {
  gotodo {长安城}{小雁塔}{buff_response_start {%1}}
};

#NOP {检查呼叫者,%1:后续指令};
#ALIAS {buff_response_start} {
  #VARIABLE {checkcount} {0};
  #VARIABLE {okflag} {0};
  #CLASS serviceclass KILL;
  #CLASS serviceclass OPEN;
  #ACTION {^你要对谁做这个动作？} {
    #MATH {checkcount} {$checkcount + 1};
  };
  #ACTION {^你轻轻地拍了拍$caller[name]的头。} {
    #VARIABLE {checkcount} {0};
  };
  #ACTION {^你悄退数步，右手支颐，左手轻轻挥出} {
  };
  #ACTION {^{设定环境变量：action \= \"checkcaller\"|你设定checkcaller为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {$checkcount == 0} {
      #VARIABLE {okflag} {0};
    };
    #ELSEIF {$checkcount < 60} {
      #DELAY {2} {
        pat $caller[id];
        echo {checkcaller};
      };
    };
    #ELSE {
      #CLASS serviceclass KILL;
      #VARIABLE {caller} {};
      %1;
    };
  };

  #ACTION {^如果你愿意和对方比试武艺，请你也} {
    #VARIABLE {idleroom[count]} {0};
    closewimpy;
    jiali 0;
    uwwp;
    unset 玉女心经;
    bei none;
    jifa cuff meinu-quanfa;
    bei cuff;
    fight $caller[id];
  };

  #ACTION {^你%*「{貂禅拜月|西施捧心|昭君出塞|麻姑献寿|天女织梭|则天垂帘|丽华梳妆|红玉击鼓|弄玉吹箫|文姬归汉}」} {
    #VARIABLE {idle} {0};
  };
  #ACTION {^你向后一纵，躬身做揖说道：阁下武艺不凡，果然高明！}{
    doheal {fight $caller[id]}
  };

  #ACTION {^$caller[name]对你抱拳道：“青山不改，绿水常流，咱们后会有期！”} {
    #CLASS serviceclass KILL;
    #VARIABLE {caller} {};
    openwimpy;
    dohalt {
      set 玉女心经;
      jobprepare;
    }
  };
  #CLASS serviceclass CLOSE;
  pat $caller[id];
  echo {checkcaller};
};
initbuffservice;
