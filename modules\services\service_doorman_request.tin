#NOP {护卫模块};
#NOP {呼叫门童,%1:钥匙,%2:后续指令};
#ALIAS {doorman_call} {
  doorman_call_start {%1} {%2}
};
#NOP {呼叫门童,%1:钥匙,%2:后续指令};
#ALIAS {doorman_call_start} {
  #CLASS serviceclass KILL;
  #CLASS serviceclass OPEN;
  #ACTION {^没有这个人。} {
    #CLASS serviceclass KILL;
    #SHOWME {<faa>保姆$conf[nanny][doorman]人不在};
    %2
  };
  #ACTION {%*(%*)告诉你：doorman_wait} {
    #CLASS serviceclass KILL;
    #VARIABLE {idle} {0};
    #DELAY {5} {doorman_call {%1} {%2}};
  };
  #ACTION {%*(%*)告诉你：doorman_come} {
    #VARIABLE {waiter} {
      {id} {@lower{%%2}}
      {name} {%%1}
    };
  };
  #CLASS serviceclass CLOSE;
  #IF {"$conf[nanny][doorman]" == ""} {
    #SHOWME {<faa>未配置doorman保姆,请自行处理};
    %2
  };
  #ELSE {
    tell $conf[nanny][doorman] doorman_request_%1
  };
};
#NOP {取消开门,%1:后续指令};
#ALIAS {doorman_cancel} {
  #IF {"$conf[nanny][doorman]" != ""} {
    tell $conf[nanny][doorman] doorman_cancel
  };
};