#NOP {冷泉神功,%1:后续指令};
#ALIAS {goquest_lqsg} {
  #VARIABLE {questmodule} {冷泉神功};
  #SWITCH {"$questlist[$questmodule][laststep]"} {
    #CASE {"0"} {
      gotonpc {苗人凤} {lqsg_askmiao {%1}}
    };
    #CASE {"1"} {
      gotonpc {苗人凤} {lqsg_getyupei {%1}}
    };
    #DEFAULT {%1};
  };
};
#ALIAS {lqsg_askmiao} {
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你向苗人凤打听有关『冷泉神功』的消息。} {
    #CLASS questresponseclass OPEN;
    #ACTION {^苗人凤说道：「冷泉神功是闯王时期四大护卫的招牌功夫} {
      #NOP {刚招待过玩家,等会再来};
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questdelay {$questmodule} {0} {10800};
      dohalt {%1}
    };
    #ACTION {^苗人凤说道：「这位%*看来还需要努力一番才行} {
      #NOP {刚招待过玩家,等会再来};
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questfail {$questmodule};
      dohalt {%1}
    };
    #ACTION {^苗人凤说道：「这位%*，如若将苗家玉佩找回来} {
      #NOP {成功};
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questupdate {$questmodule} {1};
      #SEND {shout 哈哈哈，我开了$questmodule，羡慕嫉妒恨吧！！！};
      dohalt {%1}
    };
    #ACTION {^苗人凤说道：「冷泉神功是胡家刀和苗家剑的基础内功，也只有冷泉神功才能最大限度发挥刀剑的威力。」} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questfail {$questmodule};
      dohalt {%1}
    };
    #CLASS questresponseclass CLOSE;
  };
  #CLASS questclass CLOSE;
  dohalt {
    pray pearl;
    ask miao renfeng about 冷泉神功;
  }
};
#NOP {寻找玉佩走宝藏流程};
#ALIAS {lqsg_getyupei} {
  #VARIABLE {questmodule} {冷泉神功};
  #VARIABLE {checkcount} {0};
  #VARIABLE {winflag} {0};
  #VARIABLE {askresult} {0};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你向苗人凤打听有关『闯王宝藏』的消息。} {
    #CLASS questresponseclass OPEN;
    #ACTION {^苗人凤说道：「你连我都赢不了，如何独力寻找宝藏。我这也算是为了你好。」} {
      #NOP {失败三次后询问时3/4几率有这个回复，继续问};
      #VARIABLE {askresult} {0};
    };
    #ACTION {^苗人凤说道：「宝藏图不在我这里，真是老了糊涂了} {
      #VARIABLE {askresult} {2};
    };
    #ACTION {^苗人凤交给你一张宝藏图。} {
      #VARIABLE {askresult} {1};
    };
    #ACTION {^苗人凤说道：「李闯王当年确实威震朝野，可惜时不逢时} {
      #VARIABLE {askresult} {1};
    };
    #CLASS questresponseclass CLOSE;
  };
  #ACTION {^{设定环境变量：action \= \"checkbaozang\"|你设定checkbaozang为反馈信息}} {
    #CLASS questresponseclass KILL;
    #MATH {checkcount} {$checkcount + 1};
    dohalt {
      #IF {$askresult == 1} {
        #CLASS questclass KILL;
        #IF {@carryqty{baozang tu} > 0} {
          gotonpc {胡斐} {xsfh_baozang_baodao {%1} {1}};
        };
        #ELSE {
          questdelay {$questmodule} {0} {7200};
        %1;
        };
      };
      #ELSEIF {$askresult == 2} {
        #CLASS questclass KILL;
        questdelay {$questmodule} {0} {3600};
        %1;
      };
      #ELSEIF {$askresult == 3} {
        #CLASS questclass KILL;
        xsfh_baozang_fight {%1};
      };
      #ELSE {
        ask miao renfeng about 闯王宝藏;
        i;
        echo {checkbaozang};
      };
    }
  };
  #CLASS questclass CLOSE;
  ask miao renfeng about 闯王宝藏;
  i;
  echo {checkbaozang};
};
#NOP {归还玉佩};
#ALIAS {lqsg_giveyupei} {
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你在苗人凤的耳边悄声说道：我刚才去过闯王宝藏，在那里捡到的} {
    #CLASS questclass KILL;
    dohalt {%1}
  };
  #CLASS questclass CLOSE;
  give yu pei to miao renfeng
};