#NOP {全真剑诀,%1:后续指令};
#ALIAS {goquest_qzjj} {
  #VARIABLE {questmodule} {全真剑诀};
  #IF {@carryqty{qixing jian} == 0} {
    gotodo {全真教} {后山山洞} {qzjj_qixing {%1}}
  };
  #ELSE {
    gotonpc {丘处机} {qzjj_qingjiao {%1}}
  };
};
#NOP {拿七星剑,%1:后续指令};
#ALIAS {qzjj_qixing} {
  #VARIABLE {questmodule} {全真剑诀};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你看着重阳真人的画像，想象着他傲视群雄睥睨天下的绝代风采，不由得拜了一拜。} {
    #CLASS questclass KILL;
    #NOP {时间不够};
    questdelay {$questmodule} {0} {7200};
    runwait {%1}
  };
  #ACTION {^你恭恭敬敬地在神案前的团蒲上跪了下来，对着祖师画像磕了三个响头。} {
    dohalt {
      ketou 宝剑;
      i;
      echo {checkqixing};
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkqixing\"|你设定checkqixing为反馈信息}} {
    #CLASS questclass KILL;
    #IF {@carryqty{qixing jian} == 0} {
      #NOP {可能在别人手里};
      questsuccess {$questmodule};
      %1
    };
    #ELSE {
      gotonpc {丘处机} {qzjj_qingjiao {%1}}
    };
  };
  #CLASS questclass CLOSE;
  ketou 宝剑
};
#NOP {请教,%1:后续指令};
#ALIAS {qzjj_qingjiao} {
  #VARIABLE {questmodule} {全真剑诀};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checktime\"|你设定checktime为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {$env[gametime] >= 19 || $env[gametime] < 5} {
      ask qiu chuji about 剑诀
    };
    #ELSEIF {$env[gametime] >= 5 && $env[gametime] <= 15} {
      #CLASS questclass KILL;
      questdelay {$questmodule} {1200};
      dohalt {%1}
    };
    #ELSE {
      #DELAY {6} {
        time;
        echo {checktime};
      };
    };
  };
  #ACTION {^你向丘处机打听有关『剑诀』的消息。} {
    #CLASS questresponseclass OPEN;
    #ACTION {^丘处机说道：「我现在无法看见天空中北斗七星，又如何指导你学习全真剑法的剑诀精髓} {
      #CLASS questresponseclass KILL;
      echo {checktime};
    };
    #ACTION {^丘处机说道：「%*你的剑诀造诣已经不在本道之下了} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questsuccess {$questmodule};
      dohalt {
        drop qixing jian;
        %1
      };
    };
    #ACTION {^丘处机说道：「嗯？你不是刚来请教过我剑诀吗？还是再努力段时间吧！} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questdelay {$questmodule} {} {7200};
      dohalt {
        drop qixing jian;
        %1
      };
    };
    #ACTION {^丘处机说道：「%*我刚才不是已经回答你了吗？} {
      #CLASS questresponseclass KILL;
      dohalt {
        qingjiao
      };
    };
    #ACTION {^丘处机严肃的看着你，慢慢说道：“我派的全真剑法讲究的是道家观测宇宙星斗北斗七星变换之道，取千变万化、无穷无尽之原理} {
      #CLASS questresponseclass KILL;
      dohalt {
        pray pearl;
        qingjiao
      }
    };
    #CLASS questresponseclass CLOSE;
  };
  #ACTION {^你{现在状态不佳|现在重伤在身}} {
    #VARIABLE {idle} {0};
    doheal {
      yun qi;
      qingjiao
    };
  };
  #ACTION {^恭喜你，你于%*领悟了全真剑诀。} {
    #CLASS questclass KILL;
    questsuccess {$questmodule};
    dohalt {
      %1
    };
  };
  #ACTION {^你摇了摇头，道：“好像还是很困难的} {
    #CLASS questclass KILL;
    questfail {$questmodule};
    dohalt {
      doquit
    };
  };
  #CLASS questclass CLOSE;
  time;
  echo {checktime};
};