#NOP {迷宫处理模块};
#NOP {无量山大松林,这个迷宫各个房间的出口除特定房间外均随机，有可能走不出去};
#ALIAS {matrix_wls_dsl} {
  #VARIABLE {matrixts} {@now{}};
  #VARIABLE {checkcount} {0};
  #VARIABLE {exploreroom} {};
  #LIST {matrixsteps} {create} {n;w;e;s};
  #LIST {tempsteps} {clear};
  #VARIABLE {rightstep} {};
  #CLASS matrixclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #VARIABLE {walkstoppedts} {0};
      #CLASS matrixclass KILL
    };
    #ELSEIF {@elapsed{$matrixts} > 60} {
      #CLASS matrixclass KILL;
      #NOP {超时还出不去只能退出};
      dohalt {quit}
    };
    #ELSEIF {"$room" == "大松林"} {
      execute {yun jingli;e;n;s;n;w;e};
      #MATH {checkcount} {$checkcount + 1};
      #DELAY {0.5} {
        #IF {$checkcount <= 4} {
          look;
          echo {checkmatrix};
        };
        #ELSE {
          #NOP {进入探索模式};
          look;
          echo {checkexplore};
        };
      };
    };
    #ELSE {
      #CLASS matrixclass KILL;
      loc {walk};
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkexplore\"|你设定checkexplore为反馈信息}} {
    #VARIABLE {idle} {0};
    #VARIABLE {exploreroom} {$room};
    #LIST {tempsteps} {clear};
    #VARIABLE {rightstep} {};
    #LOOP 1 4 {i} {
      #IF {"$roomways[+$i]" != "大松林"} {
        #CONTINUE;
      };
      look @longDir{$matrixsteps[+$i]};
      echo {checkdirect$matrixsteps[+$i]}
    };
    echo {checkdirect};
  };
  #ACTION {^设定环境变量：action \= \"checkdirect%*\"} {
    #IF {"%%1" == ""} {
      #IF {"$rightstep" != "" || "$exploreroom" != "大松林"} {
        $rightstep;
        loc {walk};
      };
      #ELSE {
        #DELAY {0.5} {
          $tempsteps[+@rnd{{1}{&tempsteps[]}}];
          look;
          echo {checkexplore};
        };
      };
    };
    #ELSE {
      #IF {@contains{{roomways}{大瀑布}} > 0} {
        #VARIABLE {rightstep} {%%1};
      };
      #ELSE {
        #LIST {tempsteps} {add} {%%1};
      };
    };
  };
  #ACTION {^你设定checkdirect%*为反馈信息} {
    #IF {"%%1" == ""} {
      #IF {"$rightstep" != "" || "$exploreroom" != "大松林"} {
        $rightstep;
        loc {walk};
      };
      #ELSE {
        #DELAY {0.5} {
          $tempsteps[+@rnd{{1}{&tempsteps[]}}];
          look;
          echo {checkexplore};
        };
      };
    };
    #ELSE {
      #IF {@contains{{roomways}{大瀑布}} > 0} {
        #VARIABLE {rightstep} {%%1};
      };
      #ELSE {
        #LIST {tempsteps} {add} {%%1};
      };
    };
  };
  #CLASS matrixclass CLOSE;
  echo {checkmatrix} {2};
};
#NOP {无量山山路};
#ALIAS {matrix_wls_shanlu} {
  #VARIABLE {trycount} {0};
  #CLASS matrixclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"$room" == "山中小溪"} {
      loc {walk}
    };
    #ELSE {
      #MATH {trycount} {$trycount + 1};
      #IF {$trycount > 4} {
        #IF {@rnd{{1}{2}} == 1} {
          e
        };
        #ELSE {
          w
        };
      };
      execute {yun qi;#5 s};
      #DELAY {0.5} {echo {checkmatrix}}
    };
  };
  #CLASS matrixclass CLOSE;
  echo {checkmatrix} {2};
};

#NOP {青城沙漠进};
#ALIAS {matrix_qc_in} {
  #CLASS matrixclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"$room" == "荒路"} {
      #CLASS matrixclass KILL;
      loc {walk};
    };
    #ELSE {
      #IF {"$room" == "青城"} {
        ne;
        s;
      };
      #ELSEIF {@contains{{roomexits}{nu}}} {
        nu;
      };
      #ELSE {
        s;
      };
      #DELAY {0.2} {
        echo {checkmatrix}
      };
    };
  };
  #CLASS matrixclass CLOSE;
  echo {checkmatrix} {2};
};

#NOP {青城沙漠出};
#ALIAS {matrix_qc_out} {
  #CLASS matrixclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"$room" != "沙漠"} {
      #CLASS matrixclass KILL;
      loc {walk};
    };
    #ELSE {
      #4 s;
      #DELAY {0.5} {
        echo {checkmatrix};
      };
    };
  };
  #CLASS matrixclass CLOSE;
  echo {checkmatrix} {2};
};
#NOP {绝情谷竹林进};
#ALIAS {matrix_jqg_zhulin} {
  #LIST {matrixsteps} {create} {n;s;w;e};
  #VARIABLE {errorcount} {0};
  #VARIABLE {stepflag} {0};
  #VARIABLE {rightstep} {};
  #VARIABLE {matrixts} {@now{}};
  #CLASS matrixclass KILL;
  #CLASS matrixclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkexplore\"|你设定checkexplore为反馈信息}} {
    resonate {checkexplore};
    #LOCAL {tempdirect} {@contains{{roomexits}{wd}}};
    #IF {$tempdirect == 0} {
      #LOCAL {tempdirect} {@contains{{roomexits}{su}}};
    };
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {@elapsed{$matrixts} > 60} {
      #CLASS matrixclass KILL;
      #NOP {超时还出不去只能退出};
      dohalt {quit}
    };
    #ELSEIF {"$room" != "竹林"} {
      #CLASS matrixclass KILL;
      loc {walk};
    };
    #ELSEIF {$tempdirect == 0} {
      #VARIABLE {rightstep} {};
      #LOOP 1 4 {i} {
        look @longDir{$matrixsteps[+$i]};
        echo {checkdirect$matrixsteps[+$i]};
      };
      echo {checkdirect};
    };
    #ELSE {
      #CLASS matrixclass KILL;
      #DELAY {0.5} {
        loc {walk};
      };
    };
  };
  #ACTION {^设定环境变量：action \= \"checkdirect%*\"} {
    #IF {"%%1" == ""} {
      #IF {"$rightstep" != ""} {
        $rightstep;
        set action checkexplore;
      };
      #ELSE {
        #IF {$stepflag == 0} {
          #VARIABLE {stepflag} {1};
          e
        };
        #ELSE {
          #VARIABLE {stepflag} {0};
          $matrixsteps[+@rnd{{1}{4}}];
        };
        #DELAY {0.5} {echo {checkexplore};}
      };
    };
    #ELSE {
      #IF {@contains{{roomexits}{wd}} > 0 || @contains{{roomexits}{su}} > 0} {
        #VARIABLE {rightstep} {%%1};
      };
    };
  };
  #ACTION {^你设定checkdirect%*为反馈信息} {
    #IF {"%%1" == ""} {
      #IF {"$rightstep" != ""} {
        $rightstep;
        set action checkexplore;
      };
      #ELSE {
        #IF {$stepflag == 0} {
          #VARIABLE {stepflag} {1};
          e
        };
        #ELSE {
          #VARIABLE {stepflag} {0};
          $matrixsteps[+@rnd{{1}{4}}];
        };
        #DELAY {0.5} {echo {checkexplore};}
      };
    };
    #ELSE {
      #IF {@contains{{roomexits}{wd}} > 0 || @contains{{roomexits}{su}} > 0} {
        #VARIABLE {rightstep} {%%1};
      };
    };
  };
  #CLASS matrixclass CLOSE;
  echo {checkexplore} {2};
};
#NOP {杭州长廊};
#ALIAS {matrix_changlang} {
  #CLASS matrixclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"$room" == "长廊"} {
      execute {n;s;e;w};
      #DELAY {0.5} {
        echo {checkmatrix}
      }; 
    };
    #ELSE {
      #CLASS matrixclass KILL;
      loc {walk};
    };
  };
  #CLASS matrixclass CLOSE;
  execute {n;s;e;w};
  echo {checkmatrix} {2};
};
#NOP {玄铁剑树林};
#ALIAS {matrix_xtj_shulin} {
  #VARIABLE {checkcount} {0};
  #CLASS matrixclass KILL;
  #CLASS matrixclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"$room" != "树林"} {
      #CLASS matrixclass KILL;
      loc {walk};
    };
    #ELSE {
      #MATH {checkcount} {$checkcount + 1};
      #IF {$checkcount > 5} {
        #LOCAL {direct} {w};
        #IF {@rnd{{0}{1}} == 1} {
          #LOCAL {direct} {e};
        };
        $direct;
        #VARIABLE {checkcount} {0};
      };
      execute {n;e;n;e;w;s;n;s;s;n;s};
      #DELAY {0.5} {echo {checkmatrix}}
    };
    #ELSEIF {$checkcount <= 5} {
      #MATH {checkcount} {$checkcount + 1};
      #5 s;
      #DELAY {0.5} {echo {checkmatrix}}
    };
  };
  #CLASS matrixclass CLOSE;
  echo {checkmatrix} {2};
};
#NOP {峨嵋山古德林};
#ALIAS {matrix_em_gdl} {
  #CLASS matrixclass KILL;
  #CLASS matrixclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"$room" != "古德林"} {
      #CLASS matrixclass KILL;
      loc {walk};
    };
    #ELSEIF {@contains{{roomways}{白龙洞}} > 0} {
      #CLASS matrixclass KILL;
      loc {walk};
    };
    #ELSE {
      #DELAY {0.5} {
        execute {n;s};
        echo {checkmatrix};
      };
    };
  };
  #CLASS matrixclass CLOSE;
  execute {n;s};
  echo {checkmatrix} {2};
};
#NOP {峨嵋山灌木丛};
#ALIAS {matrix_em_gmc} {
  #CLASS matrixclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {&roomexits[] == 1} {
      #CLASS matrixclass KILL;
      loc {walk}
    };
    #ELSEIF {$hp[neili] < 500} {
      #MATH {tempvalue} {$hp[qixue] - 100};
      dazuo $tempvalue
    };
    #ELSE {
      execute {#3 ne;yun jingli;hp};  
      #DELAY {0.5} {
        echo {checkmatrix};
      };
    };
  };
  #ACTION {^{$dazuo_over}} {
    execute {yun qi;hp};
    echo {checkmatrix};
  };
  #CLASS matrixclass CLOSE;
  execute {#3 ed};
  echo {checkmatrix} {2};
};
#NOP {峨嵋冷杉林};
#NOP {一共三个迷宫房间，从任意房间走nw;sw;se均可以走到连接接引殿的八十四盘房间};
#ALIAS {matrix_em_lsl} {
  #LIST {matrixstep} {create} {nw;sw;se};
  #VARIABLE {tempindex} {1};
  #CLASS matrixclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"$room" != "冷杉林"} {
      #CLASS matrixclass KILL;
      loc {walk};
    };
    #ELSE {
      #MATH {tempindex} {$tempindex + 1};
      #IF {$tempindex > &matrixstep[]} {
        #VARIABLE {tempindex} {1};
      };
      #SEND {$matrixstep[+$tempindex]};
      #DELAY {0.2} {
        echo {checkmatrix}
      };
    };
  };
  #CLASS matrixclass CLOSE;
  #SEND {$matrixstep[+$tempindex]};
  echo {checkmatrix} {2};
};
#NOP {峨嵋小竹林};
#ALIAS {matrix_em_xzl} {
  #VARIABLE {matrixdo} {};
  #VARIABLE {matrixflag} {1};
  #CLASS matrixclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSE {
      s;
      #DELAY {0.2} {
        echo {checkmatrix}
      };
    }
  };
  #ACTION {^一股暖流发自丹田流向全身，慢慢地你又恢复了知觉……} {
    #NOP {这里不做任何冷杉林房间索引的判断直接跑路};
    #CLASS matrixclass KILL;
    loc {walk};
  };
  #CLASS matrixclass CLOSE;
  s;
  echo {checkmatrix} {2};
};
#NOP {峨嵋出九老洞};
#ALIAS {matrix_em_jld} {
  #VARIABLE {matrixdo} {};
  #CLASS matrixclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"$room" == "九老洞口"} {
      #CLASS matrixclass KILL;
      loc {walk};
    };
    #ELSE {
      #DELAY {0.5} {
        drop fire;
        #5 leave;
        echo {checkmatrix}
      };
    };
  };
  #CLASS matrixclass CLOSE;
  echo {checkmatrix};
};
#NOP {峨嵋九老洞去周芷若};
#ALIAS {matrix_em_zzr} {
  #VARIABLE {matrixdo} {};
  #CLASS matrixclass KILL;
  #CLASS matrixclass OPEN;
  #ACTION {^你手中没有火折，怎么能进的了山洞} {
    #CLASS matrixclass KILL;
    runwait {
      loc {buyfire {gotoroom {$aimroomid} {$aimdo}}}
    };
  };
  #ACTION {^你点燃火折，把洞内照亮了一些} {
    #CLASS matrixclass KILL;
    runwait {
      i;
      execute {n;s;w;e;nw;ne;sw;se;out};
      loc {walk}
    };
  };
  #CLASS matrixclass CLOSE;
  use fire
};
#NOP {华山松树林};
#ALIAS {matrix_hs_ssl} {
  #CLASS matrixclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"$room" != "松树林" || @contains{{roomways}{石屋}} > 0} {
      #CLASS matrixclass KILL;
      loc {walk}
    };
    #ELSEIF {@contains{{roomways}{空地}} > 0} {
      n;
      e;
      look;
      #DELAY {0.2} {echo {checkmatrix}};
    };
    #ELSE {
      e;
      look;
      #DELAY {0.2} {echo {checkmatrix}};
    };
  };
  #CLASS matrixclass CLOSE;
  ensure {look} {checkmatrix}
};
#NOP {华山村菜地};
#ALIAS {matrix_hsc_cd} {
  #CLASS matrixclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"$room" == "村中心"} {
      #CLASS matrixclass KILL;
      loc {walk};
    };
    #ELSE {
      s;
      #DELAY {0.2} {echo {checkmatrix}};
    };
  };
  #CLASS matrixclass CLOSE;
  echo {checkmatrix} {2};
};
#NOP {武当山小径进};
#ALIAS {matrix_wdsxj_in} {
  #VARIABLE {errorcount} {0};
  #LIST {matrixsteps} {create} {n;s;w;e};
  #VARIABLE {matrixstep} {};
  #VARIABLE {checkcount} {0};
  #CLASS matrixclass KILL;
  #CLASS matrixclass OPEN;
  #ACTION {^你站在小径上，四周打量，仿佛看见%%u面有些亮光。} {
    #SWITCH {"%%1"} {
      #CASE {"北"} {#VARIABLE {matrixstep} {n}};
      #CASE {"西"} {#VARIABLE {matrixstep} {w}};
      #CASE {"南"} {#VARIABLE {matrixstep} {s}};
      #CASE {"东"} {#VARIABLE {matrixstep} {e}};
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #VARIABLE {idle} {0};
    #MATH {checkcount} {$checkcount + 1};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"$room" == "院门" || "$room" != "小径"} {
      #CLASS matrixclass KILL;
      loc {walk};
    };
    #ELSEIF {"$matrixstep" != ""} {
      $matrixstep;
      #VARIABLE {matrixstep} {};
      #VARIABLE {checkcount} {0};
      #DELAY {0.5} {
        echo {checkmatrix}
      };
    };
    #ELSEIF {$checkcount <= 10} {
      #DELAY {1} {
        echo {checkmatrix}
      };
    };
    #ELSE {
      #MATH {errorcount} {$errorcount + 1};
      #IF {$errorcount >= 3} {
        #CLASS matrixclass KILL;
        doabort;
      };
      #ELSE {
        #VARIABLE {checkcount} {0};
        $matrixsteps[+@rnd{{2}{4}}];
        #DELAY {0.5} {
          echo {checkmatrix}
        };
      };
    };
  };
  #CLASS matrixclass CLOSE;
  s;
  echo {checkmatrix} {2};
};
#NOP {武当山小径出};
#ALIAS {matrix_wdsxj_out} {
  #CLASS matrixclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"$room" == "三清殿"} {
      #CLASS matrixclass KILL;
      loc {walk};
    };
    #ELSE {
      n;
      #DELAY {0.2} {echo {checkmatrix}};
    };
  };
  #CLASS matrixclass CLOSE;
  echo {checkmatrix} {2};
};
#NOP {终南山黑林进};
#ALIAS {matrix_heilin_in} {
  #VARIABLE {matrixdo} {};
  #CLASS matrixclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"$room" != "黑林"} {
      #CLASS matrixclass KILL;
      loc {walk};
    };
    #ELSE {
      e;
      #DELAY {0.2} {echo {checkmatrix}};
    };
  };
  #CLASS matrixclass CLOSE;
  echo {checkmatrix} {2};
};

#NOP {终南山黑林出};
#ALIAS {matrix_heilin_out} {
  #VARIABLE {matrixdo} {};
  #CLASS matrixclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"$room" != "黑林"} {
      #CLASS matrixclass KILL;
      loc {walk};
    };
    #ELSE {
      w;
      #DELAY {0.2} {echo {checkmatrix}};
    };
  };
  #CLASS matrixclass CLOSE;
  echo {checkmatrix} {2};
};
#NOP {杭州柳林};
#ALIAS {matrix_hzll} {
  #VARIABLE {matrixdo} {};
  #CLASS matrixclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"$room" != "柳林"} {
      #CLASS matrixclass KILL;
      loc {walk};
    };
    #ELSE {
      #IF {@contains{{roomexits}{w}} == 0} {
        #CLASS matrixclass KILL;
        loc {walk};
      };
      #ELSE {
        n;
      #DELAY {0.2} {echo {checkmatrix}};
      };
    };
  };
  #CLASS matrixclass CLOSE;
  echo {checkmatrix} {2};
};
#NOP {长按长街进};
#ALIAS {matrix_ca_cj_in} {
  #VARIABLE {matrixdo} {};
  #CLASS matrixclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {@contains{{roomexits}{ne}} > 0} {
      #CLASS matrixclass KILL;
      loc {walk};
    };
    #ELSE {
      e;
      #DELAY {0.2} {echo {checkmatrix}};
    };
  };
  #CLASS matrixclass CLOSE;
  echo {checkmatrix} {2};
};
#NOP {长按长街出};
#ALIAS {matrix_ca_cj_out} {
  #VARIABLE {matrixdo} {};
  #CLASS matrixclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"$room" != "长街"} {
      #CLASS matrixclass KILL;
      loc {walk};
    };
    #ELSE {
      w;
      #DELAY {0.2} {echo {checkmatrix}};
    };
  };
  #CLASS matrixclass CLOSE;
  echo {checkmatrix} {2};
};
#NOP {长按柏树林，%1为方向路径};
#ALIAS {matrix_ca_bsl} {
  #VARIABLE {matrixdo} {};
  #VARIABLE {matrixflag} {1};
  #CLASS matrixclass OPEN;
  #ACTION {^你转来转去} {
    #VARIABLE {matrixflag} {0};
  };
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {$matrixflag == 0} {
      #CLASS matrixclass KILL;
      loc {walk};
    };
    #ELSE {
      %1;
      #DELAY {0.2} {echo {checkmatrix}};
    };
  };
  #CLASS matrixclass CLOSE;
  echo {checkmatrix} {2};
};
#NOP {回疆针叶林};
#ALIAS {matrix_zyl} {
  #LIST {matrixstep} {create} {s;w;n;e};
  #VARIABLE {tempindex} {1};
  #CLASS matrixclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"$room" != "针叶林" || @contains{{roomexits}{se}} > 0} {
      #CLASS matrixclass KILL;
      loc {walk};
    };
    #ELSE {
      execute {#10 $matrixstep[+$tempindex]};
      #MATH {tempindex} {$tempindex + 1};
      #IF {$tempindex > 4} {
        #VARIABLE {tempindex} {1};
      };
      #DELAY {1} {echo {checkmatrix}};
    };
  };
  #CLASS matrixclass CLOSE;
  echo {checkmatrix} {2}
};
#NOP {天龙寺有两片松树林，其中4个一组，2个一组，其中2个一组的可以通过两次n;w必定走到4个一组的松树林,其后可以通过一直s走到石板路出去};
#ALIAS {matrix_tls_ssl_out} {
  #VARIABLE {matrixdo} {};
  #VARIABLE {temprepeat} {0};
  #VARIABLE {matrixts} {@now{}};
  #CLASS matrixclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {@elapsed{$matrixts} > 60} {
      #CLASS matrixclass KILL;
      #NOP {超时还出不去只能退出};
      dohalt {quit}
    };
    #ELSEIF {"$room" != "松树林"} {
      #CLASS matrixclass KILL;
      loc {walk};
    };
    #ELSE {
      #MATH {temprepeat} {$temprepeat + 1};
      #IF {$temprepeat > 4} {
        #VARIABLE {temprepeat} {0};
        n;w;n;w;
      };
      #6 s;
      #DELAY {0.5} {echo {checkmatrix}};
    };
  };
  #CLASS matrixclass CLOSE;
  n;w;n;w;
  echo {checkmatrix} {2};
};
#NOP {天龙寺松树林进，为了避免不必要的麻烦，且有利于房间识别，这里将去龙树院的路径放置在雨花阁房间};
#ALIAS {matrix_tls_ssl_lsy} {
  #VARIABLE {matrixdo} {};
  #VARIABLE {matrixts} {@now{}};
  #CLASS matrixclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {@elapsed{$matrixts} > 60} {
      #CLASS matrixclass KILL;
      #NOP {超时还出不去只能退出};
      dohalt {quit}
    };
    #ELSEIF {"$room" == "龙树院"} {
      #CLASS matrixclass KILL;
      loc {walk};
    };
    #ELSE {
      #6 n;
      #5 w;
      #DELAY {1} {echo {checkmatrix}};
    };
  };
  #CLASS matrixclass CLOSE;
  echo {checkmatrix} {2};
};
#NOP {天龙寺松树林进，为了避免不必要的麻烦，且有利于房间识别，这里将去龙树院的路径放置在雨花阁房间};
#ALIAS {matrix_tls_ssl_xdt} {
  #VARIABLE {matrixdo} {};
  #CLASS matrixclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"$room" == "龙树院"} {
      #CLASS matrixclass KILL;
      loc {walk};
    };
    #ELSE {
      #6 n;
      #5 w;
      #DELAY {1} {echo {checkmatrix}};
    };
  };
  #CLASS matrixclass CLOSE;
  echo {checkmatrix} {2};
};
#NOP {归云庄湖滨小路进};
#ALIAS {matrix_gyz_hb_in} {
  #VARIABLE {matrixdo} {};
  #CLASS matrixclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"$room" != "湖滨小路"} {
      #CLASS matrixclass KILL;
      loc {walk};
    };
    #ELSE {
      n;
      #DELAY {0.2} {echo {checkmatrix}};
    };
  };
  #CLASS matrixclass CLOSE;
  echo {checkmatrix} {2};
};
#NOP {归云庄湖滨小路出};
#ALIAS {matrix_gyz_hb_out} {
  #VARIABLE {matrixdo} {};
  #CLASS matrixclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {@contains{{roomexits}{e}} > 0} {
      #CLASS matrixclass KILL;
      loc {walk};
    };
    #ELSE {
      s;
      look;
      #DELAY {0.2} {echo {checkmatrix}};
    };
  };
  #CLASS matrixclass CLOSE;
  echo {checkmatrix} {2};
};

#NOP {归云庄九宫桃花阵};
#ALIAS {matrix_gyz_taohua} {
  #LIST {matrixstep} {create} {w;w;s;e;e;s;w;w};
  #CLASS matrixclass OPEN;
  #ACTION {^    这是一片茂密的桃花丛，你一走进来就迷失了方向。地上有%%u株桃花(taohua)。} {
    #VARIABLE {temptaohua} {@ctd{%%1}};
  };
  #ACTION {^    这是一片茂密的桃花丛，你一走进来就迷失了方向。地上一株桃花(taohua)也没有。} {
    #VARIABLE {temptaohua} {0};
  };
  #ACTION {^一股暖流发自丹田流向全身，慢慢地你又恢复了知觉……} {
    #CLASS matrixclass KILL;
    loc {walk};
  };
  #ACTION {^{设定环境变量：action \= \"checktaohua\"|你设定checktaohua为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {$temptaohua > 0} {get $temptaohua taohua};
    #IF {&matrixstep[] > 0} {
      $matrixstep[+1];
      l;
      #LIST {matrixstep} {delete} {1};
      #DELAY {0.5} {echo {checktaohua}};
    };
    #ELSE {
      execute {
        drop 5 taohua;
        e;
        drop 5 taohua;
        e;
        drop 5 taohua;
        n;
        drop 5 taohua;
        w;
        drop 5 taohua;
        w;
        drop 5 taohua;
        n;
        drop 5 taohua;
        e;
        drop 5 taohua;
        e;
        drop 5 taohua
      };
      #DELAY {1.5} {echo {checkmatrix}};
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"$room" != "九宫桃花阵"} {
      #CLASS matrixclass KILL;
      loc {walk};
    };
    #ELSE {
      execute {n;n;e;e;l};
      echo {checktaohua};
    };
  };
  #CLASS matrixclass CLOSE;
  echo {checkmatrix} {2};
};

#NOP {明教紫衫林通往四个方向为紫衫林的房间};
#ALIAS {matrix_zsl_lin1} {
  #CLASS matrixclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"$room" != "紫杉林"} {
      #CLASS matrixclass KILL;
      loc {walk};
    };
    #ELSEIF {@count{{roomways}{紫杉林}} == 4} {
      #CLASS matrixclass KILL;
      loc {walk};
    };
    #ELSE {
      s;
      look;
      #DELAY {0.2} {echo {checkmatrix}};
    };
  };
  #CLASS matrixclass CLOSE;
  ensure {look} {checkmatrix}
};

#NOP {明教紫衫林通往四个方向为天地风雷门的房间};
#ALIAS {matrix_zsl_lin2} {
  #CLASS matrixclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"$room" != "紫杉林"} {
      #CLASS matrixclass KILL;
      loc {walk};
    };
    #ELSEIF {@count{{roomways}{紫杉林}} == 0} {
      #CLASS matrixclass KILL;
      loc {walk};
    };
    #ELSE {
      s;
      look;
      #DELAY {0.2} {echo {checkmatrix}};
    };
  };
  #CLASS matrixclass CLOSE;
  echo {checkmatrix} {2};
};

#NOP {明教紫衫林通往四个方向为天地风雷门的房间去指定门};
#ALIAS {matrix_zsl_men} {
  #LIST {matrixstep} {create} {n;w;e;s};
  #CLASS matrixclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #VARIABLE {idle} {0};
    #CLASS matrixclass KILL;
    #IF {$walkstoppedts != 0} {
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSE {
      loc {walk}
    };
  };
  #CLASS matrixclass CLOSE;
  $matrixstep[+@contains{{roomways}{%1}}];
  echo {checkmatrix} {2};
};

#NOP {明教紫衫林通向某个旗的房间};
#ALIAS {matrix_zsl_out} {
  #LIST {matrixstep} {create} {n;w;e;s};
  #CLASS matrixclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"$room" != "紫杉林"} {
      #CLASS matrixclass KILL;
      #DELAY {0.5} {
        loc {walk};
      };
    };
    #ELSEIF {@count{{roomways}{紫杉林}} == 3} {
      #CLASS matrixclass KILL;
      #DELAY {0.5} {
        loc {walk};
      };
    };
    #ELSE {
      s;
      look;
      #DELAY {0.2} {echo {checkmatrix}};
    };
  };
  #CLASS matrixclass CLOSE;
  $matrixstep[+@contains{{roomways}{%1}}];
  echo {checkmatrix} {2};
};
#NOP {树林房间出连接巨木旗的以外的房间,除east出口外,其他三个出口每次进来都会按照顺时针方向旋转};
#ALIAS {matrix_mj_shenchu} {
  #LIST {matrixstep} {create} {n;w;e;s};
  #CLASS matrixclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #CLASS matrixclass KILL;
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"$room" != "树林"} {
      loc {walk};
    };
    #ELSE {
      #LOCAL {shenchuindex} {@contains{{roomways}{树林深处}}};
      #IF {$shenchuindex > 0} {
        $matrixstep[+$shenchuindex];
      };
      loc {walk};
    };
  };
  #CLASS matrixclass CLOSE;
  ensure {look} {checkmatrix}
};
#NOP {云杉林找路,即找到毗邻特殊房间的云杉林房间};
#ALIAS {matrix_kl_ysl} {
  #LIST {matrixsteps} {create} {n;w;e;s};
  #LIST {tempsteps} {clear};
  #VARIABLE {rightstep} {};
  #CLASS matrixclass KILL;
  #CLASS matrixclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {&roomexits[] == 3} {
      #CLASS matrixclass KILL;
      loc {walk}
    };
    #ELSEIF {@contains{{roomways}{后院}} > 0 || @contains{{roomways}{葱岭谷}} > 0 || @contains{{roomways}{苦寒楼一层}} > 0} {
      #CLASS matrixclass KILL;
      loc {walk}
    };
    #ELSE {
      echo {checkexplore}
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkexplore\"|你设定checkexplore为反馈信息}} {
    #VARIABLE {idle} {0};
    #VARIABLE {exploreroom} {$room};
    #LIST {tempsteps} {clear};
    #VARIABLE {rightstep} {};
    #LOOP 1 4 {i} {
      #IF {"$roomways[+$i]" != "云杉林"} {
        #CONTINUE;
      };
      look @longDir{$matrixsteps[+$i]};
      echo {checkdirect$matrixsteps[+$i]}
    };
    echo {checkdirect};
  };
  #ACTION {^设定环境变量：action \= \"checkdirect%*\"} {
    #IF {"%%1" == ""} {
      #IF {"$rightstep" != "" || "$exploreroom" != "云杉林"} {
        #CLASS matrixclass KILL;
        $rightstep;
        loc {walk};
      };
      #ELSE {
        #DELAY {0.5} {
          $matrixsteps[+@rnd{{1}{&matrixsteps[]}}];
          look;
          echo {checkexplore};
        };
      };
    };
    #ELSEIF {&roomexits[] == 3 || (@contains{{roomways}{后院}} > 0 || @contains{{roomways}{葱岭谷}} > 0 || @contains{{roomways}{苦寒楼一层}} > 0)} {
      #VARIABLE {rightstep} {%%1};
    };
  };
  #ACTION {^你设定checkdirect%*为反馈信息} {
    #IF {"%%1" == ""} {
      #IF {"$rightstep" != "" || "$exploreroom" != "云杉林"} {
        #CLASS matrixclass KILL;
        $rightstep;
        loc {walk};
      };
      #ELSE {
        #DELAY {0.5} {
          $matrixsteps[+@rnd{{1}{&matrixsteps[]}}];
          look;
          echo {checkexplore};
        };
      };
    };
    #ELSEIF {&roomexits[] == 3 || (@contains{{roomways}{后院}} > 0 || @contains{{roomways}{葱岭谷}} > 0 || @contains{{roomways}{苦寒楼一层}} > 0)} {
      #VARIABLE {rightstep} {%%1};
    };
  };
  #CLASS matrixclass CLOSE;
  ensure {look} {checkmatrix}
};
#NOP {回疆大沙漠};
#ALIAS {matrix_hj_dsm} {
  #VARIABLE {matrixdo} {};
  #CLASS matrixclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"$room" != "大沙漠"} {
      #CLASS matrixclass KILL;
      loc {walk};
    };
    #ELSE {
      #DELAY {0.5} {
        execute {#4 e};
        echo {checkmatrix}
      };
    };
  };
  #CLASS matrixclass CLOSE;
  execute {#4 e};
  echo {checkmatrix} {2};
};

#NOP {回疆大戈壁进};
#ALIAS {matrix_hj_dgb_in} {
  #VARIABLE {matrixdo} {};
  #CLASS matrixclass OPEN;
  #ACTION {^你觉得自己已经筋疲力尽了，身上的黄沙却越堆越厚……} {
    #LINE ONESHOT #ACTION {^一股暖流发自丹田流向全身，慢慢地你又恢复了知觉……} {
      #CLASS matrixclass KILL;
      loc {hjwater {gotodo {$aimcity} {$aimroomid} {$aimdo}}}
    } {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"$room" != "大戈壁"} {
      #CLASS matrixclass KILL;
      loc {walk};
    };
    #ELSE {
      execute {#6 n};
      #DELAY {0.5} {echo {checkmatrix}};
    };
  };
  #CLASS matrixclass CLOSE;
  execute {#5 w};
  echo {checkmatrix} {2};
};
#NOP {回疆大戈壁出};
#ALIAS {matrix_hj_dgb_out} {
  #VARIABLE {matrixdo} {};
  #CLASS matrixclass OPEN;
  #ACTION {^你觉得自己已经筋疲力尽了，身上的黄沙却越堆越厚……} {
    #LINE ONESHOT #ACTION {^一股暖流发自丹田流向全身，慢慢地你又恢复了知觉……} {
      #CLASS matrixclass KILL;
      loc {hjwater {gotodo {$aimcity} {$aimroomid} {$aimdo}}}
    } {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"$room" != "大戈壁"} {
      #CLASS matrixclass KILL;
      loc {walk};
    };
    #ELSE {
      s;
      #DELAY {0.5} {echo {checkmatrix}};
    };
  };
  #CLASS matrixclass CLOSE;
  execute {#5 e};
  echo {checkmatrix} {2};
};
#NOP {回疆草原边缘};
#ALIAS {matrix_hj_cyby} {
  #VARIABLE {matrixdo} {};
  #CLASS matrixclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"$room" != "草原边缘"} {
      #CLASS matrixclass KILL;
      loc {walk};
    };
    #ELSE {
      %1;
      #DELAY {0.2} {echo {checkmatrix}};
    };
  };
  #CLASS matrixclass CLOSE;
  echo {checkmatrix} {2};
};
#NOP {回疆草海};
#ALIAS {matrix_hj_tozhaoze} {
  #VARIABLE {stepcount} {0};
  #CLASS matrixclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #DELAY {0.5} {
      loc {walk}
    };
  };
  #CLASS matrixclass CLOSE;
  %1;
  look;
  echo {checkmatrix} {2};
};
#NOP {梅庄,梅庄迷宫是一个树枝状迷宫};
#ALIAS {matrix_hz_mz} {
  #LIST {mzpath} {clear};
  #LIST {mzpath} {add} {%1};
  #VARIABLE {mzflag} {0};
  #VARIABLE {forwardstep} {%1};
  #VARIABLE {traceback} {0};
  #CLASS matrixclass OPEN;
  #ACTION {^    这是梅林中的一条小路，小路往北就出了梅林，在远处似乎折向了西边。} {
    #IF {"%1" == "n"} {
      #VARIABLE {mzflag} {1};
    };
  };
  #ACTION {^    这是梅林中的一条小路，小路往南就出了梅林，远处隐约现出白墙} {
    #IF {"%1" == "s"} {
      #VARIABLE {mzflag} {1};
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"$room" != "梅林"} {
      #CLASS matrixclass KILL;
      loc {walk}
    };
    #ELSEIF {$mzflag == 1} {
      #CLASS matrixclass KILL;
      %1;
      loc {walk}
    };
    #ELSE {
      #NOP {记录来路反向在出口中的位置并从出口中删除该方向};
      #LOCAL {tempindex} {@contains{{roomexits}{@reverseDir{$forwardstep}}}};
      #IF {$__DEBUG__ == 1} {
        #SHOWME {<ffa>上一步 $forwardstep 反向 @reverseDir{$forwardstep} 在房间出口中的位置为 $tempindex};
      };
      #NOP {从出口列表删除来路反向的出口};
      #NOP {首先从出口中排除历史路径的最后一步的反向};
      #IF {&mzpath[] > 0} {
        #LOCAL {tempstep} {@reverseDir{$mzpath[+&mzpath[]]}};
        #LOCAL {hisindex} {@contains{{roomexits}{$tempstep}}};
        #IF {$hisindex > 0} {
          #IF {$__DEBUG__ == 1} {
            #SHOWME {<ffa>历史最后一步 $mzpath[+&mzpath[]] 反向 $tempstep 从房间出口中删除};
          };
          #LIST {roomexits} {delete} {$hisindex};
        };
      };
      #IF {$__DEBUG__} {
        #SHOWME {<faf>出口:$roomexits[]};
      };
      #IF {$traceback == 0} {
        #NOP {如果不是回溯方向则取已处理出口的第一个};
        #VARIABLE {tempindex} {1};
      };
      #ELSEIF {$hisindex > $tempindex} {
        #NOP {历史最后一步反向在出口中的位置比来路反向在出口中的位置更加靠后,则需要将tempindex增加一个位置};
        #MATH {tempindex} {$tempindex + 1};
      };
      #NOP {当前指定的出口索引存在,已经将历史路径最后一步排除,出口少了一个，直接取$tempindex即为要探索的出口};
      #IF {$tempindex <= &roomexits[]} {
        #VARIABLE {traceback} {0};
        #VARIABLE {forwardstep} {$roomexits[+$tempindex]};
        #IF {$__DEBUG__ == 1} {
          #SHOWME {<ffa>取房间中第$tempindex 个出口 $forwardstep};
        };
      };
      #NOP {如果历史路径不为空则回溯,否则异常结束};
      #ELSEIF {&mzpath[] > 0} {
        #VARIABLE {traceback} {1};
        #VARIABLE {forwardstep} {@reverseDir{$mzpath[+&mzpath[]]}};
        #IF {$__DEBUG__ == 1} {
          #SHOWME {<ffa>取历史最后一步 $mzpath[+&mzpath[]] 反向 $forwardstep 回溯};
        };
        #LIST {mzpath} {delete} {&mzpath[]};
      };
      #ELSE {
        #CLASS matrixclass KILL;
        #SHOWME {<faa>梅庄已无路可走};
        #VARIABLE {forwardstep} {dao}
      };
      #IF {$__DEBUG__} {
        #SHOWME {<faf>路径:$mzpath[]};
      };
      #IF {$traceback == 0} {
        #LIST {mzpath} {add} {$forwardstep};
      };
      $forwardstep;
      look;
      #DELAY {0.2} {echo {checkmatrix}};
    };
  };
  #CLASS matrixclass CLOSE;
  ensure {look} {checkmatrix}
};
#NOP {苏州杏子林};
#ALIAS {matrix_sz_xzl} {
  #VARIABLE {matrixdo} {n;e;n;w;n;e;n;w;n};
  #CLASS matrixclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"$room" != "杏子林" || &roomexits[] !=4} {
      #CLASS matrixclass KILL;
      loc {walk};
    };
    #ELSE {
      $matrixdo;
      #DELAY {0.5} {echo {checkmatrix}};
    };
  };
  #CLASS matrixclass CLOSE;
  execute {$matrixdo};
  echo {checkmatrix} {2};
};
#NOP {探索模式处理迷宫(丢钱),%1:每次移动后操作,%2:指定忽略的方向,%:指定期望的房间};
#ALIAS {explorematrixroom} {
  #CLASS exploreclass OPEN;
  #ACTION {^%!s%u文铜钱(Coin)} {
    #VARIABLE {tempcoinnumber} {@ctd{%%1}};
  };
  #ACTION {^设定环境变量：action \= \"checkroomcoin%*\"} {
    processcoin {%1} {%%1} {%2} {%3}
  };
  #ACTION {^你设定checkroomcoin%*为反馈信息} {
    processcoin {%1} {%%1} {%2} {%3}
  };
  #CLASS exploreclass CLOSE;
  checkroomcoin;
};
#NOP {处理钱币,%1:要执行的指令,%2:探索的方向,%3:要忽略的方向,%4:期望的房间};
#ALIAS {processcoin} {
  #VARIABLE {idle} {0};
    #IF {"%2" == ""} {
      #NOP {保存当前房间的变量};
      #VARIABLE {oldroomexits} {$roomexits};
      #VARIABLE {oldroomways} {$roomways};
      #VARIABLE {shortcut} {};
      #NOP {如果当前房间数量大于下一个要丢的数量,捡起来再丢};
      #IF {$tempcoinnumber > $nextcoinnumber} {
        get $tempcoinnumber coin;
        #VARIABLE {tempcoinnumber} {0};
      };
      #NOP {丢下下一个数量的coin};
      #IF {$tempcoinnumber == 0} {
        drop $nextcoinnumber coin;
        #VARIABLE {tempcoinnumber} {$nextcoinnumber};
        #MATH {nextcoinnumber} {$nextcoinnumber + 1};
      };
      #NOP {判断当前数量的房间是否存在,否则初始化};
      #IF {"$matrixrooms[$tempcoinnumber]" == ""} {
        #FOREACH {$roomexits[]} {p} {
          #IF {"%3" == "$p"} {
            #CONTINUE;
          };
          #VARIABLE {matrixrooms[$tempcoinnumber][$p]} {0};
        };
      };
      #IF {$prevcoinnumber > 0 && "$prevcoinstep" != ""} {
        #VARIABLE {matrixrooms[$prevcoinnumber][$prevcoinstep]} {$tempcoinnumber};
      };
      #VARIABLE {currentcoinnumber} {$tempcoinnumber};
      #VARIABLE {tempcoinnumber} {0};
      #FOREACH {$roomexits[]} {p} {
        #IF {"%3" == "$p"} {
          #CONTINUE;
        };
        checkroomcoin {@longDir{$p}};
      };
    };
    #ELSE {
      #NOP {look某个方向时有可能是看不到地上的东西的,且有可能是别人丢的物品,这里兼容判断一下};
      #SWITCH {"%2"} {
        #CASE {"north"} {
          #IF {$tempcoinnumber > 0 && $tempcoinnumber < $nextcoinnumber && "$matrixrooms[$tempcoinnumber]" != ""} {
            #VARIABLE {matrixrooms[$currentcoinnumber][n]} {$tempcoinnumber}
          };
        };
        #CASE {"west"} {
          #IF {$tempcoinnumber > 0 && $tempcoinnumber < $nextcoinnumber && "$matrixrooms[$tempcoinnumber]" != ""} {
            #VARIABLE {matrixrooms[$currentcoinnumber][w]} {$tempcoinnumber}
          };
        };
        #CASE {"south"} {
          #IF {$tempcoinnumber > 0 && $tempcoinnumber < $nextcoinnumber && "$matrixrooms[$tempcoinnumber]" != ""} {
            #VARIABLE {matrixrooms[$currentcoinnumber][s]} {$tempcoinnumber}
          };
        };
        #CASE {"east"} {
          #IF {$tempcoinnumber > 0 && $tempcoinnumber < $nextcoinnumber && "$matrixrooms[$tempcoinnumber]" != ""} {
            #VARIABLE {matrixrooms[$currentcoinnumber][e]} {$tempcoinnumber}
          };
        };
      };
      #IF {"%4" != "" && @contains{{roomways}{%3}} > 0} {
        #VARIABLE {shortcut} {@shortDir{%%1}};
      };
      #IF {@contains{{oldroomexits}{@shortDir{%%1}}} == &oldroomexits[]} {
        #CLASS exploreclass KILL;
        #IF {"$shortcut" != ""} {
          $shortcut
        };
        #ELSE {
          #NOP {找到去空房间的路径};
          #VARIABLE {temppath} {@getUnVisitedRoomPath{$currentcoinnumber}};
          #IF {"$temppath" == ""} {
            #LOCAL {tempindex} {@contains{{roomexits}{%2}}};
            #IF {$tempindex > 0} {
              #LIST {roomexits} {delete} {$tempindex};
            };
            #LOCAL {tempindex} {@rnd{{1}{&roomexits[]}}};
            #NOP {这个时候肯定是出问题了,直接捡起coin并清空所有导向该数目房间的路径};
            #FOREACH {*matrixrooms[]} {r} {
              #FOREACH {*matrixrooms[$r][]} {p} {
                #IF {"$matrixrooms[$r][$p]" == "$currentcoinnumber"} {
                  #SHOWME {<faa>房间$matrixrooms[$r][$p]出口已重置};
                };
              };
            };
            get $currentcoinnumber coin;
            $roomexits[+$tempindex]
          };
          #ELSE {
            #SHOWME {$temppath};
            #LIST {tempsteps} {create} {$temppath};
            #NOP {去头掐尾};
            #LIST {tempsteps} {delete} {1};
            #VARIABLE {prevcoinstep} {$tempsteps[+&tempsteps[]]};
            #LIST {tempsteps} {delete} {&tempsteps[]};
            #VARIABLE {prevcoinnumber} {$currentcoinnumber};
            #FOREACH {$tempsteps[]} {p} {
              #VARIABLE {prevcoinnumber} {$matrixrooms[$prevcoinnumber][$p]};
            };
            $temppath
          };
        };  
        loc {%1};
      };
    };
    #VARIABLE {tempcoinnumber} {0};
};
#NOP {检查房间coin,%1:要检查的方向,空为当前房间};
#ALIAS {checkroomcoin} {
  l %1;
  echo {checkroomcoin%1};
};
#NOP {通过广度优先遍历获取导向未探索房间的额路径,%1:起始房间的coin数量};
#FUNCTION getUnVisitedRoomPath {
  #LOCAL {tracepath[%1]} {};
  #LIST {openlist} {create} {%1};
  #LIST {closelist} {create} {};
  #WHILE {&openlist[] > 0} {
    #NOP {处理未访问列表中第一个节点};
    #LOCAL {prevnode} {$openlist[+1]};
    #LIST {openlist} {delete} {1};
    #LIST {closelist} {add} {$prevnode};
    #NOP {访问节点的所有出口,如不在已访问列表中则加入未访问列表};
    #FOREACH {*matrixrooms[$prevnode][]} {p} {
      #LOCAL {nextnode} {$matrixrooms[$prevnode][$p]};
      #IF {$nextnode == 0} {
        #RETURN {$tracepath[$prevnode];$p};
      };
      #ELSEIF {@contains{{closelist}{$nextnode}} == 0} {
        #NOP {加入未访问队列且设置其路径};
        #LOCAL {tracepath[$nextnode]} {$tracepath[$prevnode];$p};
        #LIST {openlist} {add} {$nextnode};
      };
    };
  };
  #RETURN {};
};
#NOP {桃花岛桃花阵};
#ALIAS {matrix_thd_thz} {
  #VARIABLE {matrixdo} {};
  #VARIABLE {matrixrooms} {};
  #VARIABLE {prevcoinnumber} {0};
  #VARIABLE {prevcoinstep} {};
  #VARIABLE {prevcoinnumber} {0};
  #VARIABLE {nextcoinnumber} {1};
  #VARIABLE {tempcoinnumber} {0};
  #VARIABLE {currentcoinnumber} {0};
  #CLASS matrixclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"$room" != "桃花阵"} {
      #CLASS matrixclass KILL;
      loc {walk};
    };
    #ELSEIF {@contains{{roomways}{小院}} > 0} {
      #CLASS matrixclass KILL;
      loc {walk};
    };
    #ELSE {
      #VARIABLE {tempcoinnumber} {0};
      explorematrixroom {#DELAY {1} {echo {checkmatrix}}} {} {小院}
    };
  };
  #ACTION {^你在%*中累得精疲力尽，终因体力不支而昏了过去！} {
    #LINE ONESHOT #ACTION {^一股暖流发自丹田流向全身，慢慢地你又恢复了知觉……} {
      #CLASS riverclass KILL;
      loc {doabort}
    };
  };
  #CLASS matrixclass CLOSE;
  echo {checkmatrix} {2}
};
#NOP {桃花岛绿竹林积翠亭(south)->草地,固定路径};
#ALIAS {matrix_thd_zhulin1} {
  #VARIABLE {matrixdo} {};
  #CLASS matrixclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"$room" != "绿竹林"} {
      #CLASS matrixclass KILL;
      loc {walk}
    };
    #ELSEIF {@contains{{roomways}{草地}} > 0} {
      #CLASS matrixclass KILL;
      loc {walk}
    };
    #ELSE {
      #NOP {这里要避免探索积翠亭和河塘的方向};
      #LOCAL {specindex} {@contains{{roomways}{积翠亭}}};
      #IF {$specindex == 0} {
        #LOCAL {specindex} {@contains{{roomways}{河塘}}};
      };
      #VARIABLE {ignoredir} {};
      #IF {$specindex > 0} {
        #VARIABLE {ignoredir} {$matrixstep[+$specindex]};
      };
      explorematrixroom {#DELAY {0.5} {echo {checkmatrix}}} {$ignoredir} {草地};
    };
  };
  #ACTION {^你在%*中累得精疲力尽，终因体力不支而昏了过去！} {
    #LINE ONESHOT #ACTION {^一股暖流发自丹田流向全身，慢慢地你又恢复了知觉……} {
      #CLASS riverclass KILL;
      loc {doabort}
    };
  };
  #CLASS matrixclass CLOSE;
  execute {s;s;w;n;s};
  echo {checkmatrix} {2}
};

#NOP {积翠亭->河塘};
#ALIAS {matrix_thd_zhulin2} {
  #VARIABLE {matrixdo} {};
  #VARIABLE {matrixrooms} {};
  #VARIABLE {prevcoinnumber} {0};
  #VARIABLE {prevcoinstep} {};
  #VARIABLE {prevcoinnumber} {0};
  #VARIABLE {nextcoinnumber} {1};
  #VARIABLE {tempcoinnumber} {0};
  #VARIABLE {currentcoinnumber} {0};
  #LIST {matrixstep} {create} {n;w;e;s};
  #CLASS matrixclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"$room" != "绿竹林"} {
      #CLASS matrixclass KILL;
      loc {walk}
    };
    #ELSEIF {@contains{{roomways}{河塘}} > 0} {
      #CLASS matrixclass KILL;
      loc {walk}
    };
    #ELSE {
      #NOP {这里要避免探索积翠亭和草地的方向};
      #LOCAL {specindex} {@contains{{roomways}{积翠亭}}};
      #IF {$specindex == 0} {
        #LOCAL {specindex} {@contains{{roomways}{草地}}};
      };
      #VARIABLE {ignoredir} {};
      #IF {$specindex > 0} {
        #VARIABLE {ignoredir} {$matrixstep[+$specindex]};
      };
      explorematrixroom {#DELAY {0.5} {echo {checkmatrix}}} {$ignoredir} {河塘};
    };
  };
  #ACTION {^你在%*中累得精疲力尽，终因体力不支而昏了过去！} {
    #LINE ONESHOT #ACTION {^一股暖流发自丹田流向全身，慢慢地你又恢复了知觉……} {
      #CLASS riverclass KILL;
      loc {doabort}
    };
  };
  #CLASS matrixclass CLOSE;
  echo {checkmatrix} {2}
};
#NOP {桃花岛强制出去};
#ALIAS {matrix_thd_out} {
  #CLASS riverclass open;
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"$room" == "绿竹林" || "$room" == "桃花阵"} {
      execute {#10 w};
      #DELAY {0.5} {echo {checkmatrix}};
    };
    #ELSE {
      #CLASS riverclass KILL;
      loc {walk}
    };
  };
  #ACTION {^你在%*中累得精疲力尽，终因体力不支而昏了过去！} {
    #LINE ONESHOT #ACTION {^一股暖流发自丹田流向全身，慢慢地你又恢复了知觉……} {
      #CLASS riverclass KILL;
      loc {walk}
    } {1};
  };
  #CLASS riverclass close;
  echo {checkmatrix} {2}
};
#ALIAS {matrix_xx_xxh} {
  #VARIABLE {matrixdo} {};
  #CLASS matrixclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"$room" != "星宿海"} {
      #CLASS matrixclass KILL;
      #DELAY {1} {
        loc {walk};
      };
    };
    #ELSE {
      execute {#4 %1};
      #DELAY {0.5} {echo {checkmatrix}};
    };
  };
  #CLASS matrixclass CLOSE;
  execute {#4 %1};
  echo {checkmatrix} {2};
};
#NOP {南疆沙漠};
#ALIAS {matrix_xx_njsm} {
  #VARIABLE {matrixdo} {};
  #CLASS matrixclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"$room" == ""} {
      #CLASS matrixclass KILL;
      loc {walk};
    };
    #ELSE {
      n;
      #DELAY {0.2} {echo {checkmatrix}};
    };
  };
  #ACTION {^你已经感到不行了，冥冥中你觉得有人将你抬了出来。} {
    #LINE ONESHOT #ACTION {^一股暖流发自丹田流向全身，慢慢地你又恢复了知觉……} {
      #CLASS riverclass KILL;
      loc {doabort}
    } {1};
  };
  #CLASS matrixclass CLOSE;
  echo {checkmatrix} {2};
};
#NOP {桃花岛守墓下};
#ALIAS {matrix_thd_mudao_in} {
  #VARIABLE {matrixflag} {0};
  #CLASS matrixclass OPEN;
  #ACTION {^你不知道不觉的走回出口，抓了抓头。} {
    #VARIABLE {matrixflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSE {
      loc {walk}
    };
  };
  #CLASS matrixclass CLOSE;
  ensure {d} {checkmatrix}
};
#ALIAS {matrix_thd_mudao_in1} {
  #VARIABLE {matrixstep} {0};
  #CLASS matrixclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {@contains{{roomways}{墓中圹室}} > 0} {
      #CLASS matrixclass KILL;
      d;
      loc {
        walk
      }
    };
    #ELSEIF {&roomexits[] == 2} {
      #VARIABLE {matrixstep} {0};
      loc {walk}
    };
    #ELSE {
      #IF {$matrixstep == 0} {
        @getMudaoPath{}
      };
      #ELSE {
        @getMudaoPath1{}
      };
      #MATH {matrixstep} {$matrixstep + 1};
      time;
      l;
      #DELAY {0.5} {echo {checkmatrix}};
    };
  };
  #CLASS matrixclass CLOSE;
  ensure {time;look} {checkmatrix}
};
#NOP {获取守墓墓道路径};
#FUNCTION getMudaoPath {
  #LOCAL {ts} {$env[gametime]};
  #IF {$ts > 12} {
    #MATH {ts} {$ts - 12};
  };
  #IF {$ts < 1} {
    #RETURN {};
  };
  #ELSEIF {$ts < 2} {
    #RETURN {nu};
  };
  #ELSEIF {$ts < 3} {
    #RETURN {n};
  };
  #ELSEIF {$ts < 4} {
    #RETURN {nd};
  };
  #ELSEIF {$ts < 5} {
    #RETURN {ne};
  };
  #ELSEIF {$ts < 6} {
    #RETURN {e};
  };
  #ELSEIF {$ts < 7} {
    #RETURN {se};
  };
  #ELSEIF {$ts < 8} {
    #RETURN {su};
  };
  #ELSEIF {$ts < 9} {
    #RETURN {s};
  };
  #ELSEIF {$ts < 10} {
    #RETURN {sd};
  };
  #ELSEIF {$ts < 11} {
    #RETURN {sw};
  };
  #ELSEIF {$ts < 12} {
    #RETURN {w};
  };
  #ELSE {
    #RETURN {nw};
  };
};
#FUNCTION getMudaoPath1 {
  #LOCAL {ts} {$env[gametime]};
  #IF {$ts > 12} {
    #MATH {ts} {$ts - 12};
  };
  #IF {$ts < 1} {
    #RETURN {};
  };
  #ELSEIF {$ts < 2} {
    #RETURN {nw};
  };
  #ELSEIF {$ts < 3} {
    #RETURN {nu};
  };
  #ELSEIF {$ts < 4} {
    #RETURN {n};
  };
  #ELSEIF {$ts < 5} {
    #RETURN {nd};
  };
  #ELSEIF {$ts < 6} {
    #RETURN {ne};
  };
  #ELSEIF {$ts < 7} {
    #RETURN {e};
  };
  #ELSEIF {$ts < 8} {
    #RETURN {ne};
  };
  #ELSEIF {$ts < 9} {
    #RETURN {su};
  };
  #ELSEIF {$ts < 10} {
    #RETURN {s};
  };
  #ELSEIF {$ts < 11} {
    #RETURN {sd};
  };
  #ELSEIF {$ts < 12} {
    #RETURN {sw};
  };
  #ELSE {
    #RETURN {w};
  };
};
#NOP {迷宫模板};
#ALIAS {matrix_thd_bgz} {
  #VARIABLE {matrixdo} {};
  #CLASS matrixclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"$room" != "八卦桃花阵"} {
      #CLASS matrixclass KILL;
      loc {walk};
    };
    #ELSE {
      #DELAY {0.2} {
        s;
        echo {checkmatrix};
      };
    };
  };
  #CLASS matrixclass CLOSE;
  echo {checkmatrix} {2};
};
#NOP {桃花岛墓道出去};
#ALIAS {matrix_thd_mudao_out} {
  #CLASS matrixclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #CLASS matrixclass KILL;
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSE {
      #LIST {roomexits} {delete} {@contains{{roomexits}{d}}};
      execute {
        $roomexits[+1];
        u;
        u;
        out
      };
      loc {walk}
    };
  };
  #CLASS matrixclass CLOSE;
  echo {checkmatrix} {2}
};
#NOP {古墓石室};
#ALIAS {matrix_gm_shengou} {
  #VARIABLE {matrixflag} {0};
  #CLASS matrixclass OPEN;
  #ACTION {^你纵身跳下深沟，拿捏恰到好处，正好落在古墓入口前} {
    #VARIABLE {matrixflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #CLASS matrixclass KILL;
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {$matrixflag == 1} {
      loc {walk}
    };
    #ELSE {
      #SHOWME {<ffa>你的条件不满足};
    };
  };
  #CLASS matrixclass CLOSE;
  #IF {"$hp[party]" != "" && "$hp[party]" != "普通百姓" && "$hp[party]" != "古墓派"} {
    #CLASS matrixclass KILL;
    #SHOWME {<faa>你进不了古墓};
    doabort
  };
  #ELSE {
    tiao gou;
    echo {checkmatrix} {2};
  };
};
#NOP {古墓石室};
#ALIAS {matrix_gm_shishi} {
  #LIST {matrixsteps} {create} {n;w;s;e};
  #VARIABLE {tempindex} {0};
  #CLASS matrixclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {@contains{{roomexits}{enter}} > 0} {
      #CLASS matrixclass KILL;
      dohalt {
        loc {walk};
      }
    };
    #ELSE {
      #MATH {tempindex} {$tempindex + 1};
      #IF {$tempindex > &matrixsteps[]} {
        #VARIABLE {tempindex} {1};
      };
      yun jingli;
      #6 $matrixsteps[+$tempindex];
      #DELAY {0.5} {echo {checkmatrix}};
    };
  };
  #CLASS matrixclass CLOSE;
  echo {checkmatrix} {2};
};
#NOP {古墓石室};
#ALIAS {matrix_gm_shiguan} {
  #VARIABLE {matrixflag} {0};
  #VARIABLE {matrixdo} {
    execute {
      #4 search;
      turn ao left
    };
    echo {checkmatrix}
  };
  #CLASS matrixclass OPEN;
  #ACTION {^你将凹处往左转动几下} {
    #VARIABLE {matrixflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkfire\"|你设定checkfire为反馈信息}} {
    #IF {"$id[things][fire]" == ""} {
      #SHOWME {<faa>完犊子了，没带火};
      #DELAY {10} {
        dohalt {
          execute {out;get fire;tui guangai;tang guan;i};
          echo {checkfire};
        };
      };
    };
    #ELSE {
      dian fire;
      $matrixdo
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {$matrixflag == 1} {
      #CLASS matrixclass KILL;
      dohalt {
        ti up;
        loc {walk}
      };
    };
    #ELSEIF {"$room" != "石棺内"} {
      #CLASS matrixclass KILL;
      loc {walk}
    };
    #ELSE {
      #DELAY {0.5} {$matrixdo};
    };
  };
  #CLASS matrixclass CLOSE;
  echo {checkfire}
};
#NOP {少林寺塔林进};
#ALIAS {matrix_slstl_in} {
  #VARIABLE {matrixts} {@now{}};
  #VARIABLE {checkcount} {0};
  #VARIABLE {matrixdo} {ne;n;nw;sw;w;ne;w;s;nw;sw};
  #CLASS matrixclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #VARIABLE {idle} {0};
    #MATH {checkcount} {$checkcount + 1};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"$room" != "塔林" || @contains{{roomways}{古佛舍利塔}} > 0} {
      #CLASS matrixclass KILL;
      n;
      loc {walk};
    };
    #ELSEIF {@elapsed{$matrixts} > 60} {
      #CLASS matrixclass KILL;
      #NOP {超时还出不去只能退出};
      dohalt {quit}
    };
    #ELSE {
      #IF {$checkcount >= 2} {
        #VARIABLE {checkcount} {0};
        $roomexits[@rnd{{1}{&roomexits[]}}];
      };
      execute {$matrixdo};
      look;
      #DELAY {1} {echo {checkmatrix}}
    };
  };
  #CLASS matrixclass CLOSE;
  ensure {look} {checkmatrix}
};
#NOP {少林寺塔林出};
#ALIAS {matrix_slstl_out} {
  #VARIABLE {matrixts} {@now{}};
  #VARIABLE {checkcount} {0};
  #VARIABLE {matrixdo} {ne;se;n;e;sw;e;ne;se;s;se};
  #CLASS matrixclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #VARIABLE {idle} {0};
    #MATH {checkcount} {$checkcount + 1};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"$room" != "塔林" || @contains{{roomways}{舍利院}} > 0} {
      #CLASS matrixclass KILL;
      open door;
      e;
      loc {walk};
    };
    #ELSEIF {@elapsed{$matrixts} > 60} {
      #CLASS matrixclass KILL;
      #NOP {超时还出不去只能退出};
      dohalt {quit}
    };
    #ELSE {
      #IF {$checkcount >= 5} {
        #VARIABLE {checkcount} {0};
        $roomexits[@rnd{{1}{&roomexits[]}}];
      };
      execute {$matrixdo};
      look;
      #DELAY {1} {echo {checkmatrix}}
    };
  };
  #CLASS matrixclass CLOSE;
  ensure {look} {checkmatrix}
};
#NOP {武当后山丛林};
#ALIAS {matrix_wd_conglin} {
  #LIST {matrixsteps} {create} {nw;n;ne;w;e;sw;s;se};
  #VARIABLE {tempstep} {};
  #CLASS matrixclass KILL;
  #CLASS matrixclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"$room" == "丛林边缘"} {
      loc {walk}
    };
    #ELSEIF {@contains{{roomways}{丛林边缘}} > 0} {
      dohalt {
        $matrixsteps[+@contains{{roomways}{丛林边缘}}];
        loc {walk}
      }
    };
    #ELSE {
      #LOCAL {targetroom} {};
      #SWITCH {"$room"} {
        #CASE {"烈火丛林"} {#LOCAL {targetroom} {落叶丛林}};
        #CASE {"落叶丛林"} {#LOCAL {targetroom} {积雪丛林}};
        #CASE {"积雪丛林"} {#LOCAL {targetroom} {阔叶丛林}};
        #CASE {"阔叶丛林"} {#LOCAL {targetroom} {丛林边缘}};
      };
      #NOP {查找出目标房间的出口};
      #LIST {tempexits} {clear};
      #LOOP 1 &roomways[] {i} {
        #IF {"$roomways[+$i]" == "$targetroom"} {
          #LIST {tempexits} {add} {$matrixsteps[+$i]};
        };
      };
      #SHOWME {$tempexits};
      #NOP {优先排除过来的方向};
      #IF {&tempexits[] == 0} {
        #VARIABLE {tempstep} {$roomexits[+@rnd{{1}{&roomexits[]}}]};
      };
      #ELSEIF {&tempexits[] == 1} {
        #VARIABLE {tempstep} {$tempexits[+1]};
      };
      #ELSE {
        #IF {"$tempstep" != ""} {
          #LOCAL {tempindex} {@contains{{tempexits}{@reverseDir{$tempstep}}}};
          #IF {$tempindex > 0} {
            #LIST {tempexits} {delete} {$tempindex};
          };
        };
        #VARIABLE {tempstep} {$tempexits[+@rnd{{1}{&tempexits[]}}]};
      };
      dohalt {
        $tempstep;
        look;
        #DELAY {0.5} {
          echo {checkmatrix};
        };
      }
    };
  };
  #CLASS matrixclass CLOSE;
  ensure {closewimpy} {checkmatrix}
};
#NOP {白驼山密道,去天井};
#ALIAS {matrix_bts_midao1} {
  #VARIABLE {okflag} {0};
  #CLASS matrixclass OPEN;
  #ACTION {^你正走着，突然发现前面好象有了一些光亮。} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^你正走着，突然发现前面的道路有些变化} {
    #VARIABLE {okflag} {2};
  };
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {$okflag != 0} {
      #CLASS matrixclass KILL;
      loc {walk};
    };
    #ELSE {
      execute {#3 n;#4 wd};
      #DELAY {1} {echo {checkmatrix}}
    };
  };
  #CLASS matrixclass CLOSE;
  echo {checkmatrix} {2};
};
#NOP {白驼山密道,从错误密道返回};
#ALIAS {matrix_bts_midao2} {
  #VARIABLE {matrixdo} {};
  #CLASS matrixclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {@contains{{roomexits}{wu}} > 0} {
      #CLASS matrixclass KILL;
      loc {
        walk
      };
    };
    #ELSE {
      execute {e;n;w};
      #DELAY {0.5} {echo {checkmatrix}}
    };
  };
  #CLASS matrixclass CLOSE;
  execute {e;n;w};
  echo {checkmatrix} {2};
};
#NOP {白驼山密道,返回};
#ALIAS {matrix_bts_midao3} {
  #VARIABLE {okflag} {0};
  #CLASS matrixclass OPEN;
  #ACTION {^你正走着，突然发现前面好象有了一些光亮。} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^你正走着，突然发现前面的道路有些变化} {
    #VARIABLE {okflag} {2};
  };
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {$okflag != 0} {
      #CLASS matrixclass KILL;
      loc {walk};
    };
    #ELSE {
      execute {#5 s};
      #DELAY {0.5} {echo {checkmatrix}}
    };
  };
  #CLASS matrixclass CLOSE;
  echo {checkmatrix} {2};
};
#NOP {宝藏密洞};
#ALIAS {matrix_bz_midong} {
  #LIST {matrixsteps} {create} {n;s;w;e};
  #VARIABLE {tempstep} {1};
  #VARIABLE {matrixflag} {0};
  #CLASS matrixclass KILL;
  #CLASS matrixclass OPEN;
  #ACTION {^你吃惊地发现，你现在的位置竟然就是最初的位置。} {
    #VARIABLE {matrixflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"$room" != "密洞"} {
      #CLASS matrixclass KILL;
      loc {walk};
    };
    #ELSE {
      #IF {$matrixflag == 1 || $tempstep > 4} {
        #VARIABLE {tempstep} {1};
        #VARIABLE {matrixflag} {0};
      };
      execute {#10 $matrixsteps[+$tempstep]};
      #MATH {tempstep} {$tempstep + 1};
      #DELAY {1} {echo {checkmatrix}}
    };
  };
  #CLASS matrixclass CLOSE;
  echo {checkmatrix} {2};
};

#NOP {宝藏密室};
#ALIAS {matrix_bz_mishi} {
  #CLASS matrixclass KILL;
  #CLASS matrixclass OPEN;
  #ACTION {^你{将快要零散的桌子|已经绑好了|把地上的花盆转回原位}} {
    #VARIABLE {idle} {0};
    dohalt {
      move hua pen
    };
  };
  #ACTION {^你{转了转地上的花盆|把桌子又推回原来的位置}} {
    #VARIABLE {idle} {0};
    dohalt {
      push zhuo zi
    };
  };
  #ACTION {^你用力将绑好的桌子全部推到角落的一边。} {
    #VARIABLE {idle} {0};
    dohalt {
      push wall
    }
  };
  #ACTION {^你用力推开活动的暗门，奋力从门缝挤了进去。} {
    #VARIABLE {idle} {0};
    dohalt {
      loc {walk};
    };
  };
  #CLASS matrixclass CLOSE;
  bang zhuo zi
};
#NOP {天龙八部大辽救援树林,%1:方向};
#ALIAS {matrix_xx_dlsl} {
  #CLASS matrixclass KILL;
  #CLASS matrixclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"$room" != "树林"} {
      #CLASS matrixclass KILL;
      loc {walk};
    };
    #ELSE {
      execute {#5 %1};
      #DELAY {0.5} {echo {checkmatrix}}
    };
  };
  #CLASS matrixclass CLOSE;
  echo {checkmatrix} {2};
};
#NOP {天山龙潭山涧,%1:后续指令};
#ALIAS {matrix_ts_sj} {
  #VARIABLE {matrixdo} {};
  #CLASS matrixclass OPEN;
  #ACTION {这里周围长满了灌木，%*方似乎能走过去，你不由的加快了步伐} {
    #SWITCH {"%%1"} {
      #CASE {"东"} {#VARIABLE {matrixdo} {e}};
      #CASE {"东下"} {#VARIABLE {matrixdo} {ed}};
      #CASE {"东上"} {#VARIABLE {matrixdo} {eu}};
      #CASE {"西"} {#VARIABLE {matrixdo} {w}};
      #CASE {"西下"} {#VARIABLE {matrixdo} {wd}};
      #CASE {"西上"} {#VARIABLE {matrixdo} {wu}};
      #CASE {"南"} {#VARIABLE {matrixdo} {s}};
      #CASE {"南下"} {#VARIABLE {matrixdo} {sd}};
      #CASE {"南上"} {#VARIABLE {matrixdo} {su}};
      #CASE {"北"} {#VARIABLE {matrixdo} {n}};
      #CASE {"北下"} {#VARIABLE {matrixdo} {nd}};
      #CASE {"北上"} {#VARIABLE {matrixdo} {nu}};
      #CASE {"东北"} {#VARIABLE {matrixdo} {ne}};
      #CASE {"西北"} {#VARIABLE {matrixdo} {nw}};
      #CASE {"东南"} {#VARIABLE {matrixdo} {se}};
      #CASE {"西南"} {#VARIABLE {matrixdo} {sw}};
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #CLASS matrixclass KILL;
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSE {
      $matrixdo;
      #IF {"%1" != ""} {
        %1
      };
      #ELSE {
        loc {walk}
      };
    };
  };
  #CLASS matrixclass CLOSE;
  ensure {look} {checkmatrix}
};
#NOP {冰火岛绿野，%1:要去的房间};
#ALIAS {matrix_bhd_go} {
  #LIST {matrixstep} {create} {n;w;e;s};
  #CLASS matrixclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"$room" != "绿野"} {
      #CLASS matrixclass KILL;
      loc {walk};
    };
    #ELSEIF {@contains{{roomways}{%1}} == 0} {
      #DELAY {0.5} {
        s;
        l;
        echo {checkmatrix}
      };
    };
    #ELSE {
      $matrixstep[+@contains{{roomways}{%1}}];
      #CLASS matrixclass KILL;
      loc {walk};
    };
  };
  #CLASS matrixclass CLOSE;
  ensure {look} {checkmatrix}
};
#NOP {华山密洞};
#ALIAS {matrix_hshs_midong} {
  #CLASS matrixclass KILL;
  #CLASS matrixclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"$room" != "密洞"} {
      #CLASS matrixclass KILL;
      loc {walk}
    };
    #ELSE {
      #DELAY {0.2} {
        s;
        echo {checkmatrix};
      }
    };
  };
  #CLASS matrixclass CLOSE;
  ensure {pray pearl} {checkmatrix}
};
#NOP {蝴蝶谷花圃。花圃里面没人的情况下，明教弟子可以直接过去，否则也只能进去走迷宫,%1:空-进入,非空-出来};
#NOP {该迷宫纯随机迷宫会消耗精力};
#ALIAS {matrix_hdg_huapu} {
  #VARIABLE {matrixdo} {};
  #VARIABLE {matrixts} {@now{}};
  #IF {"%1" == ""} {
    #VARIABLE {matrixdo} {yun jing;nd;#5 n};
  };
  #ELSE {
    #VARIABLE {matrixdo} {yun jing;#5 s};
  };
  #CLASS matrixclass KILL;
  #CLASS matrixclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #VARIABLE {errorcount} {0};
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"%1" == "" && "$room" != "花圃" && "$room" != "牛棚"} {
      #CLASS matrixclass KILL;
      loc {walk};
    };
    #ELSEIF {"%1" != "" && "$room" != "花圃"} {
      #CLASS matrixclass KILL;
      loc {walk};
    };
    #ELSEIF {@elapsed{$matrixts} > 120} {
      #NOP {两分钟过不去就溜了};
      #CLASS matrixclass KILL;
      #IF {"$currentjob" != ""} {
        loc {doabort}
      };
    };
    #ELSE {
      $matrixdo;
      #DELAY {0.5} {echo {checkmatrix}};
    };
  };
  #CLASS matrixclass CLOSE;
  echo {checkmatrix} {2};
};
#NOP {天山山道};
#ALIAS {matrix_ts_sd} {
  #CLASS matrixclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"$room" != "山道"} {
      #CLASS matrixclass KILL;
      loc {walk};
    };
    #ELSE {
      execute {
        #4 e;
        #4 n
      };
      #DELAY {0.5} {echo {checkmatrix}};
    };
  };
  #CLASS matrixclass CLOSE;
  echo {checkmatrix} {2};
};
#NOP {铁掌山松树林};
#ALIAS {matrix_tz_ssl} {
  #CLASS matrixclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"$room" != "松树林"} {
      #CLASS matrixclass KILL;
      loc {walk};
    };
    #ELSEIF {@contains{{roomways}{山路}} > 0} {
      s;
      loc {walk}
    };
    #ELSE {
      execute {s;e;s;w;look};
      #DELAY {0.5} {echo {checkmatrix}};
    };
  };
  #CLASS matrixclass CLOSE;
  echo {checkmatrix} {2};
};
#NOP {迷宫模板};
#ALIAS {matrix_template} {
  #VARIABLE {matrixdo} {};
  #CLASS matrixclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkmatrix\"|你设定checkmatrix为反馈信息}} {
    resonate {checkmatrix};
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"$room" == ""} {
      #CLASS matrixclass KILL;
      loc {walk};
    };
    #ELSE {
      #DELAY {0.5} {echo {checkmatrix}};
    };
  };
  #CLASS matrixclass CLOSE;
  echo {checkmatrix} {2};
};
#SHOWME {<fac>@padRight{{迷宫}{12}}<fac> <cfa>模块加载完毕<cfa>};

