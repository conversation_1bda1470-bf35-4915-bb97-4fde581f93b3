#nop --- 随机迷宫求解器脚本 (终极稳健版) ---;
#nop --- 使用通配符 * 来兼容颜色代码等非打印字符 ---;

#gag;

#nop --------------------------------------------------------------------------;
#nop --- 步骤1: 捕获动作 (#action) ---;
#nop --------------------------------------------------------------------------;

#nop {【修正】在触发条件前后添加 * 通配符，使其更健壮};
#action {*情怀梦境迷宫第一层：*} {
    #echo {<118>--- [迷宫] 检测到地图，开始捕获... ---};
    #unaction get_maze_line;
    #unaction end_maze_capture;
    #list maze_map_data clear;
    
    #echo {[DEBUG] 已创建稳健型 get_maze_line (P3) 和 end_maze_capture (P2) 动作.};

    #nop {【修正】移除行首定位符 ^，直接匹配整行，这是解决问题的关键};
    #action {%1} {
        #echo {[DEBUG][捕获中] -> %1};
        #list maze_map_data add {%1};
    } {3} {get_maze_line};
    
    #nop {【修正】在结束条件的触发器前后也添加 * 通配符};
    #action {*情怀梦境迷宫第一层行走方向提示：*} {
        #echo {[DEBUG][P2] 已匹配到地图结束标志行! 捕获结束!};
        #unaction get_maze_line;
        #unaction end_maze_capture;
        #echo {<118>--- [迷宫] 地图捕获完成，开始解析... ---};
        parse_maze_map;
    } {2} {end_maze_capture};
    
} {3} {start_maze_capture};


#nop --------------------------------------------------------------------------;
#nop --- 步骤2: 解析与寻路别名  ---;
#nop --------------------------------------------------------------------------;

#alias {parse_maze_map} {
    #map clear;
    #variable start_node {};
    #variable treasure_node {};
    #variable exit_node {};

    #echo {<118>--- [迷宫] 正在内存中构建地图... ---};
    
    #loop {1} {#list maze_map_data size} {row} {
        #variable line {#list maze_map_data get $row};
        
        #math is_node_row {$row % 2};
        #if {$is_node_row == 1} {
            #loop {1} {#string length {$line}} {col} {
                
                #math is_node_col {$col % 2};
                #if {$is_node_col == 1} {
                    #variable char {#string index {$line} {$col - 1}};
                    
                    #switch {"$char"} {
                        #case {"o"} {#variable is_node {1};};
                        #case {"C"} {#variable is_node {1};};
                        #case {"B"} {#variable is_node {1};};
                        #case {"E"} {#variable is_node {1};};
                        #default {#variable is_node {0};};
                    };

                    #if {$is_node == 1} {
                        #variable vnum {R${row}C${col}};

                        #switch {"$char"} {
                             #case {"C"} {#variable start_node {$vnum}; #echo {[迷宫] 发现起点 (C) 位于 %{$vnum}}};
                             #case {"B"} {#variable treasure_node {$vnum}; #echo {[迷宫] 发现宝藏 (B) 位于 %{$vnum}}};
                             #case {"E"} {#variable exit_node {$vnum}; #echo {[迷宫] 发现出口 (E) 位于 %{$vnum}}};
                        };
                        
                        #math north_row {$row - 1};
                        #variable north_line {#list maze_map_data get $north_row};
                        #variable wall_char {#string index {$north_line} {$col - 1}};
                        #if {"$wall_char" != "|"} {
                            #math target_row {$row - 2};
                            #variable target_vnum {R${target_row}C${col}};
                            #map set exit {$vnum} {n} {$target_vnum};
                        };

                        #math south_row {$row + 1};
                        #if {$south_row <= #list maze_map_data size} {
                           #variable south_line {#list maze_map_data get $south_row};
                           #variable wall_char {#string index {$south_line} {$col - 1}};
                           #if {"$wall_char" != "|"} {
                               #math target_row {$row + 2};
                               #variable target_vnum {R${target_row}C${col}};
                               #map set exit {$vnum} {s} {$target_vnum};
                           };
                        };
                        
                        #math west_col {$col - 1};
                        #variable wall_char {#string index {$line} {$west_col - 1}};
                        #if {"$wall_char" != "-"} {
                           #math target_col {$col - 2};
                           #variable target_vnum {R${row}C${target_col}};
                           #map set exit {$vnum} {w} {$target_vnum};
                        };

                        #math east_col {$col + 1};
                        #if {$east_col <= #string length {$line}} {
                            #variable wall_char {#string index {$line} {$east_col - 1}};
                            #if {"$wall_char" != "-"} {
                                #math target_col {$col + 2};
                                #variable target_vnum {R${row}C${target_col}};
                                #map set exit {$vnum} {e} {$target_vnum};
                            };
                        };
                    };
                };
            };
        };
    };

    #echo {<118>--- [迷宫] 内存地图构建完毕。开始计算路径... ---};
    
    #if {"$start_node" == "" || "$treasure_node" == "" || "$exit_node" == ""} {
        #echo {<110>--- [迷宫] 错误: 未能找到 C, B, 或 E 中的一个或多个。操作中止。 ---};
    } {
        #path get {$start_node} {$treasure_node} path_to_b;
        #path get {$treasure_node} {$exit_node} path_to_e;

        #if {"$path_to_b" == "" || "$path_to_e" == ""} {
            #echo {<110>--- [迷宫] 错误: 无法计算出有效路径，地图可能不连通。 ---};
        } {
            #nop {注意：请务必将这里的 'b' 修改为您游戏中实际的“拾取宝藏”指令！};
            #variable final_path {$path_to_b;b;$path_to_e};
            
            #echo {<128>--- [迷宫] 路径已找到! ---};
            #echo {<128>路径 C -> B: $path_to_b};
            #echo {<128>路径 B -> E: $path_to_e};
            #echo {<178>--- 正在发送指令到服务器: $final_path ---};
            
            $final_path;
        };
    };
    
};


#nop --------------------------------------------------------------------------;
#nop --- 步骤3: 用户入口别名 (此部分逻辑无需改动) ---;
#nop --------------------------------------------------------------------------;

#alias {zouditu} {
    #echo {<178>--- [迷宫] 正在向服务器请求地图，请稍候... ---};    
    #nop {注意：如果您的游戏中查看地图的指令不是 "map", 请务必修改此处的指令！};
    mazemap;
};


#echo {<178>--- 随机迷宫求解器脚本(终极稳健版)已加载。输入 'zouditu' 来运行。 ---};