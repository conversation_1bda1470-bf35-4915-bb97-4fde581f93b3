#NOP {组队相关功能};
#NOP {通用组队触发器};
#CLASS teammodule KILL;
#CLASS teammodule OPEN;
#ACTION {^【队伍】%!*(%*)：stand by} {
  #IF {"@lower{%1}" == "$conf[team][partner]" && $conf[team][leader] != 1} {
    #VARIABLE {idle} {-60};
    look;
  };
};
#ACTION {^【队伍】%!*(%*)：to %* %* do%*} {
  #IF {"@lower{%1}" == "$conf[team][partner]" && $conf[team][leader] != 1} {
    #VARIABLE {idle} {-30};
    follow none;
    loc {
      gotodo {%2} {%3} {%4};
    };
  };
};
#ACTION {^【队伍】%!*(%*)：kill %*} {
  #IF {$conf[team][leader] == 1 && $conf[team][leader] != 1} {
    #VARIABLE {idle} {-60};
    #IF {@getSkillLevel{wuxing-zhen} > 100} {
      lineup dismiss;
      lineup form wuxing-zhen;
      lineup with $conf[team][partner];
    };
  };
  wwp;
  pfm_wuxing;
  pfm_buff_normal;
  startfight {1};
  kill %2;
};
#ACTION {^如果你愿意加入，请用 lineup with $conf[team][partner]。} {
  lineup with $conf[team][partner];
};
#ACTION {^【队伍】%!*(%*)：stopfight} {
  #IF {"@lower{%1}" == "$conf[team][partner]" && $conf[team][leader] != 1} {
    #VARIABLE {idle} {-60};
    stopfight;
    execute {
      yun jingli;
      yun jing;
      yun qi
    };
  };
};
#CLASS teammodule CLOSE;
#NOP {检查队伍,%1:后续指令,如无队伍先进行组队};
#ALIAS {checkteam} {
  #VARIABLE {mates} {0};
  #CLASS checkclass KILL;
  #CLASS checkclass OPEN;
  #ACTION {^你现在队伍中的成员有：} {
    #CLASS checkcapclass KILL;
    #CLASS checkcapclass OPEN;
    #ACTION {^  %S} {
      #MATH {mates} {$mates + 1};
    };
    #CLASS checkcapclass CLOSE;
  };
  #ACTION {^{设定环境变量：action \= \"checkteam\"|你设定checkteam为反馈信息}} {
    #VARIABLE {idle} {-60};
    #CLASS checkcapclass KILL;
    #CLASS checkclass KILL;
    #IF {$mates < 2} {
      #IF {$conf[team][leader] == 1} {
        buildteam {$conf[team][partner]} {%1};
      };
      #ELSE {
        followteam {$conf[team][partner]} {%1};
      };
    };
    #ELSE {
      %1;
    };
  };
  #CLASS checkclass CLOSE;
  #IF {"$conf[team][partner]" == ""} {
    #SHOWME {<faa>未配置队伍信息};
    #CLASS checkclass KILL;
    %1
  };
  #ELSE {
    team;
    echo {checkteam};
  };
};
#NOP {组建队伍,%1:目标id,%2:后续操作};
#ALIAS {buildteam} {
  yz {buildteam_start {%1} {%2}};
};
#ALIAS {buildteam_start} {
  #VARIABLE {okflag} {0};
  #VARIABLE {teamok} {0};
  #CLASS teamclass KILL;
  #CLASS teamclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkpartner\"|你设定checkpartner为反馈信息}} {
    #VARIABLE {idle} {-60};
    #FOREACH {*roomthings[]} {n} {
      #IF {"$roomthings[$n][+1]" == "%1"} {
        #VARIABLE {okflag} {1};
        #BREAK;
      };
    };
    #IF {$okflag == 1} {
      #VARIABLE {teamok} {0};
      team with %1;
      #DELAY {2} {
        echo {checkteam};
      };
    };
    #ELSE {
      #DELAY {2} {
        id here;
        echo {checkpartner};
      };
    };
  };
  #ACTION {^你邀请%*加入你的队伍} {
    #VARIABLE {conf[team][partnername]} {%%1};
  };
  #ACTION {^$conf[team][partnername]决定加入你的队伍} {
    #VARIABLE {teamok} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkteam\"|你设定checkteam为反馈信息}} {
    #VARIABLE {idle} {-60};
    #IF {$teamok == 0} {
      team with %1;
      #DELAY {2} {
        echo {checkteam};
      };
    };
    #ELSE {
      #CLASS teamclass KILL;
      %2;
    };
  };
  #CLASS teamclass CLOSE;
  team dismiss;
  id here;
  echo {checkpartner};
};
#NOP {跟随队伍,%1:目标id,%2:后续指令};
#ALIAS {followteam} {
  yz {followteam_start {%1} {%2}};
};
#ALIAS {followteam_start} {
  #VARIABLE {teamok} {0};
  #CLASS teamclass KILL;
  #CLASS teamclass OPEN;
  #ACTION {^如果你愿意加入，请用 team with %1。} {
    team with %1;
  };
  #ACTION {^你决定加入%*的队伍。} {
    #VARIABLE {teamok} {1};
    #VARIABLE {conf[team][partnername]} {%%1};
  };
  #ACTION {^{设定环境变量：action \= \"checkteam\"|你设定checkteam为反馈信息}} {
    #VARIABLE {idle} {-60};
    #IF {$teamok == 0} {
      #DELAY {2} {
        echo {checkteam};
      };
    };
    #ELSE {
      #CLASS teamclass KILL;
      %2;
    };
  };
  #CLASS teamclass CLOSE;
  echo {checkteam};
};
#NOP {队伍集结(队长),%1:队友,%2:后续操作};
#ALIAS {teammuster_leader} {
  #VARIABLE {okflag} {0};
  #VARIABLE {startts} {@now{}};
  #CLASS teamclass KILL;
  #CLASS teamclass OPEN;
  #ACTION {^【队伍】%!*(%*)：onposition} {
    #IF {"@lower{%%1}" == "%1"} {
      #VARIABLE {okflag} {1};
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkmuster\"|你设定checkmuster为反馈信息}} {
    #VARIABLE {idle} {-60};
    #IF {$okflag == 1} {
      #CLASS teamclass KILL;
      follow $conf[team][partner];
      tt mustered;
      %2;
    };
    #ELSEIF {@elapsed{$startts} > 180} {
      #CLASS teamclass KILL;
      #NOP {超时直接重启};
      jobprepare;
    };
    #ELSE {
      #DELAY {2} {
        echo {checkmuster};
      };
    };
  };
  #CLASS teamclass CLOSE;
  echo {checkmuster};
};
#NOP {队伍集结(成员),%1:队友,%2:后续指令};
#ALIAS {teammuster_member} {
  #VARIABLE {startts} {@now{}};
  #VARIABLE {teamok} {0};
  #VARIABLE {okflag} {0};
  #CLASS teamclass KILL;
  #CLASS teamclass OPEN;
  #ACTION {^【队伍】%!*(%*)：mustered} {
    #VARIABLE {idle} {-60};
    #IF {"@lower{%%1}" == "%1"} {
      #VARIABLE {okflag} {1};
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkmuster\"|你设定checkmuster为反馈信息}} {
    #VARIABLE {idle} {-60};
    #FOREACH {*roomthings[]} {n} {
      #IF {"$roomthings[$n][+1]" == "%1"} {
        #VARIABLE {teamok} {1};
        #BREAK;
      };
    };
    #IF {$okflag == 1} {
      #CLASS teamclass KILL;
    };
    #ELSEIF {@elapsed{$startts} > 180} {
      #CLASS teamclass KILL;
      #NOP {超时直接重启};
      jobprepare;
    };
    #ELSE {
      #IF {$teamok == 1} {
        tt onposition;
        follow $conf[team][partner];
        %2;
      };
      #DELAY {2} {
        id here;
        echo {checkmuster};
      };
    };
  };
  #CLASS teamclass CLOSE;
  id here;
  echo {checkmuster};
};
#NOP {队伍集合(队长调用),%1:城市,%2:房间,%3:队长指令,%4:队员指令};
#ALIAS {teammuster} {
  #IF {$conf[team][leader] == 1} {
    follow none;
    teamorder_todo {%1} {%2} {teammuster_member $hp[id] %4};
    #DELAY {1} {
      loc {
        gotodo {%1} {%2} {teammuster_leader {$conf[team][partner]} {%3}};
      };
    };
  };
  #ELSE {
    #SHOWME {<faa>只能队长发起召集};
  };
};
#NOP {队员待命,%1-可选，待命时间};
#ALIAS {teamorder_standby} {
  tt stand by %1;
};
#NOP {队员执行指令,%1:城市,%2:房间,%3:指令};
#ALIAS {teamorder_todo} {
  tt to %1 %2 do%3;
};
#NOP {队伍开始战斗,%1:目标id};
#ALIAS {teamorder_startfight} {
  tt kill %1;
};
#NOP {队伍开始战斗};
#ALIAS {teamorder_stopfight} {
  tt stopfight;
};