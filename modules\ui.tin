#NOP {信息界面模块};
#VA<PERSON><PERSON>LE {showsidebar} {0};
#VA<PERSON><PERSON><PERSON> {_row} {0};
#VARIABLE {rumors} {};
#VARIABLE {max_rumors} {10};
#EVENT {SCREEN RESIZE}
{
  #VARIABLE {screenheight} {%0};
  #VARIABLE {screenwidth} {%1};
  #VARIABLE {sidebarwidth} {@eval{$screenwidth / 4}};
  #VARIABLE {linecharcount} {@eval{$sidebarwidth - 2}};
  #split 1 1 0 $sidebarwidth 1;
};
#SCREEN GET {ROWS} {screenheight};
#SCREEN GET {COLS} {screenwidth};
#VARIABLE {sidebarwidth} {@eval{$screenwidth / 4}};
#VARIABLE {linecharcount} {@eval{$sidebarwidth - 2}};
#split 1 1 0 $sidebarwidth 1;

#NOP {切换边栏};
#ALIAS {togglesb} {
  #IF {$showsidebar == 0} {
    #VARIABLE {showsidebar} {1};
  };
  #ELSE {
    #VARIABLE {showsidebar} {0};
  };
  #IF {$showsidebar == 0} {
    #split 1 1 0 0 1;
    #UNTICKER {drawticker};
  };
  #ELSE {
    #split 1 1 0 $sidebarwidth 1;
    #TICKER {drawticker} {
      drawui
    } {1};
    drawui;
  };
};
#NOP {输出内容,%1:内容,%2:颜色符号,%3:行,%4:列,行列是从左边栏左上角算起};
#ALIAS {output} {
  #LOCAL {temprow} {1};
  #LOCAL {tempcol} {@eval{$screenwidth - $sidebarwidth + 2}};
  #LOCAL {color} {<ddd>};
  #IF {"%2" != ""} {
    #LOCAL {color} {%2};
  };
  #IF {"%3" != ""} {
    #LOCAL {temprow} {@eval{$temprow + %3}};
  };
  #IF {"%4" != ""} {
    #LOCAL {tempcol} {@eval{$tempcol + %4}};
  };
  #LOCAL {tempcount} {@len{$tempstr}};
  #LOCAL {spacecount} {@eval{$screenwidth - $tempcol + 2}};
  #LOCAL {tempstr} {@padRight{{%1}{$spacecount}}};
  #SHOWME {$color$tempstr} {$temprow} {$tempcol};
};
#ALIAS {drawrumor} {
  #MATH {_row} {$_row + 1};
  #LOCAL {title} {谣言信息};
  #LOCAL {tempcount} {@eval{($linecharcount - 8) / 2}};
  #LOCAL {tempstr} {@repeat{{$tempcount}{=}}};
  output {$tempstr$title$tempstr} {} {$_row} {1};
  #MATH {_row} {$_row + 1};
  
  #IF {"$rumors[]" != ""} {
    #FOREACH {$rumors[]} {r} {
      output {$r} {} {$_row} {1};
      #MATH {_row} {$_row + 1};
    };
  };
  #ELSE {
    output {暂无谣言信息} {} {$_row} {1};
    #MATH {_row} {$_row + 1};
  };
};

#ALIAS {drawui} {
  #VARIABLE {_row} {1};  
  drawchar;
  drawwalk;
  drawjob;
  drawquest;
  drawkungfu;
  drawrumor
};
#ALIAS {drawchar} {
  #LOCAL {title} {个人资料};
  #LOCAL {tempcount} {@eval{($linecharcount - 8) / 2}};
  #LOCAL {tempstr} {@repeat{{$tempcount}{=}}};
  output {$tempstr$title$tempstr} {} {$_row} {1};
  #MATH {_row} {$_row + 1};
  output {账号：$hp[name]($hp[id])} {} {$_row} {1};
  output {师承：$hp[party]$hp[master][name]} {} {$_row} {@eval{$linecharcount/2}};
  #MATH {_row} {$_row + 1};
  output {存款：$hp[balance]} {} {$_row} {1};
  output {情怀币：$hp[tongbao]} {} {$_row} {@eval{$linecharcount/2}};
  #MATH {_row} {$_row + 1};
  output {气血：$hp[qi]/$hp[qi_max]} {} {$_row} {1};
  output {内力：$hp[neili]/$hp[neili_max]} {} {$_row} {@eval{$linecharcount/2}};
  #MATH {_row} {$_row + 1};
  #IF {$hp[exp] > 10000000} {
    #LOCAL {headvalue} {@eval{$hp[exp]/1000000}};
    #LOCAL {endvalue} {@eval{($hp[exp]-$headvalue*1000000)/100000}};
    output {经验：$headvalue.$endvalue M($hp[exp_per]%)} {} {$_row} {1};
  };
  #ELSE {
    output {经验：@eval{$hp[exp]/1000} K($hp[exp_per]%)} {} {$_row} {1};
  };
  output {潜能：$hp[pot]/$hp[pot_max]} {} {$_row} {@eval{$linecharcount/2}};
  #MATH {_row} {$_row + 1};
};
#ALIAS {drawwalk} {
  #MATH {_row} {$_row + 1};
  #LOCAL {title} {快速行走};
  #LOCAL {tempcount} {@eval{($linecharcount - 8) / 2}};
  #LOCAL {tempstr} {@repeat{{$tempcount}{=}}};
  output {$tempstr$title$tempstr} {} {$_row} {1};
  #MATH {_row} {$_row + 1};
  output {当前城市：$city} {} {$_row} {1};
  #MATH {_row} {$_row + 1};
  output {当前房间：$room} {} {$_row} {1};
  #MATH {_row} {$_row + 1};
  output {目标城市：$aimcity} {} {$_row} {1};
  #MATH {_row} {$_row + 1};
  output {目标房间：$aimroom} {} {$_row} {1};
  #MATH {_row} {$_row + 1};
  output {发呆时间：$idle 秒} {} {$_row} {1};
  #MATH {_row} {$_row + 1};
  output {目标指令：} {} {$_row} {1};
  output {$aimdo} {} {$_row} {11};
  #MATH {_row} {$_row + 1};
};
#ALIAS {drawjob} {
  #MATH {_row} {$_row + 1};
  #LOCAL {title} {任务信息};
  #LOCAL {tempcount} {@eval{($linecharcount - 8) / 2}};
  #LOCAL {tempstr} {@repeat{{$tempcount}{=}}};
  output {$tempstr$title$tempstr} {} {$_row} {1};
  #MATH {_row} {$_row + 1};
  output {在线时间：@timeFormatCN{@elapsed{$env[logints]}}} {} {$_row} {1};
  #MATH {_row} {$_row + 1};
  output {任务组合：@join{conf[joblist]}} {} {$_row} {1};
  #MATH {_row} {$_row + 1};
  output {潜能用途：$conf[potpurpose]} {} {$_row} {1};
  #MATH {_row} {$_row + 1};
  #LOCAL {tempexp} {@abs{$hp[expgain]}};
  #LOCAL {symbol} {};
  #IF {$hp[expgain] < 0} {
    #LOCAL {symbol} {-};
  };
  #IF {$tempexp > 10000000} {
    #LOCAL {headvalue} {@eval{$tempexp/1000000}};
    #LOCAL {endvalue} {@eval{($tempexp-$headvalue*1000000)/100000}};
    output {经验增加：$symbol$headvalue.$endvalue M} {} {$_row} {1};
  };
  #ELSE {
    output {经验增加：@eval{$hp[expgain]/1000} K} {} {$_row} {1};
  };
  #MATH {_row} {$_row + 1};
  output {任务效率：$hp[expspeed]} {} {$_row} {1};
  #MATH {_row} {$_row + 1};
  output {上次任务：$hp[lastjob]} {} {$_row} {1};
  #MATH {_row} {$_row + 1};
  output {当前任务：$currentjob} {} {$_row} {1};
  #MATH {_row} {$_row + 1};
  output {任务地点：@getJobLocation{}} {} {$_row} {1};
  #MATH {_row} {$_row + 1};
  output {任务 NPC：@getJobNpc{}} {} {$_row} {1};
  #MATH {_row} {$_row + 1};
  #IF {&{jobroomlist} == 0} {
    output {任务房间：} {} {$_row} {1};
  };
  #ELSE {
    output {任务房间：@join{jobroomlist}} {} {$_row} {1};
  };
  #MATH {_row} {$_row + 1};
  #IF {$jobstart_ts == 0} {
    output {已用时间：} {} {$_row} {1};
  };
  #ELSE {
    output {已用时间：@elapsed{$jobstart_ts} 秒} {} {$_row} {1};
  };
  #MATH {_row} {$_row + 1};
  #IF {$scriptpause == 1} {
    output {脚本已中止执行 @elapsed{$scriptpausets} 秒，输入 rest 后恢复。} {<faa>} {$_row} {1};
  };
  #ELSE {
    output {                                          } {} {$_row} {1};
  };
};
#ALIAS {drawquest} {
  #MATH {_row} {$_row + 1};
  #LOCAL {title} {解谜信息};
  #LOCAL {tempcount} {@eval{($linecharcount - 8) / 2}};
  #LOCAL {tempstr} {@repeat{{$tempcount}{=}}};
  output {$tempstr$title$tempstr} {} {$_row} {1};
  #FOREACH {$questlist[sequence][]} {q} {
    #IF {$conf[autoquest] == 0} {
      #CONTINUE;
    };
    #IF {"$conf[subcribequests]" != "" && @contains{{conf[subcribequests]}{$q}} == 0} {
      #CONTINUE;
    };
    #IF {@contains{{conf[ignorequests]}{$q}} > 0} {
      #CONTINUE;
    };
    #IF {"$questlist[$q]" == ""} {
      #CONTINUE;
    };
    #IF {"$questlist[$q][party]" != "" && "$questlist[$q][party]" != "$hp[party]"} {
      #CONTINUE;
    };
    #IF {"$questlist[$q][gender]" != "" && "$questlist[$q][gender]" != "$hp[sex]"} {
      #CONTINUE;
    };
    #NOP {先决条件,不满足不显示};
    #IF {"$questlist[$q][quest]" != ""} {
      #LOCAL {prequest} {1};
      #FOREACH {*questlist[$q][quest][]} {pq} {
        #IF {"$questlist[$q][quest][$pq]" == ""} {
          #IF {"$questlist[$pq][done]" != "YES"} {
            #LOCAL {prequest} {0};
            #BREAK;
          };
        };
        #ELSEIF {"$questlist[$pq][done]" != "YES"} {
          #IF {$questlist[$pq][laststep] < $questlist[$q][quest][$pq]} {
            #LOCAL {prequest} {0};
            #BREAK;
          };
        };
      };
      #IF {$prequest == 0} {
        #CONTINUE;
      };
    };
    #NOP {任务次数作为先决条件};
    #IF {"$questlist[$q][jobtimes]" != ""} {
      #LOCAL {prequest} {1};
      #FOREACH {*questlist[$q][jobtimes][]} {jb} {
        #IF {@eval{$hp[jobtimes][$jb]} < @eval{$questlist[$q][jobtimes][$jb]}} {
          #LOCAL {prequest} {0};
          #BREAK;
        };
      };
      #IF {$prequest == 0} {
        #CONTINUE;
      };
    };
    #LOCAL {prefix} {@padRight{{$q}{8}}：};
    #LOCAL {spacecount} {@eval{$linecharcount - 12}};
    #LOCAL {precolor} {};
    #LOCAL {suffix} {};
    #IF {"$questlist[$q][done]" == "YES"} {
      #IF {"$conf[displaysuccess]" != "1"} {
        #CONTINUE;
      };
      #LOCAL {precolor} {<afa>};
      #IF {"$questlist[$q][steps]" != ""} {
        #LOCAL {suffix} {解谜完成};
      };
      #ELSE {
        #LOCAL {suffix} {解谜成功};
      };
    };
    #ELSEIF {"$questlist[$q][done]" == "FAIL"} {
      #IF {"$conf[displaysuccess]" != "1"} {
        #CONTINUE;
      };
      #LOCAL {precolor} {<faa>};
      #LOCAL {suffix} {已失败};
    };
    #MATH {_row} {$_row + 1};
    output {$prefix} {} {$_row} {1};
    #LOCAL {conditionok} {1};
    #NOP {通宝};
    #IF {"$suffix" == ""} {
      #IF {$hp[tongbao] < 100} {
        #LOCAL {precolor} {<faa>};
        #LOCAL {suffix} {情怀币不足 $hp[tongbao] < 100};
      };
    };
    #NOP {经验阈值};
    #IF {"$suffix" == ""} {
      #LOCAL {limitexp} {0};
      #IF {"$questlist[$q][steps]" != ""} {
        #LOCAL {tempstep} {@eval{$questlist[$q][laststep] + 1}};
        #LOCAL {limitexp} {@eval{$questlist[$q][steps][$tempstep][limitexp]}};
      };
      #ELSE {
        #LOCAL {limitexp} {$questlist[$q][limitexp]};
      };
      #IF {$hp[exp] < $limitexp} {
        #LOCAL {precolor} {<faa>};
        #LOCAL {suffix} {经验不足  $limitexp};
      };
    };
    #NOP {经验间隔};
    #IF {"$suffix" == ""} {
      #LOCAL {intervalexp} {0};
      #IF {"$questlist[$q][steps]" != ""} {
        #LOCAL {tempstep} {@eval{$questlist[$q][laststep] + 1}};
        #LOCAL {intervalexp} {@eval{$questlist[$q][steps][$tempstep][intervalexp]}};
      };
      #ELSE {
        #LOCAL {intervalexp} {$questlist[$q][intervalexp]};
      };
      #IF {@eval{$hp[exp] - $questlist[$q][lastexp]} < $intervalexp} {
        #LOCAL {precolor} {<faa>};
        #LOCAL {suffix} {经验间隔  @eval{$intervalexp - ($hp[exp] - $questlist[$q][lastexp])}};
      };
    };
    #NOP {时间间隔};
    #IF {"$suffix" == ""} {
      #LOCAL {intervaltime} {0};
      #IF {"$questlist[$q][steps]" != ""} {
        #LOCAL {tempstep} {@eval{$questlist[$q][laststep] + 1}};
        #LOCAL {intervaltime} {@eval{$questlist[$q][steps][$tempstep][intervaltime]}};
      };
      #ELSE {
        #LOCAL {intervaltime} {$questlist[$q][intervaltime]};
      };
      #IF {@elapsed{$questlist[$q][lasttime]} < $intervaltime} {
        #LOCAL {precolor} {<faa>};
        #LOCAL {suffix} {时间间隔  @timeFormat{@eval{$intervaltime - @elapsed{$questlist[$q][lasttime]}}}};
      };
    };
    #NOP {技能条件};
    #IF {"$suffix" == "" && "$questlist[$q][skills]" != ""} {
      #FOREACH {*questlist[$q][skills][]} {sk} {
        #IF {@getSkillLevel{$sk} < $questlist[$q][skills][$sk]} {
          #LOCAL {precolor} {<faa>};
          #LOCAL {suffix} {$sk < $questlist[$q][skills][$sk]};
          #BREAK;
        };
      };
    };
    #NOP {触发类的环境条件};
    #LOCAL {envcolor} {};
    #LOCAL {envsuffix} {};
    #IF {"$questlist[$q][env]" != "" && "$env[$questlist[$q][env]]" != "YES"} {
      #LOCAL {envcolor} {<ffa>};
      #LOCAL {envsuffix} {未触发};
      #LOCAL {totaltimes} {};
      #IF {"$q" == "吸星大法"} {
        #MATH {totaltimes} {@eval{$hp[jobtimes][华山]} + @eval{$hp[jobtimes][武当]}};
        #LOCAL {envsuffix} {未触发(@eval{$totaltimes % 50} / 50)};
      };
      #ELSEIF {"$q" == "独孤九剑"} {
        #MATH {totaltimes} {@eval{$hp[jobtimes][华山]} + @eval{$hp[jobtimes][送信]} + @eval{$hp[jobtimes][丐帮]}};
        #LOCAL {envsuffix} {未触发(@eval{$totaltimes % 50}) / 50};
      };
    };
    #IF {"$suffix" == ""} {
      #LOCAL {precolor} {$envcolor};
      #LOCAL {suffix} {$envsuffix}
    };
    #ELSE {
      #LOCAL {suffix} {$suffix $envsuffix}
    };

    #IF {"$suffix" == ""} {
      #LOCAL {precolor} {<afa>};
      #IF {@now{} < $questlist[$q][timestamp]} {
        #LOCAL {suffix} {等待时间 @timeFormat{@eval{$questlist[$q][timestamp] - @now{}}}};
      };
      #ELSE {
        #IF {"$q" == "$questmodule"} {
          #LOCAL {precolor} {<aff>};
          #LOCAL {suffix} {正在解谜};
        };
        #ELSE {
          #LOCAL {suffix} {等待时机};
        };
      };
    };
    #IF {"$questlist[$q][steps]" != "" && "$suffix" != "解谜完成"} {
      #LOCAL {nextstep} {@eval{$questlist[$q][laststep] + 1}};
      #LOCAL {suffix} {$suffix ($questlist[$q][steps][$nextstep][name])};
    };
    output {@padRight{{$suffix}{$tempcount}}} {$precolor} {$_row} {12};
  };
  #MATH {_row} {$_row + 1};
  output {@padLeft{{}{$spacecount}}} {} {$_row} {1};
}
#ACTION {^【谣言】%*：%*} {
  #LIST {rumors} {INSERT} {1} {%1：%2};
  #IF {&rumors[] > $max_rumors} {
      #LIST {rumors} {DELETE} {$max_rumors};
  };
};
#ACTION {^【扬州知府通告】%*，协助将彼等%*} {
  #LIST {rumors} {INSERT} {1} {扬州知府：%1。};
  #IF {&rumors[] > $max_rumors} {
      #LIST {rumors} {DELETE} {$max_rumors};
  };
};
#ALIAS {drawkungfu} {
  #MATH {_row} {$_row + 1};
  #LOCAL {title} {特殊技能};
  #LOCAL {tempcount} {@eval{($linecharcount - 8) / 2}};
  #LOCAL {tempstr} {@repeat{{$tempcount}{=}}};
  output {$tempstr$title$tempstr} {} {$_row} {1};
  #MATH {_row} {$_row + 1};

  #FOREACH {*kungfu[spec][]} {sk} {
    #IF {"$kungfu[spec][$sk][name]" == ""} {
      #CONTINUE;
    };    
    #LOCAL {skillname} {@padRight{{$kungfu[spec][$sk][name]}{12}}};
    #LOCAL {skillid} {@padRight{{($sk)}{20}}};
    #LOCAL {skilllv} {@padLeft{{$kungfu[spec][$sk][lv]}{4}}};
    #LOCAL {skillpoint} {$kungfu[spec][$sk][point]};    
    #LOCAL {color} {<ddd>};
    #IF {$skillpoint >= $kungfu[spec][$sk][max_point]} {
      #LOCAL {color} {<afa>};
    };    
    #LOCAL {arrow} {→};
    #IF {"$kungfu[base][$sk][jifa]" != ""} {
      #LOCAL {arrow} {→};
    };    
    output {$skillname $skillid $arrow $skilllv/$skillpoint} {$color} {$_row} {1};
    #MATH {_row} {$_row + 1};
  };
};
togglesb;
#SHOWME {<fac>@padRight{{侧边栏}{12}}<fac> <cfa>模块加载完毕<cfa>};



