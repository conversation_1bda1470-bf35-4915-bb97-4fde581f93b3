#NOP {解谜模块};
#NOP {古墓互搏};
import {quests/quest_gmhb};
#NOP {古墓九阴+石刻};
import {quests/quest_gmwg};
#NOP {乐音绝技};
import {quests/quest_yyjj};
#NOP {三无三不手};
import {quests/quest_swsbs};
#NOP {全真连环};
import {quests/quest_qzlh};
#NOP {全真剑诀};
import {quests/quest_qzjj};
#NOP {全真定阳针};
import {quests/quest_qzdy};
#NOP {九阳神功};
import {quests/quest_jysg};
#NOP {乾坤大挪移};
import {quests/quest_qkdny};
#NOP {七伤拳};
import {quests/quest_qsq};
#NOP {蛤蟆功一};
import {quests/quest_hmg1};
#NOP {蛤蟆功二};
import {quests/quest_hmg2};
#NOP {蛤蟆功三};
import {quests/quest_hmg3};
#NOP {越女剑法一};
import {quests/quest_ynjf1};
#NOP {越女剑法二};
import {quests/quest_ynjf2};
#NOP {九阴下};
import {quests/quest_jydown};
#NOP {九阴上};
import {quests/quest_jyup};
#NOP {雪山飞狐四部曲};
import {quests/quest_xsfh};
#NOP {胡家刀法};
import {quests/quest_hjdf};
#NOP {苗家剑法};
import {quests/quest_mjjf};
#NOP {刀剑融合};
import {quests/quest_djrh};
#NOP {冷泉神功};
import {quests/quest_lqsg};
#NOP {天龙八部N步曲};
import {quests/quest_tlbb};
#NOP {北冥神功};
import {quests/quest_bmsg};
#NOP {凌波微步};
import {quests/quest_lbwb};
#NOP {冰蚕毒掌};
import {quests/quest_bcdz};
#NOP {辟邪剑谱};
import {quests/quest_bxjp};
#NOP {葵花宝典};
import {quests/quest_khbd};
#NOP {太极宗师};
import {quests/quest_tjzs};
#NOP {刀剑舞};
import {quests/quest_dwjw};
#NOP {金刀黑剑};
import {quests/quest_jdhj};
#NOP {铁掌融合};
import {quests/quest_tzrh};
#NOP {千蛛万毒手};
import {quests/quest_qzwds};
#NOP {凝血神爪};
import {quests/quest_nxsz};
#NOP {连城诀两部曲};
import {quests/quest_lcj};
#NOP {神照经};
import {quests/quest_szj};
#NOP {躺尸剑法};
import {quests/quest_tsjf};
#NOP {以彼之道还施彼身};
import {quests/quest_dzxy};
#NOP {凌虚指力};
import {quests/quest_lxzl};
#NOP {君临天下};
import {quests/quest_jltx};
#NOP {射雕英雄传};
import {quests/quest_sdyxz};
#NOP {杨家枪};
import {quests/quest_yjq};
#NOP {独孤九剑};
import {quests/quest_dgjj};
#NOP {独孤九剑无招};
import {quests/quest_jjwz};
#NOP {吸星大法};
import {quests/quest_xxdf};
#NOP {无相劫指};
import {quests/quest_wxjz};
#NOP {倚天屠龙功};
import {quests/quest_yttlg};
#NOP {武当剑诀};
import {quests/quest_wdjj};
#NOP {空明拳};
import {quests/quest_kmq};
#NOP {左右互搏};
import {quests/quest_hubo};
#NOP {金蛇剑法};
import {quests/quest_jsjf};
#NOP {血祭神刀};
import {quests/quest_xjsd};

#VARIABLE {questmodule} {};
#VARIABLE {questlist} {
  {sequence} {
    {1}{古墓互搏}
    {2}{古墓九阴}
    {3}{古墓石刻}
    {4}{乐音绝技}
    {5}{三无三不手}
    {6}{全真连环}
    {7}{全真剑诀}
    {8}{定阳针}
    {9}{乾坤大挪移}
    {10}{九阳神功}
    {11}{铁掌融合}
    {13}{太极宗师}
    {14}{灵鹫剑舞}
    {15}{逍遥刀舞}
    {16}{凌虚指力}
    {17}{君临天下}
    {18}{斗转星移}
    {19}{独孤九剑}
    {20}{九剑无招}
    {21}{蛤蟆功一}
    {22}{蛤蟆功二}
    {23}{蛤蟆功三}
    {24}{越女剑法一}
    {25}{九阴下}
    {26}{九阴上}
    {27}{雪山飞狐}
    {28}{胡家刀法}
    {29}{苗家剑法}
    {30}{刀剑融合}
    {31}{冷泉神功}
    {32}{天龙八部}
    {33}{凌波微步}
    {34}{北冥神功}
    {35}{冰蚕毒掌}
    {36}{辟邪剑谱}
    {37}{葵花宝典}
    {38}{金刀黑剑}
    {39}{千蛛万毒手}
    {40}{凝血神爪}
    {41}{连城诀}
    {42}{神照经}
    {43}{躺尸剑法}
    {44}{射雕英雄}
    {45}{杨家枪}
    {46}{吸星大法}
    {47}{无相劫指}
    {48}{倚天屠龙功}
    {49}{武当剑诀}
    {50}{空明拳}
    {51}{左右互搏}
    {52}{金蛇剑法}
    {53}{金蛇剑法}
    {54}{血祭神刀}
    {55}{越女剑法二}
  }
  
  {古墓互搏} {
    {party} {古墓派}
    {limitexp} {170000}
    {intervalexp} {0}
    {intervaltime} {0}
    {lastexp} {0}
    {lasttime} {0}
    {laststep} {0}
    {chance} {0}
    {fail} {0}
    {done} {}
    {timestamp} {0}
    {skills} {
      {yunu-xinjing} {120}
    }
    {alias} {goquest_gmhb}
  }
  {古墓九阴} {
    {party} {古墓派}
    {limitexp} {2000000}
    {intervalexp} {0}
    {intervaltime} {86400}
    {lastexp} {0}
    {lasttime} {0}
    {laststep} {0}
    {chance} {0}
    {fail} {0}
    {done} {}
    {timestamp} {0}
    {alias} {goquest_gmwg}
  }
  {古墓石刻} {
    {party} {古墓派}
    {limitexp} {2000000}
    {intervalexp} {0}
    {intervaltime} {86400}
    {lastexp} {0}
    {lasttime} {0}
    {laststep} {0}
    {chance} {0}
    {fail} {0}
    {done} {}
    {timestamp} {0}
    {skills} {
      {yunu-xinjing} {550}
      {xuantie-jianfa} {550}
      {yunu-jianfa} {550}
    }
    {alias} {goquest_gmwg}
  }
  {乐音绝技} {
    {party} {古墓派}
    {gender} {f}
    {limitexp} {2000000}
    {intervalexp} {0}
    {intervaltime} {86400}
    {lastexp} {0}
    {lasttime} {0}
    {laststep} {0}
    {chance} {0}
    {fail} {0}
    {done} {}
    {timestamp} {0}
    {skills} {
      {yinsuo-jinling} {200}
    }
    {alias} {goquest_yyjj}
  }
  {三无三不手} {
    {party} {古墓派}
    {gender} {f}
    {limitexp} {2000000}
    {intervalexp} {0}
    {intervaltime} {86400}
    {lastexp} {0}
    {lasttime} {0}
    {laststep} {0}
    {chance} {0}
    {fail} {0}
    {done} {}
    {timestamp} {0}
    {skills} {
      {yinsuo-jinling} {200}
      {meinu-quanfa} {200}
    }
    {alias} {goquest_swsbs}
  }
  {九阳神功} {
    {party} {明教}
    {limitexp} {300000}
    {intervalexp} {0}
    {intervaltime} {86400}
    {lastexp} {0}
    {lasttime} {0}
    {laststep} {0}
    {chance} {0}
    {fail} {0}
    {done} {}
    {timestamp} {0}
    {env} {jysg}
    {alias} {goquest_jysg}
  }
  {乾坤大挪移} {
    {party} {明教}
    {limitexp} {350000}
    {intervalexp} {0}
    {intervaltime} {86400}
    {lastexp} {0}
    {lasttime} {0}
    {laststep} {0}
    {chance} {0}
    {fail} {0}
    {done} {}
    {timestamp} {0}
    {skills} {
      {shenghuo-lingfa} {150}
    }
    {alias} {goquest_qkdny}
  }
  {七伤拳} {
    {party} {明教}
    {limitexp} {1000000}
    {intervalexp} {0}
    {intervaltime} {86400}
    {lastexp} {0}
    {lasttime} {0}
    {laststep} {0}
    {chance} {0}
    {fail} {0}
    {done} {}
    {timestamp} {0}
    {quest} {
      {九阳神功} {}
    }
    {alias} {goquest_qsq}
  }
  {蛤蟆功一} {
    {limitexp} {2000000}
    {gender} {m}
    {intervalexp} {0}
    {intervaltime} {86400}
    {lastexp} {0}
    {lasttime} {0}
    {laststep} {0}
    {chance} {3}
    {fail} {0}
    {done} {}
    {timestamp} {0}
    {alias} {goquest_hmg1}
  }
  {蛤蟆功二} {
    {limitexp} {2000000}
    {gender} {m}
    {intervalexp} {0}
    {intervaltime} {86400}
    {lastexp} {0}
    {lasttime} {0}
    {laststep} {0}
    {chance} {3}
    {fail} {0}
    {done} {}
    {timestamp} {0}    
    {skills} {
      {hamagong} {80}
    }
    {alias} {goquest_hmg2}
  }
  {蛤蟆功三} {
    {limitexp} {6000000}
    {gender} {m}
    {intervalexp} {0}
    {intervaltime} {86400}
    {lastexp} {0}
    {lasttime} {0}
    {laststep} {0}
    {chance} {0}
    {fail} {0}
    {done} {}
    {timestamp} {0}    
    {alias} {goquest_hmg3}
  }
  {越女剑法一} {
    {limitexp} {2000000}
    {intervalexp} {0}
    {intervaltime} {86400}
    {gender} {f}
    {lastexp} {0}
    {lasttime} {0}
    {laststep} {0}
    {chance} {0}
    {fail} {0}
    {done} {}
    {timestamp} {0}
    {alias} {goquest_ynjf1}
  }
  {越女剑法二} {
    {limitexp} {12000000}
    {intervalexp} {0}
    {intervaltime} {86400}
    {gender} {f}
    {lastexp} {0}
    {lasttime} {0}
    {laststep} {0}
    {chance} {0}
    {fail} {0}
    {done} {}
    {timestamp} {0}
    {skills} {
      {yuenu-jian} {221}
    }
    {alias} {goquest_ynjf2}
  }
  {九阴下} {
    {limitexp} {1000000}
    {intervalexp} {0}
    {intervaltime} {86400}
    {lastexp} {0}
    {lasttime} {0}
    {laststep} {0}
    {chance} {0}
    {fail} {0}
    {done} {}
    {timestamp} {0}
    {alias} {goquest_jydown}
  } 
  {九阴上} {
    {limitexp} {2000000}
    {intervalexp} {0}
    {intervaltime} {86400}
    {lastexp} {0}
    {lasttime} {0}
    {laststep} {0}
    {chance} {0}
    {fail} {0}
    {done} {}
    {timestamp} {0}
    {skills} {
      {qimen-bagua} {51}
    }
    {alias} {goquest_jyup}
  }
  {全真连环} {
    {party} {全真教}
    {limitexp} {170000}
    {intervalexp} {0}
    {intervaltime} {86400}
    {lastexp} {0}
    {lasttime} {0}
    {laststep} {0}
    {chance} {0}
    {fail} {0}
    {done} {}
    {timestamp} {0}
    {alias} {goquest_qzlh}
  }
  {全真剑诀} {
    {party} {全真教}
    {limitexp} {2000000}
    {intervalexp} {0}
    {intervaltime} {86400}
    {lastexp} {0}
    {lasttime} {0}
    {laststep} {0}
    {chance} {0}
    {fail} {0}
    {done} {}
    {timestamp} {0}
    {timezone} {#>=19 || #<=2}
    {skills} {
      {quanzhen-jianfa} {300}
    }
    {alias} {goquest_qzjj}
  }
  {定阳针} {
    {party} {全真教}
    {limitexp} {2000000}
    {intervalexp} {0}
    {intervaltime} {86400}
    {lastexp} {0}
    {lasttime} {0}
    {laststep} {0}
    {chance} {0}
    {fail} {0}
    {done} {}
    {timestamp} {0}
    {skills} {
      {xiantian-gong} {300}
      {quanzhen-jianfa} {300}
    }
    {alias} {goquest_qzdy}
  }
  {雪山飞狐} {
    {steps} {
      {1} {
        {name} {两页刀法篇}
        {limitexp} {2000000}
        {intervalexp} {0}
        {intervaltime} {0}
      }
      {2} {
        {name} {复仇篇}
        {limitexp} {2000000}
        {intervalexp} {0}
        {intervaltime} {10800}
      }
      {3} {
        {name} {解药篇}
        {limitexp} {2000000}
        {intervalexp} {0}
        {intervaltime} {0}
      }
      {4} {
        {name} {宝藏篇}
        {limitexp} {2000000}
        {intervalexp} {0}
        {intervaltime} {10800}
      }
    }
    {lastexp} {0}
    {lasttime} {0}
    {laststep} {0}
    {chance} {0}
    {fail} {0}
    {done} {}
    {timestamp} {0}
    {alias} {goquest_xsfh}
  }
  {胡家刀法} {
    {limitexp} {2000000}
    {intervalexp} {0}
    {intervaltime} {86400}
    {lastexp} {0}
    {lasttime} {0}
    {laststep} {0}
    {chance} {0}
    {fail} {0}
    {done} {}
    {timestamp} {0}
    {quest} {
      {雪山飞狐} {1}
    }
    {alias} {goquest_hjdf}
  }
  {苗家剑法} {
    {limitexp} {2000000}
    {intervalexp} {0}
    {intervaltime} {86400}
    {lastexp} {0}
    {lasttime} {0}
    {laststep} {0}
    {chance} {0}
    {fail} {0}
    {done} {}
    {timestamp} {0}
    {quest} {
      {雪山飞狐} {3}
    }
    {alias} {goquest_mjjf}
  }
  {刀剑融合} {
    {limitexp} {2000000}
    {intervalexp} {0}
    {intervaltime} {86400}
    {lastexp} {0}
    {lasttime} {0}
    {laststep} {0}
    {chance} {0}
    {fail} {0}
    {done} {}
    {timestamp} {0}
    {quest} {
      {胡家刀法} {}
      {苗家剑法} {}
    }
    {alias} {goquest_djrh}
  }
  {冷泉神功} {
    {steps} {
      {1} {
        {name} {问苗人凤}
        {limitexp} {2000000}
        {intervalexp} {0}
        {intervaltime} {86400}
      }
      {2} {
        {name} {交还玉佩}
        {limitexp} {2000000}
        {intervalexp} {0}
        {intervaltime} {86400}
      }
    }
    {limitexp} {0}
    {intervalexp} {0}
    {intervaltime} {0}
    {lastexp} {0}
    {lasttime} {0}
    {laststep} {0}
    {chance} {0}
    {fail} {0}
    {done} {}
    {timestamp} {0}
    {quest} {
      {雪山飞狐} {4}
    }
    {alias} {goquest_lqsg}
  }
  {天龙八部} {
    {steps} {
      {1} {
        {name} {寻找段誉}
        {limitexp} {2000000}
        {intervalexp} {0}
        {intervaltime} {0}
      }
      {2} {
        {name} {营救段誉}
        {limitexp} {5000000}
        {intervalexp} {0}
        {intervaltime} {86400}
        {timezone} {#>=22 || #<=2}
      }
      {3} {
        {name} {大战天龙}
        {limitexp} {100000000}
        {intervalexp} {0}
        {intervaltime} {86400}
      }
      {4} {
        {name} {萧峰身世}
        {limitexp} {100000000}
        {intervalexp} {0}
        {intervaltime} {86400}
      }
      {5} {
        {name} {大辽救援}
        {limitexp} {100000000}
        {intervalexp} {0}
        {intervaltime} {86400}
        {timezone} {#>=12}
      }
    }
    {lastexp} {0}
    {lasttime} {0}
    {laststep} {0}
    {chance} {0}
    {fail} {0}
    {done} {}
    {timestamp} {0}
    {alias} {goquest_tlbb}
  }
  {凌波微步} {
    {limitexp} {2000000}
    {intervalexp} {0}
    {intervaltime} {86400}
    {lastexp} {0}
    {lasttime} {0}
    {laststep} {0}
    {chance} {0}
    {fail} {0}
    {done} {}
    {timestamp} {0}    
    {alias} {goquest_lbwb}
  }
  {北冥神功} {
    {limitexp} {2000000}
    {intervalexp} {0}
    {intervaltime} {86400}
    {lastexp} {0}
    {lasttime} {0}
    {laststep} {0}
    {chance} {0}
    {fail} {0}
    {done} {}
    {timestamp} {0}   
    {alias} {goquest_bmsg}
  }
  {冰蚕毒掌} {
    {party} {星宿派}
    {limitexp} {200000}
    {intervalexp} {0}
    {intervaltime} {86400}
    {lastexp} {0}
    {lasttime} {0}
    {laststep} {0}
    {chance} {0}
    {fail} {0}
    {done} {}
    {timestamp} {0}
    {alias} {goquest_bcdz}
  }
  {辟邪剑谱} {
    {gender} {m}
    {steps} {
      {1} {
        {name} {向阳老宅篇}
        {limitexp} {2000000}
        {intervalexp} {0}
        {intervaltime} {0}
      }
      {2} {
        {name} {华山听床篇}
        {limitexp} {2000000}
        {intervalexp} {0}
        {intervaltime} {86400}
        {timezone} {#>=18 && #<=21}
      }
    }
    {limitexp} {}
    {intervalexp} {0}
    {intervaltime} {}
    {lastexp} {0}
    {lasttime} {0}
    {laststep} {0}
    {chance} {0}
    {fail} {0}
    {done} {}
    {timestamp} {0}
    {alias} {goquest_bxjp}
  }
  {葵花宝典} {
    {gender} {m}
    {limitexp} {8000000}
    {intervalexp} {0}
    {intervaltime} {86400}
    {lastexp} {0}
    {lasttime} {0}
    {laststep} {0}
    {chance} {0}
    {fail} {0}
    {done} {}
    {timestamp} {0}
    {skills} {
      {pixie-jian} {151}
    }
    {alias} {goquest_khbd}
  }
  {太极宗师} {
    {party} {武当派}
    {limitexp} {200000}
    {intervalexp} {0}
    {intervaltime} {86400}
    {lastexp} {0}
    {lasttime} {0}
    {laststep} {0}
    {chance} {0}
    {fail} {0}
    {done} {}
    {timestamp} {0}
    {alias} {goquest_tjzs}
  }
  {灵鹫剑舞} {
    {party} {灵鹫宫}
    {limitexp} {200000}
    {intervalexp} {0}
    {intervaltime} {86400}
    {lastexp} {0}
    {lasttime} {0}
    {laststep} {0}
    {chance} {0}
    {fail} {0}
    {done} {}
    {timestamp} {0}
    {timezone} {#>=7 || #<=9}
    {alias} {goquest_dwjw}
  }
  {逍遥刀舞} {
    {party} {逍遥派}
    {limitexp} {200000}
    {intervalexp} {0}
    {intervaltime} {86400}
    {lastexp} {0}
    {lasttime} {0}
    {laststep} {0}
    {chance} {0}
    {fail} {0}
    {done} {}
    {timestamp} {0}
    {timezone} {#>=7 || #<=9}
    {alias} {goquest_dwjw}
  }
  {金刀黑剑} {
    {limitexp} {2000000}
    {intervalexp} {0}
    {intervaltime} {86400}
    {lastexp} {0}
    {lasttime} {0}
    {laststep} {0}
    {chance} {0}
    {fail} {0}
    {done} {}
    {timestamp} {0}
    {skills} {
      {qimen-bagua} {51}
    }
    {alias} {goquest_jdhj}
  }
  {凌虚指力} {
    {party} {姑苏慕容}
    {limitexp} {3000000}
    {intervalexp} {0}
    {intervaltime} {86400}
    {lastexp} {0}
    {lasttime} {0}
    {laststep} {0}
    {chance} {0}
    {fail} {0}
    {done} {}
    {timestamp} {0}
    {alias} {goquest_lxzl}
  }
  {君临天下} {
    {party} {姑苏慕容}
    {limitexp} {3000000}
    {intervalexp} {0}
    {intervaltime} {86400}
    {lastexp} {0}
    {lasttime} {0}
    {laststep} {0}
    {chance} {0}
    {fail} {0}
    {done} {}
    {timestamp} {0}
    {alias} {goquest_jltx}
  }
  {斗转星移} {
    {party} {姑苏慕容}
    {limitexp} {1000000}
    {intervalexp} {0}
    {intervaltime} {86400}
    {lastexp} {0}
    {lasttime} {0}
    {laststep} {0}
    {chance} {0}
    {fail} {0}
    {done} {}
    {timestamp} {0}
    {alias} {goquest_dzxy}
  }
  {独孤九剑} {
    {party} {华山派}
    {limitexp} {200000}
    {intervalexp} {0}
    {intervaltime} {0}
    {lastexp} {0}
    {lasttime} {0}
    {laststep} {0}
    {chance} {0}
    {fail} {0}
    {done} {}
    {timestamp} {0}
    {timezone} {}
    {env} {dgjj}
    {alias} {goquest_dgjj}
  }
  {九剑无招} {
    {party} {华山派}
    {limitexp} {3000000}
    {intervalexp} {0}
    {intervaltime} {86400}
    {lastexp} {0}
    {lasttime} {0}
    {laststep} {0}
    {chance} {0}
    {fail} {0}
    {done} {}
    {timestamp} {0}
    {timezone} {}
    {skills} {
      {dugu-jiujian} {450}
    }
    {quest} {
      {独孤九剑} {1}
    }
    {alias} {goquest_jjwz}
  }
  {千蛛万毒手} {
    {gender} {f}
    {limitexp} {2000000}
    {intervalexp} {0}
    {intervaltime} {86400}
    {lastexp} {0}
    {lasttime} {0}
    {laststep} {0}
    {chance} {0}
    {fail} {0}
    {done} {}
    {timestamp} {0}
    {alias} {goquest_qzwds}
  }
  {铁掌融合} {
    {party} {铁掌帮}
    {master} {上官剑南}
    {steps} {
      {1} {
        {name} {斧头篇}
        {limitexp} {2000000}
        {intervalexp} {0}
        {intervaltime} {86400}
      }
      {2} {
        {name} {刀笔篇}
        {limitexp} {2000000}
        {intervalexp} {0}
        {intervaltime} {86400}
      }
    }
    {lastexp} {0}
    {lasttime} {0}
    {laststep} {0}
    {chance} {0}
    {fail} {0}
    {done} {}
    {timestamp} {0}
    {alias} {goquest_tzrh}
  }
  {凝血神爪} {
    {limitexp} {200000}
    {intervalexp} {0}
    {intervaltime} {86400}
    {lastexp} {0}
    {lasttime} {0}
    {laststep} {0}
    {chance} {0}
    {fail} {0}
    {done} {}
    {timestamp} {0}
    {jobtimes} {
      {天地会} {1051}
    }
    {alias} {goquest_nxsz}
  }
  {连城诀} {
    {steps} {
      {1} {
        {name} {雪谷激斗篇}
        {limitexp} {500000}
        {intervalexp} {0}
        {intervaltime} {0}
      }
      {2} {
        {name} {武馆探秘篇}
        {limitexp} {500000}
        {intervalexp} {0}
        {intervaltime} {86400}
      }
    }
    {lastexp} {0}
    {lasttime} {0}
    {laststep} {0}
    {chance} {0}
    {fail} {0}
    {done} {}
    {timestamp} {0}
    {alias} {goquest_lcj}
  }
  {神照经} {
    {steps} {
      {1} {
        {name} {基础篇}
        {limitexp} {500000}
        {intervalexp} {0}
        {intervaltime} {86400}
      }
      {2} {
        {name} {提高篇}
        {limitexp} {500000}
        {intervalexp} {0}
        {intervaltime} {86400}
      }
    }
    {lastexp} {0}
    {lasttime} {0}
    {laststep} {0}
    {chance} {0}
    {fail} {0}
    {done} {}
    {timestamp} {0}
    {quest} {
      {连城诀} {1}
    }
    {alias} {goquest_szj}
  }
  {躺尸剑法} {
    {limitexp} {500000}
    {intervalexp} {0}
    {intervaltime} {86400}
    {lastexp} {0}
    {lasttime} {0}
    {laststep} {0}
    {chance} {0}
    {fail} {0}
    {done} {}
    {timestamp} {0}
    {quest} {
      {连城诀} {1}
    }
    {alias} {goquest_tsjf}
  }
  {射雕英雄} {
    {steps} {
      {1} {
        {name} {风雪惊变}
        {limitexp} {2000000}
        {intervalexp} {0}
        {intervaltime} {86400}
      }
    }
    {lastexp} {0}
    {lasttime} {0}
    {laststep} {0}
    {chance} {0}
    {fail} {0}
    {done} {}
    {timestamp} {0}
    {alias} {goquest_sdyxz}
  }
  {杨家枪} {
    {limitexp} {2000000}
    {intervalexp} {0}
    {intervaltime} {86400}
    {lastexp} {0}
    {lasttime} {0}
    {laststep} {0}
    {chance} {0}
    {fail} {0}
    {done} {}
    {timestamp} {0}    
    {alias} {goquest_yjq}
  }
  {吸星大法} {
    {limitexp} {2000000}
    {intervalexp} {0}
    {intervaltime} {86400}
    {lastexp} {0}
    {lasttime} {0}
    {laststep} {0}
    {chance} {0}
    {fail} {0}
    {done} {}
    {timestamp} {0}
    {env} {xxdf}
    {alias} {goquest_xxdf}
  }
  {无相劫指} {
    {party} {少林派}
    {limitexp} {200000}
    {intervalexp} {0}
    {intervaltime} {86400}
    {lastexp} {0}
    {lasttime} {0}
    {laststep} {0}
    {chance} {0}
    {fail} {0}
    {done} {}
    {timestamp} {0}
    {env} {wxjz}
    {jobtimes} {
      {护送} {1000}
    }
    {alias} {goquest_wxjz}
  }
  {倚天屠龙功} {
    {party} {武当派}
    {limitexp} {100000}
    {intervalexp} {0}
    {intervaltime} {0}
    {lastexp} {0}
    {lasttime} {0}
    {laststep} {0}
    {chance} {0}
    {fail} {0}
    {done} {}
    {timestamp} {0}
    {env} {yttl}
    {alias} {goquest_yttlg}
  }
  {武当剑诀} {
    {party} {武当派}
    {limitexp} {10000000}
    {intervalexp} {0}
    {intervaltime} {86400}
    {lastexp} {0}
    {lasttime} {0}
    {laststep} {0}
    {chance} {0}
    {fail} {0}
    {done} {}
    {timestamp} {0}
    {skills} {
      {yinyun-ziqi} {450}
      {taiji-jian} {450}
    }
    {alias} {goquest_wdjj}
  }
  {空明拳} {
    {limitexp} {100000}
    {intervalexp} {100000}
    {intervaltime} {86400}
    {lastexp} {0}
    {lasttime} {0}
    {laststep} {0}
    {chance} {0}
    {fail} {0}
    {done} {}
    {timestamp} {0}
    {skills} {
      {qimen-bagua} {50}
    }
    {shen} {1000}
    {alias} {goquest_kmq}
  } 
  {左右互搏} {
    {limitexp} {1000000}
    {intervalexp} {100000}
    {intervaltime} {86400}
    {lastexp} {0}
    {lasttime} {0}
    {laststep} {0}
    {chance} {3}
    {fail} {0}
    {done} {}
    {timestamp} {0}
    {quest} {
      {空明拳} {}
    }
    {skills} {
      {qimen-bagua} {50}
    }
    {alias} {goquest_hubo}
  }
  {金蛇剑法11} {
    {steps} {
      {1} {
        {name} {金蛇现世}
        {limitexp} {2000000000}
        {intervalexp} {0}
        {intervaltime} {86400}
      }
      {2} {
        {name} {剑掌无敌}
        {limitexp} {2000000}
        {intervalexp} {0}
        {intervaltime} {86400}
      }
      {3} {
        {name} {返璞归真}
        {limitexp} {2000000}
        {intervalexp} {0}
        {intervaltime} {86400}
      }
    }
    {lastexp} {0}
    {lasttime} {0}
    {laststep} {0}
    {chance} {0}
    {fail} {0}
    {done} {}
    {timestamp} {0}
    {alias} {goquest_jsjf}
  }
  {血祭神刀} {
    {party} {大轮寺}
    {limitexp} {2000000}
    {intervalexp} {0}
    {intervaltime} {86400}
    {lastexp} {0}
    {lasttime} {0}
    {laststep} {0}
    {chance} {0}
    {fail} {0}
    {done} {}
    {timestamp} {0}
    {master} {血刀老祖}
    {timezone} {#>=22 || #<=2}    
    {skills} {
      {xuedao-jing} {220}
    }
    {alias} {goquest_xjsd}
  }
};
#NOP {天山灯火，现在已经不需要解了};
#ACTION {^你正在路上走着，忽见右首山谷中露出一点} {
  stopwalk;
  dohalt {
    matrix_ts_sd
  };
};
#NOP {宝藏里面有人};
#ACTION {^突然，不知道从哪里传来一个声音道：这里不欢迎你} {
  stopwalk;
  questclose;
  #IF {"$questmodule" != ""} {
    questupdate {$questmodule}
  };
  #DELAY {1} {
    loc {jobcheck}
  };
};
#ALIAS {questclose} {
  #CLASS questresponseclass KILL;
  #CLASS questclass KILL;
};
#ALIAS {questclear} {
  #VARIABLE {questmodule} {};
  #CLASS questresponseclass KILL;
  #CLASS questclass KILL;
};
#NOP {解谜成功,%1:模块};
#ALIAS {questsuccess} {
  #IF {"$questlist[%1]" != ""} {
    #VARIABLE {questlist[%1][done]} {YES};
    savequest {%1};
    questlog {%1} {解谜成功^@^^@^^@^^@^^@^^@^^@^^@^^@^^@^^@^^@^^@^^@^};
    #NOP {通过接口通知玩家,仅适用于linux};
    #system shujian/tgsend 【$hp[name]\($hp[id]\)】【%1】解谜成功;
    logbuff {success_%1};
  };
};
#NOP {更新解谜步骤,%1:模块,%2:步骤};
#ALIAS {questupdate} {
  #IF {"$questlist[%1]" != ""} {
    #IF {"%2" != ""} {
      #IF {"%2" != "$questlist[%1][laststep]"} {
        #NOP {仅当步骤变更了才记录经验和时间};
        #VARIABLE {questlist[%1][lastexp]} {@eval{$hp[exp]}};
        #VARIABLE {questlist[%1][lasttime]} {@eval{@now{}}};
        #VARIABLE {questlist[%1][laststep]} {%2};
      };
      #VARIABLE {questlist[%1][timestamp]} {@eval{@now{} + 1200}};
      #IF {&questlist[%1][steps][] > 0 && $questlist[%1][laststep] >= &questlist[%1][steps][]} {
        questsuccess {%1};
      };
    };
    #ELSE {
      #VARIABLE {questlist[%1][timestamp]} {@eval{@now{} + 1200}};
    };
    savequest {%1};
  };
};
#NOP {延迟解谜,%1:模块,%2:经验,%3:时间};
#ALIAS {questdelay} {
  #IF {"$questlist[%1]" != ""} {
    #LOCAL {intervalexp} {0};
    #LOCAL {intervaltime} {0};
    #IF {"$questlist[%1][steps]" != ""} {
      #LOCAL {nextstep} {@eval{$questlist[%1][laststep] + 1}};
      #LOCAL {intervalexp} {$questlist[%1][steps][$nextstep][intervalexp]};
      #LOCAL {intervaltime} {$questlist[%1][steps][$nextstep][intervaltime]};
    };
    #ELSE {
      #LOCAL {intervalexp} {$questlist[%1][intervalexp]};
      #LOCAL {intervaltime} {$questlist[%1][intervaltime]};
    };
    #IF {"%2" != ""} {
      #VARIABLE {questlist[%1][lastexp]} {@eval{$hp[exp] -$intervalexp + %2}};
    };
    #IF {"%3" != ""} {
      #VARIABLE {questlist[%1][lasttime]} {@eval{@now{} -$intervaltime + %3}};
    };
    #VARIABLE {questlist[%1][timestamp]} {@now{}};
    savequest {%1};
    questlog {%1} {解谜失败，等待【%2】经验【%3】时间后重试。};
  };
};
#NOP {解谜失败,%1:模块};
#ALIAS {questfail} {
  #IF {"$questlist[%1]" != "" && "$questlist[%1][done]" != "YES"} {
    #VARIABLE {questlist[%1][lastexp]} {@eval{$hp[exp]}};
    #VARIABLE {questlist[%1][lasttime]} {@eval{@now{}}};
    #VARIABLE {questlist[%1][fail]} {@eval{$questlist[%1][fail] + 1}};
    #VARIABLE {questlist[%1][timestamp]} {@eval{@now{} + 1200}};
    #IF {$questlist[%1][chance] > 0 && $questlist[%1][fail] >= $questlist[%1][chance]} {
      #VARIABLE {questlist[%1][done]} {FAIL};
    };
    savequest {%1};
    questlog {%1} {解谜失败，经验:$hp[exp]，时间戳:@now{}。};
  };
};
#NOP {保存解谜数据,%1:任务};
#ALIAS {savequest} {
  #IF {"$questlist[%1]" != ""} {
    #IF {"$questlist[%1][done]" != ""} {
      alias quest_%1 $questlist[%1][done];
    };
    #ELSE {
      alias quest_%1 $questlist[%1][lastexp]_$questlist[%1][lasttime]_$questlist[%1][laststep]_$questlist[%1][fail];
    };
  };
};
#ALIAS {saveallquests} {
  #FOREACH {$questlist[sequence][]} {q} {
    #IF {"$questlist[$q][done]" == "" && $questlist[$q][lastexp] == 0 && $questlist[$q][lasttime] == 0 && $questlist[$q][timestamp] == 0} {
      #CONTINUE;
    };
    savequest $q;
  };
};
#ALIAS {clearquestset} {
  #FOREACH {$questlist[sequence][]} {q} {
    unset quest_$q;
  };
};
#NOP {获取当前就绪的解谜,%1:目标任务};
#FUNCTION getAvailableQuests {
  #LIST {availablequests} {clear} {};
  #FOREACH {$questlist[sequence][]} {q} {
    #NOP {是否开启解谜};
    #IF {$conf[autoquest] == 0} {
      #CONTINUE;
    };
    #NOP {优先判定关注的解谜};
    #IF {"$conf[subcribequests]" != "" && @contains{{conf[subcribequests]}{$q}} == 0} {
      #CONTINUE;
    };
    #NOP {忽略的解谜};
    #IF {@contains{{conf[ignorequests]}{$q}} > 0} {
      #CONTINUE;
    };
    #IF {"$questlist[$q]" == ""} {
      #CONTINUE;
    };
    #NOP {已完成或失败};
    #IF {"$questlist[$q][done]" == "YES" || "$questlist[$q][done]" == "FAIL"} {
      #CONTINUE;
    };
    #NOP {门派解谜};
    #IF {"$questlist[$q][party]" != "" && "$questlist[$q][party]" != "$hp[party]"} {
      #CONTINUE;
    };
    #NOP {性别限制};
    #IF {"$questlist[$q][gender]" != "" && "$questlist[$q][gender]" != "$hp[sex]"} {
      #CONTINUE;
    };
    #NOP {师傅限制};
    #IF {"$questlist[$q][master]" != "" && "$questlist[$q][master]" != "$hp[master][name]"} {
      #CONTINUE;
    };
    #NOP {多步解谜,已完成所有步骤};
    #IF {"$questlist[$q][steps]" != "" && $questlist[$q][laststep] >= &questlist[$q][steps][]} {
      #CONTINUE;
    };
    #NOP {触发类的环境条件};
    #IF {"$questlist[$q][env]" != "" && "$env[$questlist[$q][env]]" != "YES"} {
      #CONTINUE;
    };
    #NOP {神};
    #LOCAL {reqshen} {@eval{$questlist[$q][shen]}};
    #IF {$reqshen > 0 && ("$hp[shen]" == "戾气" || $hp[shen_num] < $reqshen)} {
      #CONTINUE;
    };
    #IF {$reqshen < 0 && ("$hp[shen]" == "正气" || $hp[shen_num] < @abs{$reqshen})} {
      #CONTINUE;
    };
    #NOP {技能条件不满足};
    #LOCAL {skcondition} {1};
    #FOREACH {*questlist[$q][skills][]} {sk} {
      #IF {@getSkillLevel{$sk} < $questlist[$q][skills][$sk]} {
        #LOCAL {skcondition} {0};
        #BREAK;
      };
    };
    #IF {$skcondition == 0} {
      #CONTINUE;
    };
    #NOP {经验限制};
    #LOCAL {limitexp} {0};
    #IF {"$questlist[$q][steps]" != ""} {
      #LOCAL {tempstep} {@eval{$questlist[$q][laststep] + 1}};
      #LOCAL {limitexp} {@eval{$questlist[$q][steps][$tempstep][limitexp]}};
    };
    #ELSE {
      #LOCAL {limitexp} {$questlist[$q][limitexp]};
    };
    #IF {$hp[exp] < $limitexp} {
      #CONTINUE;
    };
    #NOP {经验间隔};
    #LOCAL {intervalexp} {0};
    #IF {"$questlist[$q][steps]" != ""} {
      #LOCAL {tempstep} {@eval{$questlist[$q][laststep] + 1}};
      #LOCAL {intervalexp} {@eval{$questlist[$q][steps][$tempstep][intervalexp]}};
    };
    #ELSE {
      #LOCAL {intervalexp} {$questlist[$q][intervalexp]};
    };
    #IF {@eval{$hp[exp] - $questlist[$q][lastexp]} < $intervalexp} {
      #CONTINUE;
    };
    #NOP {时间间隔};
    #LOCAL {intervaltime} {0};
    #IF {"$questlist[$q][steps]" != ""} {
      #LOCAL {tempstep} {@eval{$questlist[$q][laststep] + 1}};
      #LOCAL {intervaltime} {@eval{$questlist[$q][steps][$tempstep][intervaltime]}};
    };
    #ELSE {
      #LOCAL {intervaltime} {$questlist[$q][intervaltime]};
    };
    #IF {@elapsed{$questlist[$q][lasttime]} < $intervaltime} {
      #CONTINUE;
    };
    #NOP {先决条件};
    #IF {"$questlist[$q][quest]" != ""} {
      #LOCAL {precondition} {1};
      #FOREACH {*questlist[$q][quest][]} {pq} {
        #IF {"$questlist[$q][quest][$pq]" == ""} {
          #IF {"$questlist[$pq][done]" != "YES"} {
            #LOCAL {precondition} {0};
            #BREAK;
          };
        };
        #ELSEIF {"$questlist[$pq][done]" != "YES"} {
          #IF {$questlist[$pq][laststep] < $questlist[$q][quest][$pq]} {
            #LOCAL {precondition} {0};
            #BREAK;
          };
        };
      };
      #IF {$precondition == 0} {
        #CONTINUE;
      };
    };
    #NOP 任务次数限制,如天地会;
    #LOCAL {jtcondition} {1};
    #FOREACH {*questlist[$q][jobtimes][]} {jt} {
      #IF {&{hp[jobtimes][$jt]} > 0}{
        #IF {$hp[jobtimes][$jt] < $questlist[$q][jobtimes][$jt]} {
            #LOCAL {jtcondition} {0};
            #BREAK;
        }
      };
      #ELSE { 
        #LOCAL {jtcondition} {0};
        #BREAK;
      };
    };
    #IF {$jtcondition == 0} {
      #CONTINUE;
    };
    #NOP {时刻限制};
    #LOCAL {timeexpr} {1 == 1};
    #IF {"$questlist[$q][steps]" != ""} {
      #LOCAL {tempstep} {@eval{$questlist[$q][laststep] + 1}};
      #IF {"$questlist[$q][steps][$tempstep][timezone]" != ""} {
        #LOCAL {timeexpr} {$questlist[$q][steps][$tempstep][timezone]};
      };
    };
    #ELSE {
      #IF {"$questlist[$q][timezone]" != ""} {
        #LOCAL {timeexpr} {$questlist[$q][timezone]}
      };
    };
    #REPLACE timeexpr {#}{$env[gametime]};
    #IF {!($timeexpr)} {
      #CONTINUE;
    };
    #NOP {重试时间};
    #IF {@now{} < $questlist[$q][timestamp]} {
      #CONTINUE;
    };
    #LIST {availablequests} {add} {$q};
  };

  #RETURN {$availablequests};
};
#NOP {指定的解谜是否激活，%1:解谜名称，%2:是否忽略CD};
#FUNCTION isQuestActivate {
  #IF {$conf[autoquest] == 0} {
    #RETURN {0};
  };
  #IF {"$questlist[%1]" == ""} {
    #RETURN {0};
  };
  #NOP {忽略的解谜};
  #IF {@contains{{conf[ignorequests]}{%1}} > 0} {
    #RETURN {0};
  };
  #NOP {是否在解谜范围};
  #IF {"$conf[subcribequests]" != "" &&  @contains{{conf[subcribequests]}{%1}} == 0} {
    #RETURN {0};
  };
  #NOP {已完成或失败};
  #IF {"$questlist[%1][done]" == "YES" || "$questlist[%1][done]" == "FAIL"} {
    #RETURN {0};
  };
  #NOP {门派解谜};
  #IF {"$questlist[%1][party]" != "" && "$questlist[%1][party]" != "$hp[party]"} {
    #RETURN {0};
  };
  #NOP {性别限制};
  #IF {"$questlist[%1][gender]" != "" && "$questlist[%1][gender]" != "$hp[sex]"} {
    #RETURN {0};
  };
  #NOP {神};
  #LOCAL {reqshen} {@eval{$questlist[$q][shen]}};
  #IF {$reqshen > 0 && ("$hp[shen]" == "戾气" || $hp[shen_num] < $reqshen)} {
    #CONTINUE;
  };
  #IF {$reqshen < 0 && ("$hp[shen]" == "正气" || $hp[shen_num] < @abs{$reqshen})} {
    #CONTINUE;
  };
  #NOP {经验限制};
  #LOCAL {limitexp} {0};
  #IF {"$questlist[%1][steps]" != ""} {
    #LOCAL {tempstep} {@eval{$questlist[%1][laststep] + 1}};
    #LOCAL {limitexp} {@eval{$questlist[%1][steps][$tempstep][limitexp]}};
  };
  #ELSE {
    #LOCAL {limitexp} {$questlist[%1][limitexp]};
  };
  #IF {$hp[exp] < $limitexp} {
    #RETURN {0};
  };
  #IF {"%2" != ""} {
    #RETURN {1};
  };
  #NOP {经验间隔};
  #LOCAL {intervalexp} {0};
  #IF {"$questlist[%1][steps]" != ""} {
    #LOCAL {tempstep} {@eval{$questlist[%1][laststep] + 1}};
    #LOCAL {intervalexp} {@eval{$questlist[%1][steps][$tempstep][intervalexp]}};
  };
  #ELSE {
    #LOCAL {intervalexp} {$questlist[%1][intervalexp]};
  };
  #IF {@eval{$hp[exp] - $questlist[%1][lastexp]} < $intervalexp} {
    #RETURN {0};
  };
  #NOP {时间间隔};
  #LOCAL {intervaltime} {0};
  #IF {"$questlist[%1][steps]" != ""} {
    #LOCAL {tempstep} {@eval{$questlist[%1][laststep] + 1}};
    #LOCAL {intervaltime} {@eval{$questlist[%1][steps][$tempstep][intervaltime]}};
  };
  #ELSE {
    #LOCAL {intervaltime} {$questlist[%1][intervaltime]};
  };
  #IF {@elapsed{$questlist[%1][lasttime]} < $intervaltime} {
    #RETURN {0};
  };
  #RETURN {1};
};
#NOP {调整解谜天赋,%1:名称,%2:后续指令};
#VARIABLE {orginalgift} {};
#ALIAS {quest_gift_setup} {
  #LOCAL {tempgift} {$questlist[%1][gift]};
  #IF {"$tempgift" == ""} {
    %2
  };
  #ELSE {
    #VARIABLE {orginalgift} {
      {str} {$hp[str_xt]}
      {con} {$hp[con_xt]}
      {dex} {$hp[dex_xt]}
      {int} {$hp[int_xt]}
    };
    adjustgift {$tempgift[str]} {$tempgift[con]} {$tempgift[dex]} {$tempgift[int]} {%2}
  };
};
#NOP {恢复原有天赋,%1:后续指令};
#ALIAS {quest_gift_restore} {
  #LOCAL {tempgift} {$orginalgift};
  #IF {"$tempgift" == ""} {
    %1
  };
  #ELSE {
    #VARIABLE {orginalgift} {
      {str} {$tempgift[str]}
      {con} {$tempgift[con]}
      {dex} {$tempgift[dex]}
      {int} {$tempgift[int]}
    };
    adjustgift {$tempgift[str]} {$tempgift[con]} {$tempgift[dex]} {$tempgift[int]} {%1}
  };
};
#NOP {开始解谜,做一些准备(拿珍珠,调整天赋),%1:名称,%2:后续指令};
#ALIAS {quest_start} {
  questlog {%1} {解谜开始。};
  gotodo {扬州城} {小吃店} {
    startfull {
      quest_gift_setup {%1} {
        wwp;
        #IF {@carryqty{pearl} == 0} {
          getpearl {$questlist[%1][alias] {quest_end {guide_destiny {%2}}}}
        };
        #ELSE {
          $questlist[%1][alias] {quest_end {guide_destiny {%2}}}
        };
      };
    };
  };
};
#NOP {解谜完毕,做一些收尾(存珍珠,恢复原天赋)};
#ALIAS {quest_end} {
  questlog {$questmodule} {解谜结束。};
  #VARIABLE {questmodule} {};
  quest_gift_restore {
    #IF {@carryqty{pearl} > 0} {
      storethings {pearl} {%1}
    };
    #ELSE {
      %1
    };
  }
};
#NOP {切换是否显示已成功解密};
#ALIAS {togglequest} {
  #IF {"$conf[displaysuccess]" == "1"} {
    #VARIABLE {conf[displaysuccess]} {0};
  };
  #ELSE {
    #VARIABLE {conf[displaysuccess]} {1};
  };
  reload ui
};
#NOP {周伯通结拜条件};
#FUNCTION fnJiebai {
  #IF {$hp[int_xt] <= 25 && "$hp[party]" != "少林寺" && "$hp[party]" != "天龙寺" && "$hp[party]" != "大轮寺"} {
    #RETURN {1};
  };
  #ELSE {
    #RETURN {0};
  };
};
questclear;
#SHOWME {<fac>@padRight{{解谜}{12}}<fac> <cfa>模块加载完毕<cfa>};