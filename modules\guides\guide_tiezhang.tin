#NOP {铁掌拜师指南};
#NOP {各级别师傅的拜师限制和需要关注的技能};
#VARIABLE {guide[铁掌帮][masters]} {
  {sequence} {
    {1} {裘千丈}
    {2} {张浩天}
    {3} {裘千仞}
    {4} {上官剑南}
  }
  {裘千丈} {
    {id} {qiu qianzhang}
    {conditions} {
      {shen} {-1000}
      {gift} {
        {str} {20}
        {con} {20}
        {dex} {10}
        {int} {30}
      }
    }
    {favourites} {
      {1} {force}
      {2} {guiyuan-tunafa}
    }
    {beforedo} {}
  }
  {张浩天} {
    {id} {zhang haotian}
    {conditions} {
      {shen} {-10000}
      {skills} {
        {guiyuan-tunafa} {31}
      }
    }
    {favourites} {
      {1} {tiezhang-xinfa}
      {2} {force}
      {3} {guiyuan-tunafa}
      {4} {dodge}
      {5} {shuishangpiao}
      {6} {strike}
      {7} {tiezhang-zhang<PERSON>}
      {8} {parry}
    }
    {beforedo} {}
  }
  {裘千仞} {
    {id} {qiu qianren}
    {conditions} {
      {shen} {-100000}
      {skills} {
        {guiyuan-tunafa} {120}
      }
    }
    {favourites} {
      {1} {tiezhang-xinfa}
      {2} {force}
      {3} {guiyuan-tunafa}
      {4} {dodge}
      {5} {shuishangpiao}
      {6} {strike}
      {7} {tiezhang-zhangfa}
      {8} {parry}
    }
    {beforedo} {}
  }
  {上官剑南} {
    {id} {qiu qianren}
    {conditions} {
      {shen} {10000}
      {skills} {
        {guiyuan-tunafa} {220}
      }
    }
    {favourites} {
      {1} {tiezhang-xinfa}
      {2} {force}
      {3} {guiyuan-tunafa}
      {4} {dodge}
      {5} {shuishangpiao}
      {6} {strike}
      {7} {tiezhang-zhangfa}
      {8} {parry}
    }
    {beforedo} {guide_baijiannan {#}}
  }
};
#NOP {按照指定技能的各个等级(>=0)决定任务使用的pfm及其要做的任务};
#VARIABLE {guide[铁掌帮][pfms]} {
  {100} {
    {pfmskill} {tiezhang-zhangfa}
    {jobs} {华山;送信}
    {weapon} {
      {primary} {};
      {secondary} {}
    }
    {wuxing} {}
    {normalbuff} {}
    {fightbuff} {}
    {busy} {}
    {attack} {
      jiali max;
      perform tiezhang-zhangfa.xuanfeng;
      jiali 10;
    }
  }
  {120} {
    {pfmskill} {tiezhang-zhangfa}
    {jobs} {雪山;送信2}
    {weapon} {
      {primary} {};
      {secondary} {}
    }
    {wuxing} {}
    {normalbuff} {}
    {fightbuff} {}
    {busy} {}
    {attack} {
      jiali max;
      perform tiezhang-zhangfa.judu;
      jiali 10;
    }
  }
  {150} {
    {pfmskill} {tiezhang-zhangfa}
    {jobs} {雪山;送信2}
    {weapon} {
      {primary} {};
      {secondary} {}
    }
    {wuxing} {}
    {normalbuff} {}
    {fightbuff} {
      jifa dodge shuishangpiao;
      perform dodge.piao
    }
    {busy} {}
    {attack} {
      jiali max;
      perform tiezhang-zhangfa.tianlei;
    }
  }
  {250} {
    {pfmskill} {tiezhang-zhangfa}
    {jobs} {雪山;送信2}
    {weapon} {
      {primary} {};
      {secondary} {}
    }
    {wuxing} {}
    {normalbuff} {}
    {fightbuff} {
      jifa dodge shuishangpiao;
      perform dodge.piao
    }
    {busy} {}
    {attack} {
      jiali max;
      perform tiezhang-zhangfa.pushan;
      yun juli;
    }
  }
};
#NOP {门派武器武功需要的武器};
#VARIABLE {guide[铁掌帮][weapons]} {
  {liuye-daofa} {blade}
};

#NOP {寻找裘千丈,%1:后续指令};
#ALIAS {guide_findqqz} {
  #LIST {masterrooms} {create} {679;677;680};
  #VARIABLE {roomindex} {1};
  #CLASS guideclass ;
  #CLASS guideclass KILL;
  #CLASS guideclass OPEN;
  #ACTION {^这里没有 qiu qianzhang。} {
    #MATH {roomindex} {$roomindex + 1};
    #IF {$roomindex > &masterrooms[]} {
      #VARIABLE {roomindex} {1};
    };
    #DELAY {0.2} {
      gotoroom {$masterrooms[+$roomindex]} {follow qiu qianzhang}
    }
  };
  #ACTION {^你{决定跟随裘千丈一起行动|已经这样做了}} {
    #CLASS guideclass KILL;
    %1
  };
  #CLASS guideclass CLOSE;
  gotoroom {$masterrooms[+$roomindex]} {follow qiu qianzhang}
};
#NOP {拜上官剑南};
#ALIAS {guide_baijiannan} {
  #VARIABLE {okflag} {0};
  #CLASS guideclass KILL;
  #CLASS guideclass OPEN;
  #ACTION {^^他是我师父。我在十三岁那年曾救过他，之后他感恩图报} {
    dohalt {
      gotonpc {顾炎武} {ask gu yanwu about 上官剑南}
    }
  };
  #ACTION {^顾炎武交给你} {
    dohalt {gotoroom {2830} {open xiang;open jiaceng;echo {checkxlt}}}
  };
  #ACTION {^你随手打开一轴画卷，怎么可能？竟然是据说失传已久的北宋范宽的真迹“溪山行旅图”！} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkxlt\"|你设定checkxlt为反馈信息}} {
    #IF {$okflag == 0} {
      #DELAY {0.2} {
        fan painting;
        echo {checkxlt}
      };
    };
    #ELSE {
      dohalt {
        gotodo {铁掌山} {天然洞穴} {move haigu}
      };
    };
  };
  #ACTION {^你轻轻地挪动骸骨，发现下面有一本书，你急忙拣起揣到怀中。} {
    dohalt {
      guide_findqqz {ask qiu qianzhang about 闹鬼};
    };
  };
  #ACTION {^你向裘千丈打听有关『闹鬼』的消息} {
    gotoroom {3293} {give jiannan yishu}
  };
  #ACTION {^你帮我找回这本书，我不会亏待你的} {
    #CLASS guideclass KILL;
    %1
  };
  #CLASS guideclass CLOSE;
  gotonpc {裘千仞} {ask qiu qianren about 上官剑南}
};