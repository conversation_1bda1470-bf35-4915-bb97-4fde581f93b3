#NOP {钓鱼任务模块};
#VARIABLE {pinkbeach} {};
#ALIAS {jobgo_fish} {
  set;
  checkrequest {
    gotodo {福州城} {小岛} {checkbeach {jobcheck_fish}};
  };
};

#action {^卖掉的乌龟%s%1}{#variable turtles %1};
#NOP {确定是否有粉色沙滩,小岛房间只可能通向蓝色沙滩、粉色沙滩和自身};
#NOP {蓝色沙滩n方向为巨岩,可以通过此特点断定是否存在粉色沙滩和路径};
#ALIAS {checkbeach} {
  #LIST {tempsteps} {create} {n;w;e;s};
  #VARIABLE {pinkbeach} {};
  #VARIABLE {tempways} {$roomways};
  #VARIABLE {tempindex} {1};
  #CLASS jobcheckclass KILL;
  #CLASS jobcheckclass OPEN;
  #ACTION {^设定环境变量：action \= \"checkbeah\"} {
    #IF {"$room" == "沙滩" && @contains{{roomways}{巨岩}} == 0} {
      #VARIABLE {pinkbeach} {$tempsteps[+$tempindex]};
      #CLASS jobcheckclass KILL;
      %1
    };
    #ELSE {
      #MATH {tempindex} {$tempindex + 1};
      #IF {$tempindex > 4} {
        #CLASS jobcheckclass KILL;
        %1
      };
      look @longDir{$tempsteps[+$tempindex]};
      set action checkbeah;
    };
  };
  #CLASS jobcheckclass CLOSE;
  look @longDir{$tempsteps[+$tempindex]};
  set action checkbeah;
};
#NOP {检查钓鱼器具};
#ALIAS {jobcheck_fish} {
  #CLASS jobcheckclass OPEN;
  #ACTION {^设定环境变量：action \= \"checkid\"} {
    #CLASS jobcheckclass KILL;
    #IF {"$id[things][yu gan]" == "" || "$id[things][yu lou]" == ""} {
      jobask_fish
    };
    #ELSE {
      loc {jobdo_fish}
    };
  };
  #CLASS jobcheckclass CLOSE;
  i;
  set action checkid
};
#NOP {获取钓鱼器具};
#ALIAS {jobask_fish} {
  #LIST {tempsteps} {create} {n;n;enter;out;s;w;s;eu};
  #IF {"$pinkbeach" != ""} {
    #NOP {添加粉色沙滩,粉色沙滩n->小岛};
    #LIST {tempsteps} {insert} {1} {w};
    #LIST {tempsteps} {insert} {1} {$pinkbeach};
  };
  #VARIABLE {tempindex} {1};
  #VARIABLE {tempflag} {0};
  #VARIABLE {specialflag} {0};
  #CLASS jobrequestclass OPEN;
  #ACTION {^这里没有 lao zhe。} {
    #IF {"$caller" != ""} {
      #CLASS jobrequestclass KILL;
      checkrequest {jobcheck};
    };
    #ELSEIF {$tempindex > &tempsteps[]} {
      #IF {$specialflag == 0} {
        #NOP {除粉色小岛外所有路房间均已搜索,尝试去粉色沙滩(该沙滩不是会必定出现)};
        #VARIABLE {specialflag} {1};
        wd;n;w;s;
        follow lao zhe;
      };
      #ELSE {
        #CLASS jobrequestclass KILL;
        #NOP {小岛s方向通向随机的沙滩,所有沙滩的出口w通向的房间均为确定房间};
        w;
        loc {
          jobgo_fish
        }
      };
    };
    #ELSE {
      #DELAY {0.5} {
        $tempsteps[+$tempindex];
        #MATH {tempindex} {$tempindex + 1};
        follow lao zhe
      };
    };
  };
  #ACTION {^你{决定跟随老者一起行动|已经这样做了}} {
    #IF {"$id[things][yu lou]" == ""} {
      set action checkyulou 
    };
    #ELSE {
      ask lao zhe about 鱼竿;
      set action checkyugan
    };
  };
  #ACTION {^老者说道：「我不是给你了吗？你弄哪去了？」} {
    #VARIABLE {tempflag} {1};
  };
  #ACTION {^设定环境变量：action \= \"checkyulou\"} {
    #IF {"$id[things][yu lou]" == ""} {
      #IF {$tempflag == 1} {
        #CLASS jobrequestclass KILL;
        quit
      };
      #ELSE {
        #VARIABLE {tempflag} {0};
        ask lao zhe about 鱼篓;
        #DELAY {2} {
          i;
          set action checkyulou
        }
      };
    };
    #ELSEIF {"$id[things][yu gan]" == ""} {
      ask lao zhe about 鱼竿;
      i;
      set action checkyugan
    };
    #ELSE {
      #CLASS jobrequestclass KILL;
      follow none;
      jobdo_fish
    };
  };
  #ACTION {^设定环境变量：action \= \"checkyugan\"} {
    dohalt {
      #IF {$tempflag == 1} {
        #CLASS jobrequestclass KILL;
        follow none;
        #IF {$specialflag == 1} {
          w
        };
        loc {jobfind_fish};
      };
      #ELSE {
        ask lao zhe about 鱼竿;
        i;
        set action checkyugan
      };
    }
    
  };
  #ACTION {^老者{冲上前去|说道：「不是告诉你了吗}} {
    #VARIABLE {tempflag} {1};
  };
  #CLASS jobrequestclass CLOSE;
  follow lao zhe
};
#ALIAS {jobfind_fish} {
  #VARIABLE {tempflag} {0};
  #CLASS jobrequestclass OPEN;
  #ACTION {^你找了半天，没发现什么对你有用的东西} {
    #VARIABLE {tempflag} {1};
  };
  #ACTION {^设定环境变量：action \= \"checkyugan\"} {
    #DELAY {0.5} {
      dohalt {
        #IF {$tempflag == 1} {
          #CLASS jobrequestclass KILL;
          jobask_fish
        };
        #ELSEIF {"$id[things][yu gan]" == ""} {
          find 鱼竿;
          i;
          set action checkyugan
        };
        #ELSE {
          #CLASS jobrequestclass KILL;
          jobdo_fish
        };
      }
    };
  };
  #CLASS jobrequestclass CLOSE;
  gotodo {福州城} {小木棚} {set action checkyugan}
};
#NOP {去钓鱼};
#ALIAS {jobdo_fish} {
  gotodo {福州城} {礁石} {
    #IF {$hp[neili] < @eval{$hp[neili_max]/5}}
    {
      startfull {jobfight_fish}
    };
    #ELSE {jobfight_fish};
  };
};
#NOP {开始钓鱼};
#ALIAS {jobfight_fish} {
  #VARIABLE {brokenflag} {0};
  #VARIABLE {overlowflag} {0};
  #VARIABLE {dummyflag} {0};
  #CLASS jobfightclass OPEN;
  #ACTION {^{正拿起鱼竿想要放线出去|你突然觉得手中鱼竿一颤|先收线吧，有鱼咬着你的钩呢|只见鱼漂一动|你气得破口大骂起来|你将一条|趁你提鱼竿的工夫}} {
    dohalt {
      shouxian;
      hp;
      i;
      dohalt {
        set action checkfish
      }
    }
  };
  #ACTION {^你还是多花点时间练功吧，免得武功荒废了。} {
    #VARIABLE {dummyflag} {1};
    set action checkfish;
  };
  #ACTION {^地上已经那么多鱼了，不要暴敛天物啊} {
    get yu;
  };
  #ACTION {^设定环境变量：action \= \"checkfish\"} {
    #VARIABLE {idle} {0};
    yun jingli;
    #IF {$dummyflag == 1} {
      #CLASS jobfightclass KILL;
      #NOP {去做任务};
      #LIST {conf[joblist]} {create} {华山;送信};
      #NOP {增加100k经验回来钓鱼};
      setexpout {100000} {#LIST {conf[joblist]} {create} {钓鱼};};
      dohalt {
        jobcheck;
      };
    };
    #ELSEIF {"$id[things][hai gui]" != ""} {
      zhuang yu;
      set action checkzhuangyu
    };
    #ELSEIF {"$id[things][duan yugan]" != ""} {
      #CLASS jobfightclass KILL;
      drop duan yugan;
      loc {
        jobgo_fish
      }
    };
    #ELSEIF {"$id[things][yu gan]" == ""} {
      #CLASS jobfightclass KILL;
      loc {
        jobgo_fish
      }
    };
    #ELSE {
      #LOCAL {dropyu} {};
      #FOREACH {*id[things][]} {t} {
        #IF {@endWiths{{$id[things][$t][name]}{鱼}} == 1} {
          #LOCAL {dropyu} {$t};
          #BREAK;
        };
      };

      #IF {"$dropyu" != ""} 
      {
        #CLASS jobfightclass KILL;
        wd;drop yu;
        n;drop yu;
        w;drop yu;
        swim;
        w;drop yu;
        w;drop yu;
        w;drop yu;
        w;drop yu;
        n;drop yu;
        n;drop yu;
        #delay {1}
        {
          e;drop yu;
          w;drop yu;
          w;drop yu;
          e;drop yu;
          s;drop yu;
          w;drop yu;
          e;drop yu;
          s;drop yu;
          w;drop yu;
          e;drop yu;
          #delay {1}
          {
            s;drop yu;
            w;drop yu;
            e;drop yu;
            s;drop yu;
            w;drop yu;
            e;drop yu;
            s;drop yu;
            #delay {0.5}
            {
              loc {gotodo {福州城} {礁石} {jobfight_fish}}
            }
          }
        };
      };
      #ELSEIF {$hp[neili] < 100} {
        #CLASS jobfightclass KILL;
        startfull {jobfight_fish}
      };
      #ELSEIF {$hp[pot] >= $hp[pot_max]} {
        #CLASS jobfightclass KILL;
        checkpot {jobcheck};
      };
      #ELSEIF {"$caller" != ""} {
        #CLASS jobfightclass KILL;
        checkrequest {jobcheck};
      };
      #ELSE {
        diaoyu
      }
    };
  };
  #ACTION {^海龟对鱼篓来说太重了。} {
    #VARIABLE {overlowflag} {1};
  };
  #ACTION {^设定环境变量：action \= \"checkzhuangyu\"} {
    #IF {$overlowflag == 0} {
      set action checkfish
    };
    #ELSE {
      #CLASS jobfightclass KILL;
      loc {
        jobfinish_fish
      };
    };
  };
  #ACTION {^一股暖流发自丹田流向全身，慢慢地你又恢复了知觉} {
    yun jing;
    yun qi;
    yun jingli;
    i;
    set action checkfish
  };
  #CLASS jobfightclass CLOSE;
  i;
  set action checkfish
};
#NOP {卖鱼};
#ALIAS {jobfinish_fish} {
  #VARIABLE {jobdo} {
    get hai gui from yu lou;
    i;
    set action checkhaigui
  };
  #CLASS jobfinishclass OPEN;
  #ACTION {^设定环境变量：action \= \"checkhaigui\"} {
    #IF {"$id[things][hai gui]" == ""} {
      #CLASS jobfinishclass KILL;
      jobcheck
    };
    #ELSE {
      #DELAY {2} {
        sell hai gui;
        $jobdo
      }
    };
  };
  
  #action {^你%%*卖掉了一只海龟给张老板。}
  {
    #variable turtles @eval{$turtles+1};
    set 卖掉的乌龟 $turtles;
    #line log $logpath/job.log <acc>一共卖掉第<dfa>$turtles<080>只乌龟;
    comm_window_show 打卡 <acc>@nowtime{} 一共卖掉第<dfa>$turtles<080>只乌龟;
  };
  #CLASS jobfinishclass CLOSE;
  gotodo {福州城} {当铺} {$jobdo}
};

