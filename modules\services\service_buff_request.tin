#NOP {发起启动资金支持,%1:后续指令,%2:数额,默认600};
#ALIAS {buff_call} {
  buff_call_start {%1}
};
#NOP {开始呼叫,先到位置再呼叫,%1:后续指令,%2:数额};
#ALIAS {buff_call_start} {
  #VARIABLE {checkresualt} {0};
  #VARIABLE {checkcount} {0};
  #CLASS serviceclass KILL;
  #CLASS serviceclass OPEN;
  #ACTION {^没有这个人。} {
    #VARIABLE {buffer_ready} {0};
    #VARIABLE {checkresualt} {3};
    #SHOWME {<faa>保姆$conf[nanny][buffer]人不在};
  };
  #ACTION {%*(%*)告诉你：buff_wait} {
    #VARIABLE {idle} {0};
    #VARIABLE {checkresualt} {2};
  };
  #ACTION {%*(%*)告诉你：buff_come} {
    #VARIABLE {checkresualt} {1};
    #VARIABLE {waiter} {
      {id} {@lower{%%2}}
      {name} {%%1}
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkresponse\"|你设定checkresponse为反馈信息}} {
    #MATH {checkcount} {$checkcount + 1};
    #DELAY {1} {
      #SWITCH {$checkresualt} {
        #CASE {1} {
          #CLASS serviceclass KILL;
          gotodo {长安城} {小雁塔} {buff_request_wait {%1}}
        };
        #CASE {2} {
          #CLASS serviceclass KILL;
          %1;
        };
        #CASE {3} {
          #CLASS serviceclass KILL;
          #VARIABLE {env[buffts]} {@now{}};
          %1;
        };
        #DEFAULT {
          #IF {$checkcount >= 3 } {
            #CLASS serviceclass KILL;
            #VARIABLE {env[buffts]} {@now{}};
            %1;
          };
          #ELSE {
            echo {checkresponse}; 
          };
        };
      };
    };
  };
  #CLASS serviceclass CLOSE;
  #IF {"$conf[nanny][buffer]" == ""} {
    #SHOWME {<faa>未配置buffer保姆,请自行处理};
    #DELAY {1} {jobprepare;jobgo};
  };
  #ELSE {
    tell $conf[nanny][buffer] buff_request;
    echo {checkresponse}; 
  };
};
#NOP {去地点等待碰头,%1:后续指令,%2:数额};
#ALIAS {buff_request_wait} {
  #VARIABLE {startts} {@now{}};
  #VARIABLE {arrived} {0};
  #VARIABLE {okcount} {0};
  #CLASS serviceclass KILL;
  #CLASS serviceclass OPEN;
  #ACTION {^$waiter[name]轻轻地拍了拍你的头} {
    #VARIABLE {arrived} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkresponse\"|你设定checkresponse为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {@elapsed{$startts} > 300} {
      #CLASS serviceclass KILL;
      loc {
        buff_call {%1};
      };
    };
    #ELSEIF {$arrived == 0} {
      #DELAY {5} {
        echo {checkresponse};
      };
    };
    #ELSE {
      buff_request_fight {%1};
    };
  };
  #CLASS serviceclass CLOSE;
  startfull {echo {checkresponse}}
};
#NOP {去地点等待碰头,%1:后续指令};
#ALIAS {buff_request_fight} {
  #VARIABLE {fightts} {@now{}};
  #VARIABLE {startts} {@now{}};
  #CLASS serviceclass KILL;
  #CLASS serviceclass OPEN;
  #ACTION {^%*你不由得呆了，用充满爱慕的的眼光看着} {
    #VARIABLE {startts} {@now{}};
  };

  #ACTION {^如果你愿意和对方比试武艺，请你也} {
    #VARIABLE {fightts} {@now{}};
    #VARIABLE {startts} {@now{}};
    fight $conf[nanny][buffer];
    echo {checkstatus};
  };

  #ACTION {^$waiter[name]%*「{貂禅拜月|西施捧心|昭君出塞|麻姑献寿|天女织梭|则天垂帘|丽华梳妆|红玉击鼓|弄玉吹箫|文姬归汉}」} {
    #VARIABLE {fightts} {@now{}};
  };
  #ACTION {^你激凌凌打个冷战，心智为之一清} {
    #MATH {okcount} {$okcount + 1};
    jifa;
  };
  #ACTION {^你想攻击谁} {
    #CLASS serviceclass KILL;
    #DELAY {2} {jobprepare}
  };
  #ACTION {^{设定环境变量：action \= \"checkstatus\"|你设定checkstatus为反馈信息}} {
    #VARIABLE {idle} {0};
    #VARIABLE {duration} {90};
    #IF {$okcount < 3} {
      #VARIABLE {duration} {120};
    };
    #IF {$okcount >= 3} {
      #VARIABLE {duration} {60};
    };
    #IF {$okcount >= 5} {
      #VARIABLE {duration} {60};
    };
    #IF {@elapsed{$fightts} >= 6} {
      #CLASS serviceclass KILL;
      dohalt {
        doheal {
          gotodo {长安城} {小雁塔} {buff_request_fight {%1}}
        };
      };
    };
    #ELSEIF {@elapsed{$startts} <= $duration && $okcount <= 5} {
      #IF {@eval{$hp[qi_max]-$hp[qi]} > @eval{$hp[qi_max]/4}} {
        yun qi;
      };
      #DELAY {1} {
        hp;
        echo {checkstatus};
      }
    };
    #ELSE {
      #NOP {太久加不上结束};
      #CLASS serviceclass KILL;
      #VARIABLE {env[powerup]} {YES};
      set env_powerup;
      dohalt {
        bye $waiter[id];
        openwimpy;
        wear all;
        getBaseMaxMapSkill;
        %1;
      };
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkready\"|你设定checkready为反馈信息}} {
    #VARIABLE {idle} {0};
    #VARIABLE {fightts} {@now{}};
    #VARIABLE {startts} {@now{}};
    yun qi;
    yun jing;
    #IF {$hp[neili] < $hp[neili_max]} {
      startfull {
        echo {checkready}
      };
    };
    #ELSE {
      fight $conf[nanny][buffer];
      echo {checkstatus};
    };
  };
  #CLASS serviceclass CLOSE;
  #VARIABLE {fightts} {@now{}};
  #VARIABLE {startts} {@now{}};
  remove all;
  jifa dodge none;
  jifa parry none;
  closewimpy;
  uwwp;
  bei none;
  jiali 0;
  #DELAY {1} {echo {checkready}}
};
#NOP {取消资金请求,%1:后续指令};
#ALIAS {buff_cancel} {
  #IF {"$conf[nanny][buffer]" != ""} {
    tell $conf[nanny][buffer] buff_cancel
  };
};