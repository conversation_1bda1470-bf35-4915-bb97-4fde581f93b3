#nop {躺尸剑法;%1:后续指令};
#ALIAS goquest_tsjf {
	#VARIABLE {questmodule} {躺尸剑法};
	gotonpc {狄云} {tsjf_askdiyun {%1}};
};
#ALIAS {tsjf_askdiyun} {
	#CLASS questclass KILL;
	#CLASS questclass OPEN;
	#ACTION {^狄云说道：「今天先教到到这里吧，明天吧} {
		#CLASS questclass KILL;
		#NOP {时间不够};
		questdelay {$questmodule} {0} {3600};
		dohalt {%1;};
	};
	#ACTION {^狄云说道：「以%*当前的经验恐怕还是难以领悟} {
		#CLASS questclass KILL;
		#NOP {经验不够};
		questdelay {$questmodule} {} {7200};
		dohalt {%1;};
	};
	#ACTION {^你听了狄云的指点，对躺尸剑法的奥妙似乎有些心得} {
		#CLASS questclass KILL;
		#NOP {开了};
		questsuccess {$questmodule};
		dohalt {%1;};
	};
	#ACTION {^听了狄云的指点，可是对躺尸剑法的奥妙全然不得要领} {
		#CLASS questclass KILL;
		#NOP {失败};
		questfail {$questmodule};
		dohalt {%1;};
	};
	#CLASS questclass CLOSE;
	pray pearl;
	dohalt {
		ask di yun about 躺尸剑法
	};
};