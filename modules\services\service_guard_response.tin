#NOP {护卫模块};
#ALIAS {initguardservice} {
  #CLASS serviceguardclass KILL;
  #CLASS serviceguardclass OPEN;
  #ACTION {^%*(%*)告诉你：guard_request_%*_%*_%*} {
    guard_accept {@lower{%%2}} {%%1} {%%3} {%%4} {%%5};
  };
  #ACTION {^! %*(%*)告诉你：guard_request_%*_%*_%*} {
    guard_accept {@lower{%%2}} {%%1} {%%3} {%%4} {%%5};
  };
  #ACTION {^%*(%*)告诉你：guard_cancel} {
    #VARIABLE {caller} {};
  };
  #ACTION {^! %*(%*)告诉你：guard_cancel} {
    #VARIABLE {caller} {};
  };
  #CLASS serviceguardclass CLOSE;
};
#NOP {注册护卫请求,%1:请求人id,%2:请求人name,%3:目标城市,%4:目标房间,%5:目标id};
#ALIAS {guard_accept} {
  #NOP {超过五分钟重置};
  #IF {"$caller" != "" && @elapsed{$caller[timestamp]} > 300} {
    #VARIABLE {caller} {};
  };
  #NOP {更新请求};
  #IF {"$caller" != "" && "$caller[id]" != "%1"} {
    tell %1 guard_wait
  };
  #ELSE {
    #VARIABLE {caller} {
      {request} {guard}
      {timestamp} {@now{}}
      {id} {%1}
      {name} {%2}
      {city} {%3}
      {room} {%4}
      {target} {%5}
    };
    tell %1 guard_come
  };
};
#NOP {响应杀人请求,%1:后续指令};
#ALIAS {guard_response} {
  #IF {"$caller[city]" == "" || "$caller[room]" == "" || "$caller[target]" == ""} {
    tell $caller[id] guard_onposition;
    #VARIABLE {caller} {};
    %1;
  };
  #ELSE {
    gotodo {$city} {$cities[$city][cityroom]} {
      startfull {
        gotodo {$caller[city]} {$caller[room]} {guard_response_start {%1}};
      };
    };
  };
};
#NOP {开始护卫,%1:后续指令};
#ALIAS {guard_response_start} {
  #VARIABLE {startts} {@now{}};
  #CLASS serviceclass KILL;
  #CLASS serviceclass OPEN;
  #ACTION {^苗家庄早已经封闭，无法进去。} {
    #CLASS serviceclass KILL;
    stopwalk;
    #DELAY {2} {
      tell $caller[id] guard_onposition;
      #VARIABLE {caller} {};
      %1;
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkguard\"|你设定checkguard为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {"$caller[target]" == ""} {
      #CLASS serviceclass KILL;
      stopfight;
      %1;
    };
    #ELSEIF {@elapsed{$startts} > 300} {
      #CLASS serviceclass KILL;
      #VARIABLE {caller} {};
      stopfight;
      %1;
    };
    #ELSE {
      guard $caller[id];
      kill $caller[target] 1;
      kill $caller[target] 2;
      kill $caller[target] 3;
      #IF {@eval{@elapsed{$startts} % 3} == 0} {
        tell $caller[id] guard_onposition;
      };
      #DELAY {2} {
        echo {checkguard};
      };
    };
  };
  #ACTION {^$caller[name]神志迷糊，脚下一个不稳，倒在地上昏了过去} {
    #CLASS serviceclass KILL;
    logbuff {guard_$caller[id]};
    #VARIABLE {caller} {};
    stopfight;
    dohalt {
      %1;
    };
  };
  #ACTION {^$caller[name]「啪」的一声倒在地上，挣扎着抽动了几下就死了} {
    #CLASS serviceclass KILL;
    logbuff {guard_$caller[id]};
    #VARIABLE {caller} {};
    stopfight;
    dohalt {
      %1;
    };
  };
  #ACTION {^$caller[name]对你抱拳道：“青山不改，绿水常流，咱们后会有期！”} {
    #CLASS serviceclass KILL;
    logbuff {guard_$caller[id]};
    #VARIABLE {caller} {};
    stopfight;
    dohalt {
      %1;
    };
  };
  #CLASS serviceclass CLOSE;
  wwp;
  pfm_wuxing;
  pfm_buff_normal;
  startfight;
  tell $caller[id] guard_onposition;
  echo {checkguard};
};
initguardservice;