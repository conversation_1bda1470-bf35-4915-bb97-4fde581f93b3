#NOP {巡城任务模块};
#ALIAS xcpath1 {execute {wu;eu;wu;nu;n;nu;nd;n;n;s}};
#ALIAS xcpath2 {execute {s;su;sd;s;sd;ed;sd;eu;sd;wu}};
#ALIAS xcpath3 {execute {sd;sw;su;nd;w;nw;sw;up;down;ne}};
#ALIAS xcpath4 {execute {se;sw;su;nd;ne;e;ne;ed;e;n}};
#ALIAS xcpath5 {execute {w;e;n;w;e;n;w;e;n;w;e;n}};
#ALIAS xcpath6 {execute {s;e;s;e;n;n;s;s}};
#ALIAS xcpath7 {
  execute {s;s;n;e;n;n;n};
  #if {@carryqty{silver} > 110} 
  {
    cun 100 silver;
  };
};
#ALIAS xcpath8 {execute {s;e;n;s;e;w;s;e;w;s;e}};
#ALIAS xcpath9 {execute {w;s;e;w;s;e;e;se;n;s;s;e;w;s;e;w}};
#ALIAS xcpath10 {execute {su;enter;d;d;d;e;e;up;e;w;d;w;w;up}};
#ALIAS xcpath11 {execute {up;up;out;nd;n;n;nw;ne;eu;eu;se;se}};
#ALIAS xcpath12 {execute {enter;n;n;n;s;s;s;out;nw;nw}};
#ALIAS xcpath13 {execute {wd;wd;sw;w;w;s;e;w;s;e;w;s}};
#ALIAS xcpath14 {execute {e;w;s;e;w;s;n;w;s;n;w;n;e}};
#ALIAS xcpath15 {execute {w;w;e;n;s;s;s;s;su;e;w}};
#ALIAS xcpath16 {execute {se;w;w;e;e;s}};
#ALIAS xcpath17 {execute {su;sw;ne;nd;n;su;n;s;s;n}};
#ALIAS xcpath18 {execute {nd;nw;nd;n;n;w;s}};
#ALIAS xcpath19 {execute {n;w;s;n;w;e;n;w;e;n;w;e;n;w;e;n;w}};
#NOP {检查城东};
#ALIAS xcpath30 {execute {e;s;s;s;s;e;e;e;e;n;n;n;n;e}};
#ALIAS xcpath31 {execute {e;ne;eu;eu;se;se;enter;n;n}};
#ALIAS xcpath32 {execute {n;s;s;s;out;nw;nw;wd;wd;sw}};
#ALIAS xcpath33 {execute {w;w;s;s;s;s;w;w;w;w;n;n;n;n;w}};
#NOP {检查城南};
#ALIAS xcpath40 {execute {e;s;s;s;s;e;e;s;s;su;e;w}};
#ALIAS xcpath41 {execute {se;w;w;e;e;s}};
#ALIAS xcpath42 {execute {su;sw;ne;nd;n;su;n;s;s;n}};
#ALIAS xcpath43 {execute {nd;nw;nd;n;n;w;s}};
#ALIAS xcpath44 {execute {n;w;s;n;w;e;n;w;e;n;w;e;n;w;e;n;w}};
#NOP {检查城西};
#ALIAS xcpath50 {execute {wu;eu;wu;nu;n;nu;nd;n;n;s}};
#ALIAS xcpath51 {execute {s;su;sd;s;sd;ed;sd;eu;sd;wu}};
#ALIAS xcpath52 {execute {sd;sw;su;nd;w;nw;sw;up;down;ne}};
#ALIAS xcpath53 {execute {se;sw;su;nd;ne;e;ne;ed}};
#ALIAS {jobgo_xuncheng} {
  #CLASS jobcheckclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkhp\"|你设定checkhp为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {$hp[jingli] >= 400} {
      #CLASS jobcheckclass KILL;
      #IF {$env[guanfu] == 0} {
        joingf {
          gotodo {大理城} {西门} {jobcheck_xuncheng}
        }
      };
      #ELSE {
        gotodo {大理城} {西门} {jobcheck_xuncheng}
      };
    };
    #ELSE {
      #DELAY {6} {
        yun jingli;
        hp;
        echo {checkhp};
      }
    };
  };
  #CLASS jobcheckclass CLOSE;
  hp;
  echo {checkhp};
};
#ALIAS {jobcheck_xuncheng} {
  #CLASS jobcheckclass KILL;
  #CLASS jobcheckclass OPEN;
  #ACTION {^你一觉醒来} {
    execute {
      s;out;#2 n;#2 w;#3 s;w;
      yun jingli;
      hp;
      cond
    };
    echo {checkhp};
  };
  #ACTION {^{设定环境变量：action \= \"checkhp\"|你设定checkhp为反馈信息}} {
    #IF {$hp[jingli] < 50} {
      yun jingli;
      hp;
      echo {checkhp};
    };
    #ELSEIF {$hp[jingli] < 300} {
      execute {e;#3 n;#2 e;#2 s;give 5 silver to xiao er;enter;n;sleep};
    };
    #ELSEIF {"$hp[condition][双倍经验]" == ""} {
      #CLASS jobcheckclass KILL;
      tbbuy {ebook} {1} {
        read book;
        jobgo_xuncheng;
      };
    };
    #ELSEIF {$hp[pot] > 2000} {
      #CLASS jobcheckclass KILL;
      cun_pot {jobgo_xuncheng}
    };
    #ELSE {
      #CLASS jobcheckclass KILL;
      jobask_xuncheng
    };
  };
  #CLASS jobcheckclass CLOSE;
  execute {
    yun jingli;
    hp;
    cond
  };
  echo {checkhp};
};
#ALIAS {jobask_xuncheng} {
  #CLASS jobrequestclass KILL;
  #CLASS jobrequestclass OPEN;
  #ACTION {^你向朱丹臣打听有关『巡城』的消息。} {
    #VARIABLE {askresult} {0};
    #CLASS jobresponseclass OPEN;
    #ACTION {^朱丹臣说道：「%*你刚做完任务} {
      #CLASS jobresponseclass KILL;
      #VARIABLE {askresult} {1};
    };
    #ACTION {^朱丹臣说道：「好吧，你就在大理城周围四处查看一下，巡城时应当小心防范，防止外敌。」} {
      #CLASS jobresponseclass KILL;
      #VARIABLE {xcerror} {
        {东} {0}
        {西} {0}
        {南} {0}
        {北} {0}
      };
    };
    #ACTION {^朱丹臣说道：「你不是已经领了巡城的任务吗？还不快去做。」} {
      #CLASS jobresponseclass KILL;
    };
    #ACTION {^朱丹臣说道：「%*你不是本王府随从，此话从何说起？」} {
      #CLASS jobresponseclass KILL;
      #VARIABLE {askresult} {2};
    };
    
    #ACTION {^朱丹臣说道：「我看你的武功已有相当的功底了} {
      #CLASS jobresponseclass KILL;
      #VARIABLE {askresult} {3};
    };
    #CLASS jobresponseclass CLOSE;
  };
  #ACTION {^{设定环境变量：action \= \"checkask\"|你设定checkask为反馈信息}} {
    #VARIABLE {idle} {0};
    #CLASS jobresponseclass KILL;
    #CLASS jobrequestclass KILL;
    #SWITCH {$askresult} {
      #CASE {0} {
        #VARIABLE {xcstep} {1};
        dohalt {
          jobdo_xuncheng
        }
      };
      #CASE {1} {
        dohalt {
          jobask_xuncheng
        }
      };
      #CASE {2} {
        dohalt {
          joingf {
            gotodo {大理城} {西门} {jobcheck_xuncheng}
          }
        }
      };
      #CASE {3} {
        dohalt {
          guide_newbie {startjob};
        }
      };
      #DEFAULT {
        dohalt {
          jobask_xuncheng
        }
      };
    };
  };
  #CLASS jobrequestclass CLOSE;
  ask zhu danchen about 巡城;
  echo {checkask};
};
#ALIAS {jobdo_xuncheng} {
  #VARIABLE {retryflag} {0};
  #VARIABLE {checkdo} {
    yun jingli;
    hp;
    echo {checkhp}
  };
  #CLASS jobdoclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkhp\"|你设定checkhp为反馈信息}} {
    #IF {$xcstep < 20} {
      #IF {$hp[jingli] < 200} {
        #DELAY {5} {$checkdo}
      };
      #ELSE {
        xcpath$xcstep;
        #MATH {xcstep} {$xcstep + 1};
        #DELAY {1} {$checkdo}
      };
    };
    #ELSEIF {$xcstep == 20} {
      #CLASS jobdoclass KILL;
      loc {
        gotodo {大理城} {西门} {jobfinish_xuncheng} 
      }
    };
    #ELSEIF {$xcstep < 34} {
      #IF {$hp[jingli] < 200} {
        #DELAY {5} {$checkdo}
      };
      #ELSE {
        xcpath$xcstep;
        #MATH {xcstep} {$xcstep + 1};
        #DELAY {1} {$checkdo}
      };
    };
    #ELSEIF {$xcstep == 34} {
      #CLASS jobdoclass KILL;
      loc {
        gotodo {大理城} {西门} {jobfinish_xuncheng} 
      }
    };
    #ELSEIF {$xcstep < 45} {
      #IF {$hp[jingli] < 200} {
        #DELAY {5} {$checkdo}
      };
      #ELSE {
        xcpath$xcstep;
        #MATH {xcstep} {$xcstep + 1};
        #DELAY {1} {$checkdo}
      };
    };
    #ELSEIF {$xcstep == 45} {
      #CLASS jobdoclass KILL;
      loc {
        gotodo {大理城} {西门} {jobfinish_xuncheng} 
      }
    };
    #ELSEIF {$xcstep < 54} {
      #IF {$hp[jingli] < 200} {
        #DELAY {5} {$checkdo}
      };
      #ELSE {
        xcpath$xcstep;
        #MATH {xcstep} {$xcstep + 1};
        #DELAY {1} {$checkdo}
      };
    };
    #ELSE {
      #CLASS jobdoclass KILL;
      loc {
        gotodo {大理城} {西门} {jobfinish_xuncheng} 
      }
    };
  };
  #CLASS jobdoclass CLOSE;
  #UNTICKER {idleticker};
  yun jingli;
  hp;
  echo {checkhp};
};
#ALIAS {jobfinish_xuncheng} {
  #VARIABLE {askresult} {255};
  #CLASS jobrequestclass OPEN;
  #ACTION {^你这么快回来了} {
    #VARIABLE {askresult} {1};
  };
  #ACTION {^你是不是偷懒，城内漏了} {
    #VARIABLE {askresult} {255};
  };
  #ACTION {^你是不是偷懒，城东漏了} {
    #VARIABLE {askresult} {2};
    #VARIABLE {xcerror[东]} {@eval{$xcerror[东] + 1}};
    #IF {$xcerror[东] >= 2} {
      #VARIABLE {askresult} {255};
    };
  };
  #ACTION {^你是不是偷懒，城南漏了} {
    #VARIABLE {askresult} {3};
    #VARIABLE {xcerror[南]} {@eval{$xcerror[南] + 1}};
    #IF {$xcerror[南] >= 2} {
      #VARIABLE {askresult} {255};
    };
  };
  #ACTION {^你是不是偷懒，城西漏了} {
    #VARIABLE {askresult} {4};
    #VARIABLE {xcerror[西]} {@eval{$xcerror[西] + 1}};
    #IF {$xcerror[西] >= 2} {
      #VARIABLE {askresult} {255};
    };
  };
  #ACTION {^朱丹臣轻轻地拍了拍你的头} {
    #VARIABLE {askresult} {0};
  };
  #ACTION {^你没巡城跑来领什么功} {
    #VARIABLE {askresult} {0};
  };
  #ACTION {^{设定环境变量：action \= \"checkhp\"|你设定checkhp为反馈信息}} {
    #IF {$hp[jingli] < 200} {
      #DELAY {2} {
        hp;
        echo {checkhp}
      };
    };
    #ELSE {
      #CLASS jobrequestclass KILL;
      #DELAY {1} {
        #IF {$retryflag == 1} {
          #VARIABLE {xcerror} {
            {东} {0}
            {西} {0}
            {南} {0}
            {北} {0}
          };
          #VARIABLE {xcstep} {1};
          dohalt {
            jobdo_xuncheng
          }
        };
        #ELSE {
          #VARIABLE {retryflag} {1};
          #IF {$askresult == 2} {
            #VARIABLE {xcstep} {30};
          };
          #ELSEIF {$askresult == 3} {
            #VARIABLE {xcstep} {40};
          };
          #ELSE {
            #VARIABLE {xcstep} {50};
          };
          jobdo_xuncheng
        };
      };
    };
  };
  #ACTION {^{设定环境变量：action \= \"checktask\"|你设定checktask为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {$askresult == 255} {
      #CLASS jobrequestclass KILL;
      #VARIABLE {xcerror} {
        {东} {0}
        {西} {0}
        {南} {0}
        {北} {0}
      };
      #VARIABLE {xcstep} {1};
      dohalt {
        jobdo_xuncheng
      }
    };
    #ELSEIF {$askresult == 1} {
      #DELAY {5} {
        task ok;
        echo {checktask}
      };
    };
    #ELSEIF {$askresult == 2 || $askresult == 3 || $askresult == 4} {
      hp;
      echo {checkhp};
    };
    #ELSE {
      #CLASS jobrequestclass KILL;
      i;
      jobcheck_xuncheng
    };
  };
  #CLASS jobrequestclass CLOSE;
  task ok;
  echo {checktask};
};