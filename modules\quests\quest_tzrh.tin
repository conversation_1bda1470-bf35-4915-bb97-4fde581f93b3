#NOP {铁掌融合,%1:后续指令};
#ALIAS {goquest_tzrh} {
  #VARIABLE {questmodule} {铁掌融合};
  #SWITCH {$questlist[$questmodule][laststep]} {
    #CASE {0} {tzrh_axe};
    #CASE {1} {tzrh_bb};
    #DEFAULT {
      questfail {$questmodule};
      %1
    };
  };
};
#ALIAS {tzrh_axe} {
  #VARIABLE {eventflag} {0};
  #CLASS questclass KILL;
	#CLASS questclass OPEN;
  #ACTION {^你向一灯大师打听有关『慈恩』的消息} {
    #CLASS questresponseclass KILL;
    #CLASS questresponseclass OPEN;
    #ACTION {^一灯大师说道：「慈恩心愿已了，不愿再沾染尘世之事} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questupdate {$questmodule} {1};
      dohalt {%1}
    };
    #ACTION {^一灯大师说道：「慈恩见老衲心念故国，出去打探消息} {
      #CLASS questresponseclass KILL;
      dohalt {gotodo {襄阳城} {中军大帐} {echo {checkevent}}}
    };
    #CLASS questresponseclass CLOSE;
  };
  #ACTION {^你忽然听到远处传来一阵打斗之声，不禁心下疑惑，想过去看个究竟} {
    #VARIABLE {eventflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkevent\"|你设定checkevent为反馈信息}} {
    #IF {$eventflag == 0} {
      #CLASS questclass KILL;
      questfail {$questmodule};
      dohalt {%1}
    };
    #ELSE {
      openwimpy;
      createpfm {} {1};
      kill jinlun fawang;
      autopfm
    };
  };
  #ACTION {^金轮法王恨恨的对你说} {
    dohalt {ask ci en about 一灯大师}
  };
  #ACTION {^你向慈恩打听有关『一灯大师』的消息} {
    dohalt {ask ci en about 搭救}
  };
  #ACTION {^你细想刚才战斗经过，发现慈恩用的虽然是双掌} {
    #CLASS questclass KILL;
    questupdate {$questmodule} {1};
    loc {dohalt {%1}}
  };
  #CLASS questclass CLOSE;
  gotodo {桃源县} {石屋正房} {ask yideng dashi about 慈恩}
};
#ALIAS {tzrh_bb} {
  #VARIABLE {eventflag} {0};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你向上官剑南打听有关『宝物』的消息} {
    dohalt {
      pray pearl;
      gotodo {铁掌山} {大石室} {echo {checkevent}}
    };
  };
  #ACTION {^看到兵器架上的武器，忽然若有所思} {
    #VARIABLE {eventflag} {2};
  };
  #ACTION {^你看到兵器架上的武器，忽然想起当日慈恩所使用的掌法} {
    #VARIABLE {eventflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkevent\"|你设定checkevent为反馈信息}} {
    #IF {$eventflag == 0} {
      #CLASS questclass KILL;
      questupdate {$questmodule} {2};
      dohalt {%1}
    };
    #ELSEIF {$eventflag == 2} {
      questdelay {$questmodule} {10800};
      dohalt {%1}
    };
    #ELSE {
      #VARIABLE {idle} {0};
      #NOP {等};
    };
  };
  #ACTION {^你成功的把铁掌武技融入了铁掌掌法中} {
    #CLASS questclass KILL;
    questupdate {$questmodule} {2};
    dohalt {%1}
  };
  #ACTION {^你沉思许久，不得要领} {
    #CLASS questclass KILL;
    questfail {$questmodule};
    dohalt {%1}
  };
  #CLASS questclass CLOSE;
  gotonpc {上官剑南} {ask shangguan jiannan about 宝物}
};