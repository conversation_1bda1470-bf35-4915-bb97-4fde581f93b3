#NOP {胡家刀法,%1:后续指令};
#ALIAS {goquest_hjdf} {
  #VARIABLE {questmodule} {胡家刀法};
  gotonpc {胡斐} {hjdf_askhu {%1}}
};
#ALIAS {hjdf_askhu} {
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你向胡斐打听有关『胡家刀法』的消息。} {
    #CLASS questresponseclass OPEN;
    #ACTION {^你向胡斐请教有关胡家刀法的奥妙。} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questsuccess {$questmodule};
      dohalt {%1}
    };
    #ACTION {^胡斐说道：「有些累了，这刀法还是以后切磋吧} {
      #NOP {刚招待过玩家,等会再来};
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questupdate {$questmodule};
      dohalt {%1}
    };
    #ACTION {^胡斐说道：「现在时间紧急，这刀法还是以后切磋吧} {
      #NOP {复仇篇开始,解药篇未结束};
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questupdate {$questmodule};
      dohalt {%1}
    };
    #ACTION {^胡斐说道：「{今天先切磋到这里吧，明天吧|以你当前的经验恐怕还是难以领悟}} {
      #NOP {经验或时间不足,一般不会出现};
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questdelay {$questmodule} {} {10800};
      dohalt {%1}
    };
    #ACTION {^胡斐说道：「这位%*武功未能返璞归真，只怕难以领悟胡家刀法的绝技} {
      #NOP {没撑住};
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questupdate {$questmodule};
      dohalt {%1}
    };
    #ACTION {^你拼命地回忆瞬间交手的一幕，回忆着刚才的招数，你开始认真思索着} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      #VARIABLE {idle} {0};
      pray pearl;
      hjdf_thinking {%1};
    };
    #CLASS questresponseclass CLOSE;
  };
  #CLASS questclass CLOSE;
  ask hu fei about 胡家刀法
};
#ALIAS {hjdf_thinking} {
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你于%*解开雪山飞狐胡家刀法篇} {
    #NOP {成功};
    #CLASS questclass KILL;
    questsuccess {$questmodule};
    %1
  };
  #ACTION {^你脑海里似乎抓住了什么，可是依然不是很明白} {
    #NOP {失败};
    #CLASS questclass KILL;
    questfail {$questmodule};
    %1
  };
  #CLASS questclass CLOSE;
  #VARIABLE {idle} {-30}
};