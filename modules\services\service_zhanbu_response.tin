#NOP {占卜模块};
#LIST {authorizedusers} {create} {xcjhmi;mormont};
#LIST {authorizetargets} {create} {xia xueyi;lu wushuang;yin li;gui zhong;chen jinnan};
#ALIAS {initzhanbuservice} {
  #CLASS servicezhanbuclass KILL;
  #CLASS servicezhanbuclass OPEN;
  #ACTION {^%*(%*)告诉你：zhanbu_request %*} {
    zhanbu_accept {@lower{%%2}} {%%1} {@lower{%%3}}
  };
  #ACTION {^%*(%*)告诉你：zhanbu_cancel} {
    #VARIABLE {caller} {};
  };
  #ACTION {^! %*(%*)告诉你：zhanbu_cancel} {
    #VARIABLE {caller} {};
  };
  #CLASS servicezhanbuclass CLOSE;
};
#NOP {接收占卜请求，%1:id,%2:名字,%3:目标id};
#ALIAS {zhanbu_accept} {
  #IF {@contains{{authorizetargets}{%3}} == 0} {
    tell %1 不在占卜对象列表;
  };
  #ELSEIF {@carryqty{tie bagua} == 0} {
    tell %1 zhanbu_error;
  };
  #ELSE {
    #NOP {超过五分钟重置};
    #IF {"$caller" != "" && @elapsed{$caller[timestamp]} > 300} {
      #VARIABLE {caller} {};
    };
    #NOP {更新请求};
    #IF {"$caller" != ""} {
      tell %1 zhanbu_wait
    };
    #ELSE {
      #VARIABLE {caller} {
        {request} {zhanbu}
        {timestamp} {@now{}}
        {id} {%1}
        {name} {%2}
        {target} {%3}
      };
      zhanbu_prepare
    };
  };
};
#ALIAS {zhanbu_prepare} {
  #VARIABLE {okflag} {0};
  #CLASS serviceclass KILL;
  #CLASS serviceclass OPEN;
  #ACTION {^你告诉%*：zhanbu_help} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkhelp\"|你设定checkhelp为反馈信息}} {
    #CLASS serviceclass KILL;
    #IF {$okflag == 0} {
      tell $caller[id] zhanbu_error;
      #VARIABLE {caller} {};
      startjob
    };
    #ELSE {
      tell $caller[id] zhanbu_come;
      #VARIABLE {zhanbuing} {1};
    };
  };
  #CLASS serviceclass CLOSE;
  #IF {"$conf[assistant]" == ""} {
    #CLASS serviceclass KILL;
    tell $caller[id] zhanbu_error;
    #VARIABLE {caller} {};
    startjob;
  };
  #ELSE {
    tell $conf[assistant] zhanbu_help $zhanbucity $zhanburoom;
    echo {checkhelp};
  };
  #VARIABLE {zhanbuing} {1};
};
#ALIAS {zhanbu_response} {
  #IF {@contains{{$conf[joblist]}{钓鱼}} > 0} {
    #VARIABLE {zhanbucity} {福州城};
    #VARIABLE {zhanburoom} {礁石};
  };
  #ELSE {
    #VARIABLE {zhanbucity} {扬州城};
    #VARIABLE {zhanburoom} {小吃店};
  };
  gotodo {$zhanbucity} {$zhanburoom} {zhanbu_start {%1}};
};
#NOP {通知助手过来，准备开始};
#ALIAS {zhanbu_start} {
  #VARIABLE {startts} {@now{}};
  #VARIABLE {trycount} {0};
  #VARIABLE {meetflag} {0};
  #CLASS serviceclass KILL;
  #CLASS serviceclass OPEN;
  #ACTION {^%*双手抱拳，对你作了个揖道：这位姑娘请了} {
    #VARIABLE {meetflag} {1};
    
  };
  #ACTION {^{设定环境变量：action \= \"checkmeet\"|你设定checkmeet为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {$meetflag == 0} {
      #IF {@elapsed{$startts} > 300} {
        #CLASS serviceclass KILL;
        tell $caller[id] zhanbu_fail;
        #VARIABLE {caller} {};
        jobcheck;
      };
      #ELSE {
        #DELAY {2} {
          echo {checkmeet};
        };
      };
    };
    #ELSE {
      yun zhanbu $caller[target];
    };
  };
  #ACTION {^你双手抱拳，对} {
    yun zhanbu $caller[target];
  };
  #ACTION {^{可是你费了半天的力气|你发现目前没这个人在线|但你沮丧地发现你现在}} {
    #VARIABLE {idle} {0};
    #MATH {trycount} {$trycount + 1};
    dohalt {
      #IF {$trycount > 3} {
        #CLASS serviceclass KILL;
        bye $conf[assistant];
        tell $caller[id] zhanbu_fail;
        #VARIABLE {caller} {};
        %1
      };
      #ELSE {
        yun jingli;
        startfull {
          yun zhanbu $caller[target];
        };
      };
    }
  };
  #ACTION {^你掐指算出%!*(%!*)现在好象在%*一带活动} {
    #CLASS serviceclass KILL;
    bye $conf[assistant];
    dohalt {
      tell $caller[id] zhanbu_success %%1;
      #VARIABLE {caller} {};
      %1
    };
  };
  #CLASS serviceclass CLOSE;
  startfull {
    echo {checkmeet};
  };
};
initzhanbuservice;