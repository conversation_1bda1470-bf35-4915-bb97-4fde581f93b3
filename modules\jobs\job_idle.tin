#NOP {发呆模块};
#VARIABLE {itemts} {
  {tiebagua} {0}
};
#ALIAS {jobgo_idle} {
  gotoroom {424} {jobidle_start}
};
#ALIAS {jobidle_start} {
  #VARIABLE {drugscount} {0};
  #LIST {emotes} {create} {18mo;9191;9494;accuse;addoil;admire;admire2;admit;afraid;agree;ah;angry;angry3;beg1;beg2;laugh;haha;hehe;smile};
  #CLASS jobcheckclass KILL;
  #CLASS jobcheckclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkhp\"|你设定checkhp为反馈信息}} {
    #VARIABLE {idle} {};
    #IF {"$caller" != ""} {
      #CLASS jobcheckclass KILL;
      checkrequest {gotoroom {424} {jobidle_start}}
    };
    #ELSEIF {(@contains{{conf[services]}{zhanbu}} > 0 || @contains{{conf[services]}{assist}} > 0) && @carryqty{tie bagua} == 0 && @elapsed{$itemts[tiebagua]} > 600} {
      #VARIABLE {itemts[tiebagua]} {@now{}};
      gettiebagua {gotoroom {424} {jobserve_start} {%1}};
    };
    #ELSE {
      $emotes[+@rnd{{1}{&emotes[]}}] qian;
      #DELAY {6} {
        hp;
        i;
        score;
        time;
        echo {checkhp}
      };
    };
  };
  
  #CLASS jobcheckclass CLOSE;
  #IF {"$caller[request]" == "suanming"} {
    #VARIABLE {caller} {};
  };
  hp;
  i;
  score;
  echo {checkhp}
};