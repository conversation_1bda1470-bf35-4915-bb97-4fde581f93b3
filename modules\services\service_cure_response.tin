#NOP {门诊响应};
#LIST {common[mergencydisease]} {create} {怪蛇;星宿掌};
#ALIAS {initcureservice} {
  #CLASS servicecureclass KILL;
  #CLASS servicecureclass OPEN;
  #ACTION {^%*(%*)告诉你：cure_request %* %*} {
    cure_accept {@lower{%%2}} {%%1} {%%3} {%%4}
  };
  #ACTION {^! %*(%*)告诉你：cure_request %* %*} {
    cure_accept {@lower{%%2}} {%%1} {%%3} {%%4}
  };
  #ACTION {^%*(%*)告诉你：cure_cancel} {
    #VARIABLE {caller} {};
  };
  #ACTION {^! %*(%*)告诉你：cure_cancel} {
    #VARIABLE {caller} {};
  };
  #CLASS servicecureclass CLOSE;
};
#NOP {接受问诊请求,%1:id,%2:名字,%3:症状,%4:地点};
#ALIAS {cure_accept} {
  #NOP {超过五分钟重置};
  #IF {"$caller" != "" && @elapsed{$caller[timestamp]} > 300} {
    #VARIABLE {caller} {};
  };
  #NOP {更新请求};
  #IF {"$caller" != "" && "$caller[id]" != "%1"} {
    tell %1 cure_wait
  };
  #ELSE {
    #VARIABLE {caller} {
      {request} {doctor}
      {timestamp} {@now{}}
      {id} {%1}
      {name} {%2}
      {disease} {%3}
      {room} {%4}
    };
    tell %1 cure_come
  };
};
#NOP {去诊所接诊,%1:后续指令};
#ALIAS {cure_response} {
  #IF {@contains{{common[mergencydisease]}{$caller[disease]}} > 0 && @carryqty{da huandan} < 2} {
    tbbuy {da huandan} {2} {cure_response {%1}}
  };
  #ELSEIF {@carryqty{chuanbei wan} < 4} {
    buymedicine {chuanbei wan} {4} {cure_response {%1}}
  };
  #ELSE {
    gotoroom {$caller[room]} {cure_response_start {%1}}
  };
};
#ALIAS {cure_response_start} {
  #VARIABLE {curets} {@now{}};
  #VARIABLE {startts} {@now{}};
  #VARIABLE {okflag} {0};
  #CLASS serviceclass KILL;
  #CLASS serviceclass OPEN;
  #ACTION {^你轻轻地拍了拍$caller[name]的头} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkcaller\"|你设定checkcaller为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {$okflag == 1} {
      #CLASS serviceclass KILL;
      cure_response_heal {%1}
    };
    #ELSEIF {@elapsed{$startts} > 120} {
      #CLASS serviceclass KILL;
      curelog {未拍到【$caller[name]】狗头，放弃治疗。};
      logbuff {abandon};
      #VARIABLE {caller} {};
      %1
    };
    #ELSE {
      #DELAY {2} {
        pat $caller[id];
        echo {checkcaller};
      };
    };
  };
  #CLASS serviceclass CLOSE;
  curelog {开始治疗【$caller[name]】的【$caller[disease]】。};
  pat $caller[id];
  echo {checkcaller};
};
#NOP {开始治疗,%1:后续指令};
#ALIAS {cure_response_heal} {
  #IF {@contains{{conf[departments]}{$caller[disease]}} == 0} {
    #NOP {未开设的科室};
    cure_response_abandon {%1}
  };
  #ELSE {
    #SWITCH {"$caller[disease]"} {
      #CASE {"怪蛇"} {cure_response_common {%1}};
      #CASE {"星宿掌"} {cure_response_common {%1}};
      #CASE {"冰魄银针"} {cure_response_common {%1}};
      #CASE {"寒冰绵掌"} {cure_response_common {%1}};
      #CASE {"蔓陀萝花"} {cure_response_common {%1}};
      #CASE {"腐尸"} {cure_response_common {%1}};
      #CASE {"火"} {cure_response_common {%1}};
      #CASE {"寒"} {cure_response_common {%1}};
      #CASE {"混元掌"} {cure_response_hunyuan {%1}};
      #CASE {"蓝砂手"} {cure_response_lansha {%1}};
      #DEFAULT {cure_response_abandon {%1}};
    };
  };
};
#NOP {通用解毒,需要蛤蟆功和yinyun-ziqi,%1:后续指令};
#ALIAS {cure_response_common} {
  #VARIABLE {okflag} {0};
  #CLASS serviceclass KILL;
  #CLASS serviceclass OPEN;
  #ACTION {^开始以「纯阳无极功」吸取} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^你已身中%*恐会散及} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^%*身上的所中的%*太深了你现在还吸不出来} {
    #VARIABLE {okflag} {3};
  };
  #ACTION {^你的「纯阳无极功」修为还不能为%*吸取} {
    #VARIABLE {okflag} {3};
  };
  #ACTION {^你要给谁疗毒} {
    #VARIABLE {okflag} {2};
  };
  #ACTION {^这个是个死物，给它疗毒} {
    #VARIABLE {okflag} {2};
  };
  #ACTION {^%*并未中毒} {
    #VARIABLE {okflag} {2};
  };
  #ACTION {^{设定环境变量：action \= \"checkxidu\"|你设定checkxidu为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {"$kungfu[base][force][jifa]" != "yinyun-ziqi"} {
      jifa force yinyun-ziqi;
      jifa;
      hp;
      #DELAY {0.5} {echo {checkxidu}}
    };
    #ELSEIF {$hp[neili] < 1000} {
      #NOP {根据紧急情况选择吃药还是打坐};
      #IF {@contains{{common[mergencydisease]}{$caller[disease]}} > 0} {
        fu dahuan dan;
        i;
        hp;
        #DELAY {0.5} {echo {checkxidu}}
      };
      #ELSE {
        #CLASS serviceclass KILL;
        #NOP {去洗澡};
        showerfull {gotoroom {$caller[room]} {cure_response_common {%1}}}
      };
    };
    #ELSEIF {$okflag == 1} {
      #NOP {等待吸完};
      #VARIABLE {idle} {-300};
      #VARIABLE {okflag} {0};
    };
    #ELSEIF {$okflag == 2} {
      #NOP {结束};
      #CLASS serviceclass KILL;
      #VARIABLE {caller} {};
      ok $caller[id];
      dohalt {%1}
    };
    #ELSEIF {$okflag == 3} {
      #NOP {治不了};
      #CLASS serviceclass KILL;
      #VARIABLE {caller} {};
      shake $caller[id];
      wear all;
      prepareforce;
      dohalt {showerfull {%1}}
    };
    #ELSE {
      #NOP {未能开始};
      #DELAY {0.5} {
        dohalt {
          yun qudu $caller[id];
          echo {checkxidu}
        };
      };
    };
  };
  #ACTION {^过了约莫半个时辰，只见} {
    dohalt {
      jifa force hamagong;
      jifa;
      #IF {@contains{{common[mergencydisease]}{$caller[disease]}} > 0} {
        fu dahuan dan
      };
      #ELSE {
        fu chuanbei wan
      };
      i;
      hp;
      cond;
      echo {checkqudu}
    }
  };
  #ACTION {^你{倒运气息，头下脚上，气血逆行，将毒气从进入身子之处逼了出去|深深吸了口气，口中“咕咕。。。”地叫了几声}} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^{$dazuo_over}} {
    dohalt {
      yun qi;
      yun qudu;
      hp;
      #DELAY {0.5} {echo {checkqudu}}
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkqudu\"|你设定checkqudu为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {"$kungfu[base][force][jifa]" != "hamagong"} {
      dohalt {
        jifa force hamagong;
        jifa;
        #IF {@contains{{common[mergencydisease]}{$caller[disease]}} > 0} {
          fu dahuan dan
        };
        #ELSE {
          fu chuanbei wan
        };
        i;
        hp;
        #DELAY {0.5} {echo {checkqudu}}
      }
    };
    #ELSEIF {$okflag == 1} {
      #VARIABLE {okflag} {0};
      #NOP {继续吸毒};
      ? $caller[id];
      dohalt {echo {checkover}}
    };
    #ELSEIF {$hp[neili < 1000]} {
      dohalt {
        #IF {@contains{{common[mergencydisease]}{$caller[disease]}} > 0} {
          fu dahuan dan;
          hp;
          #DELAY {0.5} {echo {checkqudu}}
        };
        #ELSE {
          dohalt {dzn}
        };
      };
    };
    #ELSE {
      dohalt {
        yun qudu;
        #DELAY {0.5} {echo {checkqudu}}
      };
    };
  };
  #ACTION {^$caller[name]痛快地对你说道：好吧} {
    #NOP {这里是为了早点确认，尽量避免重新jifa yinyun-ziqi};
    #VARIABLE {okflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkover\"|你设定checkover为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {$okflag == 1} {
      #NOP {结束};
      #CLASS serviceclass KILL;
      curelog {治疗完毕，共耗时【@elapsed{$curets}】秒。};
      logbuff {cure};
      ok $caller[id];
      #VARIABLE {caller} {};
      wear all;
      prepareforce;
      dohalt {showerfull {%1}}
    };
    #ELSE {
      #NOP {继续吸毒};
      #NOP {检查药物够不够};
      #IF {@contains{{common[mergencydisease]}{$caller[disease]}} > 0 && @carryqty{da huandan} < 1} {
        tbbuy {da huandan} {2} {gotoroom {$caller[room]} {cure_response_common {%1}}}
      };
      #ELSEIF {@carryqty{chuanbei wan} < 1} {
        buymedicine {chuanbei wan} {4} {gotoroom {$caller[room]} {cure_response_common {%1}}}
      };
      #ELSE {
        jifa force yinyun-ziqi;
        jifa;
        hp;
        #DELAY {0.5} {echo {checkxidu}}
      };
    };
  };
  #CLASS serviceclass CLOSE;
  jifa;
  yun qi;
  hp;
  #DELAY {0.5} {echo {checkxidu}}
};
#NOP {紫霞功解混元掌内伤,%1:后续指令};
#ALIAS {cure_response_hunyuan} {
  #VARIABLE {okflag} {0};
  #CLASS serviceclass KILL;
  #CLASS serviceclass OPEN;
  #ACTION {^你要用真气为谁疗伤} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^它不是活物，给它疗伤干什么} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^%*并没有受到混元无极内劲的伤害} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^%*已经受伤过重，经受不起你的真气震荡} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkcure\"|你设定checkcure为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {"$kungfu[base][force][jifa]" != "zixia-gong"} {
      jifa force zixia-gong;
      jifa;
      hp;
      #DELAY {0.5} {echo {checkcure}}
    };
    #ELSEIF {$hp[neili] < 1000} {
      #IF {@carryqty{chuanbei wan} > 0} {
        fu chuanbei wan;
        i;
        hp;
        #DELAY {0.5} {echo {checkcure}}
      };
      #ELSE {
        #CLASS serviceclass KILL;
        buymedicine {chuanbei wan} {2} {gotoroom {$caller[room]} {cure_response_hunyuan {%1}}}
      };
    };
    #ELSEIF {$okflag == 1} {
      #CLASS serviceclass KILL;
      curelog {治疗完毕，共耗时【@elapsed{$curets}】秒。};
      ok $caller[id];
      dohalt {%1}
    };
    #ELSE {
      dohalt {
        yun lifeforce $caller[id];
        hp;
        echo {checkcure}
      };
    };
  };
  #CLASS serviceclass CLOSE;
  jifa;
  hp;
  #DELAY {0.5} {echo {checkcure}}
};
#NOP {日月神教蓝砂手之刑,%1:后续指令};
#ALIAS {cure_response_lansha} {
  #VARIABLE {okflag} {0};
  #CLASS serviceclass KILL;
  #CLASS serviceclass OPEN;
  #ACTION {^没有这个人啊？} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^$caller[name]并没有受「蓝砂手之刑」} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^你盘膝坐下，突然间一伸手，在$caller[name]肩头一拍，韩鱼乌蓝的嘴皮渐渐红润，脸色看起来好多了} {
    #VARIABLE {okflag} {2};
  };
  #ACTION {^{设定环境变量：action \= \"checkcure\"|你设定checkcure为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {"$kungfu[base][force][jifa]" != "riyue-shengong"} {
      jifa force riyue-shengong;
      jifa;
      hp;
      #DELAY {0.5} {echo {checkcure}}
    };
    #ELSEIF {$hp[neili] < 2000} {
      #CLASS serviceclass KILL;
      showerfull {gotoroom {$caller[room]} {cure_response_lansha {%1}}}
    };
    #ELSEIF {$okflag == 1} {
      #CLASS serviceclass KILL;
      curelog {治疗完毕，共耗时【@elapsed{$curets}】秒。};
      ok $caller[id];
      #VARIABLE {caller} {};
      dohalt {%1}
    };
    #ELSE {
      #VARIABLE {okflag} {0};
      #DELAY {1} {
        dohalt {
          perform hand.jie $caller[id];
          hp;
          echo {checkcure}
        };
      };
    };
  };
  #CLASS serviceclass CLOSE;
  uwwp;
  bei none;
  jifa hand lansha-shou;
  jifa parry lansha-shou;
  bei hand;
  jifa;
  hp;
  #DELAY {0.5} {echo {checkcure}}
};
#NOP {弃疗};
#ALIAS {cure_response_abandon} {
  curelog {病入膏肓，无药可救。};
  shake $caller[id];
  #VARIABLE {caller} {};
  %1
};
initcureservice