#NOP {地图路径方向};
#VARIABLE {mapdir} {
  {n} {1}
  {e} {2}
  {s} {4}
  {w} {8}
  {u} {16}
  {d} {32}
  {ne} {5}
  {se} {6}
  {nw} {9}
  {sw} {12}
  {nu} {17}
  {eu} {18}
  {su} {20}
  {wu} {24}
  {nd} {33}
  {ed} {34}
  {sd} {36}
  {wd} {40}
  {out} {61}
  {enter} {62}
};
#NOP {遍历地图,%1:过来的方向,此方向视为起始方向};
#ALIAS {mmwalk} {
  #VARIABLE {mmcity} {};
  #LIST {mmrooms} {clear};
  #LIST {mmsteps} {clear};
  mmwalk_forward {%1} {0}
};
#NOP {从房间移动离开,%1:移动的方向,%2:房间号,%3:后续操作};
#ALIAS {mmwalk_movefrom} {
  #LIST {desclines} {clear};
  #VARIABLE {movecount} {0};
  #VARIABLE {mmarea} {};
  #VARIABLE {mmname} {};
  #VARIABLE {needfee} {0};
  #VARIABLE {croom} {};
  #CLASS mmclass KILL;
  #CLASS mmclass OPEN;
  #ACTION {^【你现在正处于%*】} {
    #VARIABLE {mmarea} {%%1};
    #LIST {desclines} {clear};
    #LINE ONESHOT #ACTION {^%* -} {
      #CLASS mmcapclass KILL;
      #CLASS mmcapclass OPEN;
      #VARIABLE {mmname} {%%%1};
      #ACTION {%*} {
        #IF {@instr{{%%%%1}{这是一个}} == 0} {
          #LIST {desclines} {add} {%%%%1}
        };
      };
      #ACTION {这里{唯一|明显}的出口是 %*。} {
        #CLASS mmcapclass KILL;
        #LIST {roomexits} {clear};
        #LIST {roomexits} {sort} {@formatExits{%%%%2}};
      } {1};
    };
    #CLASS mmcapclass CLOSE;
  } {1};
  #ACTION {^{设定环境变量：action \= \"confirmroom\"|你设定confirmroom为反馈信息}} {
    #VARIABLE {backdirect} {@reverseDir{%1}};
    #VARIABLE {croom} {
      {id} {@eval{&mmrooms[]+1}}
      {name} {$mmname}
      {area} {$mmarea}
      {desc} {@join{{$desclines}{nop}}}
      {src} {$backdirect}
      {visited} {
        {1} {$backdirect}
      }
      {exits} {
        {$backdirect} {%2}
      }
      {roomexits} {$roomexits}
    };
    #IF {%2 == 0} {
      #CLASS mmclass KILL;
      %3
    };
    #ELSEIF {@isSameRoom{{$croom}{$mmrooms[%2]}} == 1} {
      #NOP {房间和来源房间相同，移动受阻};
      #MATH {movecount} {$movecount + 1};
      #IF {$movecount > 5} {
        #CLASS mmclass KILL;
        #SHOWME {<faa>无法向%1方向移动，已将该方向房间设置为9998};
        #VARIABLE {mmrooms[%2][exits][%1]} {9998};
        #LIST {mmrooms[%2][visited]} {add} {%1};

        #VARIABLE {troom} {$mmrooms[%2]};
        #FOREACH {$troom[visited][]} {e} {
          #LOCAL {eindex} {@contains{{roomexits}{$e}}};
          #IF {$eindex > 0} {
            #LIST {roomexits} {delete} {$eindex};
          };
        };
        #DELAY {0.5} {
          #IF {&roomexits[] > 0} {
            #NOP {选择未访问的方向继续};
            mmwalk_forward {$roomexits[+1]} {$troom[id]}
          };
          #ELSEIF {$troom[id] > 1} {
            #NOP {通过来源方向返回};
            mmwalk_backward {$troom[src]} {$troom[id]}
          };
          #ELSE {
            #NOP {绘图结束};
            #SHOWME {<faa>绘制完毕，共计&mmrooms[]个房间};
          };
        };  
      };
      #ELSE {
        #SHOWME {<faa>移动受阻，继续尝试。};
        #DELAY {1} {
          %1;
          look;
          echo {confirmroom}
        }
      };
    };
    #ELSEIF {@contains{{croom[roomexits]}{$backdirect}} == 0} {
      #NOP {房间和来源房间路径不对称};
      #SHOWME {<faa>房间$mmrooms[%2][name]($mmrooms[%2][id])无法在%1方向上连通$croom[name]($croom[id])};
      #CLASS mmclass KILL;
    };
    #ELSE {
      #CLASS mmclass KILL;
      %3
    };
  };
  #CLASS mmclass CLOSE;
  %1;
  look;
  echo {confirmroom}
};
#NOP {移动至某房间,%1:移动方向,%2:房间号,%3:后续操作};
#ALIAS {mmwalk_moveto} {
  #LIST {desclines} {clear};
  #VARIABLE {movecount} {0};
  #VARIABLE {mmarea} {};
  #VARIABLE {mmname} {};
  #VARIABLE {croom} {};
  #CLASS mmclass KILL;
  #CLASS mmclass OPEN;
  #ACTION {^【你现在正处于%*】} {
    #VARIABLE {mmarea} {%%1};
    #LINE ONESHOT #ACTION {^%* -} {
      #CLASS mmcapclass KILL;
      #CLASS mmcapclass OPEN;
      #VARIABLE {mmname} {%%%1};
      #ACTION {%*} {
        #IF {@instr{{%%%%1}{这是一个}} == 0} {
          #LIST {desclines} {add} {%%%%1}
        };
      };
      #ACTION {这里{唯一|明显}的出口是 %*。} {
        #CLASS mmcapclass KILL;
        #LIST {roomexits} {clear};
        #LIST {roomexits} {sort} {@formatExits{%%%%2}};
      } {1};
    };
    #CLASS mmcapclass CLOSE;
  } {1};
  #ACTION {^{设定环境变量：action \= \"confirmroom\"|你设定confirmroom为反馈信息}} {
    #VARIABLE {croom} {
      {id} {@eval{&mmrooms[]+1}}
      {name} {$mmname}
      {area} {$mmarea}
      {desc} {@join{{$desclines}{nop}}}
      {src} {$backdirect}
      {visited} {
        {1} {$backdirect}
      }
      {exits} {
        {$backdirect} {%2}
      }
      {roomexits} {$roomexits}
    };
    #IF {%1 == 0} {
      #CLASS mmclass KILL;
      %3
    };
    #ELSEIF {@isSameRoom{{$croom}{$mmrooms[%2]}} == 0} {
      printvar croom;
      printvar mmrooms[%2];
      #MATH {movecount} {$movecount + 1};
      #IF {$movecount > 5} {
        #CLASS mmclass KILL;
        #SHOWME {<faa>无法向%1方向移动，请手动处理。};
      };
      #ELSE {
        #SHOWME {<faa>移动受阻，继续尝试。};
        #DELAY {2} {
          %1;
          look;
          echo {confirmroom}
        }
      };
    };
    #ELSE {
      #CLASS mmclass KILL;
      %3
    };
  };
  #CLASS mmclass CLOSE;
  %1;
  look;
  echo {confirmroom}
};
#NOP {前进并记录房间,%1:移动的方向,%2:出发的房间号};
#ALIAS {mmwalk_forward} {
  #SHOWME {<faa>前进 %1 of %2};
  mmwalk_movefrom {%1} {%2} {mmwalk_forward_do {%1} {%2}}
};
#NOP {前进并记录房间,%1:移动的方向,%2:出发的房间号};
#ALIAS {mmwalk_forward_do} {
  #VARIABLE {backdirect} {@reverseDir{%1}};
  #IF {"$mmcity" == ""} {
    #VARIABLE {mmcity} {$mmarea};
  };
  #LOCAL {eroomid} {@isExistsRoom{{$croom}}};
  #IF {$eroomid != 0} {
    #NOP {房间已经存在则建立连接，用于处理环形路径。建立连通后应重新从%2房间寻找未访问出口或者回退，否则可能会漏房间};
    #VARIABLE {mmrooms[%2][exits][%1]} {$eroomid};
    #LIST {mmrooms[%2][visited]} {add} {%1};
    #VARIABLE {mmrooms[$eroomid][exits][$backdirect]} {%2};
    #IF {@contains{{mmrooms[$eroomid][visited]}{$backdirect}} == 0} {
      #LIST {mmrooms[$eroomid][visited]} {add} {$backdirect};
    };

    #SHOWME {<afa>环形路径%2和$eroomid已在%1方向上建立连接};
    #NOP {从%2触发继续寻找路径};
    mmwalk_backward {$backdirect} {$eroomid}
  };
  #ELSEIF {"$mmarea" != "$mmcity"} {
    #NOP {非预期的区域房间视为边界，此房间不记录但是标识来源房间的出口为9999};
    #VARIABLE {mmrooms[%2][exits][%1]} {9999};
    #LIST {mmrooms[%2][visited]} {add} {%1};
    mmwalk_edge {$backdirect} {%2}
  };
  #ELSE {
    #IF {%2 != 0} {
      #VARIABLE {mmrooms[%2][exits][%1]} {$croom[id]};
      #LIST {mmrooms[%2][visited]} {add} {%1};
    };
    #SHOWME {<aff>新增房间 $croom[name]($croom[id])};
    #VARIABLE {mmrooms[$croom[id]]} {$croom};

    #NOP {寻找一个方向行走};
    #LOCAL {eindex} {@contains{{roomexits}{$backdirect}}};
    #IF {$eindex > 0} {
      #LIST {roomexits} {delete} {$eindex};
    };
    #DELAY {0.5} {
      #IF {&roomexits[] > 0} {
        mmwalk_forward {$roomexits[+1]} {$croom[id]}
      };
      #ELSE {
        mmwalk_backward {$backdirect} {$croom[id]}
      };
    };
  };
};
#NOP {后退返回前一个房间,%1:移动的方向,%2:出发的房间号};
#ALIAS {mmwalk_backward} {
  #SHOWME {<ffa>后退 %1 of %2};
  mmwalk_movefrom {%1} {%2} {mmwalk_backward_do {%1} {%2}}
};
#NOP {后退返回前一个房间,%1:移动的方向,%2:出发的房间号};
#ALIAS {mmwalk_backward_do} {
  #VARIABLE {troom} {$mmrooms[$mmrooms[%2][exits][%1]]};
  #FOREACH {$troom[visited][]} {e} {
    #LOCAL {eindex} {@contains{{roomexits}{$e}}};
    #IF {$eindex > 0} {
      #LIST {roomexits} {delete} {$eindex};
    };
  };
  #DELAY {0.5} {
    #IF {&roomexits[] > 0} {
      #NOP {选择未访问的方向继续};
      mmwalk_forward {$roomexits[+1]} {$troom[id]}
    };
    #ELSEIF {$troom[id] > 1} {
      #NOP {通过来源方向返回};
      mmwalk_backward {$troom[src]} {$troom[id]}
    };
    #ELSE {
      #NOP {绘图结束};
      #SHOWME {<faa>绘制完毕，共计&mmrooms[]个房间};
    };
  }
};
#NOP {边界返回,%1:移动的方向,%2:要返回的房间号,%3:后续指令};
#ALIAS {mmwalk_edge} {
  #SHOWME {<aff>边界 %1 to %2};
  mmwalk_moveto {%1} {%2} {mmwalk_edge_do {%1} {%2}}
};
#NOP {边界返回,%1:移动的方向,%2:要返回的房间};
#ALIAS {mmwalk_edge_do} {
  #VARIABLE {croom} {$mmrooms[%2]};
  #FOREACH {$croom[visited][]} {e} {
    #LOCAL {eindex} {@contains{{roomexits}{$e}}};
    #IF {$eindex > 0} {
      #LIST {roomexits} {delete} {$eindex};
    };
  };
  #DELAY {0.5} {
    #IF {&roomexits[] > 0} {
      #NOP {选择未访问的方向继续};
      mmwalk_forward {$roomexits[+1]} {$croom[id]}
    };
    #ELSEIF {$croom[id] > 1} {
      #NOP {通过来源方向返回};
      mmwalk_backward {$croom[src]} {$croom[id]}
    };
    #ELSE {
      #NOP {绘图结束};
      #SHOWME {<faa>绘制完毕，共计&mmrooms[]个房间};
    };
  }
};
#NOP {是否是同一个房间,%1:房间1,%2:房间2};
#FUNCTION isSameRoom {
  #VARIABLE {vroom1} {%1};
  #VARIABLE {vroom2} {%2};
  #IF {"$vroom1[name]" != "$vroom2[name]"} {
    #RETURN {0};
  };
  #IF {"$vroom1[area]" != "$vroom2[area]"} {
    #RETURN {0};
  };
  #IF {"$vroom1[desc]" != "$vroom2[desc]"} {
    #RETURN {0};
  };
  #IF {&vroom1[roomexits][] != &vroom2[roomexits][]} {
    #RETURN {0};
  };
  #FOREACH {$vroom1[roomexits][]} {e} {
    #IF {@contains{{vroom2[roomexits]}{$e}} == 0} {
      #RETURN {0};
    };
  };
  #RETURN {1};
};
#NOP {房间是否已经存在,%1:房间信息};
#FUNCTION isExistsRoom {
  #FOREACH {$mmrooms[]} {r} {
    #IF {@isSameRoom{{%1}{$r}} == 1} {
      #RETURN {$r[id]};
    };
  };
  #RETURN {0};
};
#NOP {保存已绘制地图,%1:起始编号};
#ALIAS {mmsave} {
  #CLASS mmclass KILL;
  #CONFIG {LOG} {PLAIN};
  #FORMAT ts %t {%Y%m%d%H%M%S};
  #LOCAL {mmfile} {map_$ts.tin};
  #FOREACH {$mmrooms[]} {r} {
    #NOP {房间描述};
    #LOCAL {rid} {$r[id]};
    #IF {$rid != 0 && $rid != 9999} {
      #MATH {rid} {$rid + %1};
    };
    #LOCAL {rline} {R \x7B@padLeft{{$rid}{5}}\x7D \x7B0\x7D \x7B\x7D \x7B$r[name]\x7D \x7B \x7D \x7B$r[desc]\x7D \x7B$r[area]\x7D \x7B\x7D \x7B\x7D \x7B\x7D \x7B1.000\x7D \x7B\x7D};
    #LINE LOG {$mmfile} {$rline};
    #NOP {出口描述};
    #FOREACH {*r[exits][]} {e} {
      #LOCAL {dir} {$mapdir[$e]};
      #IF {"$dir" == ""} {
        #LOCAL {dir} {63};
      };
      #LOCAL {rid} {$r[exits][$e]};
      #IF {$rid != 0 && $rid != 9999} {
        #MATH {rid} {$rid + %1};
      };
      #LOCAL {eline} {E \x7B@padLeft{{$rid}{5}}\x7D \x7B$e\x7D \x7B$e\x7D \x7B$dir\x7D \x7B0\x7D \x7B \x7D \x7B1.000\x7D \x7B \x7D \x7B0.00\x7D};
      #LINE LOG {$mmfile} {$eline};
    };
    #LINE LOG {$mmfile} {\r};
    #MATH {rid} {$rid + 1};
  };
  #CONFIG {LOG} {HTML};
  #SHOWME {<faa>地图数据已保存至临时文件<afa>$mmfile};
};
#SHOWME {<fac>@padRight{{地图制作}{12}}<fac> <cfa>模块加载完毕<cfa>};