#NOP {辟邪剑谱,%1:后续指令};
#ACTION {^你跳窗上瘾了吗} {
	#CLASS questclass KILL;
	stopwalk;
	#DELAY {2} {
		questdelay {辟邪剑谱} {0} {10800};
		loc {jobprepare};
	};
};
#ALIAS {goquest_bxjp} {
	#VARIABLE {questmodule} {辟邪剑谱};
  #SWITCH {$questlist[$questmodule][laststep]} {
    #CASE {0} {
			gotonpc {林震南} {bxjp_asklin {%1}};
    };
    #CASE {1} {
			gotodo {华山} {悬崖下} {bxjp_listen {%1}}
    };
    #DEFAULT {
      questfail {$questmodule};
      %1;
    };
  };
};
#ALIAS {bxjp_asklin} {
	#CLASS questclass KILL;
	#CLASS questclass OPEN;
	#ACTION {^你向林震南打听有关『福威镖局』的消息。} {
		dohalt {
			ask lin zhennan about 远图公
		};
	};
	#ACTION {^你向林震南打听有关『远图公』的消息。} {
		dohalt {
			ask lin zhennan about 辟邪剑法
		};
	};
	#ACTION {^你向林震南打听有关『辟邪剑法』的消息。} {
		#CLASS questresponseclass OPEN;
		#ACTION {^林震南不悦地说道：「我不是都告诉你了吗？我的剑法不及先祖} {
			#CLASS questresponseclass KILL;
			dohalt {
				ask lin zhennan about 铜钱
			};
		};
		#ACTION {^林震南顿一顿，接着低声说道：「先祖去世前，曾给家父留下%*} {
			#CLASS questresponseclass KILL;
			#CLASS questclass KILL;
			#VARIABLE {env[pxjpwd]} {@ctd{%%%1}};
			dohalt {
				gotodo {福州城} {密室房梁} {bxjp_fakejiasha {%1}}
			};
		};
		#CLASS questresponseclass CLOSE;
	};
	#ACTION {^你向林震南打听有关『铜钱』的消息。} {
		#CLASS questresponseclass OPEN;
		#ACTION {^林震南低声说：「先祖去世前，曾给家父留下%*个铜钱} {
			#CLASS questresponseclass KILL;
			#CLASS questclass KILL;
			#VARIABLE {env[pxjpwd]} {@ctd{%%%1}};
			dohalt {
				gotodo {福州城} {密室房梁} {bxjp_fakejiasha {%1}}
			};
		};
		#CLASS questresponseclass CLOSE;
	};
	#CLASS questclass CLOSE;
	ask lin zhennan about 福威镖局;
};
#NOP {拿假袈裟};
#ALIAS {bxjp_fakejiasha} {
	#CLASS questclass KILL;
	#CLASS questclass OPEN;
	#ACTION {^毒蛇「啪」的一声倒在地上，挣扎着抽动了几下就死了。} {
		dohalt {move jia sha};
	};
	#ACTION {^这里没有这个人。} {
		dohalt {move jia sha};
	};
	#ACTION {^你轻轻地挪动袈裟，抖起一些尘土...，突然从袈裟的窟窿中掉出一件比较小的袈裟到你怀中。} {
		#CLASS questclass KILL;
		bxjp_waitfaint {%1};
	};
	#ACTION {^你轻轻地挪动袈裟，抖起一些尘土，呛得你直打喷嚏} {
		#CLASS questclass KILL;
		questdelay {$questmodule} {} {7200};
		dohalt {%1};
	};
	#CLASS questclass CLOSE;
	kill du she;
};
#NOP {等晕};
#ALIAS {bxjp_waitfaint} {
	#VARIABLE {robflag} {0};
	#CLASS questclass KILL;
	#CLASS questclass OPEN;
	#ACTION {^你这时又觉一阵头晕，当即吸了几口气，辨明方向，径向那向阳巷老宅走去} {
		#VARIABLE {robflag} {1};
		#ACTION {^一股暖流发自丹田流向全身，慢慢地你又恢复了知觉} {
			#CLASS questclass KILL;
			questupdate {$questmodule} {1};
			questdelay {$questmodule} {} {7200};
			hp;
			loc {doheal {%1}};
		};
	};
	#ACTION {^你这等临敌应变的奇技怪招，却跟第一流高手还差着老大一截} {
		#VARIABLE {robflag} {2};
	};
	#ACTION {^{设定环境变量：action \= \"checkrob\"|你设定checkrob为反馈信息}} {
		#VARIABLE {idle} {0};
		#IF {$robflag == 2} {
			startfight;
			kill pu chen;
			kill jiang shatian;
		};
		#ELSEIF {$robflag == 0} {
			#DELAY {1} {
				#IF {"$room" == "向阳老宅"} {
					w;
					e
				};
				#ELSE {
					break men;
					n;
				};
				echo {checkrob};
			}
		};
	};
	#ACTION {^卜沉「啪」的一声倒在地上，挣扎着抽动了几下就死了} {
		kill jiang shatian;
	};
	#ACTION {^江沙天「啪」的一声倒在地上，挣扎着抽动了几下就死了} {
		kill pu chen;
	};
	#ACTION {^这里没有这个人。} {
		#VARIABLE {robflag} {0};
		startfight;
		doheal {startfull {echo {checkrob}}};
	};
	#CLASS questclass CLOSE;
	gotodo {福州城} {老宅后院} {echo {checkrob}};
};
#ALIAS {bxjp_listen} {
	#CLASS questclass KILL;
	#CLASS questclass OPEN;
	#ACTION {^亥时低语在崖上} {
		#VARIABLE {idle} {0};
		#DELAY {6} {
			listen
		};
	};
	#ACTION {^女子%*道} {
		#VARIABLE {idle} {0};
	};
	#ACTION {^便在此时，只听得窗子呀的一声打开} {
		pray pearl;
		hook jia sha;
	};
	#ACTION {^眼看那袈裟从身旁飘过，你伸手一抓，差了数尺，没能抓到} {
		hook jia sha;
	};
	#ACTION {^当真运气不好，竟没有将那袈裟勾到，落入了天声峡下的万仞深渊之中} {
		#CLASS questclass KILL;
		questfail {$questmodule};
		dohalt {%1};
	};

	#ACTION {^你右手搭在崖上，左脚拚命向外一勾，只觉脚尖似乎碰到了袈裟，立即缩回，当真幸运得紧，竟将那袈裟勾到了} {
		#CLASS questclass KILL;
		questsuccess {$questmodule};
		dohalt {%1};
	};
	#CLASS questclass CLOSE;
	listen;
};