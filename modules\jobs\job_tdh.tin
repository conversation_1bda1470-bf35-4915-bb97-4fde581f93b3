#NOP {天地会任务模块};
#ALIAS {jobgo_tdh} {
  checkrequest {on_tdh_before_go {gotodo {扬州城} {大虹桥} {startfull {jobask_tdh}}}}
};
#ALIAS {jobask_tdh} {
  dohalt {on_tdh_before_ask {gotodo {扬州城} {小金山} {jobask_tdh_ask}}}
};
#ALIAS {jobask_tdh_ask} {
  #VARIABLE {askresult} {0};
  #VARIABLE {jobnpc_gm} {};
  #VARIABLE {jobnpc_gm_id} {};
  #CLASS jobrequestclass OPEN;
  #ACTION {^你向李式开打听有关『job』的消息。} {
    #CLASS jobresponseclass OPEN;
    #ACTION {^李式开白了你一眼，说：你无聊不无聊啊} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      #IF {"$hp[shen]" == "戾气" || $hp[shen_num] < 10000} {
        dohalt {gozshen {10000} {jobgo_tdh}}
      };
      #ELSE {
        dohalt {join_tdh {jobask_tdh_ask}};
      };
    };
    #ACTION {^李式开说道：「%*你貌似并无行侠仗义之心} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {on_tdh_wait};
    };
    #ACTION {^李式开在你的耳边悄声说道：你马上去%*联络一个会里的兄弟。} {
      #VARIABLE {joblocation} {%%%1};
    };
    #ACTION {^李式开在你的耳边悄声说道：他的名字叫%*，你路上小心。} {
      #VARIABLE {askresult} {1};
      #VARIABLE {jobnpc_gm} {%%%1};
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {parsejoblocation {$joblocation} {jobdo_tdh_gm} {
        joblog {无法解析地址【$joblocation】。};
        jobfangqi_tdh
      } {2}}
    };
    #ACTION {^李式开在你的耳边悄声说道：速去} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {jobfangqi_tdh};
    };
    #ACTION {^李式开说道：「你还在做任务呢} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {on_tdh_wait};
    };
    #ACTION {^李式开说道：「你刚完成任务，还是去休息会吧。」} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {jobprepare};
    };
    #ACTION {^李式开说道：「听说陈总舵主有事找你，你还是先去找总舵主询问} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {jobprepare};
    };
    #CLASS jobresponseclass CLOSE;
  };
  #CLASS jobrequestclass CLOSE;
  #VARIABLE {jobstart_ts} {0};
  jobclear_tdh;
  time;
  cond;
  ask li shikai about job;
};
#NOP {加入天地会,%1:后续指令};
#ALIAS {join_tdh} {
  #CLASS joinclass KILL;
  #CLASS joinclass OPEN;
  #ACTION {^你向李式开打听有关『万云龙』的消息} {
    dohalt {
      ask li shikai about 天地会
    };
  };
  #ACTION {^你向李式开打听有关『天地会』的消息} {
    dohalt {
      ask li shikai about 反清复明
    };
  };
  #ACTION {^你向李式开打听有关『反清复明』的消息} {
    dohalt {
      %1;
    };
  };
  #CLASS joinclass CLOSE;
  ask li shikai about 万云龙
};
#NOP {移除天地会最好不做的房间列表};
#ALIAS {execudegmrooms} {
  #VARIABLE {temproomlist} {$jobroomlist};
  #LIST {jobroomlist} {clear};
  #LIST {limitrooms} {create} {$common[limitedzone][$jobcity][range]};
  #FOREACH {$temproomlist[]} {r} {
    #IF {@contains{{limitrooms}{$r}} > 0} {
      #CONTINUE;
    };
    
    #LIST {jobroomlist} {add} {$r};
  };
};
#ALIAS {jobdo_tdh_gm} {
  #VARIABLE {jobstart_ts} {@now{}};
  #NOP {距离太远的房间当有捷径或者技能已满的情况下才去};
  #LOCAL {rejectflag} {0};
  #IF {"$jobcity" == "神龙岛" && "$hp[party]" != "神龙教" && "@getLingwuSkill{2}" != ""} {
    #LOCAL {rejectflag} {1};
  };
  #IF {"$jobcity" == "曼佗罗山庄" && "$hp[party]" != "姑苏慕容" && "@getLingwuSkill{2}" != ""} {
    #LOCAL {rejectflag} {1};
  };
  #IF {$rejectflag == 1} {
    joblog {未订阅区域【$joblocation】，直接放弃。};
    jobfangqi_tdh
  };
  #ELSE {
    #IF {"$jobcity" == "绝情谷" || "$jobcity" == "襄阳城" || "$jobcity" == "桃源县"} {
      execudegmrooms
    };
    taskbegin;
    joblog {寻找位于【$joblocation】的接引人【$jobnpc_gm】。};
    jobnextroom {tdh_checkgm}
  };
};
#ALIAS {tdh_checkgm} {
  #VARIABLE {joblocation2} {};
  #VARIABLE {jobnpc_gm_id} {};
  #VARIABLE {jobnpc_hero} {};
  #VARIABLE {jobnpc_hero_id} {};
  #VARIABLE {jobnpc_killers} {};
  #VARIABLE {jobnpc_superkillers} {};
  #VARIABLE {gm_roomid} {};
  #CLASS jobcheckclass OPEN;
  #ACTION {$jobnpc_gm(%*)} {
    #VARIABLE {jobnpc_gm_id} {@lower{%%1}};
  };
  #ACTION {^{设定环境变量：action \= \"checkhgm\"|你设定checkhgm为反馈信息}} {
    resonate {checkhgm};
    #VARIABLE {echots} {0};
    #VARIABLE {idle} {0};
    #IF {"$jobnpc_gm_id" == ""} {
      #CLASS jobcheckclass KILL;
      runwait {jobnextroom {tdh_checkgm}}
    };
    #ELSE {
      #VARIABLE {gm_roomid} {$aimroomid};
      time;
      dohalt {
        ask $jobnpc_gm_id about 反清复明;
      };
    };
  };
  #NOP {问了反清复明后NPC可能移动了，问切口人不在，这个时候重新找};
  #ACTION {^这里没有这个人} {
    #CLASS jobresponseclass KILL;
    #CLASS jobcheckclass KILL;
    joblog {【$jobnpc_gm】走失，重新寻找。};
    dohalt {parsejoblocation {$joblocation} {jobdo_tdh_gm} {joblog {无法解析地址【$joblocation】。};jobfangqi_tdh} {2}}
  };
  #ACTION {^你向$jobnpc_gm打听有关『反清复明』的消息。} {
    #CLASS jobresponseclass OPEN;
    #ACTION {^$jobnpc_gm说道：「{阁下不妨报个字号|那是自然}} {
      #CLASS jobresponseclass KILL;
      dohalt {
        time;
        ask $jobnpc_gm_id about 切口;
      }; 
    };
    #CLASS jobresponseclass CLOSE;
    #VARIABLE {jobnpc_found} {1};
    #IF {"$jobroom" != "$room"} {
      joblog {预期在房间【$jobroom】实际在房间【$room】找到接引人【$jobnpc_gm】。};
    };
  };
  #ACTION {^你向$jobnpc_gm打听有关『切口』的消息。} {
    #CLASS jobresponseclass OPEN;
    #ACTION {^$jobnpc_gm说道：「} {
      #CLASS jobresponseclass KILL;
      dohalt {qiekou} 
    };
    #CLASS jobresponseclass CLOSE;
  };
  #ACTION {^$jobnpc_gm说道：「原来大家是自己人} {
    #CLASS jobresponseclass OPEN;
    #ACTION {^$jobnpc_gm说道：「在%*有一位叫做%*的} {
      #CLASS jobresponseclass KILL;
      #CLASS jobcheckclass KILL;
      #VARIABLE {jobnpc_hero} {%%%2};
      #VARIABLE {joblocation2} {%%%1};
      parsejoblocation {$joblocation2} {jobdo_tdh_hero} {
        joblog {无法解析地址【$joblocation2】。};
        jobfangqi_tdh;
      } {2};
    };
    #CLASS jobresponseclass CLOSE;
  };
  #CLASS jobcheckclass CLOSE;
  ensure {look} {checkhgm}
};
#ALIAS {execudeherorooms} {
  #VARIABLE {temproomlist} {$jobroomlist};
  #LIST {jobroomlist} {clear};
  #LIST {limitrooms} {create} {$common[tdhlimitedzone][$jobcity][range]};
  #FOREACH {$temproomlist[]} {r} {
    #IF {@contains{{limitrooms}{$r}} > 0} {
      #CONTINUE;
    };
    
    #LIST {jobroomlist} {add} {$r};
  };
};
#ALIAS {jobdo_tdh_hero} {
  #NOP {这俩区域NPC可能带不出来，直接排除，桃源县的地图不适合左右横条};
  #IF {"$jobcity" == "绝情谷" || "$jobcity" == "襄阳城" || "$jobcity" == "桃源县"} {
    execudeherorooms
  };
  pfm_buff_normal;
  joblog {寻找位于【$joblocation2】的好汉【$jobnpc_hero】。};
  jobnextroom {tdh_checkhero}
};
#NOP {寻找绿林好汉失败,%1:重复搜索标志};
#ALIAS {jobfail_tdh_hero} {
  #LOCAL {iter} {@eval{@eval{%1} + 1}};
  #LOCAL {wanderroom} {$common[wanderwheres][$jobcity$jobroom]};
  #NOP {针叶林不扩散搜索，直接漫游};
  #IF {$iter == 1 && "$wanderroom" != ""} {
    #LOCAL {iter} {2}
  };
  #IF {$iter == 1} {
    joblog {未能找到位于【$joblocation2】的好汉【$jobnpc_hero】，扩大范围搜索。};
    parsejoblocation {$joblocation2} {jobnextroom {tdh_checkhero {$iter}} {jobfail_tdh_hero {$iter}}} {jobfail_tdh_hero {$iter}} {5};
  };
  #ELSEIF {$iter == 2 && "$wanderroom" != ""} {
    joblog {还是未能找到位于【$joblocation2】的好汉【$jobnpc_hero】，开始漫游搜索。};
    loc {gotoroom {$wanderroom[roomid]} {jobwander_tdh {$wanderroom[roomname]} {$wanderroom[range]}}}
  };
  #ELSE {
    joblog {终究未能找到位于【$joblocation2】的好汉【$jobnpc_hero】。};
    jobfangqi_tdh;
  };
};
#NOP {%1:房间名称,%2:步数};
#ALIAS {jobwander_tdh} {
  job_wander {tdh_checkhero {} {jobwander_tdh {%1} {@eval{%2 - 1}}}} {jobfail_tdh_hero {3}} {%1} {%2}
};
#NOP {检查绿林好汉,%1:重复检查标志,%2:额外操作};
#ALIAS {tdh_checkhero} {
  #VARIABLE {hero_roomid} {};
  #VARIABLE {hero_lastroom} {};
  #VARIABLE {jobtdh_wanderroom} {};
  #VARIABLE {finishflag} {0};
  #VARIABLE {checkcount} {0};
  #VARIABLE {jobnpc_count} {0};
  #VARIABLE {jobnpc_supercount} {0};
  #VARIABLE {jobnpc_tdhmiss} {0};
  #VARIABLE {jobfight_ts} {@now{}};
  #VARIABLE {jobstep_tdh} {0};
  #CLASS jobcheckclass KILL;
  #CLASS jobcheckresponseclass KILL;
  #CLASS jobcheckclass OPEN;
  #ACTION {$jobnpc_hero(%*)} {
    #VARIABLE {jobnpc_hero_id} {@lower{%%1}};
  };
  #ACTION {^{设定环境变量：action \= \"checkhero\"|你设定checkhero为反馈信息}} {
    resonate {checkhero};
    #VARIABLE {echots} {0};
    #VARIABLE {idle} {0};
    #IF {"$jobnpc_hero_id" == ""} {
      #CLASS jobcheckclass KILL;
      #IF {"%2" != ""} {
        %2
      };
      #ELSE {
        runwait {jobnextroom {tdh_checkhero {%1}} {jobfail_tdh_hero {%1}}}
      };
    };
    #ELSE {
      checkdanger {ask $jobnpc_hero_id about 反清复明};
    };
  };
  #ACTION {^你向$jobnpc_hero打听有关『反清复明』的消息。} {
    #CLASS jobresponseclass OPEN;
    #ACTION {^$jobnpc_hero对你道：多谢阁下援手，在下这里先谢过了} {
      #CLASS jobresponseclass KILL;
      #CLASS jobcheckclass KILL;
      #VARIABLE {hero_roomid} {$aimroomid};
      #VARIABLE {jobfight_ts} {@now{}};
      #VARIABLE {jobtdh_wanderroom} {@getTdhWanderRoom{}};
      jobdo_tdh_fight
    };
    #ACTION {^$jobnpc_hero挺了挺胸，神气地对你说：那当然啦。} {
      #CLASS jobresponseclass KILL;
      #CLASS jobcheckclass KILL;
      #VARIABLE {jobtdh_wanderroom} {@getTdhWanderRoom{}};
      jobdo_tdh_fight
    };
    #CLASS jobresponseclass CLOSE;
    #IF {"$jobroom" != "$room"} {
      joblog {预期在房间【$jobroom】实际在房间【$room】找到好汉【$jobnpc_hero】。};
    };
  };
  #CLASS jobcheckclass CLOSE;
  ensure {
    pfm_wuxing;
    pfm_buff_normal;
    look
  } {checkhero}
};
#NOP {获取左右横跳的房间，一般采用地图标志房间，部分特殊场景自定义如黑木崖};
#NOP {如果弟子和好汉在相同区域那么在获取横跳房间和去横跳房间应避免提前和弟子见面};
#FUNCTION getTdhWanderRoom {
  #LOCAL {wanderroom} {$common[tdhwanderroom][$city]};
  #NOP {是否在设定的分区};
  #IF {"$wanderroom[partition]" != ""} {
    #FOREACH {*wanderroom[partition][]} {p} {
      #LIST {tdwandrooms} {create} {$wanderroom[partition][$p][rooms]};
      #LIST {tdwandplaces} {create} {$wanderroom[partition][$p][places]};
      #NOP {从待选列表中排除天地会npc所在的房间};
      #LOCAL {gmindex} {@contains{{tdwandplaces}{$gm_roomid}}};
      #IF {$gmindex > 0} {
        #LIST {tdwandplaces} {delete} {$gmindex};
      };
      #IF {@contains{{tdwandrooms}{$roomid}} > 0} {
        #IF {@contains{{tdwandplaces}{$roomid}} > 0} {
          #RETURN {$roomid};
        };
        #NOP {找一个距离最近的房间};
        #VARIABLE {targetplaces} {};
        #FOREACH {$tdwandplaces[]} {r} {
          #VARIABLE {targetplaces[$r]} {
            {steps} {@getPathLength{{$roomid}{$r}}}
          };
        };
        #VARIABLE {sortedplaces} {@sort{{targetplaces}{steps}}};
        #RETURN {$sortedplaces[+1][sidx]};
      };
    };
  };

  #NOP {是否在默认分区};
  #IF {"$wanderroom[default]" != ""} {
    #LIST {tdwandplaces} {create} {$wanderroom[default]};
    #IF {@contains{{tdwandplaces}{$roomid}} > 0} {
      #RETURN {$roomid};
    };
    #VARIABLE {targetplaces} {};
    #FOREACH {$tdwandplaces[]} {r} {
      #VARIABLE {targetplaces[$r]} {
        {steps} {@getPathLength{{$roomid}{$r}}}
      };
    };
    #VARIABLE {sortedplaces} {@sort{{targetplaces}{steps}}};
    #RETURN {$sortedplaces[+1][sidx]};
  };
  
  #NOP {直接返回区域房间};
  #VARIABLE {result} {$cities[$city][cityroomid]};
};
#NOP {天地会战斗全程，因为侍卫出现神出鬼没，无论左右横条，还是回去交任务统一这里触发，杀完以后根据任务阶段(jobstep_tdh)决定执行下一步操作};
#NOP {天地会战斗任务阶段,0-首次侍卫，1-去横跳地点途中,2-左右横跳,3-去完成地点途中,4-完成地点等待};
#ALIAS {jobdo_tdh_fight} {
  #VARIABLE {errorcount} {0};
  #VARIABLE {movefail} {0};
  #CLASS jobdoclass KILL;
  #CLASS jobdoclass OPEN;
  #NOP {记录好汉最后出现的房间，以便于在回到第一个NPC后好汉丢失回溯寻找};
  #ACTION {^$jobnpc_hero紧跟着你快步走了过来} {
    #VARIABLE {hero_lastroom} {$room};
    #SHOWME {<faa>好汉最后跟进房间为$hero_lastroom};
  };
  #ACTION {^这个方向没有出路} {
    #MATH {errorcount} {$errorcount + 1};
  } {1};
  #ACTION {^糟了！$jobnpc_hero死亡，任务失败！} {
    #CLASS jobdoclass KILL;
    #CLASS jobkillclass KILL;
    #CLASS jobfightclass KILL;
    dohalt {jobfangqi_tdh};
  };
  #ACTION {^%*的太阳穴高高隆起，一看就知道是大内高手} {
    #VARIABLE {jobnpc_superkillers[%%1]} {none};
  };
  #ACTION {^%*{怒|叫|喊}道：%*说罢便与{你|$jobnpc_hero|$hp[name]等}} {
    #NOP {天地会侍卫刷新时玩家直接busy两秒，直接停止快速行走进入战斗状态，战斗完成后战斗完成echo checkshiwei由这里触发进一步的逻辑};
    #VARIABLE {jobnpc_killers[%%1]} {none};
    stopwalk;
    #IF {$jobstep_tdh == 1 || $jobstep_tdh == 3 || $jobstep_tdh == 4} {
      #DELAY {0.5} {jobkill_tdh}
    };
  };
  #NOP {回到第一个NPC时如果侍卫未杀够会出现该信息并陆续刷侍卫，且最多一次刷新两个，一个显示上面触发器的信息，一个显示大内高手或者杀死你的信息};
  #ACTION {^只听$jobnpc_gm向远处大喝一声：“狗贼，鬼鬼祟祟跟到这里，还不出来受死！”喊声中气十足} {
    #VARIABLE {jobnpc_tdhmiss} {1};
  };
  #ACTION {^看起来%*想杀死你} {
    #IF {"$jobnpc_killers[%%1]" == ""} {
      #VARIABLE {jobnpc_killers[%%1]} {none};
    };
  };
  #ACTION {^你的动作还没有完成，不能移动} {
    #VARIABLE {movefail} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkshiwei\"|你设定checkshiwei为反馈信息}} {
    resonate {checkshiwei};
    #VARIABLE {echots} {0};
    #VARIABLE {idle} {0};
    
    #IF {$movefail == 0} {
      #MATH {checkcount} {$checkcount + 1};
    };
    #VARIABLE {shiwei} {@getNextShiwei{}};
    #NOP {发现侍卫进入杀侍卫流程};
    #IF {"$shiwei" != ""} {
      jobkill_tdh
    };
    #ELSEIF {@elapsed{$jobfight_ts} > 300} {
      #NOP {加一个超时，防止异常情况导致卡死，无论如何也不应该超过5分钟};
      #CLASS jobdoclass KILL;
      #CLASS jobkillclass KILL;
      #CLASS jobfinishclass KILL;
      loc {jobfangqi_tdh}
    };
    #ELSEIF {$finishflag == 1} {
      #CLASS jobdoclass KILL;
      #CLASS jobkillclass KILL;
      #CLASS jobfinishclass KILL;
      on_tdh_finish;
      taskgain {@ctd{$jobreward_exp}};
      taskend;
      joblog {成功完成，获得$jobreward_exp点经验$jobreward_pot点潜能，耗时@elapsed{$jobstart_ts}秒。};
      jobclear_tdh;
      jobprepare
    };
    #ELSEIF {$jobstep_tdh == 0} {
      #NOP {对话完成原地等待检查五次，没有侍卫就进入阶段1};
      #IF {$checkcount < 5} {
        #DELAY {1} {echo {checkshiwei}}
      };
      #ELSE {
        #CLASS jobkillclass KILL;
        #CLASS jobdoclass KILL;
        #VARIABLE {jobstep_tdh} {1};
        jobdo_tdh_fight;
      };
    };
    #ELSEIF {$jobstep_tdh == 2} {
      #NOP {已达安全横跳地点，继续横跳，如果以杀至14个侍卫或者检查次数足够则认为完成};
      #IF {$errorcount >= 6} {
        #NOP {如果连续走错路，那么需要重新规划路径};
        #DELAY {1} {loc {jobmakepath_tdh {joblinger_tdh}}}
      };
      #ELSEIF {$jobnpc_count >= 14 || $jobnpc_supercount >= 8 || ($jobnpc_count >= 10 && $checkcount >= 20) || ($jobnpc_count < 10 && $checkcount >= 30)} {
        #CLASS jobkillclass KILL;
        #CLASS jobdoclass KILL;
        #VARIABLE {jobstep_tdh} {3};
        joblog {共计杀死【$jobnpc_count】个御前侍卫，其中【$jobnpc_supercount】个大内侍卫，耗时【@elapsed{$jobfight_ts}】秒。};
        jobdo_tdh_fight
      };
      #ELSE {
        #DELAY {0.1} {joblinger_tdh}
      };
    };
    #ELSE {
      #CLASS jobkillclass KILL;
      #CLASS jobdoclass KILL;
      jobdo_tdh_fight
    };
  };
  #CLASS jobdoclass CLOSE;
  #NOP {0-对话完成会很快刷新一个侍卫，这里直接等待发送echo checkshiwei，要有判定次数。};
  #NOP {1-去横跳途中，继续行走};
  #NOP {2-已到达安全横跳地点并完成规划路径，开始横跳};
  #NOP {3-完成任务途中，继续行走吧};
  #SWITCH {$jobstep_tdh} {
    #CASE {0} {echo {checkshiwei} {2}};
    #CASE {1} {time;loc {gotoroom {$jobtdh_wanderroom} {jobmakepath_tdh {joblinger_tdh}}}};
    #CASE {2} {joblinger_tdh};
    #CASE {3} {time;loc {jobfinish_tdh}};
  };
};
#NOP {击杀侍卫战斗过程};
#ALIAS {jobkill_tdh} {
  #CLASS jobkillclass KILL;
  #CLASS jobkillclass OPEN;
  #ACTION {御前侍卫 %*(%*)} {
    #IF {"$jobnpc_killers[%%1]" == "none"} {
      #VARIABLE {jobnpc_killers[%%1]} {@lower{%%2}};
    };
  } {1};
  #ACTION {^{设定环境变量：action \= \"checkconfirm\"|你设定checkconfirm为反馈信息}} {
    resonate {checkconfirm};
    #VARIABLE {echots} {0};
    printvar jobnpc_killers;
    #VARIABLE {shiwei} {@getNextShiwei{}};
    #IF {"$roomthings[$shiwei]" == ""} {
      #NOP {找不到刷新的侍卫一般仅在横跳阶段出现，这里可以处理的更谨慎一些};
      #IF {$jobstep_tdh == 2} {
        #DELAY {0.5} {jobonstep_tdh {echo {checkmissingshiwei} {2}}}
      };
      #ELSE {
        #NOP {非横跳阶段直接丢掉继续走起};
        #UNVARIABLE {jobnpc_killers[$shiwei]};
        #CLASS jobkillclass KILL;
        jobdo_tdh_fight
      };
    };
    #ELSEIF {"$jobnpc_killers[$shiwei]" == "none"} {
      #NOP {confirm后未找到表明该NPC不是侍卫，直接放弃};
      #UNVARIABLE {jobnpc_killers[$shiwei]};
      #DELAY {0.5} {echo {checkover}}
    };
    #ELSE {
      createpfm {$conf[pfm][scene][tdh]} {1} {$jobnpc_killers[$shiwei]};
      kill $jobnpc_killers[$shiwei];
      startfight;
      autopfm
    };
  };
  #ACTION {^这里不准战斗} {
    #VARIABLE {passedrooms} {2};
    #UNVARIABLE {jobnpc_killers[$shiwei]};
    #DELAY {0.5} {echo {checkover}}
  };
  #ACTION {^%*{转眼间走的无影无踪|「啪」的一声倒在地上}} {
    #IF {"$jobnpc_killers[%%1]" != ""} {
      #VARIABLE {idle} {0};
      #NOP {因为侍卫可能一次刷多个，createpfm并未指定目标(未防止意外不指定目标)，所以击杀顺序可能错误};
      #MATH {jobnpc_count} {$jobnpc_count + 1};
      #UNVARIABLE {jobnpc_killers[%%1]};
      #IF {"$jobnpc_superkillers[%%1]" != ""} {
        #MATH {jobnpc_supercount} {$jobnpc_supercount + 1};
        #UNVARIABLE {jobnpc_superkillers[%%1]};
      };
      #NOP {如果死亡是当前目标shiwei};
      #IF {"%%1" == "$shiwei"} {
        #IF {$jobstep_tdh == 0} {
          #VARIABLE {jobstep_tdh} {1};
        };
        #DELAY {1} {
          dohalt {
            #IF {$jobstep_tdh != 4 && "$jobnpc_killers" == ""} {
              get silver from corpse;
              get gold from corpse;
              get zhuanji;
              yun jingli;
              yun qi
            };
            #IF {$jobstep_tdh < 4 && $hp[neili] < 7000 && $hp[neili] < @eval{$hp[neili_max]*2/3}} {
              startfull {
                pfm_wuxing;
                pfm_buff_normal;
                id here;
                echo {checkover}
              };
            };
            #ELSE {
              #DELAY {0.5} {
                ensure {pfm_wuxing;pfm_buff_normal;id here} {checkover}
              }
            };
          };
        };
      };
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkover\"|你设定checkover为反馈信息}} {
    resonate {checkover};
    #VARIABLE {echots} {0};
    #VARIABLE {checkcount} {0};
    #IF {$finishflag == 1} {
      jobfinish_tdh_ask
    };
    #ELSEIF {"$jobnpc_killers" != ""} {
      #NOP {还有侍卫，继续战斗};
      ensure {look;id here} {checkconfirm}
    };
    #ELSEIF {"$roomthings[$jobnpc_hero]" == ""} {
      #NOP {找不到好汉一般仅在横跳阶段出现，这里可以处理的更谨慎一些};
      #IF {$jobstep_tdh == 2} {
        #DELAY {0.5} {jobonstep_tdh {echo {checkmisshero} {2}}}
      };
      #ELSE {
        #NOP {非横跳阶段丢失好汉直接放弃};
        #CLASS jobkillclass KILL;
        jobfangqi_tdh
      };
    };
    #ELSE {
      #NOP {杀完继续};
      #CLASS jobkillclass KILL;
      dohalt {jobdo_tdh_fight}
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkmissingshiwei\"|你设定checkmissingshiwei为反馈信息}} {
    resonate {checkmissingshiwei};
    #VARIABLE {echots} {0};
    #IF {"$roomthings[$shiwei]" != ""} {
      createpfm {$conf[pfm][scene][tdh]};
      kill $jobnpc_killers[$shiwei];
      startfight;
      autopfm;
    };
    #ELSE {
      #UNVARIABLE {jobnpc_killers[$shiwei]};
      echo {checkover}
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkmisshero\"|你设定checkmisshero为反馈信息}} {
    resonate {checkmisshero};
    #VARIABLE {echots} {0};
    #IF {"$roomthings[$jobnpc_hero]" != ""} {
      echo {checkover}
    };
    #ELSE {
      #CLASS jobkillclass KILL;
      jobfangqi_tdh
    };
  };
  #CLASS jobkillclass CLOSE;
  ensure {look;id here} {checkconfirm}
};
#NOP {制定左右横跳的路径,%1:后续指令};
#ALIAS {jobmakepath_tdh} {
  #NOP {组织天地会巡逻房间信息};
  #VARIABLE {tdhroom[origin]} {
    {id} {$roomid}
    {name} {$room}
    {ways} {$roomways}
  };
  #LOCAL {temproom} {@getSafeExit{$roomid}};
  #VARIABLE {tdhroom[dest]} {$temproom};
  #IF {$temproom == $roomid} {
    #VARIABLE {tdhroom[pathto]} {$roomexits[+@rnd{{1}{&roomexits[]}}]}
  };
  #ELSE {
    #VARIABLE {tdhroom[pathto]} {@getWalkPath{{$roomid}{$temproom}}};
  };
  #NOP {处理一下特殊情况};
  #LIST {templist} {create} {$tdhroom[pathto]};
  #IF {@startWiths{{$templist[+1]}{matrix_}} > 0} {
    #IF {&templist[] == 1} {
      log {房间$roomid未能找到安全出口} {map};
    };
    #ELSE {
      #VARIABLE {tdhroom[pathto]} {$templist[+&templist[]]}
    };
  };
  #VARIABLE {tdhroom[pathback]} {@reversePath{{$tdhroom[pathto]}}};

  #NOP {处理river_halt情况};
  #REGEXP {$tdhroom[pathto]} {river_halt} {#VARIABLE {needhalt} {1}};
  #REPLACE {tdhroom[pathto]} {river_halt} {laugh};
  #REGEXP {$tdhroom[pathback]} {river_halt} {#VARIABLE {needhalt} {1}};
  #REPLACE {tdhroom[pathback]} {river_halt} {cry};

  #VARIABLE {jobstep_tdh} {2};
  #IF {"%1" != ""} {
    #DELAY {0.5} {%1}
  };
};
#NOP {天地会左右横跳};
#ALIAS {joblinger_tdh} {
  #VARIABLE {movefail} {0};
  #IF {@isStartRoom{} == 1} {
    $tdhroom[pathto];
    $tdhroom[pathback]
  };
  #ELSE {
    $tdhroom[pathback];
    $tdhroom[pathto]
  };
  echo {checkshiwei} {2};
};
#NOP {移动一步并观察,%1:后续指令};
#ALIAS {jobonstep_tdh} {
  #IF {@isStartRoom{} == 1} {
    $tdhroom[pathto];
  };
  #ELSE {
    $tdhroom[pathback];
  };
  look;
  id here;
  %1
};
#NOP {获取要杀的侍卫};
#FUNCTION getNextShiwei {
  #IF {&jobnpc_killers[] == 0} {
    #RETURN {};
  };
  #FOREACH {*jobnpc_killers[]} {n} {
    #IF {"$jobnpc_killers[$n]" == "none"} {
      #RETURN {$n};
    };
  };
  #RETURN {*jobnpc_killers[+1]};
};
#NOP {是否巡逻起始房间};
#FUNCTION isStartRoom {
  #IF {"$room" != "$tdhroom[origin][name]"} {
    #RETURN {0};
  };
  #IF {&roomways[] != &tdhroom[origin][ways][]} {
    #RETURN {0};
  };
  #LOOP 1 &roomways[] {i} {
    #IF {"$roomways[+$i]" != "$tdhroom[origin][ways][+$i]"} {
      #RETURN {0};
    };
  };
  #RETURN {1};
};
#NOP {完成天地会任务，去第一个NPC交差,%1:特别逻辑标识};
#ALIAS {jobfinish_tdh} {
  #VARIABLE {checkcount} {0};
  #CLASS jobfinishclass KILL;
  #CLASS jobfinishclass OPEN;
  #ACTION {^恭喜你！你成功的完成了天地会任务！你被奖励了：} {
    #CLASS rewardclass KILL;
    #CLASS rewardclass OPEN;
    #ACTION {^%*点经验!} {
      #VARIABLE {jobreward_exp} {%%%1};
    };
    #ACTION {^%*点潜能!} {
      #CLASS rewardclass KILL;
      #VARIABLE {jobreward_pot} {%%%2};
    };
    #CLASS rewardclass CLOSE;
    #VARIABLE {finishflag} {1};
  };
  #CLASS jobfinishclass CLOSE;
  #VARIABLE {jobstep_tdh} {3};
  #VARIABLE {hero_roomid} {$roomid};
  gotoroom {$gm_roomid} {jobfinish_tdh_ask {%1}}
};
#NOP {完成询问，%1:重试标识,1:原地位移重试,%2:回溯寻找重试};
#ALIAS {jobfinish_tdh_ask} {
  #VARIABLE {checkts} {0};
  #CLASS jobrequestclass KILL;
  #CLASS jobrequestclass OPEN;
  #VARIABLE {checkcount} {0};
  #ACTION {^{设定环境变量：action \= \"checkfinish\"|你设定checkfinish为反馈信息}} {
    resonate {checkfinish};
    #VARIABLE {echots} {0};
    #VARIABLE {idle} {0};
    #MATH {checkcount} {$checkcount + 1};
    #IF {$finishflag == 1} {
      #CLASS jobrequestclass KILL;
      #CLASS jobfinishclass KILL;
      #CLASS jobdoclass KILL;
      #CLASS jobkillclass KILL;
      on_tdh_finish;
      taskgain {@ctd{$jobreward_exp}};
      taskend;
      joblog {成功完成，获得$jobreward_exp点经验$jobreward_pot点潜能，耗时@elapsed{$jobstart_ts}秒。};
      #IF {"%1" == "2"} {
        logbuff {tdhtraceback}
      };
      jobclear_tdh;
      jobprepare
    };
    #ELSEIF {$jobnpc_tdhmiss == 1} {
      #CLASS jobrequestclass KILL;
      #NOP {少杀侍卫了，原地等候};
    };
    #ELSEIF {$checkcount <= 5} {
      #DELAY {1} {id here;echo {checkfinish}}
    };
    #ELSE {
      #CLASS jobrequestclass KILL;
      #NOP {检查好汉是否存在};
      #IF {"$roomthings[$jobnpc_hero]" == ""} {
        #IF {"%1" != ""} {
          jobfangqi_tdh
        };
        #ELSE {
          jobbacktrace_tdh
        };
      };
      #ELSEIF {"%1" == ""} {
        #NOP {好汉在第二时间到达时，不会触发完成，走动一下即可};
        jobmakepath_tdh {jobfinish_tdh_retry}
      };
      #ELSE {
        #NOP {好汉不存在或者已经重试则放弃};
        #CLASS jobkillclass KILL;
        jobfangqi_tdh
      };
    };
  };
  #CLASS jobrequestclass CLOSE;
  #VARIABLE {jobstep_tdh} {4};
  echo {checkfinish} {2}
};
#NOP {移动一步重新回来等待完成};
#ALIAS {jobfinish_tdh_retry} {
  $tdhroom[pathto];
  $tdhroom[pathback];
  jobfinish_tdh_ask {1}
};
#NOP {回溯寻找走丢的好汉};
#ALIAS {jobbacktrace_tdh} {
  #NOP {从好汉房间->当前房间(gm_roomid)路径回溯第一个和hero_lastroom相符的房间名称，并以此去重新寻找一下};
  #VARIABLE {hero_roomid} {1150};
  #VARIABLE {gm_roomid} {2795};
  #VARIABLE {hero_lastroom} {南大街};
  #VARIABLE {passedrooms} {@getPathRooms{{$hero_roomid}{$gm_roomid}}};
  #LIST {matcherooms} {clear} {};
  #FOREACH {$passedrooms} {r} {
    #NOP {太远的地方不去，比如桃源县里面，神龙岛，姑苏慕容};
    #IF {"@getRoomInfo{{$r}{ROOMNAME}}" != "$hero_lastroom"} {
      #CONTINUE;
    };
    #LOCAL {_area} {@getRoomInfo{{$r}{ROOMAREA}}};
    #IF {"$_area" == "神龙岛" || "$_area" == "姑苏慕容" || "$_area" == "燕子坞" || "$_area" == "曼佗罗山庄"} {
      #CONTINUE;
    };
    #IF {"$_area" == "桃源县" && ($r < 1203 || $r > 1207)} {
      #CONTINUE;
    };
    #LIST {matcherooms} {add} {$r};
  };
  #IF {&matcherooms[] > 0 && "$conf[nanny][visitor]" != ""} {
    #LIST {matcherooms} {collapse} {|};
    visit_call {matcherooms} {tdh_waitvisit} {jobfangqi_tdh}
  };
  #ELSE {
    jobfangqi_tdh
  };
};
#NOP {等待大米访问，最多等一分钟};
#ALIAS {tdh_waitvisit} {
  #VARIABLE {visitts} {0};
  #CLASS jobcheckclass KILL;
  #CLASS jobcheckclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkwait\"|你设定checkwait为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {"$roomthings[$jobnpc_hero]" != ""} {
      #CLASS jobcheckclass KILL;
      jobmakepath_tdh {jobfinish_tdh_retry}
    };
    #ELSEIF {@elapsed{$visitts} > 60} {
      #CLASS jobcheckclass KILL;
      visit_cancel;
      jobfangqi_tdh
    };
    #ELSE {
      #DELAY {2} {
        id here;
        echo {checkwait}
      }
    };
  };
  #CLASS jobcheckclass CLOSE;
  id here;
  echo {checkwait}
};
#ALIAS {jobfangqi_tdh} {
  gotodo {扬州城} {小金山} {jobfangqi_tdh_ask};
};
#ALIAS {jobfangqi_tdh_ask} {
  #VARIABLE {askresult} {0};
  #CLASS jobdoclass KILL;
  #CLASS jobkillclass KILL;
  #CLASS jobfinishclass KILL;
  #CLASS jobrequestclass OPEN;
  #ACTION {^你向李式开打听有关『fangqi』的消息} {
    #VARIABLE {idle} {0};
    #CLASS jobresponseclass OPEN;
    #ACTION {^李式开说道：「既然你做不了，也就算了} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      joblog {未能完成任务，耗时@elapsed{$jobstart_ts}秒};
      cond;
      #NOP {天地会任务放弃公共和天地会busy固定3分钟，所以无需切换任务，只能领悟或者打坐了};
      dohalt {jobprepare};
    };
    #ACTION {^李式开说道：「你又没领过任务，放弃什么} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {jobprepare};
    };
    #CLASS jobresponseclass CLOSE;
  };
  #CLASS jobrequestclass CLOSE;
  ask li shikai about fangqi;
};
#ALIAS {jobclear_tdh} {
  #VARIABLE {jobnpc_gm} {};
  #VARIABLE {jobnpc_gm_id} {};
  #VARIABLE {jobnpc_hero} {};
  #VARIABLE {jobnpc_hero_id} {};
  #VARIABLE {jobnpc_killers} {};
  #VARIABLE {joblocation} {};
  #VARIABLE {joblocation2} {};
  #LIST {jobroomlist} {clear};
  #VARIABLE {jobfight_ts} {0};
  #VARIABLE {jobnpc_count} {0};
  #VARIABLE {jobnpc_supercount} {0};
  #VARIABLE {jobstart_ts} {0};
};