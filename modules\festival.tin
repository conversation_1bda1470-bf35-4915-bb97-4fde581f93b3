#NOP {节日模块};
#NOP {上次领取礼物的日期};
#VARIABLE {env[festival]} {0};
#NOP {节日列表};
#VARIABLE {common[festivals]} {
  {20250101} {元旦}
  {20250129} {春节}
  {20250501} {劳动节}
  {20250531} {端午节}
  {20251001} {国庆节}
  {20251006} {中秋节}
  {20260101} {元旦}
  {20260218} {春节}
  {20260501} {劳动节}
  {20260620} {端午节}
  {20260927} {中秋节}
  {20261001} {国庆节}
  {20270101} {元旦}
  {20270207} {春节}
  {20270501} {劳动节}
  {20270609} {端午节}
  {20270917} {中秋节}
  {20271001} {国庆节}
  {20280101} {元旦}
  {20280127} {春节}
  {20280501} {劳动节}
  {20280528} {端午节}
  {20281004} {中秋节}
  {20281001} {国庆节}
  {20290101} {元旦}
  {20290214} {春节}
  {20290501} {劳动节}
  {20290616} {端午节}
  {20290924} {中秋节}
  {20291001} {国庆节}
  {20300101} {元旦}
  {20300203} {春节}
  {20300501} {劳动节}
  {20300605} {端午节}
  {20301013} {中秋节}
  {20301001} {国庆节}
  {20310101} {元旦}
  {20310124} {春节}
  {20310501} {劳动节}
  {20310526} {端午节}
  {20311002} {中秋节}
  {20311001} {国庆节}
  {20320101} {元旦}
  {20320211} {春节}
  {20320501} {劳动节}
  {20320613} {端午节}
  {20320921} {中秋节}
  {20321001} {国庆节}
  {20330101} {元旦}
  {20330131} {春节}
  {20330501} {劳动节}
  {20330602} {端午节}
  {20331010} {中秋节}
  {20331001} {国庆节}
  {20350101} {元旦}
  {20350206} {春节}
  {20350501} {劳动节}
  {20350527} {端午节}
  {20351016} {中秋节}
  {20351001} {国庆节}
};

#NOP {获取下一个可用的节日,节日当天和下一天有效};
#FUNCTION getAvailableFestival {
  #FORMAT dt %t {%Y%m%d};
  #FOREACH {*common[festivals][]} {t} {
    #NOP {已领取的忽略};
    #IF {$t <= $env[festival]} {
      #CONTINUE
    };
    #NOP {两天内的节日有效};
    #IF {$dt >= $t && @eval{$dt - $t} <= 1} {
      #RETURN {$t}
    }
  };
  #RETURN {0}
};
#NOP {领取节日礼物,%1:节日,%2:后续指令};
#ALIAS {getgift} {
  gotoroom {武馆大门} {getgift_start {%1} {%2}}
};
#ALIAS {getgift_start} {
  #VARIABLE {okflag} {0};
  #CLASS festivalclass KILL;
  #CLASS festivalclass OPEN;
  #ACTION {^你向武馆门卫打听有关『%*』的消息} {
    #CLASS festivalresponseclass KILL;
    #CLASS festivalresponseclass OPEN;
    #ACTION {^你已经领取了%*礼包，明年再来吧！} {
      #VARIABLE {okflag} {1}
    };
    #ACTION {^你获得了%*小时双倍经验！} {
      #VARIABLE {okflag} {1}
    };
    #ACTION {^武馆门卫递给你%*书剑通宝} {
      #VARIABLE {okflag} {1}
    };
    #ACTION {^你忽然想起自己是死大米，顿时万念俱灰。} {
      #VARIABLE {okflag} {1}
    };
    #CLASS festivalresponseclass CLOSE
  };
  #ACTION {^{设定环境变量：action \= \"checkgift\"|你设定checkgift为反馈信息}} {
    #CLASS festivalresponseclass KILL;
    #IF {$okflag == 0} {
      dohalt {
        ask men wei about %1;
        i;
        echo {checkgift}
      }
    };
    #ELSE {
      #FORMAT {dt} {%t} {%Y%m%d};
      #VARIABLE {env[festival]} {$dt};
      set env_time @getTimeSets{};
      echo {checkbox}
    }
  };
  #ACTION {^{设定环境变量：action \= \"checkbox\"|你设定checkbox为反馈信息}} {
    #IF {@carryqty{box} == 0} {
      #CLASS festivalclass KILL;
      dohalt {
        checkreward {jobgo}
      }
    };
    #ELSE {
      open box;
      i;
      #DELAY {1} {echo {checkbox}}
    }
  };
  #CLASS festivalclass CLOSE;
  dohalt {
    ask men wei about %1;
    i;
    echo {checkgift}
  }
};
#ALIAS {giftok} {
  #FORMAT dt %t {%Y%m%d};
  #VARIABLE {env[festival]} {$dt};
  set env_time @getTimeSets{};
  jobcheck
};
#SHOWME {<fac>@padRight{{礼物}{12}}<fac> <cfa>模块加载完毕<cfa>};