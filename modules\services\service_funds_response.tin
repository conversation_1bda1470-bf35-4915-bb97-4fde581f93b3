#NOP {初始化启动资金响应};
#ALIAS {initfundsservice} {
  #CLASS servicefundsclass KILL;
  #CLASS servicefundsclass OPEN;
  #ACTION {^%*(%*)告诉你：funds_request} {
    funds_accept {@lower{%%2}} {%%1}
  };
  #ACTION {^! %*(%*)告诉你：funds_request} {
    funds_accept {@lower{%%2}} {%%1}
  };
  #ACTION {^%*(%*)告诉你：funds_cancel} {
    #VARIABLE {caller} {};
  };
  #ACTION {^! %*(%*)告诉你：funds_cancel} {
    #VARIABLE {caller} {};
  };
  #CLASS servicefundsclass CLOSE;
};
#NOP {注册启动资金请求,%1:id,%2:name};
#ALIAS {funds_accept} {
  #NOP {超过五分钟重置};
  #IF {"$caller" != "" && @elapsed{$caller[timestamp]} > 300} {
    #VARIABLE {caller} {};
  };
  #NOP {更新请求};
  #IF {"$caller" != "" && "$caller[id]" != "%1"} {
    tell %1 funds_wait
  };
  #ELSE {
    #VARIABLE {caller} {
      {request} {funds}
      {timestamp} {@now{}}
      {id} {%1}
      {name} {%2}
    };
    tell %1 funds_come
  };
};
#NOP {去襄阳钱庄响应,%1:后续指令};
#ALIAS {funds_response} {
  yz {funds_response_start {%1}}
};
#NOP {检查呼叫者,%1:后续指令};
#ALIAS {funds_response_start} {
  #VARIABLE {checkcount} {0};
  #VARIABLE {okflag} {0};
  #VARIABLE {amount} {0};
  #CLASS serviceclass KILL;
  #CLASS serviceclass OPEN;
  #ACTION {^你要对谁做这个动作？} {
    #MATH {checkcount} {$checkcount + 1};
  };
  #ACTION {^你轻轻地拍了拍$caller[name]的头。} {
    #VARIABLE {checkcount} {0};
  };
  #ACTION {^{设定环境变量：action \= \"checkcaller\"|你设定checkcaller为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {$checkcount == 0} {
      #VARIABLE {okflag} {0};
      qu 100 gold;
      echo {checkqu};
    };
    #ELSEIF {$checkcount < 60} {
      #DELAY {2} {
        pat $caller[id];
        echo {checkcaller};
      };
    };
    #ELSE {
      #CLASS serviceclass KILL;
      %1;
    };
  };
  #ACTION {^你从银号里取出一百锭黄金} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkqu\"|你设定checkqu为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {$okflag == 1} {
      #VARIABLE {okflag} {0};
      give 100 gold to $caller[id];
      echo {checkgive};
    };
    #ELSE {
      #DELAY {1} {
        #VARIABLE {okflag} {0};
        qu 100 gold;
        i;
        echo {checkqu};
      };
    };
  };
  #ACTION {^你没有那么多的黄金} {
    #VARIABLE {okflag} {0};
    qu 100 gold;
    i;
    echo {checkqu};
  };
  #ACTION {^黄金对$caller[name]而言太重了} {
    #VARIABLE {okflag} {2};
  };
  #ACTION {^你给$caller[name]一百锭黄金} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkgive\"|你设定checkgive为反馈信息}} {
    #VARIABLE {idle} {0};
    #DELAY {1} {
      #IF {$okflag == 2} {
        #CLASS serviceclass KILL;
        servicelog {支援$caller[name]($caller[id])共计【$amount】两黄金。};
        #VARIABLE {caller} {};
        %1;
      };
      #ELSEIF {$okflag == 1} {
        #MATH {amount} {$amount + 100};
      };
      #ELSEIF {$okflag == 0} {
        #VARIABLE {okflag} {0};
        give 100 gold to $caller[id];
        echo {checkgive};
      };
    };
  };
  #ACTION {^$caller[name]高兴地跳进了你的怀里} {
    #VARIABLE {okflag} {0};
    qu 100 gold;
    echo {checkqu};
  };
  #ACTION {^$caller[name]对你抱拳道：“青山不改，绿水常流，咱们后会有期！”} {
    #CLASS serviceclass KILL;
    servicelog {支援$caller[name]($caller[id])共计【$amount】两黄金。};
    #VARIABLE {caller} {};
    %1;
  };
  #CLASS serviceclass CLOSE;
  pat $caller[id];
  echo {checkcaller};
};
initfundsservice;
