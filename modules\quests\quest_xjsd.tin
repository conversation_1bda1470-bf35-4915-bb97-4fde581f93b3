#NOP {血祭神刀,%1:后续指令};
#ALIAS {goquest_xjsd} {
  #VARIABLE {questmodule} {血祭神刀};
  gotonpc {血刀老祖} {xjsd_asklaozu {%1}}
};
#NOP {问血刀老祖};
#ALIAS {xjsd_asklaozu} {
  #VARIABLE {missingflag} {0};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你向血刀老祖打听有关『祭刀』的消息。} {
    #CLASS questresponseclass OPEN;
    #ACTION {^血刀老祖说道：「%*{你比我都强了|不行，虽然你是我徒弟}} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      #NOP {成功，去天湖};
      dohalt {gotodo {大雪山} {天湖} {xjsd_jidao {%1}}}
    };
    #ACTION {^血刀老祖说道：「%*月圆了，是该祭刀了，既然你问起来，就用你的血来祭我这把刀吧} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      #NOP {失败};
      questfail {$questmodule};
      dohalt {%1}
    };
    #ACTION {^血刀老祖说道：「%*今天先问到这里，明天吧} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questdelay {$questmodule} {} {7200};
      dohalt {%1}
    };
    #ACTION {^血刀老祖说道：「%*以你当前的经验恐怕还是难以领悟，还是抓紧去练功去吧} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questdelay {$questmodule} {} {7200};
      dohalt {%1}
    };
  };
  #CLASS questclass CLOSE;
  ask laozu about 祭刀
};
#ALIAS {xjsd_jidao} {
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你起码先找把刀来吧} {
    #LOCAL {bladeweapon} {@getBaseWeapon{xuedao-jing}};
    #IF {"$bladeweapon" == ""} {
      buynormalweapon {xue sui} {gotodo {大雪山} {天湖} {xjsd_jidao {%1}}}
    };
    #ELSE {
      #DELAY {1} {
        wwp {$bladeweapon};
        jidao
      }
    };
  };
  #ACTION {^祭刀要到晚上夜深时分方可} {
    #VARIABLE {idle} {0};
    #DELAY {6} {jidao}
  };
  #ACTION {^你已经鲜血祭刀成功了，不需要再祭了} {
    #CLASS questclass KILL;
    questsuccess {$questmodule};
    dohalt {%1}
  };
  #ACTION {^你体内神照经真气自然激荡，顷刻间布满全身奇经八脉} {
    #VARIABLE {idle} {-100};
  };
  #ACTION {^你心中恶念顿生，就要下山杀人试刀} {
    #CLASS questclass KILL;
    questsuccess {$questmodule};
    dohalt {%1}
  };
  #CLASS questclass CLOSE;
  jidao
};