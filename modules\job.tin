#NOP {任务通用模块};
#NOP {注意事项,在实现各任务模块时,向NPC发起询问的操作应单独设置一个ALIAS,这样会尽量减少因未预料到的原因导致询问的触发器CLASS关闭导致无法响应的情况出现};
#NOP {单独设置询问ALIAS后,其最后的aimdo就是这个ALIAS,即便发呆后重启也会在这个ALIAS中创建相关的询问触发器,保证有相关的响应};
#NOP {武馆任务模块};
import {jobs/job_wuguan}
#NOP {服务模块};
import {jobs/job_serve}
#NOP {发呆模块};
import {jobs/job_idle}
#NOP {门童模块};
import {jobs/job_doorman}
#NOP {巡城任务模块};
import {jobs/job_xuncheng}
#NOP {钓鱼任务模块};
import {jobs/job_fish}
#NOP {桃花岛守墓任务模块};
import {jobs/job_shoumu}
#NOP {明教巡逻任务模块，空};
import {jobs/job_xunluo}
#NOP {少林教和尚任务模块};
import {jobs/job_teachmonk}
#NOP {少林护送任务模块};
import {jobs/job_husong}
#NOP {长乐帮任务模块};
import {jobs/job_clb}
#NOP {丐帮任务模块};
import {jobs/job_gaibang}
#NOP {华山任务模块};
import {jobs/job_huashan}
#NOP {嵩山任务模块};
import {jobs/job_songshan}
#NOP {送信任务模块};
import {jobs/job_songxin}
#NOP {天地会任务模块};
import {jobs/job_tdh}
#NOP {武当任务模块};
import {jobs/job_wudang}
#NOP {雪山任务模块};
import {jobs/job_xueshan}
#NOP {官府任务模块};
import {jobs/job_guanfu}
#NOP {七窍玲珑任务模块};
import {jobs/job_qqll}
#NOP {做菜任务模块};
import {jobs/job_zuocai}
#NOP {组队任务模块};
import {jobs/job_team}
#NOP {颂摩崖模块};
import {jobs/job_smy}
#NOP {守卫襄阳模块};
import {jobs/job_swxy}

#NOP {通用变量};
#VARIABLE {currentjob} {};
#VARIABLE {targetjob} {};
#VARIABLE {jobcity} {};
#VARIABLE {jobroom} {};
#VARIABLE {nextroom} {};
#VARIABLE {lastjob} {};
#VARIABLE {jobstarttime} {0};
#VARIABLE {joblocation} {}
#VARIABLE {routine} {};
#VARIABLE {jobstart_ts} {0};
#VARIABLE {jobfight_ts} {0};
#VARIABLE {jobnpc_count} {0};
#VARIABLE {jobreward_exp} {};
#VARIABLE {jobreward_pot} {};
#VARIABLE {jobfight_target} {};

#NOP {任务统计数据};
#VARIABLE {jobstatitics} {};

#NOP {官府已放弃npc};
#VARIABLE {guanfunpcs} {};

#NOP {是否是特殊任务,无需保持食物和药品};
#FUNCTION isSepcialJob {
  #IF {@contains{{conf[joblist]}{钓鱼}} > 0} {
    #RETURN {1};
  };
  #ELSE {
    #RETURN {0};
  };
};
#NOP {清除任务相关触发器类};
#ALIAS {jobclear} {
  #CLASS jobrequestclass KILL;
  #CLASS jobresponseclass KILL;
  #CLASS jobcheckclass KILL;
  #CLASS jobcheckresponseclass KILL;
  #CLASS jobwaitclass KILL;
  #CLASS jobfightclass KILL;
  #CLASS jobdoclass KILL;
  #CLASS jobfinishclass KILL;
  #CLASS rewardclass KILL;
  #CLASS fullclass KILL;
  #CLASS joinclass KILL;
  #CLASS questclass KILL;
  #CLASS teamclass KILL;
  #CLASS dangercheckclass KILL;
  #CLASS checkclass KILL;
  #CLASS checkcapclass KILL;
  #VARIABLE {jobfight_target} {};
  #VARIABLE {escapeflag} {0};
  #LIST {jobroomlist} {clear} {};
};
#NOP {开始任务};
#ALIAS {startjob} {
  #NOP {新手逻辑};
  #IF {@contains{{conf[joblist]}{服务}} > 0} {
    jobgo_serve
  };
  #ELSEIF {@contains{{conf[joblist]}{发呆}} > 0} {
    jobgo_idle
  };
  #ELSEIF {@contains{{conf[joblist]}{门童}} > 0} {
    jobgo_doorman
  };
  #ELSEIF {@contains{{conf[joblist]}{洗澡}} > 0} {
    fullbeauty {startjob}
  };
  #ELSEIF {$hp[exp] < 3000} {
    jobwuguan_start
  };
  #ELSEIF {$hp[exp] < 150000} {
    jobgo_xuncheng
  };
  #ELSEIF {$hp[exp] < 200000 && "$env[shaolin]" != "YES"} {
    haha;
    guide_newbie {startjob};
  };
  #ELSEIF {@getSkillLevel{medicine} < 52} {
    fullmedicine {golearn {jobprepare}} {52}
  };
  #ELSEIF {$env[guigu] == 1} {
    closeguigu {startjob}
  };
  #ELSE {
    #NOP {对于几个比较特殊的任务无需几个需要申请开放的区域};
    #IF {@isSepcialJob{} == 1} {
      #VARIABLE {env[wudujiao]} {1};
      #VARIABLE {env[wdjfeedback]} {
        {status}{1}
        {timestamp}{$env[realtime]}
      };
    };
    #VARIABLE {checkidle} {1};
    #NOP {断线重连无需检查杂货铺存货};
    #IF {$env[loginmode] == 1} {
      checkweapon {jobprepare}
    };
    #ELSE {
      loadstock {checkweapon {jobprepare}}
    };
  };
};

#NOP {准备任务,为了尽量避免发呆，这里将jobcheck设置为aimdo};
#ALIAS {jobprepare} {
  #NOP {复位调整的地图连接};
  #IF {@isSepcialJob{} == 0 && $env[guanfu] != 1} {
    joingf {jobprepare};
  };
  #ELSEIF {@isSepcialJob{} == 0 && $env[huanggong] != 1} {
    joinhg {jobprepare};
  };
  #ELSEIF {@isSepcialJob{} == 0 && @isZoneAllow{苗疆} == 1 && "$env[wudujiao]" == ""} {
    #IF {@isZoneCan{苗疆} == 1} {
      #VARIABLE {aimdo} {jobcheck};
      executecmd {jobcheck};
    };
    #ELSE {
      joinwdj {jobprepare}
    };
  };
  #ELSE {
    executecmd {jobcheck};
  };
};

#NOP {获取下一个要做的任务,%1:要排除的任务,%2:指定最后一次的任务};
#FUNCTION getNextJob {
  #LIST {templist} {create} {$conf[joblist][]};
  #VARIABLE {tempindex} {0};
  #NOP {排除指定任务};
  #IF {"%1" != ""} {
    #LOCAL {tempindex} {@contains{{templist}{%1}}};
    #IF {$tempindex > 0} {
      #LIST {templist} DELETE {$tempindex};
    };
  };
  #NOP {检索最后一次任务};
  #VARIABLE {tempindex} {0};
  #LOCAL {lstjob} {$hp[lastjob]};
  #IF {"%2" != ""} {
    #LOCAL {lstjob} {%2};
  };
  #LOOP 1 &templist[] {i} {
    #IF {@startWiths{{$templist[+$i]}{$lstjob}} > 0} {
      #VARIABLE {tempindex} {$i};
      #BREAK;
    };
  };
  #IF {$tempindex != 0} {
    #LIST {templist} DELETE {$tempindex};
  };
  #IF {$tempindex > &templist[] || $tempindex <= 0} {
    #VARIABLE {tempindex} {1};
  };
  #IF {$tempindex > &templist[]} {
    #RETURN {};
  };
  #ELSE {
    #RETURN {$templist[+$tempindex]};
  };
};
#NOP {获取当前就绪的扩展任务};
#FUNCTION getExtendJob {
  #VARIABLE {extjob} {};
  #FOREACH {$conf[extendjob][]} {j} {
    #NOP {判定各扩展任务是否具备条件};
    #SWITCH {"$j"} {
      #CASE {"官府"} {
        #IF {"$hp[condition][双倍经验]" != ""} {
          #CONTINUE;
        };
        #IF {$env[wanted] != 0 && @elapsed{$env[wanted]} < 120} {
          #VARIABLE {extjob} {$j};
          #BREAK;
        };
      };
      #CASE {"护送"} {
        #IF {@elapsed{$env[husong]} > 1200} {
          #VARIABLE {extjob} {$j};
          #BREAK;
        };
      };
      #DEFAULT {}
    };
  };

  #RETURN {$extjob};
};
#NOP {获取正神过渡任务};
#FUNCTION getZsTransitJob {
  #IF {&conf[zstransitjoblist][] == 0} {
    #LIST {conf[zstransitjoblist]} {create} {送信;华山};
  };
  #IF {"$hp[lastjob]" == "$conf[zstransitjoblist][+1]"} {
    #RETURN {$conf[zstransitjoblist][+2]};
  };
  #ELSE {
    #RETURN {$conf[zstransitjoblist][+1]};
  };
};
#NOP {获取负神过渡任务};
#FUNCTION getFsTransitJob {
  #IF {&conf[fstransitjoblist][] == 0} {
    #LIST {conf[fstransitjoblist]} {create} {送信;长乐帮};
  };
  #IF {"$hp[lastjob]" == "$conf[fstransitjoblist][+1]"} {
    #RETURN {$conf[fstransitjoblist][+2]};
  };
  #ELSE {
    #RETURN {$conf[fstransitjoblist][+1]};
  };
};
#NOP {获取当前就绪的扩展任务};
#FUNCTION getExtendDesc {
  #VARIABLE {extdesc} {};
  #FOREACH {$conf[extendjob][]} {j} {
    #NOP {判定各扩展任务是否具备条件};
    #SWITCH {"$j"} {
      #CASE {"官府"} {
        #IF {"$hp[condition][双倍经验]" != ""} {
          #CONTINUE;
        };
        #VARIABLE {extdesc} {$j};
        #IF {"$currentjob" == "$j"} {
          #CAT {extdesc} {(进行中)};
        };
        #ELSEIF {$env[wanted] == 0} {
          #CAT {extdesc} {(未触发)};
        };
        #ELSEIF {@elapsed{$env[wanted]} < 120} {
          #CAT {extdesc} {(@eval{120 - @elapsed{$env[wanted]}}秒)};
        };
        #ELSE {
          #CAT {extdesc} {(未触发)};
        };
      };
      #CASE {"护送"} {
        #VARIABLE {extdesc} {$j};
        #IF {"$currentjob" == "$j"} {
          #CAT {extdesc} {(进行中)};
        };
        #ELSEIF {@elapsed{$env[husong]} < 1200} {
          #CAT {extdesc} {(@eval{1200 - @elapsed{$env[husong]}}秒)};
        };
        #ELSE {
          #CAT {extdesc} {(待机)};
        };
      };
      #CASE {"炼毒"} {
        #VARIABLE {extdesc} {$j};
        #IF {$hp[neili_max] < @getLianduLiandu{}} {
          #CAT {extdesc} {(内力不足)};
        };
        #ELSE {
          #IF {"$currentjob" == "$j"} {
            #CAT {extdesc} {(进行中)};
          };
          #ELSEIF {@now{} > $env[liandu]} {
            #CAT {extdesc} {(待机)};
          };
          #ELSE {
            #CAT {extdesc} {(@eval{$env[liandu] - @now{}}秒)};
          };
        };
      };
      #DEFAULT {}
    };
  };

  #RETURN {$extdesc};
};
#NOP {去做任务，%1:指定任务，%2:完成指定任务后要进行的任务};
#ALIAS {jobgo} {
  stopwalk;
  questclear;
  kungfuclear;
  commonclear;
  jobclear;
  #VARIABLE {currentjob} {@getNextJob{}};
  #IF {"%1" != ""} {
    #VARIABLE {currentjob} {%1};
    #VARIABLE {targetjob} {%2};
  };
  #ELSEIF {"$targetjob" != ""} {
    #VARIABLE {currentjob} {$targetjob};
    #VARIABLE {targetjob} {};
  };
  
  #FOREACH {$conf[extendjob][]} {j} {
    #NOP {判定各扩展任务是否具备条件};
    #SWITCH {"$j"} {
      #CASE {"官府"} {
        #IF {"$hp[condition][双倍经验]" != ""} {
          #CONTINUE;
        };
        #IF {$env[wanted] != 0 && @elapsed{$env[wanted]} < 120} {
          #VARIABLE {currentjob} {$j};
          #BREAK;
        };
      };
      #CASE {"护送"} {
        #IF {@elapsed{$env[husong]} > 1200} {
          #VARIABLE {currentjob} {$j};
          #BREAK;
        };
      };
      #DEFAULT {}
    };
  };
  #NOP {处理有关独孤九剑和吸星大法的逻辑};
  #NOP {在完成华山任务时如华山+送信+丐帮总次数被50整除有几率提示面壁，如华山+武当总次数被50整除有几率提示吸星大法};
  #NOP {具体还要看福源的随机数，假如下次任务进行华山任务时即可满足条件，那么要调整任务流程以满足条件};
  #VARIABLE {hspray} {};
  #IF {"%1" == "" && "%2" == "" && "$hp[shen]" == "正气"} {
    #LOCAL {totaltimes} {0};
    #NOP {吸星大法，华山+武当};
    #IF {@isQuestActivate{{吸星大法}{1}} == 1 && "$env[xxdf]" == ""} {
      #MATH {totaltimes} {@eval{$hp[jobtimes][华山]} + @eval{$hp[jobtimes][武当]}};
    };
    #NOP {独孤九剑，华山+送信+丐帮};
    #IF {@isQuestActivate{独孤九剑} == 1 && "$env[dgjj]" == ""} {
      #MATH {totaltimes} {@eval{$hp[jobtimes][华山]} + @eval{$hp[jobtimes][送信]} + @eval{$hp[jobtimes][丐帮]}};
    };
    #NOP {是否余数49};
    #IF {($totaltimes % 50) == 49} {
      #IF {"$hp[lastjob]" == "华山"} {
        #SHOWME {<ffa>执行长乐帮->华山};
        #NOP {做一个长乐帮进行缓冲并指定下次任务为华山};
        #VARIABLE {currentjob} {长乐帮};
        #VARIABLE {targetjob} {华山};
      };
      #ELSE {
        #SHOWME {<faf>华山祝福模式};
        #VARIABLE {currentjob} {华山};
        #VARIABLE {hspray} {1};
      };
    };
  };
  #VARIABLE {xlpray} {};
  #IF {"$currentjob" == "巡逻" && @isQuestActivate{九阳神功} == 1 && "$env[jysg]" != "YES"} {
    #LOCAL {xltimes} {@eval{$hp[jobtimes][巡逻]}};
    #IF {($xltimes % 20) == 19} {
      #VARIABLE {xlpray} {1};
    };
  };
  #SWITCH {"$currentjob"} {
    #CASE {"长乐帮"} {jobgo_clb};
    #CASE {"长乐帮2"} {jobgo_clb};
    #CASE {"武当"} {jobgo_wudang};
    #CASE {"华山"} {jobgo_huashan {$hspray}};
    #CASE {"华山2"} {jobgo_huashan};
    #CASE {"送信"} {jobgo_songxin};
    #CASE {"送信2"} {jobgo_songxin};
    #CASE {"嵩山"} {jobgo_songshan};
    #CASE {"雪山"} {jobgo_xueshan};
    #CASE {"天地会"} {jobgo_tdh};
    #CASE {"钓鱼"} {jobgo_fish};
    #CASE {"守墓"} {jobgo_shoumu};
    #CASE {"巡逻"} {jobgo_xunluo {$xlpray}};
    #CASE {"官府"} {jobgo_guanfu};
    #CASE {"七窍玲珑"} {jobgo_qqll};
    #CASE {"教和尚"} {jobgo_tm};
    #CASE {"护送"} {jobgo_husong};
    #CASE {"护镖"} {jobgo_hubiao};
    #CASE {"颂摩崖"} {jobgo_smy};
    #CASE {"守卫襄阳"} {jobgo_swxy};
    #CASE {"做菜"} {jobgo_zuocai};
    #DEFAULT {#SHOW <faa>无法识别的任务 $currentjob};
  };
};

#NOP {放弃job};
#ALIAS {jobfangqi} {
  #SWITCH {"$currentjob"} {
    #CASE {"长乐帮"} {jobfangqi_clb};
    #CASE {"长乐帮2"} {jobfangqi_clb};
    #CASE {"武当"} {jobfangqi_wudang};
    #CASE {"华山"} {jobfangqi_huashan};
    #CASE {"华山2"} {jobfangqi_huashan};
    #CASE {"送信"} {jobfangqi_songxin};
    #CASE {"送信2"} {jobfangqi_songxin};
    #CASE {"雪山"} {jobfangqi_xueshan};
    #CASE {"嵩山"} {jobfangqi_songshan};
    #CASE {"天地会"} {jobfangqi_tdh};
    #CASE {"巡逻"} {jobfangqi_xunluo};
    #CASE {"官府"} {jobfangqi_guanfu};
    #CASE {"七窍玲珑"} {jobfangqi_qqll};
    #CASE {"护送"} {jobfangqi_husong};
    #CASE {"护镖"} {jobfangqi_hubiao};
    #CASE {"做菜"} {jobfangqi_zuocai};
    #DEFAULT {startjob};
  };
  #NOP {复位pfm目标};
  #VARIABLE {dangerousflag} {0};
  #VARIABLE {escapeflag} {0};
  createpfm
};
#VARIABLE {env[status][dodge]} {
  {level} {0}
  {last} {0}
};
#NOP {任务前检查};
#ALIAS {jobcheck} {
  #VARIABLE {interrupt} {0};
  #VARIABLE {dangerousflag} {0};
  #VARIABLE {escapeflag} {0};
  openwimpy;
  questclear;
  donext {
    follow none;
    i;
    exp;
    score;
    jobtimes;
    cond;
    time;
    checkenv;
  } {checkkungfu {jobgogogo}}
};
#ALIAS {jobgogogo} {
  prepareforce;
  bei;
  createpfm {} {1};
  #DELAY {1} {
    doqudu {checkhp {checkweapon {checkarmor {checkreward {checkmedicine {checkrequest {checkreboot {checkexchange {checkpot {checkmoney {checkother {#DELAY {1} {
      jobgo;
    }}}}}}}}}}}}};
  };
};
#NOP {检查功夫是否完备,%1-后续指令};
#ALIAS {checkkungfu} {
  #CLASS checkclass KILL;
  #CLASS checkclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkjifa\"|你设定checkjifa为反馈信息}} {
    #CLASS checkclass KILL;
    #VARIABLE {idle} {0};
    resonate {checkjifa};
    #IF {"$kungfu[base][force][jifa]" == "" || "$kungfu[base][parry][jifa]" == "" || "$kungfu[base][dodge][jifa]" == "" || "kungfu[bei]" == ""} {
      closesaving;
      dzn {
        preparekungfu;
        checkkungfu {%1}
      }
    };
    #ELSE {
      %1
    };
  };
  #CLASS checkclass CLOSE;
  ensure {jifa} {checkjifa}
};
#NOP {检查服务请求,%1:后续指令,检查一下各种服务的必须条件,%2:执行request前的指令};
#ALIAS {checkrequest} {
  #IF {(@contains{{conf[services]}{zhanbu}} > 0 || @contains{{conf[services]}{assist}} > 0) && @carryqty{tie bagua} == 0} {
    %2;
    gettiebagua {checkrequest {%1}};
  };
  #ELSEIF {"$caller" != "" && @contains{{conf[services]}{$caller[request]}}} {
    commonclear;
    kungfuclear;
    jobclear;
    stopwalk;
    loc {
      %2;
      #SWITCH {"$caller[request]"} {
        #CASE {"funds"} {funds_response {%1}};
        #CASE {"recycle"} {recycle_response {%1}};
        #CASE {"guard"} {guard_response {%1}};
        #CASE {"killer"} {killer_response {%1}};
        #CASE {"zhanbu"} {zhanbu_response {%1}};
        #CASE {"library"} {library_response {%1}};
        #CASE {"assist"} {assist_response {%1}};
        #CASE {"buff"} {buff_response {%1}};
        #CASE {"doctor"} {cure_response {%1}};
        #CASE {"doorman"} {doorman_response {%1}};
        #CASE {"supply"} {supply_response {%1}};
        #CASE {"visit"} {visit_response {%1}};
      };
    };
  };
  #ELSE {
    #VARIABLE {caller} {};
    %1
  };
};

#NOP {驱毒,%1:后续指令};
#ALIAS {doqudu} {
  #IF {"$kungfu[force]" != "" && ("$kungfu[base][force][jifa]" == "" || "$kungfu[base][dodge][jifa]" == "" || "$kungfu[base][parry][jifa]" == "")} {
    #DELAY {1} {prepareskills {doqudu {%1}}};
  };
  #ELSE {
    #LOCAL {pname} {};
    #FOREACH {*conf[nanny][doctor][]} {p} {
      #NOP {治疗冷却时间,一般是无法治疗时设置的时间戳};
      #IF {@elapsed{@eval{$env[curets][$p]}} < 1200} {
        #CONTINUE;
      };
      #NOP {超过5分钟的星宿毒};
      #IF {"$p" == "星宿掌" && @eval{$hp[condition][$p]} > 300} {
        #LOCAL {pname} {$p};
        #BREAK;
      };
      #NOP {5分钟以内的其他毒忽略};
      #IF {@eval{$hp[condition][$p]} < 300} {
        #CONTINUE;
      };
      #LOCAL {pname} {$p};
    };
    #IF {"$pname" != "" && "$pname" != "混元掌" && "$hp[party]" == "古墓派" && @elapsed{$env[gmbed]} > 400} {
      gmpoison {%1}
    };
    #ELSEIF {"$pname" != "" && ("$pname" == "混元掌" || "$pname" == "蓝砂手" || @getSkillLevel{hamagong} < 200)} {
      doqudu_doctor {$pname} {%1}
    };
    #ELSE {
      doqudu_self {%1}
    };
  };
};
#NOP {自行驱毒};
#ALIAS {doqudu_self} {
  #IF {"$hp[condition][怪蛇]" != ""} {
    doqudu_guaishe {%1}
  };
  #ELSE {
    doqudu_other {%1}
  };
};
#NOP {找医生,%1:毒,%2:后续指令};
#ALIAS {doqudu_doctor} {
  cure_call {%1} {%2}
};
#NOP {怪蛇毒必定附带闭气，只能吃药硬抗,%1:后续指令};
#ALIAS {doqudu_guaishe} {
  #NOP {首先考虑tianqi解毒};
  #IF {$hp[tongbao] > 5000 || @getSkillLevel{hamagong} >= 200} {
    #NOP {吃大还丹硬抗};
    healguaishe {%1}
  };
  #ELSEIF {@carryqty{tianqi} > 0} {
    healpoison {怪蛇} {%1}
  };
  #ELSEIF {$env[oot] == 0} {
    fu dahuan dan;
    gettianqi {doqudu_guaishe {%1}}
  };
  #ELSEIF {$hp[tongbao] > 10000} {
    #NOP {通宝够多直接冰蟾};
    fu dahuan dan;
    curepoison {%1}
  };
  #ELSE {
    #NOP {只能等死};
    %1
  };
};
#NOP {其他毒处理，蛤蟆功直接qudu，否则根据角色配置处理};
#ALIAS {doqudu_other} {
  #IF {@getSkillLevel{hamagong} >= 100 && @getSkillLevel{poison} > 50 && "$kungfu[base][force][jifa]" == "hamagong"} {
    hmgqudu {%1};
  };
  #ELSE {
    #NOP {此处废弃角色配置里面的detoxify配置项，由脚本内置解决方案 2024/02/01};
    #IF {"$hp[condition][星宿掌]" != ""} {
      antipoison {星宿掌} {%1}
    };
    #ELSE {
      %1
    };
  };
};
#NOP {检查状态,%1:后续指令};
#ALIAS {checkhp} {
  #CLASS checkclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkhp\"|你设定checkhp为反馈信息}} {
    #VARIABLE {idle} {0};
    #CLASS checkclass KILL;
    #NOP {兼容性初始化};
    #IF {"$conf[healthreshold]" == ""} {
      #VARIABLE {conf[healthreshold]} {100};
    };
    #IF {$hp[exp] < 3000000 && $hp[balance] < 200} {
      funds_call {checkhp {%1}};
    };
    #ELSEIF {$hp[jing_per] < 80} {
      #IF {@carryqty{huoxue dan} == 0} {
        buymedicine {huoxue dan} {2} {checkhp {%1}};
      };
      #ELSE {
        #DELAY {0.5} {
          fu huoxue dan;
          yun jing;
          i;
          hp;
          checkhp {%1}
        }
      };
    };
    #ELSEIF {$hp[neili_max] < 600 && @eval{$hp[neili_limit_max] - $hp[neili_max]} > 100} {
      #CLASS checkclass KILL;
      #NOP {6000以前全吃yuji wan};
      doeatyuji {checkdazuopoint {checkhp {%1}} {1}}
    };
    #ELSEIF {"$conf[autoyuji][state]" != "" && $hp[tongbao] > @eval{$conf[autoyuji][threshold]} && @eval{$hp[neili_limit] - $hp[neili_max]} > 100} {
      doeatyuji {checkheal {checknegative {%1}}}
    };
    #ELSE {
      checkheal {checknegative {%1}}
    };
  };
  #CLASS checkclass CLOSE;
  hp;
  cond;
  echo {checkhp};
};
#NOP {检查是否需要疗伤,%1:后续指令};
#ALIAS {checkheal} {
  #IF {$hp[qi_per] >= $conf[healthreshold]} {
    %1
  };
  #ELSE {
    doheal {%1}
  };
};
#NOP {检测负面的状态，如果身上带着封招、气息不匀、七伤拳内伤等影响战斗的状态则等待，%1:后续指令};
#ALIAS {checknegative} {
  #IF {@hasNegative{} == 1} {
    waitnegative {%1};
  };
  #ELSE {
    %1;
  };
};
#NOP {检查药物,%1:后续指令};
#ALIAS {checkmedicine} {
  #VARIABLE {tempdrugs} {};
  #VARIABLE {tempmedicine} {};
  #NOP {默认配置};
  #FOREACH {*common[medicine][]} {m} {
    #VARIABLE {tempdrugs[$m]} {
      {warning} {0}
      {carry} {@eval{$common[medicine][$m]}}
    };
  };
  #NOP {合并用户配置};
  #FOREACH {*conf[medicine][]} {m} {
    #IF {"$conf[medicine][$m][warning]" != ""} {
      #VARIABLE {tempdrugs[$m][warning]} {@eval{$conf[medicine][$m][warning]}}
    };
    #IF {"$conf[medicine][$m][carry]" != ""} {
      #VARIABLE {tempdrugs[$m][carry]} {@eval{$conf[medicine][$m][carry]}}
    };
  };
  #NOP {经验高且通宝足忽略配置内的大还丹};
  #IF {$hp[exp] >= 100000000 && $hp[tongbao] > 5000} {
    #UNVARIABLE {tempdrugs[chantui yao]};
    #UNVARIABLE {tempdrugs[dahuan dan]};
    #UNVARIABLE {tempdrugs[da huandan]};
  };
  #FOREACH {*tempdrugs[]} {m} {
    #IF {@isSepcialJob{} == 1} {
      #CONTINUE;
    };
    #NOP {新手模式或者无库存忽略田七};
    #IF {"$m" == "tianqi" && ($env[oot] > 0 || "$conf[newbie][party]" != "")} {
      #CONTINUE;
    };
    #IF {@contains{{common[raredrugs]}{$m}} > 0 && $hp[tongbao] < 1000} {
      #CONTINUE;
    };
    #IF {@carryqty{$m} <= $tempdrugs[$m][warning] && @carryqty{$m} < $tempdrugs[$m][carry]} {
      #VARIABLE {tempmedicine} {
        {name} {$m}
        {qty} {$tempdrugs[$m][carry]}
      };
      #BREAK;
    };
  };
  #NOP {经验高且通宝足的必买大还丹};
  #IF {$hp[exp] >= 100000000 && $hp[tongbao] > 5000 && @carryqty{da huandan} == 0 && $hp[tongbao] >= 1000} {
    #VARIABLE {tempmedicine} {
      {name} {da huandan}
      {qty} {1}
    };
  };
  #IF {"$tempmedicine" != ""} {
    #IF {"$tempmedicine[name]" == "tianqi"} {
      gettianqi {$tempmedicine[qty]} {checkmedicine {%1}};
    };
    #ELSE {
      buymedicine {$tempmedicine[name]} {$tempmedicine[qty]} {checkmedicine {%1}};
    };
  };
  #ELSE {
    %1
  };
};

#NOP {主武器为打造武器丢失的时候,暂时使用第二武器抗30分钟后再退出};
#VARIABLE {weaponloadts} {0};
#NOP {检查武器,%1:后续指令};
#ALIAS {checkweapon} {
  #CLASS weaponclass KILL;
  #CLASS weaponclass open;
  #ACTION {^{设定环境变量：action \= \"checkweapon\"|你设定checkweapon为反馈信息}} {
    #CLASS weaponclass KILL;
    defineweaponmap;
    #NOP {处理weaponloadts,人数登录限制>=360后非VIP只能看风景,不考虑IP问题保留五个位置};
    #IF {$weaponloadts > 0 && $weaponloadts < @now{}} {
      #IF {"$env[wudujiao]" != "RSP" && ($env[vip] == 1 || $env[playernumber] < 320)} {
        log {主武器丢失} {quit};
        doquit
      };
      #ELSE {
        #NOP {延迟判定时间戳};
        #MATH {weaponloadts} {@now{} + 1200};
        %1
      };
    };
    #ELSEIF {"$conf[weapon][primary]" != "" && (@carryqty{$conf[weapon][primary]} == 0 || @instr{{$id[things][$conf[weapon][primary]][name]}{断}} > 0)} {
      joblog {主武器丢失} {公共信息};
      logbuff {weaponlost};
      #IF {@carryqty{$conf[weapon][primary]} == 1} {
        drop $conf[weapon][primary];
        i;
      };
      #IF {@isUserWeapon{$conf[weapon][primary]} == 1 && $weaponloadts == 0} {
        #NOP {第二武器转正,20分钟后必定刷新后重进};
        #VARIABLE {weaponloadts} {@eval{@now{} + 1200}};
        #VARIABLE {conf[weapon][primary]} {$conf[weapon][secondary]};
        checkweapon {%1};
      };
      #ELSE {
        findweapon {$conf[weapon][primary]} {checkweapon {%1}}
      };
    };
    #ELSEIF {"$conf[weapon][secondary]" != "" && (@carryqty{$conf[weapon][secondary]} == 0 || @instr{{$id[things][$conf[weapon][secondary]][name]}{断}} > 0)} {
      #IF {@carryqty{$conf[weapon][secondary]} == 1} {
        drop $conf[weapon][secondary];
        i;
      };
      findweapon {$conf[weapon][secondary]} {checkweapon {%1}}
    };
    #ELSEIF {"$conf[weapon][chop]" != "" && @carryqty{$conf[weapon][chop]} == 0} {
      findweapon {$conf[weapon][chop]} {checkweapon {%1}}
    };
    #ELSEIF {"@getMissingBaseWeapon{}" != ""} {
      findweapon {@getMissingBaseWeapon{}} {checkweapon {%1}}
    };
    #ELSEIF {"$conf[weapon][primary]" == "lanyu duzhen" && @elapsed{@eval{$env[weapongs][lanyu duzhen]}} > 1200} {
      #NOP {蓝玉毒针用一段时间就没毒了，扔掉换新的};
      drop lanyu duzhen;
      i;
      checkweapon {%1}
    };
    #ELSE {
      checkrepair {
        wwp;
        %1
      };
    };
  };
  #CLASS weaponclass close;
  #IF {@isSepcialJob{} == 1} {
    %1
  };
  #ELSE {
    #IF {$weaponloadts > 0 && $weaponloadts < @now{}} {
      mudlist
    };
    echo {checkweapon};
  };
};

#NOP {检查护甲，%1:后续指令。护甲不需要修理，直接穿吧};
#ALIAS {checkarmor} {
  remove all;
  wear all;
  %1;
};

#NOP {检查打造武器是否需要修理，检查用户武器配置中的打造武器，%1:后续指令};
#ALIAS {checkrepair} {
  #VARIABLE {okflag} {0};
  #VARIABLE {targetweapon} {};
  #CLASS weaponclass KILL;
  #CLASS weaponclass OPEN;
  #ACTION {^一枚蓝莹莹的细针，通体晶莹，偶尔有一丝蓝亮游动，似乎喂有剧毒} {
  };
  #ACTION {^隐约能看见兵器制造者的姓名} {
    #VARIABLE {okflag} {0};
  };
  #ACTION {^看起来{需要修理了|马上就要坏了}} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^设定环境变量：action \= \"checkweapon%*\"} {
    #IF {$okflag == 1} {
      #VARIABLE {okflag} {0};
      #VARIABLE {targetweapon} {%%1};
    };
  };
  #ACTION {^你设定checkweapon%*为反馈信息} {
    #IF {$okflag == 1} {
      #VARIABLE {okflag} {0};
      #VARIABLE {targetweapon} {%%1};
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkrepair\"|你设定checkrepair为反馈信息}} {
    #CLASS weaponclass KILL;
    #IF {"$targetweapon" == ""} {
      %1;
    };
    #ELSE {
      repairweapon {$targetweapon} {checkrepair {%1}};
    };
  };
  #CLASS weaponclass CLOSE;
  #LIST {uweapons} {clear} {};
  #FOREACH {*conf[weapon][userweapons][]} {bsk} {
    #LIST {bweapons} {create} {$conf[weapon][userweapons][$bsk]};
    #FOREACH {$bweapons[]} {w} {
      #LIST {uweapons} {add} {$w};
    };
  };
  #FOREACH {$uweapons[]} {w} {
    look $w;
    echo {checkweapon$w};
  };
  echo {checkrepair};
};
#NOP {检查潜能,%1:后续指令};
#ALIAS {checkpot} {
  #IF {@isNeedFullDaodejing{} == 1} {
    fulldaodejing {checkpot {%1}}
  };
  #ELSEIF {$hp[pot] < $hp[pot_max]} {
    %1;
  };
  #ELSEIF {"$hp[condition][双倍经验]" != "" && @eval{$hp[max_lv] - $kungfu[base][parry][lv]} < 20} {
    cun_pot {%1};
  };
  #ELSEIF {$conf[allowdiff][skill] == 0} {
    cun_pot {%1};
  };
  #ELSE {
    #SWITCH {"$conf[potpurpose]"} {
      #CASE {"learn"} {
        checklearn {%1};
      };
      #CASE {"lingwu"} {
        checklingwu {%1};
      };
      #CASE {"auto"} {
        #IF {@isNeedAutoFullSkill{} == 1} {
          checklingwu {%1}
        };
        #ELSE {
          cun_pot {%1}
        };
      };
      #DEFAULT {cun_pot {%1}};
    };
  };
};
#NOP {检查学习，%1:后续指令，技能等级220以后自动转为领悟};
#ALIAS {checklearn} {
  #IF {"$hp[master][name]" == ""} {
    cun_pot {checkpot {%1}}
  };
  #ELSEIF {"@getLearnSkill{}" == ""} {
    #IF {$hp[max_lv] >= 220} {
      #VARIABLE {conf[potpurpose]} {lingwu};
      checkpot {%1};
    };
    #ELSE {
      cun_pot {checkweapon {%1}}
    };
  };
  #ELSE {
    joblog {开始学习。} {技能};
    golearn {joblog {学习结束。} {技能};checkweapon {%1}}
  };
};

#NOP {检查领悟，%1:后续指令。这里检查是否有任何技能等级差超过设定的脱节等级，实际练习时进练到涨级，熟练度均通过任务间隙练习};
#ALIAS {checklingwu} {
  #NOP {清除临时挂起的技能};
  clearsuspendskills;
  #IF {"@getLianSkill{}" != ""} {
    golian {%1} {1};
  };
  #ELSEIF {"@getLingwuSkill{}" == ""} {
    cun_pot {%1}
  };
  #ELSEIF {$hp[pot] < 2000} {
    qu_pot {checklingwu {%1}};
  };
  #ELSE {
    joblog {开始领悟练习。} {技能};
    golingwu {joblog {领悟练习结束。} {技能};checkweapon {%1}}
  };
};

#NOP {检查任务奖励,%1:后续指令};
#NOP {一封襄阳秘函(Xiangyang mihan)};
#ALIAS {checkreward} {
  #VARIABLE {itemcat} {0};
  #VARIABLE {itemname} {};
  #VARIABLE {zjqty} {0};
  #CLASS checkclass KILL;
  #CLASS checkclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkreward\"|你设定checkreward为反馈信息}} {
    #VARIABLE {idle} {0};
    #FOREACH {*id[things][]} {t} {
      #IF {@contains{{common[jade]}{$t}} > 0} {
        #VARIABLE {itemcat} {1};
        #VARIABLE {itemname} {$t};
        #BREAK;
      };
      #ELSEIF {@contains{{common[valuable]}{$t}} > 0} {
        #IF {"$t" != "tianqi" || ("$t" == "tianqi" && @carryqty{tianqi} > 0)} {
          #VARIABLE {itemcat} {2};
          #VARIABLE {itemname} {$t};
          #BREAK;
        };
      };
      #ELSEIF {@contains{{common[garbage]}{$t}} > 0} {
        #VARIABLE {itemcat} {3};
        #VARIABLE {itemname} {$t};
        #BREAK;
      };
      #ELSEIF {(@endWiths{{$id[things][$t][name]}{丹}} == 1 || @endWiths{{$id[things][$t][name]}{丸}} == 1) && @contains{{common[normaldrugs]}{$t}} == 0 && @contains{{common[raredrugs]}{$t}} == 0} {
        #VARIABLE {itemcat} {7};
        #VARIABLE {itemname} {$t};
        #BREAK;
      };
      #ELSEIF {"$t" == "jin he"} {
        #VARIABLE {itemcat} {3};
        #VARIABLE {itemname} {$t};
        #BREAK;
      };
      #ELSEIF {"$t" == "xiangyang mihan"} {
        #VARIABLE {itemcat} {5};
        #BREAK;
      };
      #ELSEIF {"$t" == "menggu mihan"} {
        #VARIABLE {itemcat} {6};
        #BREAK;
      };
      #ELSEIF {@endWiths{{$t}{'s book}} > 0} {
        #VARIABLE {itemcat} {8};
        #VARIABLE {itemname} {$t};
        #BREAK;
      };
      #ELSEIF {@endWiths{{$t}{'s zhuanji}} > 0} {
        #VARIABLE {zjqty} {@eval{$zjqty + $id[things][$t][qty]}};
      };
    };
    #CLASS checkclass KILL;
    #SWITCH {$itemcat} {
      #CASE {1} {
        checkjade {$itemname} {checkreward {%1}};
      };
      #CASE {2} {
        storethings {$itemname} {checkreward {%1}};
      };
      #CASE {3} {
        drop $itemname;
        checkreward {%1};
      };
      #CASE {4} {
        checkjinhe {checkreward {%1}};
      };
      #CASE {5} {
        checkxymihan {checkreward {%1}};
      };
      #CASE {6} {
        checkmgmihan {checkreward {%1}};
      };
      #CASE {7} {
        #IF {"$itemname" == "yuji wan"} {
          fu $itemname
        };
        #ELSE {
          eat $itemname;
        };
        checkreward {%1};
      };
      #CASE {8} {
        read $itemname;
        drop $itemname;
        checkreward {%1};
      };
      #DEFAULT {
        %1
      };
    };
  };
  #CLASS checkclass CLOSE;
  i;
  echo {checkreward};
};
#NOP {检查携带的金钱，%1:后续指令，仅在急速模式下有效};
#ALIAS {checkmoney} {
  #IF {"$conf[extrememode]" == "1" && @isNeedDeposit{} == 1} {
    #LOCAL {bank} {@getNearestBank{}};
    gotodo {$bank[city]} {$bank[room]} {balance {%1}}
  };
  #ELSE {
    %1
  };
};
#NOP {其他检查,%1:后续指令};
#ALIAS {checkother} {
  #IF {@carryqty{fire} == 0} {
    buyfire {checkother {%1}};
  };
  #ELSEIF {@carryqty{xiong huang} == 0} {
    buyxionghuang {checkother {%1}};
  };
  #ELSEIF {"$env[wudujiao]" == "RSP" && @elapsed{$env[wdjrsp]} > 1200} {
    feedbackwdj {checkother {%1}};
  };
  #ELSEIF {"$env[wudujiao]" != "RSP" && @carryqty{tie jiang} > 0} {
    doquit;
  };
  #ELSEIF {"$hp[condition][双倍经验]" == "" && "$conf[autoexchange]" == "" && "$conf[autoexchange][lockexp]" == "" && $conf[allowdiff][neili] > 0 && @eval{$hp[neili_limit] - $hp[neili_max]} >= $conf[allowdiff][neili]} {
    fullneili {checkother {%1}};
  };
  #ELSEIF {"$hp[condition][双倍经验]" == "" && $conf[allowdiff][jingli] > 0 && @eval{$hp[jingli_limit] - $hp[jingli_max]} >= $conf[allowdiff][jingli]} {
    fulljingli {checkother {%1}};
  };
  #ELSEIF {$env[naked] == 1} {
    buycloth {checkother {%1}};
  };
  #ELSEIF {$env[waitresponse] == 1} {
    responsewait {checkother {%1}};
  };
  #ELSE {
    checkfestival {checkquest {checkknow {checkstock {%1}}}};
  };
};
#NOP {检查知识技能,%1:后续指令};
#ALIAS {checkknow} {
  #IF {$hp[exp] > 165000 && @getSkillLevel{literate} < 80 && @getSkillLevel{literate} < @eval{$hp[int_xt] * 10} && $hp[balance] > 50} {
    goliterate {%1} {82}
  };
  #ELSEIF {$hp[exp] > 800000 && @getSkillLevel{literate} < 150 && @getSkillLevel{literate} < @eval{$hp[int_xt] * 10} && $hp[balance] > 200} {
    goliterate {%1}
  };
  #ELSEIF {$hp[exp] > 2000000 && @getSkillLevel{literate} < @eval{($hp[int_xt] - 1) * 10} && $hp[balance] > 200} {
    goliterate {%1}
  };

 #NOP  #ELSEIF {$hp[exp] > 2000000 && @eval{$kungfu[know][medicine][lv]} < 121} {
 #NOP    fullmedicine {%1};
 #NOP  };
  #NOP #ELSEIF {$hp[exp] > 2000000 && @eval{$kungfu[know][jingmai-xue][lv]} < 121} {
  #NOP   fulljingmai {%1};
  #NOP };
  #ELSEIF {($hp[exp] > 500000 && @eval{$kungfu[know][qimen-bagua][lv]} < 51) || ($hp[exp] > 2000000 && @eval{$kungfu[know][qimen-bagua][lv]} < 151)} {
    fullqmbg {%1}
  };
  #ELSE {
    checkshen {%1}
  };
};
#NOP {检查一下神,%1:下一步};
#ALIAS {checkshen} {
  #IF {"$hp[party]" == "桃花岛" && $hp[shen_num] > 180000 && $hp[shen_num] < 200000} {
    gojiaohui {checkshen {%1}};
  };
  #ELSEIF {"$hp[party]" == "桃花岛" && $hp[shen_num] >= 200000} {
    #NOP {先去降神};
    #IF {"$hp[shen]" == "正气"} {
      gofshen {0} {checkshen {%1}}
    };
    #ELSE {
      gozshen {0} {checkshen {%1}}
    };
  };
  #ELSEIF {@contains{{common[zsjob]}{@getNextJob{}}} > 0 && ($hp[shen] == "戾气" || $hp[shen_num] < 10000)} {
    gozshen {10000} {checkshen {%1}}
  };
  #ELSEIF {"$hp[shen]" == "戾气" && $hp[shen_num] >= 30000 && @hasZhengqiForce{} == 1} {
    #NOP {负神任务如果有需要练习的依赖正气的技能，则保持戾气处于较低水平};
    #IF {@carryqty{zhengqi dan} == 0} {
      buymedicine {zhengqi dan} {4} {checkshen {%1}}
    };
    #ELSE {
      fu zhengqi dan;
      i;
      %1
    };
  };
  #ELSEIF {"$conf[drugdealer][id]" != "" && "$conf[drugdealer][room]" != "" && @carryqty{zhengqi dan} < 4} {
    godrugdealer {%1}
  };
  #ELSE {
    %1
  };
};
#NOP {检查解谜,%1:后续指令,%2:执行解谜前的准备指令};
#ALIAS {checkquest} {
  #LOCAL {availablequests} {@getAvailableQuests{}};
  #IF {&availablequests[] == 0 || @isSepcialJob{} == 1 || $hp[tongbao] < 100} {
    %1
  };
  #ELSE {
    %2;
    quest_start {$availablequests[+1]} {checkquest {%1}}
  };
};

#NOP {检查副本,%1:后续指令,%2:执行副本前的准备指令};
#ALIAS {checkfuben} {
  #LOCAL {availablequests} {@getAvailableQuests{}};
  #IF {&availablequests[] == 0 || @isSepcialJob{} == 1 || $hp[tongbao] < 100} {
    %1
  };
  #ELSE {
    %2;
    quest_start {$availablequests[+1]} {checkquest {%1}}
  };
};


#NOP {检查节日礼物,%1:后续指令};
#ALIAS {checkfestival} {
  #LOCAL {fts} {@getAvailableFestival{}};
  #IF {$fts == 0 || $hp[exp] < 1000000} {
    %1;
  };
  #ELSE {
    getgift {$common[festivals][$fts]} {%1};
  };
};

#NOP {检查buff};
#ALIAS {checkbuff} {
  #IF {"$env[powerup]" != "YES" && "$conf[nanny][buffer]" != "" && @elapsed{$env[buffts]} > 300} {
    buff_call {%1};
  };
  #ELSE {
    selfbuff_call {%1}
  };
};
#NOP {检查自动换内力,%1:后续指令,默认大于100m才开始};
#ALIAS {checkexchange} {
  #NOP {默认100m后才能兑换，整体原则是兑换经验后不得出现技能等级高于最大等级的情况，此逻辑在exchangeneili里面进行限制};
  #LOCAL {neilidiff} {@eval{$hp[neili_limit] - $hp[neili_max]}};
  #LOCAL {exthreshold} {@eval{$conf[autoexchange][threshold]}};
  #IF {$hp[exp] > 100000000 && "$conf[autoexchange][state]" == "1" && $exthreshold >= 100 && $neilidiff >= $exthreshold} {
    exchangeneili {$exthreshold} {%1}
  };
  #ELSE {
    %1
  };
};
#NOP {检测是否有重连请求,%1:后续指令};
#ALIAS {checkreboot} {
  #IF {$env[restart] == 1} {
    gotodo {扬州城} {杂货铺} {#zap}
  };
  #ELSEIF {$env[reboot] == 1} {
    #VARIABLE {env[reboot]} {0};
    #IF {"$env[invalidmodule]" != ""} {
      reload $env[invalidmodule];
      #VARIABLE {env[invalidmodule]} {};
    };
    #ELSE {
      loadmodules;
      #VARIABLE {env[loginmode]} {1};
      preparestart
    };
    
  };
  #ELSEIF {$env[countdown] > 0 && $env[countdown] <= 5 && "$conf[autoexchange][state]" == "auto"} {
    exchangeneili {} {%1}
  };
  #ELSEIF {$env[countdown] > 0 && $env[countdown] <= 3} {
    waitreboot;
  };
  #ELSE {
    %1;
  };
};
#NOP {杂货铺是否需要盘点,%1:后续指令};
#ALIAS {checkstock} {
  #LOCAL {lastpd} {@eval{$env[pdts]}};
  #IF {@elapsed{$lastpd} > 8640000} {
    resetstock {%1}
  };
  #ELSE {
    %1
  };
};
#NOP {解析任务地址};
#NOP {%1:要解析地址};
#NOP {%2:成功后的指令};
#NOP {%3:失败后执行的指令，默认为jobfangqi};
#NOP {%4:额外搜索的范围};
#NOP {%5:是否按照距离对房间进行排序};
#NOP {%6:是否是短描述匹配};
#NOP {武当任务，应按距离进行搜索，否则极可能路过NPC所在房间导致失败，雪山任务就可以优先访问同名房间，然后再搜索附近房间，无需排序};
#ALIAS {parsejoblocation} {
  #VARIABLE {faildo} {jobfangqi};
  #IF {"%3" != ""} {
    #VARIABLE {faildo} {%3};
  };
  #IF {"%1" == "峨嵋山土路"} {
    #VARIABLE {tempcity} {峨嵋山};
    #VARIABLE {temproom} {土路};
  };
  #ELSEIF {"%1" == "回疆草原小路"} {
    #VARIABLE {tempcity} {回疆};
    #VARIABLE {temproom} {小路};
  };
  #ELSE {
    parseaddress {%1} {%6};
  };
  
  #IF {"$tempcity" == "" || "$temproom" == ""} {
    log {未能解析地址%1} {map};
    #SHOWME {<faa>未能解析地址%1};
    $faildo
  };
  #ELSE {
    #VARIABLE {jobcity} {$tempcity};
    #VARIABLE {jobroom} {$temproom};
    #NOP {几个特殊区域};
    #NOP {桃花岛};
    #IF {"$jobcity" == "桃花岛" && @getSkillLevel{qimen-bagua} < 120} {
      $faildo
    };
    #ELSE {
      #VARIABLE {jobroomlist} {@findRooms{{$tempcity}{$temproom}}};
      #IF {"%1" == "峨嵋山土路"} {
        #LIST {jobroomlist} {create} {1054};
      };
      excludeRooms;
      
      #IF {"%4" != ""} {
        #VARIABLE {jobroomlist} {@getNearRoomsEx{{jobroomlist}{%4}}}
      };
      excludeRooms;
      #NOP {桃源县的情况比较特殊，这里默认采用按距离访问};
      #IF {"%5" != "" || @startWiths{{%1}{桃源县}}} {
        sortroomlistex
      };
      #IF {&jobroomlist[] == 0} {
        log {未能找到房间$tempcity $temproom} {map};
        #SHOWME {<faa>未能找到房间$tempcity $temproom};
        $faildo
      };
      #ELSE {
        %2;
      };
    };
  };
};
#NOP {排除特定房间，包括预设的受限访问区域以及仅特定性别、门派或解谜才能访问的区域，具体参考sjmap.tin中房间描述的roomdata域};
#ALIAS excludeRooms {
  #NOP {排除标识性别或无法访问的房间};
  #VARIABLE {invalidroom} {};
  #FOREACH {$jobroomlist[]} {r} {
    #LOCAL {roomcity} {@getRoomInfo{{$r}{ROOMAREA}}};
    #LOCAL {roomflag} {@getRoomInfo{{$r}{ROOMDATA}}};
    #IF {"$roomflag" != ""} {
      #VARIABLE {invalidroom[$r]} {};
    };
    #IF {@caontains{{unreachrooms[$hp[party]]}{$r} > 0}} {
      #VARIABLE {invalidroom[$r]} {};
    };
    #NOP {玄铁剑无法去襄阳树林};
    #IF {($env[yangpass] == 1 || @eval{$kungfu[spec][xuantie-jianfa][lv]} > 0) && @contains{{unreachrooms[xuantie-jianfa]}{$r}} > 0} {
      #VARIABLE {invalidroom[$r]} {};
    };
  };
  #NOP {特殊区域开放判定,直接从房间列表中删除房间};
  #LOCAL {allow} {@isZoneCan{$jobcity}};
  #IF {$allow == 0} {
    #LOCAL {tempzone} {$common[limitedzone][$jobcity]};
    #IF {"$tempzone[range]" != ""} {
      #LIST {roomranges} {create} {$tempzone[range]};
      #FOREACH {$jobroomlist[]} {r} {
        #IF {@contains{{roomranges}{$r}} > 0} {
          #VARIABLE {invalidroom[$r]} {};
        };
      };
    };
    #ELSE {
      #FOREACH {$jobroomlist[]} {r} {
        #VARIABLE {invalidroom[$r]} {};
      };
    };
  };
  #FOREACH {*invalidroom[]} {r} {
    #LIST {jobroomlist} {delete} {@contains{{jobroomlist}{$r}}};
  };
};

#NOP {对roomlist按照与当前房间的距离进行排序,这样可以避免因路过房间导致武当任务失败的情况出现};
#ALIAS {sortroomlist} {
  #VARIABLE {temproomlist} {$jobroomlist};
  #NOP {因为tintin地图的查询所得的距离权重貌似是坐标距离,这个和实际地图的结构有关,并不能真实反馈房间之间的路径};
  #NOP {所以这里通过自己的排序算法进行排序};
  #NOP {临时路径序列};
  #LOCAL {temppathlist} {};
  #NOP {距离排序序列};
  #LOCAL {tempdislist} {};
  #FOREACH {$temproomlist[]} {r} {
    #LOCAL {tempdistance} {@getRoomDistance{$r}};
    #LOCAL {temppathlist[$r]} {$tempdistance};
    #LOCAL {tempdislist[$tempdistance]} {$r};
  };
  #LIST {jobroomlist} {clear};
  #NOP {按照路径长度从近至远将房间加入列表};
  #NOP {按照距离序列循环,依次将距离相等的房间加入到列表};
  #FOREACH {*tempdislist[]} {d} {
    #FOREACH {*temppathlist[]} {r} {
      #IF {$temppathlist[$r] == $d} {
        #LIST {jobroomlist} {add} {$r}
      };
    };
  };
  #IF {$__DEBUG__ == 1} {
    #SHOWME {<ffa>$jobroomlist};
  };
};
#NOP {对roomlist按照与当前房间的距离进行排序,这样可以避免因路过房间导致武当任务失败的情况出现};
#ALIAS {sortroomlistex} {
  #VARIABLE {temproomlist} {};
  #FOREACH {$jobroomlist[]} {r} {
    #VARIABLE {temproomlist[$r]} {
      {roomid} {$r}
      {distance} {@getRoomDistance{$r}}
    };
  };
  #NOP {进行排序};
  #VARIABLE {temproomlist} {@sort{{temproomlist}{distance}}};
  #LIST {jobroomlist} {clear};
  #LOOP 1 &temproomlist[] {i} {
    #LIST {jobroomlist} {add} {$temproomlist[+$i][roomid]};
  };
  #IF {$__DEBUG__ == 1} {
    #SHOWME {<ffa>$jobroomlist};
  };
};

#NOP {获取目标房间路径距离,%1:目标房间};
#FUNCTION getRoomDistance {
  #LOCAL {distance} {0};
  #LOCAL {tempath} {@getWalkPath{{$roomid}{%1}}};
  #IF {$__DEBUG__ == 1} {
    #SHOWME {<ffa>房间%1路径为:$tempath};
  };
  #LIST {tempstamps} {create} {$tempath};
  #FOREACH {$tempstamps[]} {s} {
    #IF {@startWiths{{$s}{river_}} == 1} {
      #MATH {distance} {$distance + 10};
    };
    #ELSEIF {@startWiths{{$s}{matrix_}} == 1} {
      #MATH {distance} {$distance + 10};
    };
    #ELSEIF {@startWiths{{$s}{killnpc}} == 1} {
      #MATH {distance} {$distance + 10};
    };
    #ELSEIF {@startWiths{{$s}{night_}} == 1} {
      #MATH {distance} {$distance + 10};
    };
    #ELSE {
      #MATH {distance} {$distance + 1};
    };
  };
  #RETURN {$distance};
};

#NOP {继续搜索下一个房间,%1:到达房间后动作,%2:结束的动作,%3:是否对剩下房间进行排序};
#ALIAS {jobnextroom} {
  #NOP {处理未完善的收费房间错误};
  #CLASS jobwalkclass KILL;
  #CLASS jobwalkclass OPEN;
  #ACTION {^%*想白住} {
    #CLASS jobwalkclass KILL;
    log {未处理的收费房间$room -> $jobroomlist[+1]} {maperror};
    stopwalk;
    #DELAY {2} {
      jobnextroom {%1} {%2} {%3};
    };
  };
  #CLASS jobwalkclass CLOSE;
  #IF {&jobroomlist[] == 0} {
    #CLASS jobwalkclass KILL;
    #CLASS jobdoclass KILL;
    #CLASS jobcheckclass KILL;
    #CLASS jobfightclass KILL;
    #IF {"%2" != ""} {
      %2
    };
    #ELSE {
      jobfangqi
    };
  };
  #ELSE {
    #VARIABLE {walkstoppedts} {0};
    #IF {"%3" != ""} {
      sortroomlistex
    };
    #VARIABLE {nextroom} {$jobroomlist[+1]};
    #LIST {jobroomlist} DELETE {1};
    #NOP {这里判断一下玄铁剑树林问题};
    #IF {$env[yangpass] == 1 && @contains{{unreachrooms[xuantie-jianfa]}{$nextroom}} > 0} {
      jobnextroom {%1} {%2} {%3}
    };
    #ELSE {
      #LOCAL {nextcity} {@getRoomInfo{{$nextroom}{ROOMAREA}}};
      #IF {"%1" != ""} {
        gotodo {$nextcity} {$nextroom} {%1};
      };
      #ELSE {
        gotodo {$nextcity} {$nextroom} {$aimdo};
      };
    };
  };
  #ELSE {
    #SHOWME {<faa>停止标志};
  };
};
#NOP {无目的搜索,%1:搜索动作,%2:失败动作,%3:目标房间,%4:可搜索步数};
#ALIAS {job_wander} {
  #MATH {wanderstep} {@eval{%4 -1}};
  #CLASS jobwalkclass KILL;
  #CLASS jobwalkclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkwander\"|你设定checkwander为反馈信息}} {
    #CLASS jobwalkclass KILL;
    #IF {$wanderstep <= 0 || "$room" != "%3"} {
      %2
    };
    #ELSE {
      $roomexits[@rnd{{1}{&roomexits[]}}];
      #DELAY {0.5} {%1}
    };
  };
  #CLASS jobwalkclass CLOSE;
  echo {checkwander}
};
#NOP {检查房间危险npc，%1:后续指令。为了尽量降低风险优先杀死NPC,%2:是否不等待perform cd};
#NOP {清理完成后尝试normalbuff并等待释放技能的CD};
#ALIAS {checkdanger} {
  #VARIABLE {performcd} {0};
  #VARIABLE {dangeflag} {0};
  #CLASS dangercheckclass KILL;
  #CLASS dangercheckclass OPEN;
  #ACTION {^%*{「啪」的一声倒在地上|惨嚎一声|凄惨的嚎了几声|啪的一声断成两截|被你一掌挥落|抽搐两下}} {
    #IF {@contains{{common[dangernpcs]}{%%1}} > 0} {
      id here;
      echo {checkdanger};
    };
  } {2};
  #ACTION {^这里不准战斗，也不准打坐。} {
    #CLASS dangercheckclass KILL;
    %1;
  };
  #ACTION {^{设定环境变量：action \= \"checkdanger\"|你设定checkdanger为反馈信息}} {
    resonate {checkdanger};
    #VARIABLE {idle} {0};
    #VARIABLE {dangeflag} {0};
    #VARIABLE {dangernpc} {};
    #VARIABLE {dangerpfm} {0};
    #VARIABLE {abortflag} {0};
    #FOREACH {*roomthings[]} {npc} {
      #NOP {判定房间的NPC是否需要处理，是否需要杀掉};
      #LOCAL {blocker} {$common[blocker][$roomthings[$npc][+1]]};
      #IF {"$blocker" != ""} {
        #NOP {kos，一般是对其他门派叫杀};
        #IF {"$blocker[kos]" != "" && "$blocker[party]" != "$hp[party]"} {
          #VARIABLE {dangeflag} {1};
        };
        #NOP {同门相帮};
        #IF {"%2" != "" && "$blocker[party]" != "" && "$blocker[party]" != "%2"} {
          #VARIABLE {dangeflag} {1};
        };
        #IF {$dangeflag == 1 && $blocker[passexp] > $hp[exp]} {
          #NOP {打不过直接溜};
          #VARIABLE {abortflag} {1};
        };
        #IF {$blocker[performexp] == 0 || $blocker[performexp] > $hp[exp]} {
          #VARIABLE {dangerpfm} {1};
        };
      };
      #ELSEIF {@contains{{common[dangernpcs]}{$npc}} > 0} {
        #VARIABLE {dangeflag} {1};
      };
      #IF {$dangeflag == 1} {
        #VARIABLE {dangernpc} {$roomthings[$npc][+1]};
        #BREAK;
      };
    };
    #IF {$abortflag == 1} {
      #CLASS dangercheckclass KILL;
      doabort
    };
    #ELSEIF {$dangeflag == 1} {
      createpfm;
      #IF {$dangerpfm == 1} {
        openwimpy;
      };
      kill $dangernpc
    };
    #ELSE {
      ensure {
        pfm_wuxing;
        pfm_buff_normal;
        autopfm
      } {checkcd} {2}
    };
  } {1};
  #ACTION {^你聚气于丹田，冥想凌波微步帛卷中所绘步法} {
    #VARIABLE {performcd} {1};
  };
  #ACTION {^你施展出幻阴步，身法变得飘忽异常} {
    #VARIABLE {performcd} {1};
  };
  #ACTION {^你屏气凝神，口中默念「%*」的玉女心经正反要诀} {
    #VARIABLE {performcd} {1};
  };
  #ACTION {^%*目前不能施用外功} {
    #VARIABLE {performcd} {1};
  };
  #ACTION {^你现在不能激发特殊技能} {
    #VARIABLE {performcd} {1};
  };
  #ACTION {^你上一个动作还没有完成} {
    #VARIABLE {performcd} {1};
  };
  #ACTION {^「%*」只能{对|在}战斗中} {
    #NOP {纯粹为了替代fight.tin的全局触发};
  } {1};
  #ACTION {^你只能{对|在}战斗中%*使用「} {
    #NOP {纯粹为了替代fight.tin的全局触发};
  } {1};
  #ACTION {^{设定环境变量：action \= \"checkcd\"|你设定checkcd为反馈信息}} {
    resonate {checkcd};
    #VARIABLE {idle} {0};
    #IF {$performcd != 0 && "%2" == ""} {
      #DELAY {1} {
        #VARIABLE {performcd} {0};
        pfm_wuxing;
        pfm_buff_normal;
        $conf[pfm][attack];
        echo {checkcd};
      };
    };
    #ELSE {
      #CLASS dangercheckclass KILL;
      #NOP {如果有设置武器战斗前装备};
      %1
    };
  };
  #CLASS dangercheckclass CLOSE;
  ensure {wwp;id here} {checkdanger} {2}
};
#NOP {检查并等待performcd,%1:后续指令};
#ALIAS {checkperformcd} {
  #VARIABLE {checkcount} {0};
  #VARIABLE {performcd} {0};
  #VARIABLE {maxcount} {3};
  #IF {"%1" != ""} {
    #VARIABLE {maxcount} {@eval{%1}};
  };
  #CLASS dangercheckclass KILL;
  #CLASS dangercheckclass OPEN;
  #ACTION {^你聚气于丹田，冥想凌波微步帛卷中所绘步法} {
    #VARIABLE {performcd} {1};
  };
  #ACTION {^你施展出幻阴步，身法变得飘忽异常} {
    #VARIABLE {performcd} {1};
  };
  #ACTION {^你屏气凝神，口中默念「%*」的玉女心经正反要诀} {
    #VARIABLE {performcd} {1};
  };
  #ACTION {^%*目前不能施用外功} {
    #VARIABLE {performcd} {1};
  };
  #ACTION {^你现在不能激发特殊技能} {
    #VARIABLE {performcd} {1};
  };
  #ACTION {^你现在不能激发特殊技能} {
    #VARIABLE {performcd} {1};
  };  
  #ACTION {^你微一凝神，运起九阳神功，只见你的脸色变得红润多了。} {
    #VARIABLE {performcd} {1};
  };
  #ACTION {^「%*」只能对战斗中的对手} {
    #NOP {纯粹为了替代fight.tin的全局触发};
  } {1};
  #ACTION {^你只能对战斗中的对手使用「} {
    #NOP {纯粹为了替代fight.tin的全局触发};
  } {1};
  #ACTION {^{设定环境变量：action \= \"checkcd\"|你设定checkcd为反馈信息}} {
    resonate {checkcd};
    #VARIABLE {idle} {0};
    #MATH {checkcount} {$checkcount + 1};
    #IF {$performcd != 0 && $checkcount < $maxcount} {
      #DELAY {1} {
        #VARIABLE {performcd} {0};
        pfm_wuxing;
        pfm_buff_normal;
        autopfm;
        echo {checkcd};
      };
    };
    #ELSE {
      #CLASS dangercheckclass KILL;
      #NOP {如果有设置武器战斗前装备};
      %1
    };
  };
  #CLASS dangercheckclass CLOSE;
  ensure {
    pfm_wuxing;
    pfm_buff_normal;
    autopfm
  } {checkcd} {2}
};
#NOP {获取任务NPC描述};
#FUNCTION getJobNpc {
  #LOCAL {_npcstr} {};
  #SWITCH {"$currentjob"} {
    #CASE {"长乐帮"} {
      #LOCAL {_npcstr} {$jobnpc_member};
    };
    #CASE {"华山"} {
      #LOCAL {_npcstr} {$jobnpc_mmr};
    };
    #CASE {"华山2"} {
      #LOCAL {_npcstr} {$jobnpc_mmr};
    };
    #CASE {"送信"} {
      #LOCAL {_npcstr} {$jobnpc_receiver};
    };
    #CASE {"送信2"} {
      #LOCAL {_npcstr} {$jobnpc_receiver $jobnpc_killer_desc};
    };
    #CASE {"雪山"} {
      #LOCAL {_npcstr} {};
    };
    #CASE {"武当"} {
      #LOCAL {_npcstr} {$jobnpc_bastard_title $jobnpc_bastard $jobnpc_skill $jobnpc_desc};
    };
    #CASE {"嵩山"} {
      #LOCAL {_npcstr} {$jobnpc_songshan};
    };
    #CASE {"天地会"} {
      #LOCAL {_npcstr} {$jobnpc_gm};
      #IF {"$jobnpc_hero" != ""} {
        #LOCAL {_npcstr} {$jobnpc_gm -> $jobnpc_hero【已杀死 $jobnpc_count 个侍卫($jobnpc_supercount)】};
      };
    };
    #CASE {"官府"} {
      #LOCAL {_npcstr} {$jobnpc_wanted};
    };
    #CASE {"做菜"} {
      #LOCAL {_npcstr} {$jobitem_zuoai $jobnpc_zuocai};
    };
  };
  #RETURN {$_npcstr};
};

#NOP {获取任务地址描述};
#FUNCTION getJobLocation {
  #LOCAL {_locstr} {};
  #SWITCH {"$currentjob"} {
    #CASE {"天地会"} {
      #LOCAL {_locstr} {$joblocation};
      #IF {"$joblocation2" != ""} {
        #LOCAL {_locstr} {$joblocation -> $joblocation2};
      };
    };
    #DEFAULT {
      #LOCAL {_locstr} {$joblocation};
    };
  };
  #RETURN {$_locstr};
};

#NOP {获取NPC技能和描述信息};
#FUNCTION getKillerNpc {
  #LOCAL {_npcstr} {};
  #SWITCH {"$currentjob"} {
    #CASE {"华山2"} {
      #LOCAL {_npcstr} {$jobnpc_mmr};
    };
    #CASE {"送信"} {
      #LOCAL {_npcstr} {$jobnpc_receiver};
    };
    #CASE {"送信2"} {
      #LOCAL {_npcstr} {使用【$jobnpc_killer_skill】描述为【$jobnpc_killer_desc】的杀手};
    };
    #CASE {"雪山"} {
      #IF {$daneiflag == 0} {
        #LOCAL {_npcstr} {来自【$jobnpc_guard_party】的【$jobnpc_guard_name】保镖};
      };
      #ELSE {
        #LOCAL {_npcstr} {来自【$jobnpc_guard_party】的【大内高手】【$jobnpc_guard_name】保镖}
      };
    };
    #CASE {"武当"} {
      #LOCAL {_npcstr} {使用【$jobnpc_skill】描述为【$jobnpc_desc】的$jobnpc_bastard_title};
    };
    #CASE {"嵩山"} {
    };
    #CASE {"天地会"} {
    };
  };
  #RETURN {$_npcstr};
};

#NOP {获取等待时startfull的类型，%1:等待时间};
#NOP {根据当前的busy时间结合每次打坐耗时给出一个最优方案，即尽可能的早接任务};
#NOP {返回1:仅打坐一次，2:打坐至增长};
#FUNCTION getWaitfullType {
  #NOP {计算当前状态下打坐增长内力继续打坐至full状态需要多少点};
  #LOCAL {points} {@eval{$hp[neili_max] * $conf[neilithreshold] / 100 + $hp[neili_max] - $hp[neili]}};
  #NOP {计算打坐所需时间};
  #LOCAL {dztimes} {@eval{$points / $dazuo_point + 1}};
  #LOCAL {dzelapsed} {@eval{$dztimes * $env[dzduration]}};
  #IF {%1 >20 && %1 >= $dzelapsed} {
    #RETURN {2};
  };
  #ELSE {
    #RETURN {1};
  };
};
#NOP {==============================================通用扩展接口==============================================开始};
#NOP {任务间神准备,%1:预期要做的任务};
#NOP {此功能主要用于处理在任务进行中预先处理下个任务需要的神要求，这里仅限制于自动吃药，更为复杂的操作在下面定义的before_go进行处理};
#ALIAS {on_prepare_shen} {
  #IF {@contains{{common[zsjobs]}{%1}} > 0 && ("$hp[shen]" == "戾气" || $hp[shen_num] < 10000) && @carryqty{zhengqi dan} > 0} {
    fu zhengqi dan;
    hp;
    i
  };
  #ELSEIF {@contains{{common[fsjobs]}{%1}} > 0 && ("$hp[shen]" == "正气" || $hp[shen_num] < 10000) && @carryqty{xieqi wan} > 0} {
    fu xieqi wan;
    hp;
    i
  };
};
#NOP {任务间神判定,%1:预期要做的任务，用于处理超过要求的神};
#ALIAS {on_assert_shen} {
  #IF {@contains{{common[zsjobs]}{%1}} > 0 && ("$hp[shen]" == "戾气" && $hp[shen_num] >= 19000) && @carryqty{zhengqi dan} > 0} {
    fu zhengqi dan;
    hp;
    i
  };
  #ELSEIF {@contains{{common[fsjobs]}{%1}} > 0 && ("$hp[shen]" == "正气" || $hp[shen_num] >= 19000) && @carryqty{xieqi wan} > 0} {
    fu xieqi wan;
    hp;
    i
  };
};
#NOP {任务转神处理,%1:后续指令};
#NOP {扩展接口};
#NOP {当做雪山任务前，%1:后续指令};
#ALIAS {on_xueshan_before_go} {
  #NOP {检查CD};
  #IF {@eval{$hp[busy][雪山]} >= 60 && $env[pingkilled] == 0 && @elapsed{$env[pingkillts]} < 120} {
    pingkill {jobgo_xueshan}
  };
  #ELSEIF {("$hp[shen]" == "正气" || $hp[shen_num] < 10000) && ( @carryqty{xieqi wan} < 4 || @canQuickFushen{} == 0)} {
    #IF {@canQuickFushen{} == 1} {
      gofshen {10000} {on_xueshan_before_go {%1}} {1}
    };
    #ELSE {
      buymedicine {xieqi wan} {10} {on_xueshan_before_go {%1}};
    };
  };
  #ELSEIF {(@eval{$hp[busy][雪山]} >= 60 || @eval{$hp[busy][公共]} >= 60) && @isNeedBusyLingwu{} == 1} {
    #IF {"$room" != "墨玉斋"} {
      #NOP {这里等到达襄阳城在判断时间};
      cd {donext {cond} {on_xueshan_before_go {%1}}}
    };
    #ELSE {
      golingwu {jobgo_xueshan} {{busy}{雪山;公共}}
    };
  };
  #ELSEIF {@eval{$hp[busy][雪山]} >= 120} {
    on_prepare_shen {雪山};
    #LOCAL {nextjob} {@getNextJob{雪山}};
    #IF {"$nextjob" == "" || @eval{$hp[busy][$nextjob]} >= 60} {
      #LOCAL {nextjob} {@getFsTransitJob{}};
    };
    jobgo {$nextjob} {雪山}
  };
  #ELSE {
    on_prepare_shen {雪山};
    #NOP {如果采用练习白骨爪的方式可能会超出1w比较多，这里判定处理一下};
    on_assert_shen {@getNextJob{{}{雪山}}};
    %1
  };
};
#NOP {当接雪山任务前，%1:后续指令};
#ALIAS {on_xueshan_before_ask} {
  #IF {"@getExtendJob{}" != ""} {
    jobgo {@getExtendJob{}};
  };
  #ELSEIF {"$caller" != ""} {
    checkrequest {jobgo_xueshan}
  };
  #ELSE {
    on_prepare_shen {雪山};
    %1
  };
}; 
#NOP {当等待雪山任务时};
#ALIAS {on_xueshan_wait} {
  waitlian;
  #LOCAL {fulltype} {@getWaitfullType{@getConditionTick{{公共;雪山}}}};
  #IF {"$hp[shen]" == "正气" || $hp[shen_num] < 10000} {
    #IF {@carryqty{xieqi wan} == 0} {
      jobgo_xueshan
    };
    #ELSE {
      #LOCAL {fulltype} {1};
      checkrequest {gotodo {大雪山} {风见台} {startfull {jobask_xueshan} {$fulltype} {{busy}{雪山;公共}}}};
    };
  };
  #ELSE {
    checkrequest {gotodo {大雪山} {风见台} {startfull {jobask_xueshan} {$fulltype} {{busy}{雪山;公共}}}};
  };
};
#NOP {做雪山任务前,%1:后续指令};
#ALIAS {on_xueshan_go} {
  on_prepare_shen {@getNextJob{{}{雪山}}};
  %1
};
#NOP {当雪山保镖搞定后，%1:后续指令};
#ALIAS {on_xueshan_down} {
  on_prepare_shen {@getNextJob{{}{雪山}}};
  %1
};
#NOP {当雪山任务完成，%1:后续指令};
#ALIAS {on_xueshan_finish} {
  on_prepare_shen {@getNextJob{{}{雪山}}};
  %1
};
#NOP {当做武当任务前，%1:后续指令};
#ALIAS {on_wudang_before_go} {
  #IF {@eval{$hp[busy][武当]} >= 60 && $env[pingkilled] == 0 && @elapsed{$env[pingkillts]} < 120} {
    pingkill {jobgo_wudang}
  };
  #ELSEIF {"$hp[shen]" == "戾气" && $hp[shen_num] > 30000} {
    gozshen {10000} {jobgo_wudang}
  };
  #ELSEIF {("$hp[shen]" == "戾气" || $hp[shen_num] < 10000) && @carryqty{zhengqi dan} < 4} {
    buymedicine {zhengqi dan} {10} {on_wudang_before_go {%1}};
  };
  #ELSEIF {(@eval{$hp[busy][武当]} >= 60 || @eval{$hp[busy][公共]} >= 60) && @isNeedBusyLingwu{} == 1} {
    #IF {"$room" != "宝龙斋"} {
      #NOP {这里等到达襄阳城在判断时间};
      xy {donext {cond} {on_wudang_before_go {%1}}}
    };
    #ELSE {
      golingwu {jobgo_wudang} {{busy}{武当;公共}}
    };
  };
  #ELSEIF {@eval{$hp[busy][武当]} >= 120} {
    #LOCAL {nextjob} {@getNextJob{武当}};
    #IF {"$nextjob" == "" || @eval{$hp[busy][$nextjob]} >= 60} {
      #LOCAL {nextjob} {@getZsTransitJob{}};
    };
    jobgo {$nextjob} {武当}
  };
  #ELSE {
    on_prepare_shen {武当};
    %1
  };
};
#NOP {当接武当任务前，%1:后续指令};
#ALIAS {on_wudang_before_ask} {
  #IF {"@getExtendJob{}" != ""} {
    jobgo {@getExtendJob{}};
  };
  #ELSEIF {"$caller" != ""} {
    checkrequest {jobgo_wudang}
  };
  #ELSE {
    on_prepare_shen {武当};
    %1
  };
};
#NOP {当等待武当任务时};
#ALIAS {on_wudang_wait} {
  waitlian;
  #LOCAL {fulltype} {@getWaitfullType{@getConditionTick{{公共;武当}}}};
  #IF {"$hp[shen]" == "戾气" || $hp[shen_num] < 10000} {
    #IF {@carryqty{zhengqi dan} == 0} {
      jobgo_wudang
    };
    #ELSE {
      #LOCAL {fulltype} {1};
      checkrequest {gotodo {武当山} {天乙真庆宫} {startfull {jobask_wudang} {$fulltype} {{busy}{武当;公共}}}};
    };
  };
  #ELSE {
    checkrequest {gotodo {武当山} {天乙真庆宫} {startfull {jobask_wudang} {$fulltype} {{busy}{武当;公共}}}};
  };
};
#NOP {当武当NPC搞定后，%1:后续指令};
#ALIAS {on_wudang_down} {
  on_prepare_shen {@getNextJob{{}{武当}}};
  %1
};
#NOP {当武当任务完成，%1:后续指令};
#ALIAS {on_wudang_finish} {
  on_prepare_shen {@getNextJob{{}{武当}}};
  %1
};
#NOP {当做华山任务前，%1:后续指令};
#ALIAS {on_huashan_before_go} {
  %1
};
#NOP {当接华山任务前，%1:后续指令};
#ALIAS {on_huashan_before_ask} {
  #IF {"@getExtendJob{}" != ""} {
    jobgo {@getExtendJob{}};
  };
  #ELSE {
    %1;
  };
};
#NOP {当做送信任务前，%1:后续指令};
#ALIAS {on_songxin_before_go} {
  %1
};
#NOP {当接送信任务前，%1:后续指令};
#ALIAS {on_songxin_before_ask} {
  #IF {"@getExtendJob{}" != ""} {
    jobgo {@getExtendJob{}};
  };
  #ELSEIF {"$caller" != ""} {
    checkrequest {jobgo_songxin}
  };
  #ELSE {
    %1;
  };
};
#NOP {当送信任务等待时};
#ALIAS {on_songxin_wait} {
  waitlian;
  #LOCAL {fulltype} {@getWaitfullType{@getConditionTick{{公共;送信}}}};
  checkrequest {gotodo {大理城} {马房} {startfull {jobask_songxin} {$fulltype}}};
};
#NOP {当送信任务完成，%1:后续指令};
#ALIAS {on_songxin_finish} {
  %1
};
#NOP {当做天地会任务前，%1:后续指令};
#ALIAS {on_tdh_before_go} {
  #NOP {检查CD};
  #IF {@eval{$hp[busy][公共]} >= 60 && $env[pingkilled] == 0 && @elapsed{$env[pingkillts]} < 120} {
    pingkill {jobgo_tdh}
  };
  #ELSEIF {"$hp[shen]" == "戾气" && $hp[shen_num] > 30000} {
    gozshen {10000} {jobgo_tdh}
  };
  #ELSEIF {("$hp[shen]" == "戾气" || $hp[shen_num] < 10000) && @carryqty{zhengqi dan} < 4} {
    buymedicine {zhengqi dan} {4} {on_tdh_before_go {%1}};
  };
  #ELSEIF {@eval{$hp[busy][公共]} >= 60 && @isNeedBusyLingwu{} == 1} {
    on_prepare_shen {@getNextJob{{}{天地会}}};
    #IF {"$room" != "天阁斋"} {
      #NOP {这里等到达襄阳城在判断时间};
      yz {donext {cond} {on_tdh_before_go {%1}}}
    };
    #ELSE {
      golingwu {jobgo_tdh} {{busy}{公共}}
    };
  };
  #ELSE {
    on_prepare_shen {@getNextJob{{}{天地会}}};
    %1
  };
};
#NOP {当接天地会任务前，%1:后续指令};
#ALIAS {on_tdh_before_ask} {
  #IF {"@getExtendJob{}" != ""} {
    jobgo {@getExtendJob{}};
  };
  #ELSEIF {"$caller" != ""} {
    checkrequest {jobgo_xueshan}
  };
  #ELSE {
    on_prepare_shen {天地会};
    %1
  };
}; 
#NOP {当等待天地会任务时};
#ALIAS {on_tdh_wait} {
  waitlian;
  #LOCAL {fulltype} {@getWaitfullType{@getConditionTick{{公共;天地会}}}};
  #IF {"$hp[shen]" == "戾气" || $hp[shen_num] < 10000} {
    #IF {@carryqty{zhengqi dan} == 0} {
      jobgo_tdh
    };
    #ELSE {
      #LOCAL {fulltype} {1};
      checkrequest {gotodo {扬州城} {大虹桥} {startfull {jobask_tdh} {$fulltype} {{busy}{公共}}}};
    };
  };
  #ELSE {
    checkrequest {gotodo {扬州城} {大虹桥} {startfull {jobask_tdh} {$fulltype} {{busy}{公共;天地会}}}};
  };
};
#NOP {当天地会侍卫搞定后，%1:后续指令};
#ALIAS {on_tdh_down} {
  on_prepare_shen {@getNextJob{{}{天地会}}};
  %1
};
#NOP {当天地会任务完成，%1:后续指令};
#ALIAS {on_tdh_finish} {
  on_prepare_shen {@getNextJob{{}{天地会}}};
  %1
};
#NOP {当等待做菜任务时};
#ALIAS {on_zuocai_wait} {
  waitlian;
  #LOCAL {fulltype} {@getWaitfullType{@getConditionTick{{公共}}}};
  gotoroom {1865} {startfull {jobask_zuocai} {$fulltype}}
};
#NOP {==============================================颂摩崖任务扩展==============================================开始};
#NOP {当做颂摩崖任务前，%1:后续指令};
#ALIAS {on_smy_before_go} {
  #NOP {检查CD};
  #IF {@eval{$hp[busy][颂摩崖]} >= 60} {
    #NOP {去做一轮其他的};
    #LOCAL {nextjob} {@getNextJob{颂摩崖}};
    #IF {"$nextjob" == "" || @eval{$hp[busy][$nextjob]} >= 60} {
      #LOCAL {nextjob} {@getZsTransitJob{}};
    };
    jobgo {$nextjob} {颂摩崖}
  };
  #ELSEIF {$hp[max_lv] > 200 && @isNeedLianForce{{$kungfu[base][force][jifa]}{1}{0}} == 1 && $hp[pot] < 4000} {
    #NOP {打坐很多，取潜能练习};
    qu_pot {on_smy_before_go {%1}}
  };
  #ELSE {
    %1
  };
};
#NOP {当接颂摩崖任务前，%1:后续指令};
#ALIAS {on_smy_before_ask} {
  #IF {"$caller" != ""} {
    checkrequest {jobgo_smy}
  };
  #ELSE {
    %1
  };
};
#NOP {当等待颂摩崖任务时};
#ALIAS {on_smy_wait} {
  waitlian;
  #LOCAL {fulltype} {@getWaitfullType{@getConditionTick{{公共;颂摩崖}}}};
  startfull {jobask_smy} {$fulltype} {{busy}{公共;颂摩崖}}
};
#NOP {==============================================颂摩崖任务扩展==============================================结束};
#NOP {==============================================守卫襄阳任务扩展==============================================开始};
#NOP {当做守卫襄阳任务前，%1:后续指令};
#ALIAS {on_swxy_before_go} {
  #NOP {检查CD};
  #IF {@eval{$hp[busy][守卫襄阳]} >= 60} {
    #NOP {去做一轮其他的};
    #LOCAL {nextjob} {@getNextJob{守卫襄阳}};
    #IF {"$nextjob" == "" || @eval{$hp[busy][$nextjob]} >= 60} {
      #LOCAL {nextjob} {@getZsTransitJob{}};
    };
    jobgo {$nextjob} {守卫襄阳}
  };
  #ELSEIF {$hp[max_lv] > 200 && @isNeedLianForce{{$kungfu[base][force][jifa]}{1}{0}} == 1 && $hp[pot] < 4000} {
    #NOP {打坐很多，取潜能练习};
    qu_pot {on_swxy_before_go {%1}}
  };
  #ELSE {
    %1
  };
};
#NOP {当接守卫襄阳任务前，%1:后续指令};
#ALIAS {on_swxy_before_ask} {
  #IF {"$caller" != ""} {
    checkrequest {jobgo_swxy}
  };
  #ELSE {
    %1
  };
};
#NOP {当等待守卫襄阳任务时};
#ALIAS {on_swxy_wait} {
  waitlian;
  #LOCAL {fulltype} {@getWaitfullType{@getConditionTick{{公共;守卫襄阳}}}};
  startfull {jobask_swxy} {$fulltype} {{busy}{公共;守卫襄阳}}
};
#NOP {==============================================颂摩崖任务扩展==============================================结束};
#NOP {==============================================通用扩展接口==============================================结束};

#NOP {扩展接口实现};
#SHOWME {<fac>@padRight{{任务}{12}}<fac> <cfa>模块加载完毕<cfa>};