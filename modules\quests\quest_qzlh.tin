#NOP {全真连环,%1:后续指令};
#ALIAS {goquest_qzlh} {
  #VARIABLE {questmodule} {全真连环};
  gotodo {全真教} {花丛中} {qzlh_wait_book {%1}}
};
#ALIAS {qzlh_wait_book} {
  #VARIABLE {queststart} {0};
  #CLASS questclass OPEN;
  #ACTION {^你小心翼翼地藏到了花丛中。} {
  };
  #ACTION {^你还藏什么？} {
    #CLASS questclass KILL;
    runwait {
      #IF {@carryqty{ce zi} > 0} {
        gotodo {扬州城} {坟墓} {qzlh_lingwu_start {%1}}
      };
      #ELSE {
        %1
      };
    };
  };
  #ACTION {^突然间山后传来脚步声响，两个人一面说话，一面走近。原来竟是尹志平和赵志敬。两个人越说越大声，竟似在互相争辩} {
    #VARIABLE {idle} {0};
  };
  #ACTION {^只见尹志平大声怒喝，连走险招，竟然不再挡驾对方来招，一味猛攻。} {
    
  };
  #ACTION {^再拆数招，赵志敬在对方拼命下渐渐招架不住，撒腿向别处逃去。} {
    
  };
  #ACTION {^尹志平一愣，随后追去，你只听见嗒地一声，一本小册子从尹志平身上掉了下来。} {
  };
  #ACTION {^待尹志平走后，你忙起身将那小册子拣了起来。} {
    #CLASS questclass KILL;
    dohalt {
      gotodo {扬州城} {坟墓} {qzlh_lingwu_start {%1}}
    }
  };
  #CLASS questclass CLOSE;
  hide
};
#NOP {开始领悟连环,%1:后续指令};
#ALIAS {qzlh_lingwu_start} {
  #CLASS questclass OPEN;
  #ACTION {^你左思右想始终不能领悟，不由得心中大燥。} {
    #VARIABLE {idle} {0};
    yun jing;
    yun jingli;
    hp;
    read ce zi
  };
  #ACTION {^你精神不佳，无法领悟心法中的奥妙。} {
    dzn
  };
  #ACTION {^你静坐良久，似乎对三连环的运用有一丝领悟。} {
    #VARIABLE {idle} {0};
    dzn
  };
  #ACTION {^{恭喜，你终于领悟出〖三连环〗秘决的精要|你已经领悟三连环精要}} {
    #CLASS questclass KILL;
    questsuccess {$questmodule};
    startfull {%1};
  };
  #ACTION {^{$dazuo_over}} {
    #IF {$env[pray] == 0} {
      #CLASS questclass KILL;
      getpearl {gotodo {扬州城} {坟墓} {qzlh_lingwu_start {%1}}}
    };
    #ELSEIF {$hp[neili] > 1500} {
      yun jing;
      yun jingli;
      read ce zi
    };
    #ELSE {
      dzn
    };
  };
  #CLASS questclass CLOSE;
  pray pearl;
  i;
  read ce zi
};