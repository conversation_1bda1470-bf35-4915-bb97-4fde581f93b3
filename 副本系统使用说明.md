# 副本系统使用说明

## 概述

基于quest系统的设计，我为项目创建了一个完整的副本管理系统，提供了类似`checkquest`的`checkfuben`函数，可以智能地检查和执行副本任务。

## 代码注释说明

为了帮助理解系统工作原理，我在代码中添加了详细的注释：

### 1. 副本配置结构注释
每个副本的配置参数都有详细说明：
- `alias`: 执行副本的命令别名
- `intervaltime`: 时间间隔限制（秒）
- `intervalexp`: 经验间隔限制
- `skills`: 技能等级要求
- `timezone`: 游戏时间段限制
- `party/gender`: 门派/性别限制
- `done/fail/timestamp`: 状态记录字段

### 2. 检查逻辑注释
`getAvailableFubens`函数包含十层检查逻辑：
1. **基础开关检查** - 是否开启自动副本
2. **用户配置过滤** - 白名单/黑名单过滤
3. **副本定义有效性** - 配置是否存在
4. **完成状态检查** - 跳过已完成/失败的副本
5. **角色属性限制** - 门派/性别匹配
6. **技能等级要求** - 检查所需技能等级
7. **经验间隔限制** - 确保获得足够经验
8. **时间间隔限制** - 冷却时间检查
9. **游戏时间段限制** - 特定时间段执行
10. **重试延迟时间** - 失败后的等待时间

### 3. 执行流程注释
- `checkfuben`: 主入口函数，智能选择副本
- `fuben_start`: 副本开始准备工作
- `fuben_end`: 副本结束收尾工作

## 主要功能

### 1. checkfuben 函数

类似于`checkquest`，`checkfuben`函数可以智能检查副本可用性并自动执行：

```tintin
checkfuben {后续指令} {执行副本前的准备指令}
```

**使用示例：**
```tintin
# 在任务间隙检查副本
checkfuben {jobprepare} {wwp}

# 在特定条件下检查副本
checkfuben {继续其他任务} {准备挑战副本}
```

### 2. 副本配置

在配置文件中添加了副本相关配置：

```tintin
#NOP {是否开启自动副本};
#VARIABLE {conf[autofuben]} {0};

#NOP {是否显示已完成副本};
#VARIABLE {conf[displayfubensuccess]} {0};

#NOP {参与的副本};
#LIST {conf[subcribefubens]} {create} {};

#NOP {不参与的副本};
#LIST {conf[ignorefubens]} {create} {};
```

### 3. 支持的副本

目前系统支持以下副本：

1. **通天塔** - 每日挑战，使用`tt.go`命令
2. **情怀梦** - 每日挑战，使用`otherquest_qinghuaimeng`命令  
3. **情怀岛** - 每日挑战，使用`otherquest_battleship`命令

### 4. 时间管理

副本系统使用与quest相同的时间管理机制：

- **系统时间戳**：使用`@now{}`获取Unix时间戳
- **时间间隔控制**：每个副本有`intervaltime`设置（默认86400秒=24小时）
- **重试延迟**：失败后自动设置重试时间
- **游戏时间限制**：支持基于游戏内时间的执行条件

### 5. 管理命令

#### 查看副本状态
```tintin
fubeninfo
```
显示所有副本的当前状态、冷却时间等信息。

#### 重置副本状态
```tintin
# 重置单个副本
fubenreset 通天塔

# 重置所有副本
fubenresetall
```

#### 手动标记副本状态
```tintin
# 标记副本成功
fubensuccess 通天塔

# 标记副本失败
fubenfail 通天塔
```

#### 手动执行副本
```tintin
# 直接执行通天塔
tt.go {jobprepare}

# 直接执行情怀梦
otherquest_qinghuaimeng {jobprepare}
```

### 6. UI界面显示

副本信息会在游戏界面中显示，包括：
- 副本名称和状态
- 冷却时间倒计时
- 执行条件检查结果
- 当前正在执行的副本

### 7. 智能检查机制

`checkfuben`函数会检查以下条件：

1. **基础条件**：
   - 是否开启自动副本（`conf[autofuben]`）
   - 情怀币是否充足（≥100）
   - 是否正在执行特殊任务

2. **副本条件**：
   - 时间间隔是否满足
   - 经验间隔是否满足
   - 技能等级是否满足
   - 门派限制检查
   - 性别限制检查
   - 游戏时间限制检查

3. **优先级**：
   - 优先执行`subcribefubens`中配置的副本
   - 跳过`ignorefubens`中配置的副本
   - 跳过已完成或失败的副本

## 集成方式

### 在任务系统中集成

可以在任务间隙调用`checkfuben`：

```tintin
# 在jobprepare中添加
checkfuben {原来的后续任务} {副本准备指令}
```

### 在解谜系统中集成

可以与`checkquest`配合使用：

```tintin
# 先检查解谜，再检查副本
checkquest {checkfuben {其他任务} {准备副本}} {准备解谜}
```

## 配置建议

1. **新角色**：建议先设置`conf[autofuben] = 0`，手动完成副本后再开启自动化
2. **老角色**：可以使用`fubenreset`重置状态，或手动标记已完成的副本
3. **选择性执行**：使用`subcribefubens`只执行特定副本，或使用`ignorefubens`跳过某些副本

## 注意事项

1. 副本系统会自动保存状态到alias中，重启后会自动加载
2. 每个副本默认24小时冷却时间，可根据需要调整
3. 副本执行前会自动准备（如获取珍珠等）
4. 建议在稳定的网络环境下使用自动副本功能

这个副本系统完全基于现有的quest系统架构，保持了代码的一致性和可维护性。
