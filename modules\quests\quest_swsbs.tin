#NOP {古墓三无三不手,%1:后续指令};
#ALIAS {goquest_swsbs} {
  #VARIABLE {questmodule} {三无三不手};
  gotonpc {李莫愁} {swsbs_askli {%1}}
};
#NOP {问李莫愁,%1:后续指令};
#ALIAS {swsbs_askli} {
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你向李莫愁打听有关『三无三不手』的消息。} {
    #VARIABLE {idle} {0};
    #VARIABLE {askresult} {0};
    #CLASS questresponseclass OPEN;
    #ACTION {^李莫愁说道：「你不是已经学会了三无三不手么} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questsuccess {$questmodule};
      dohalt {%1};
    };
    #ACTION {^李莫愁说道：「我不是已经告诉你了么，还不快去帮我找回《五毒秘传》} {
      #NOP {意外了，可能找不到陆无双了,退出重置标志};
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      dohalt {
        #IF {"$conf[nanny][zhan<PERSON>]" == ""} {
          #SHOWME {<faa>未配置占卜信息,请手动寻找};
          %1;
        };
        #ELSE {
          swsbs_zhanbulu {%1};
        };
      };
    };
    #ACTION {^李莫愁说道：「暂时我没有这个兴趣} {
      #NOP {陆无双还在,或者房间不空，延时};
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questdelay {$questmodule} {0} {3600};
      dohalt {%1};
    };
    #ACTION {^李莫愁说道：「我那本《五毒秘传》被陆无双那小贱人给偷走了，如果你能帮我找回此书，我就传授你三无三不手} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      #NOP {找陆无双};
      dohalt {
        #IF {"$conf[nanny][zhanbu]" == ""} {
          #SHOWME {<faa>未配置占卜信息,请手动寻找};
          %1;
        };
        #ELSE {
          swsbs_zhanbulu {%1};
        };
      };
    };
    #CLASS questresponseclass CLOSE;
  };
  #CLASS questclass CLOSE;
  ask li mochou about 三无三不手;
};
#NOP {找陆无双,%1:后续指令};
#ALIAS {swsbs_zhanbulu} {
  #VARIABLE {zbflag} {0};
  #VARIABLE {zbloc} {};
  #VARIABLE {zbtimes} {0};
  #VARIABLE {startts} {0};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^没有这个人。} {
    #VARIABLE {idle} {0};
    #CLASS questclass KILL;
    %1;
  };
  #ACTION {^%*(%*)告诉你：zhanbu_wait} {
    #CLASS questclass KILL;
    #VARIABLE {idle} {0};
    #DELAY {10} {
      swsbs_zhanbulu {%1};
    };
  };
  #ACTION {^%!*(%!*)告诉你：zhanbu_come} {
    #VARIABLE {idle} {0};
    #VARIABLE {startts} {@now{}};
    echo {checkzhanbu}
  };
  #ACTION {^%!*(%!*)告诉你：zhanbu_success %*} {
    #VARIABLE {zbflag} {1};
    #VARIABLE {zbloc} {%%1};
  };
  #ACTION {^%!*(%!*)告诉你：zhanbu_fail} {
    #VARIABLE {zbflag} {2};
  };
  #ACTION {^{设定环境变量：action \= \"checkzhanbu\"|你设定checkzhanbu为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {@elapsed{$startts} > 300} {
      #CLASS questclass KILL;
      questdelay {$questmodule} {0} {7200};
      #NOP {必须quit才能重启};
      doquit;
    };
    #ELSEIF {$zbflag == 0} {
      #DELAY {2} {
        echo {checkzhanbu};
      };
    };
    #ELSEIF {$zbflag == 1} {
      #CLASS questclass KILL;
      #NOP {占卜给出的描述不定,这里通过长描述和短描述处理两遍};
      parsejoblocation {$zbloc} {swsbs_findlu {%1}} {
        parsejoblocation {$zbloc} {swsbs_findlu {%1}} {
          swsbs_zhanbulu {%1};
        } {2} {1} {1};
      } {2} {1};
    };
    #ELSEIF {$zbflag == 2} {
      #MATH {zbtimes} {$zbtimes + 1};
      #IF {$zbtimes >= 3} {
        #CLASS questclass KILL;
        questdelay {$questmodule} {0} {7200};
        #NOP {必须quit才能重启};
        doquit;
      };
      #ELSE {
        zhanbu_call {lu wushuang};
      };
    };
  };
  #CLASS questclass CLOSE;
  zhanbu_call {lu wushuang};
};
#NOP {找陆无双,%1:后续操作};
#ALIAS {swsbs_findlu} {
  wwp;
  pfm_buff_normal;
  jobnextroom {swsbs_checklu {%1}} {swsbs_zhanbulu {%1}} {1};
};
#ALIAS {swsbs_checklu} {
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^这里没有 lu wushuang} {
    #CLASS questclass KILL;
    jobnextroom {swsbs_checklu {%1}} {swsbs_zhanbulu {%1}} {1};
  };
  #ACTION {^你决定跟随陆无双一起行动} {
    kill lu wushuang;
  };
  #ACTION {^言毕，陆无双将一本书丢了过来，顺势往后跃开数步，你接过书后，当下，也不再追赶} {
    #CLASS questclass KILL;
    gotonpc {李莫愁} {swsbs_give {%1}};
  };
  #CLASS questclass CLOSE;
  follow lu wushuang;
};
#NOP {给李莫愁五毒秘传};
#ALIAS {swsbs_give} {
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你身上没有这样东西} {
    #CLASS questclass KILL;
    questdelay {$questmodule} {0} {7200};
    %1;
  };
  #ACTION {^李莫愁说道：「好吧，那我就传授你三无三不手的心法吧} {
    #CLASS questclass KILL;
    questsuccess {$questmodule};
    %1;
  };
  #CLASS questclass CLOSE;
  give wudu mizhuan to li mochou;
};