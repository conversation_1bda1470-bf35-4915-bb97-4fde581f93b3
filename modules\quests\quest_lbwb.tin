#NOP {凌波微步,%1:后续指令};
#ALIAS {goquest_lbwb} {
  #VARIABLE {questmodule} {凌波微步};
  pray pearl;
  gotodo {无量山} {内室} {lbwb_fan {%1}}
};
#ALIAS {lbwb_fan} {
  #VARIABLE {questmodule} {凌波微步};
  #VARIABLE {okflag} {0};
  #VARIABLE {ketouflag} {1};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你口中数着，恭恭敬敬的向玉像磕起头来} {
    #VARIABLE {ketouflag} {1};
  };
  #ACTION {^你看见蒲团上的薄草早已破裂，不由伸手进去，里面什么也没有，好象已经被取走了。} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^你虽然已经领悟到了凌波微步，还是忍不住回到这里，看来你已经形似痴狂。} {
    #VARIABLE {okflag} {2};
  };
  #ACTION {^你看见蒲团上的薄草早已破裂，不由伸手进去，里面好像什么也没有，你不甘心又摸索一会，果然找出一个帛卷。} {
    #VARIABLE {okflag} {3};
  };
  #ACTION {^{设定环境变量：action \= \"checkketou\"|你设定checkketou为反馈信息}} {
    #VARIABLE {idle} {0};
    #DELAY {1} {
      #IF {$ketouflag == 0} {
        #CLASS questclass KILL;
        questdelay {$questmodule} {0} {7200};
        %1;
      };
      #ELSEIF {$okflag == 1} {
        #CLASS questclass KILL;
        questdelay {$questmodule} {0} {7200};
        %1;
      };
      #ELSEIF {$okflag == 2} {
        #CLASS questclass KILL;
        questsuccess {$questmodule};
        %1;
      };
      #ELSEIF {$okflag == 3} {
        #VARIABLE {okflag} {0};
        pfm_wuxing;
        pfm_buff_normal;
        pray pearl;
        #8 fan bo juan;
        echo {checkfan};
      };
      #ELSE {
        #VARIABLE {ketouflag} {0};
        #8 ketou yuxiang;
        echo {checkketou};
      };
    };
  };
  #ACTION {^你翻到最后，不由得大为狂喜，这部分并没有被撕烂，题着“凌波微步”四字。} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^你翻到最后发现帛卷撕的乱七八糟，什么都看不清，根本无法从里面学到东西。} {
    #VARIABLE {okflag} {2};
  };
  #ACTION {^{设定环境变量：action \= \"checkfan\"|你设定checkfan为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {$okflag == 1} {
      #CLASS questclass KILL;
      questsuccess {$questmodule};
      dohalt {
        goreadbook {bo juan} {prepareskills {golian {%1}}} {} {} {lingbo-weibu} {122};
      };
    };
    #ELSEIF {$okflag == 2} {
      #CLASS questclass KILL;
      questfail {$questmodule};
      #NOP {天龙八部时间公共CD，仅对天龙营救有效};
      #IF {$questlist[天龙八部][laststep] == 2} {
        questfail {天龙八部};
      };
      dohalt {%1};
    };
    #ELSE {
      #DELAY {1} {
        #8 fan bo juan;
        echo {checkfan};
      }
    };
  };
  #CLASS questclass CLOSE;
  look yuxiang;
  look left;
  look right;
  echo {checkketou};
};