#NOP {夜晚关门房间处理};
#NOP {最大等待开门的时间};
#VARIABLE {night_wait_hours} {2};
#NOP {扬州瘦西湖珠宝店卯时二刻(6)开门,亥时一刻(21.5)关门};
#NOP {是否需要晚上关门放弃};
#FUNCTION isCanGiveUp {
  #IF {"$currentjob" == "雪山" && $jobnpc_superguard == 1} {
    #RETURN {0};
  };

  #IF {"$currentjob" == "天地会"} {
    #RETURN {0};
  };

  #RETURN {1};
};
#NOP {扬州瘦西湖};
#ALIAS {night_yz} {
  #VARIABLE {opents} {6};
  #VARIABLE {closets} {22.5};
  #VARIABLE {waithours} {$night_wait_hours};
  #IF {@isCanGiveUp{} == 0} {
    #VARIABLE {waithours} {@eval{$night_wait_hours * 10}};
  };
  #CLASS nightclass OPEN;
  #ACTION {^{$dazuo_over}} {
    yun qi;
    time;
    look;
    echo {checknight};
  };
  #ACTION {^{设定环境变量：action \= \"checknight\"|你设定checknight为反馈信息}} {
    resonate {checknight};
    #VARIABLE {idle} {0};
    #IF {$hp[neili_max] >= 6000} {
      closesaving;
    };
    #DELAY {0.5} {
      #IF {$walkstoppedts != 0} {
        #CLASS matrixclass KILL;
        #VARIABLE {walkstoppedts} {0}
      };
      #ELSEIF {"$room" != "西大街"} {
        #CLASS nightclass KILL;
        loc {walk};
      };
      #ELSEIF {@contains{{roomexits}{%1}} > 0} {
        #CLASS nightclass KILL;
        runpath
      };
      #ELSE {
        #LOCAL {tempts} {@eval{$opents - $env[gametime]}};
        #IF {$tempts < 0} {
          #MATH {tempts} {$tempts + 24};
        };
        #IF {$tempts > $waithours} {
          #CLASS nightclass KILL;
          runwait {
            loc {doabort};
          };
        };
        #ELSE {
          waitlian;
          dzn {time;look;echo {checknight}}
        };
      };
    }
  };
  #CLASS nightclass CLOSE;
  ensure {
    time;
    look
  } {checknight}
};
#NOP {福州南门子时三刻(0.5)开门,酉时二刻(18)关门};
#ALIAS {night_fz} {
  #VARIABLE {opents} {0.5};
  #VARIABLE {closets} {18};
  #VARIABLE {waithours} {$night_wait_hours};
  #IF {@isCanGiveUp{} == 0} {
    #VARIABLE {waithours} {@eval{$night_wait_hours * 10}};
  };
  #CLASS nightclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checknight\"|你设定checknight为反馈信息}} {
    resonate {checknight};
    #VARIABLE {idle} {0};
    #IF {$hp[neili_max] >= 6000} {
      closesaving;
    };
    #DELAY {0.5} {
      #IF {$walkstoppedts != 0} {
        #CLASS matrixclass KILL;
        #VARIABLE {walkstoppedts} {0}
      };
      #ELSEIF {"$room" != "南门" && "$room" != "南门吊桥" && "$room" != "西门" && "$room" !="山路"} {
        #CLASS nightclass KILL;
        loc {walk};
      };
      #ELSEIF {@contains{{roomexits}{%1}} > 0} {
        #CLASS nightclass KILL;
        runpath
      };
      #ELSE {
        #LOCAL {tempts} {@eval{$opents - $env[gametime]}};
        #IF {$tempts < 0} {
          #MATH {tempts} {$tempts + 24};
        };
        #IF {$tempts > $night_wait_hours} {
          #CLASS nightclass KILL;
          time;
          runwait {
            loc {walk};
          };
        };
        #ELSE {
          waitlian;
          dzn {time;look;echo {checknight}}
        };
      };
    };
  };
  #CLASS nightclass CLOSE;
  ensure {
    time;
    look
  } {checknight}
};
#NOP {伊犁城门丑时正(1)开门,戌时正(19)关门};
#ALIAS {night_yl} {
  #VARIABLE {opents} {1};
  #VARIABLE {closets} {19};
  #VARIABLE {waithours} {$night_wait_hours};
  #IF {@isCanGiveUp{} == 0} {
    #VARIABLE {waithours} {@eval{$night_wait_hours * 10}};
  };
  #CLASS nightclass OPEN;
  #ACTION {^{$dazuo_over}} {
    yun qi;
    time;
    look;
    echo {checknight};
  };
  #ACTION {^{设定环境变量：action \= \"checknight\"|你设定checknight为反馈信息}} {
    resonate {checknight};
    #VARIABLE {idle} {0};
    #IF {$hp[neili_max] >= 6000} {
      closesaving;
    };
    #DELAY {0.5} {
      #IF {$walkstoppedts != 0} {
        #CLASS matrixclass KILL;
        #VARIABLE {walkstoppedts} {0}
      };
      #ELSEIF {"$room" != "城中心" && "$room" != "南城门"} {
        #CLASS nightclass KILL;
        loc {walk};
      };
      #ELSEIF {@contains{{roomexits}{%1}} > 0} {
        #CLASS nightclass KILL;
        runpath
      };
      #ELSE {
        #LOCAL {tempts} {@eval{$opents - $env[gametime]}};
        #IF {$tempts < 0} {
          #MATH {tempts} {$tempts + 24};
        };
        #IF {$tempts > $waithours && "%1" == "n"} {
          #CLASS nightclass KILL;
          runwait {
            loc {doabort};
          };
        };
        #ELSE {
          waitlian;
          dzn {time;look;echo {checknight}}
        };
      };
    }
  };
  #CLASS nightclass CLOSE;
  ensure {
    time;
    look
  } {checknight}
};
#NOP {思过崖洞口，夜里关门>6 && <21开门};
#ALIAS {night_sgy} {
  #VARIABLE {opents} {6};
  #VARIABLE {closets} {21};
  #VARIABLE {waithours} {$night_wait_hours};
  #IF {@isCanGiveUp{} == 0} {
    #VARIABLE {waithours} {@eval{$night_wait_hours * 10}};
  };
  #CLASS nightclass OPEN;
  #ACTION {^{$dazuo_over}} {
    yun qi;
    time;
    look;
    echo {checknight};
  };
  #ACTION {^{设定环境变量：action \= \"checknight\"|你设定checknight为反馈信息}} {
    resonate {checknight};
    #VARIABLE {idle} {0};
    #IF {$hp[neili_max] >= 6000} {
      closesaving;
    };
    #DELAY {0.5} {
      #IF {$walkstoppedts != 0} {
        #CLASS matrixclass KILL;
        #VARIABLE {walkstoppedts} {0}
      };
      #ELSEIF {"$room" != "思过崖"} {
        #CLASS nightclass KILL;
        loc {walk};
      };
      #ELSEIF {@contains{{roomexits}{enter}} > 0} {
        #CLASS nightclass KILL;
        runpath
      };
      #ELSE {
        #LOCAL {tempts} {@eval{$opents - $env[gametime]}};
        #IF {$tempts < 0} {
          #MATH {tempts} {$tempts + 24};
        };
        #IF {$tempts > $waithours} {
          #CLASS nightclass KILL;
          runwait {
            loc {doabort};
          };
        };
        #ELSE {
          waitlian;
          dzn {time;look;echo {checknight}}
        };
      };
    }
  };
  #CLASS nightclass CLOSE;
  ensure {
    time;
    look
  } {checknight}
};
#NOP {归云庄小酒馆卯时二刻(6)开门,亥时正(21)关门};
#ALIAS {night_gyz} {
  #VARIABLE {opents} {6};
  #VARIABLE {closets} {21};
  #CLASS nightclass OPEN;
  #IF {@isCanGiveUp{} == 0} {
    #VARIABLE {waithours} {@eval{$night_wait_hours * 10}};
  };
  #ACTION {^{$dazuo_over}} {
    yun qi;
    time;
    look;
    echo {checknight};
  };
  #ACTION {^{设定环境变量：action \= \"checknight\"|你设定checknight为反馈信息}} {
    resonate {checknight};
    #VARIABLE {idle} {0};
    #IF {$hp[neili_max] >= 6000} {
      closesaving;
    };
    #DELAY {0.5} {
      #IF {"$room" != "湖滨小路"} {
        #CLASS nightclass KILL;
        loc {walk};
      };
      #ELSEIF {@contains{{roomexits}{%1}} > 0} {
        #CLASS nightclass KILL;
        runpath
      };
      #ELSE {
        #LOCAL {tempts} {@eval{$opents - $env[gametime]}};
        #IF {$tempts < 0} {
          #MATH {tempts} {$tempts + 24};
        };
        #IF {$tempts > $night_wait_hours} {
          #CLASS nightclass KILL;
          runwait {
            loc {doabort};
          };
        };
        #ELSE {
          waitlian;
          dzn {time;look;echo {checknight}}
        };
      };
    }
  };
  #CLASS nightclass CLOSE;
  ensure {
    time;
    look
  } {checknight}
};
#NOP {处理福州关门时的城市连通};
#NOP {福州南门子时三刻(0.5)开门,酉时二刻(18)关门};
#VARIABLE {env[fzlink]} {1};
#ALIAS {night_process} {
  #NOP {如果距离关门时间小于2,距离开门时间超过2小时，断开福州和佛山之间的连接，并根据所在位置处理福州与宁波的连接};
  #NOP {10008佛山镇,10009福州城,10029宁波城};
  #IF {$env[gametime] >= 16 && $env[gametime] < 22.5} {
    #IF {$env[fzlink] == 1} {
      #VARIABLE {env[fzlink]} {0};
      #NOP {断开南门吊桥和西门的出口连接};
      #MAP AT {42} {#MAP UNLINK {night_fz {n};n} {43}};
      #MAP AT {43} {#MAP UNLINK {night_fz {s};s} {42}};
      #MAP AT {75} {#MAP UNLINK {night_fz {e};e} {76}};
      #MAP AT {76} {#MAP UNLINK {night_fz {w};w} {75}};
      #NOP {断开佛山与福州，宁波与福州的连接};
      #MAP AT {10009} {#MAP UNLINK {宁波城} {10029}};
      #MAP AT {10009} {#MAP UNLINK {佛山镇} {10008}};
      #MAP AT {10029} {#MAP UNLINK {福州城} {10009}};
      #MAP AT {10008} {#MAP UNLINK {福州城} {10009}};
    };
  };
  #ELSEIF {$env[fzlink] == 0} {
    #VARIABLE {env[fzlink]} {1};
    #NOP {开启南门吊桥和西门的连接};
    #MAP AT {42} {#MAP LINK {night_fz {n};n} {43}};
    #MAP AT {43} {#MAP LINK {night_fz {s};s} {42}};
    #MAP AT {75} {#MAP LINK {night_fz {e};e} {76}};
    #MAP AT {76} {#MAP LINK {night_fz {w};w} {75}};
    #NOP {开启佛山与福州，宁波与福州的连接};
    #MAP AT {10009} {#MAP LINK {宁波城} {10029}};
    #MAP AT {10009} {#MAP LINK {佛山镇} {10008}};
    #MAP AT {10029} {#MAP LINK {福州城} {10009}};
    #MAP AT {10008} {#MAP LINK {福州城} {10009}};
  };
};
#SHOWME {<fac>@padRight{{夜晚}{12}}<fac> <cfa>模块加载完毕<cfa>};