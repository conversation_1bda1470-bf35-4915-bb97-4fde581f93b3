#NOP {==============================================账号配置==============================================开始};
#NOP {会话ID和账号一致即可};
#VARIABLE {user_session} {我的账号};
#NOP {账号，创建账号时有效};
#VARIABLE {user_char} {我的账号};
#NOP {性别(m或f)，创建账号时有效};
#VARIABLE {user_gender} {m};
#NOP {名称，创建账号时有效};
#VARIABLE {user_name} {我的名字};
#NOP {昵称};
#VARIABLE {user_nick} {};
#NOP {密码};
#VARIABLE {user_password} {我的密码};
#NOP {角色配置文件名称，必须与实际文件名称一致};
#VARIABLE {user_profile} {char_example};
#NOP {killmsg};
#VARIABLE {user_killmsg} {};
#NOP {==============================================账号配置==============================================结束};

#NOP {仔细阅读注释内容，下面所述变量中以#LIST开头的后面大括号内支持多项，各项间用;隔开};
#NOP {下面配置内容带“示例”字样需手动删除};
#NOP {==============================================武器配置==============================================开始};
#NOP {primary:主武器,丢失时会启用备用武器，当主武器为打造武器(不在脚本内置的可购买武器范围内)时会持续使用备用武器20分钟然后退出并重新登录};
#NOP {seconday:备用武器，打造武器或可购买武器};
#NOP {chop:砍头或砍树用武器，不支持mujian，mu jian，推荐用changjian};
#NOP {wuxing:增加智力的武器，学习或领悟时会装备该武器};
#NOP {userweapons:身上携带的打造武器类型sword,blade等,每个基本技能可有多个武器，使用;隔开};
#VARIABLE {conf[weapon]} {
	{primary} {}
	{secondary} {}
	{chop} {changjian}
	{wuxing} {}
	{userweapons} {
		{sword} {示例武器1;示例武器2}
	}
};
#NOP {==============================================武器配置==============================================结束};

#NOP {==============================================护甲配置==============================================开始};
#NOP {护甲配置,armor:护甲,coat:大裳(女),mantle:披风(男),belt:腰带,glove:手套,cap:帽子,boat:鞋子};
#VARIABLE {conf[armor]} {
	{armor} {}
	{coat} {}
	{mantle} {}
	{belt} {}
	{glove} {}
	{cap} {}
	{boot} {}
};
#NOP {==============================================护甲配置==============================================结束};

#NOP {==============================================任务配置==============================================开始};
#NOP {开启急速模式，开启后不再按区域行走，直接走最短路径};
#VARIABLE {conf[extrememode]} {1};

#NOP {接任务时要到达的内力阈值(最大内力的百分比80~180)};
#VARIABLE {conf[neilithreshold]} {120};

#NOP {允许最低的气血上限，低于该值会尝试疗伤，否则继续任务。};
#VARIABLE {conf[healthreshold]} {80};

#NOP {角色登录后进行的活动，job:任务};
#VARIABLE {conf[role]} {job};

#NOP {限制访问的受限区域，此处配置的区域不接收任务。未在此处配置的受限区域满足条件后才会访问，具体参考common[limitezone]中的内容};
#LIST {conf[limitedzone]} {create} {};

#NOP {任务列表, 武馆,巡城,长乐帮,丐帮,华山,嵩山,送信,天地会,武当,雪山,做菜,护送,护镖,官府,巡逻,教和尚,守墓,七窍玲珑,钓鱼};
#NOP {要做的任务列表,二段任务在后面带2，如送信2};
#LIST {conf[joblist]} {create} {雪山;送信2};

#NOP {备用任务};
#LIST {conf[backupjoblist]} {create} {};

#NOP {正神过渡任务，用于正牌任务存在较长busy用于过渡，如果不填默认为华山;送信};
#NOP {配置此项时应避免主任务和过渡任务神要求不一致的情况，虽然可以正常工作但是会浪费时间转神};
#LIST {conf[zstransitjoblist]} {create} {};

#NOP {负神过渡任务，用于正牌任务存在较长busy用于过渡，如果不填默认为送信;长乐帮};
#NOP {配置此项时应避免主任务和过渡任务神要求不一致的情况，虽然可以正常工作但是会浪费时间转神};
#LIST {conf[fstransitjoblist]} {create} {};

#NOP {送信1任务是否需要等待杀手,0:不等,1:等待，在特定的逻辑下会不等待sx1杀手而忽略这里的配置};
#VARIABLE {conf[waitkiller]} {1};

#NOP {需要放弃的武当或送信NPC门派列表};
#LIST {conf[fangqiparty]} {create} {};

#NOP {需要放弃的武当或送信NPC武功列表};
#LIST {conf[fangqiskill]} {create} {独孤九剑;玄阴剑法};

#NOP {需要放弃的武当或送信实力描述，如极其厉害和已入化境};
#LIST {conf[fangqidesc]} {create} {};

#NOP {需要放弃的雪山大内技能，具体为保镖的门派和武器连起来，如：大理天龙寺，少林长鞭};
#LIST {conf[fangqiguard]} {create} {};

#NOP {是否进行其他扩展任务，在条件满足时优先去做扩展任务，一般就是指官府};
#LIST {conf[extendjob]} {create} {};

#NOP {官府任务允许的综合描述差};
#NOP {揭榜时会根据自身攻、防和躲三项的平均值与榜文中NPC描述值进行对比,NPC描述与自身描述差值不大于该值的可揭榜};
#NOP {另外官府NPC很猛，最好不要越级挑战，容易死人，要根据自身实力进行配置};
#VARIABLE {conf[guanfulv]} {4};

#NOP {能做的官府NPC的最高compare描述，做官府时要根据自身技能进行设置};
#VARIABLE {conf[guanfudes]} {深不可测};

#NOP {官府揭榜时最低的赏金,单位为coin};
#VARIABLE {conf[guanfubounds]} {};

#NOP {组队任务设置,队员角色文件也要配置此处信息，当前组队支持qqll任务};
#NOP {leader:自己是否是队长,0:不是,1:是};
#NOP {partner:队友ID,只支持一个队友};
#VARIABLE {conf[team]} {
	{leader} {0}
	{partner} {}
};
#NOP {==============================================任务配置==============================================结束};

#NOP {==============================================携带物品和解毒配置==============================================开始};
#NOP {身上携带金钱};
#NOP {carry:身上正常要保留的数量};
#NOP {warning:预警数量，小于这个数字会去取钱};
#NOP {deposit:超载数量，大于这个数字会去存钱};
#NOP {如未配置默认携带2gold，50silver，预警数量为1gold 10silver，超载数量为5倍};
#VARIABLE {conf[money]} {
	{gold} {
		{carry} {2}
		{warning} {1}
		{deposit} {10}
	}
	{silver} {
		{carry} {50}
		{warning} {10}
		{deposit} {250}
	}
};
#NOP {身上携带的药物数量，carry:补货时补满的数量，warning:数量小于或等于时进行补货};
#VARIABLE {conf[medicine]} {
	{chantui yao} {
		{carry} {2}
		{warning} {0}
	}
	{huoxue dan} {
		{carry} {2}
		{warning} {0}
	}
	{chuanbei wan} {
		{carry} {2}
		{warning} {0}
	}
	{dahuan dan} {
		{carry} {0}
		{warning} {0}
	}
	{da huandan} {
		{carry} {0}
		{warning} {0}
	}
	{tianqi} {
		{carry} {0}
		{warning} {0}
	}
};

#NOP {解毒的配置,吃药或者硬抗,drug:吃药,anti:去药铺硬抗，大于处理方式的数字(秒)则使用该处理方式};
#VARIABLE {conf[detoxify]} {
	{星宿掌} {
		{drug} {1200}
		{anti} {120}
	}
	{腐尸} {
		{drug} {1200}
		{anti} {120}
	}
	{火} {
		{drug} {2400}
	}
	{蓝砂手} {
		{drug} {2400}
	}
	{寒} {
		{drug} {2400}
	}
};
#NOP {==============================================携带物品和解毒配置==============================================结束};

#NOP {==============================================学习和领悟配置=========================================开始};
#NOP {skill:允许的脱节等级，0:不full技能，一般设置为1，不要设置的太高。经验等级与技能等级差>=该配置时去学习或领悟};
#NOP {neili:内力脱节点数，内力第一上限与最大内力差>=该配置值时去打坐。紫檀站6000前会一直吃yuji wan};
#NOP {jingli:精力脱节点数，精力上限与最大精力差值>=该配置值时去吐纳};
#VARIABLE {conf[allowdiff]} {
	{skill} {1}
	{neili} {4000}
	{jingli} {10000}
};

#NOP {是否自动吃yuji wan补内力。启用该设置后上面的allowdiff[neili]失效};
#NOP {state:非空自动补};
#NOP {threshold:最低通宝数量};
#VARIABLE {conf[autoyuji]} {
	{state} {}
	{threshold} {10000}
};

#NOP {经验换内力配置，默认100M后有效，启用该设置后上面的allowdiff[neili]失效};
#NOP {state:是否启动,非空启用};
#NOP {threshold:内力差阈值，最小不应小于100};
#VARIABLE {conf[autoexchange]} {
	{state} {}
	{threshold} {2000}
};

#NOP {潜能用途,none:不去学习或领悟,learn:学习,lingwu:领悟,auto:自动处理，仅在任务cd间隙进行领悟练习，当脱节较多时才会专门去。};
#NOP {当技能>=220时learn会自动转为lingwu，向导模式时需设置为learn};
#VARIABLE {conf[potpurpose]} {learn};

#NOP {忽略的技能列表，列表内武功不参与学习、领悟和练习};
#LIST {conf[ignoreskills]} {create} {};

#NOP {任务用主内功};
#NOP {当角色拥有多个特殊内功时(不含在忽略列表的技能)，需要指定一个任务用的主内功，否则激发的内功是不确定的};
#VARIABLE {conf[primaryforce]} {};

#NOP {增加悟性的内功};
#NOP {当角色拥有多个特殊内功时(不含在忽略列表的技能)且有内功可增加悟性，可配置此项，在领悟前会激发该内功};
#VARIABLE {conf[wuxingforce]} {};
#NOP {==============================================学习和领悟配置=========================================结束};

#NOP {==============================================解谜配置==============================================开始};
#NOP {新使用这个脚本的角色需要标识一下自身已经完成的解谜，如果未标识还是会尝试去解谜，结果不确定。或者设定subcribequests和ignorequests来限定解谜范围。};
#NOP {对于一般的解谜输入questsuccess 名称 可设置为该解谜成功，对于分步骤的解谜输入questupdate 数字 数字大于等于该解谜步骤数即可};
#NOP {脚本使用内置的alias来保存角色各解谜的状态，最好先删除一些无用的alias};
#NOP {分步的解谜有天龙八部、雪山飞狐、连城诀、射雕英雄传、神照经，具体参考quest.tin文件};
#NOP {是否开启自动解谜};
#VARIABLE {conf[autoquest]} {1};

#NOP {是否显示已完成解密};
#VARIABLE {conf[displaysuccess]} {0};

#NOP {参与的解谜，该参数比下面的ingorequests优先级高，仅参与这里已配置的解谜};
#LIST {conf[subcribequests]} {create} {};

#NOP {不参与的解谜，所有未在该处配置的且脚本支持的解谜均会参与};
#LIST {conf[ignorequests]} {create} {辟邪剑谱;金刀黑剑;千蛛万毒手;躺尸剑法};
#NOP {==============================================解谜配置==============================================结束};

#NOP {==============================================副本配置==============================================开始};
#NOP {新使用这个脚本的角色需要标识一下自身已经完成的副本，如果未标识还是会尝试去挑战副本，结果不确定。};
#NOP {对于副本输入fubensuccess 名称 可设置为该副本成功};
#NOP {脚本使用内置的alias来保存角色各副本的状态，最好先删除一些无用的alias};
#NOP {是否开启自动副本};
#VARIABLE {conf[autofuben]} {0};

#NOP {是否显示已完成副本};
#VARIABLE {conf[displayfubensuccess]} {0};

#NOP {参与的副本，该参数比下面的ignorefubens优先级高，仅参与这里已配置的副本};
#LIST {conf[subcribefubens]} {create} {};

#NOP {不参与的副本，所有未在该处配置的且脚本支持的副本均会参与};
#LIST {conf[ignorefubens]} {create} {};
#NOP {==============================================副本配置==============================================结束};

#NOP {==============================================服务配置==============================================开始};
#NOP {角色提供的服务项目，与保姆配置相对应，提供服务的账号会响应保姆的呼叫请求};
#NOP {funds:响应启动资金请求，配合向导模式可以实现从零开始无人值守};
#NOP {guard:响应护卫请求，当前支持雪山飞狐复仇篇黑衣人，宝藏篇高手和射雕英雄传金兵};
#NOP {killer:响应杀人请求，可呼叫服务号杀死某个NPC，NPC必须已在地图中支持};
#NOP {library:响应书籍请求，当前支持易经、九宫八卦图谱(服务号必须为桃花岛)，其他书籍服务号会检查杂货铺存货，请服务号事先准备好并存储在杂货铺};
#NOP {当前已支持medicine的kejin jijie,douzhen dinglun和boji xidoufang的自动还书};
#NOP {zhanbu:响应占卜请求，占卜需桃花岛账号且需要有一个助手才行};
#NOP {assist:占卜助手};
#NOP {doctor:开门诊,处理问诊请求};
#NOP {visit:访问者，遍历指定房间};
#LIST {conf[services]} {create} {};

#NOP {医生负责的科室,具体参考status.tin中关于异常状态抓取的触发和conf[detoxify]中的默认配置};
#LIST {conf[departments]} {create} {};

#NOP {保姆配置，设置各种服务提供者的ID};
#NOP {banker:启动资金,与funds服务对应。巡城完成后会请求资金并学习literate至122级};
#NOP {killer:杀手,与killer服务对应，当前并无实际应用};
#NOP {library:图书馆,与library服务对应，当前学习medicine和奇门八卦时会向服务号请求相关书籍};
#NOP {guard:护卫,与guard服务对应,当前在解谜雪山飞狐复仇篇、宝藏篇和射雕英雄传杀金兵时会请求guard服务};
#NOP {zhanbu:占卜,与zhanbu服务对应,当前在解谜三无三不手时会请求占卜陆无双位置};
#NOP {doctor:门诊,与doctor服务对应,负责解毒和治疗内伤,下面为病症和对应的医师id};
#NOP {visitor:访问者，帮你遍历指定房间};
#VARIABLE {conf[nanny]} {
	{banker} {}
	{killer} {}
	{library} {}
	{guard} {}
	{zhanbu} {}
	{doctor} {}
	{visitor} {}
};
#NOP {==============================================服务配置==============================================结束};

#NOP {==============================================向导配置==============================================开始};
#NOP {新手模式要拜的师门，账号巡城完毕后会自动加入该门派，其后续何时拜师、学习什么技能、做什么任务、使用什么技能均由向导配置脚本决定};
#NOP {具体请参考guides下的门派向导脚本};
#NOP {当前脚本已支持的门派有古墓派、昆仑派、姑苏慕容、全真教、少林派、桃花岛、铁掌帮和星宿派};
#VARIABLE {conf[newbie]} {
	{party} {}
};
#NOP {==============================================向导配置==============================================结束};

#NOP {==============================================战斗配置==============================================开始};
#NOP {被峨嵋幽冥后是否继续战斗，0:跑路，1:继续战斗};
#VARIABLE {conf[fight][youming]} {0};
#NOP {==============================================战斗配置==============================================结束};

#NOP {==============================================技能配置==============================================开始};
#NOP {向导模式时优先使用门派向导脚本中配置的技能};
#NOP {==============================================通用配置==============================================开始};
#NOP {空手武技准备，技能准备时会执行此处指令};
#VARIABLE {conf[pfm][bei]} {
	bei none;
	bei claw
};

#NOP {增加悟性的指令，在读书、学习和领悟时会执行，如玉女心经可设置为yun xinjing};
#VARIABLE {conf[pfm][wuxing]} {
	yun xinjing
};

#NOP {非战斗增幅指令，在接收到任务去做任务前会执行，主要是一些可以在非战斗状态使用的perform，如桃花岛五转、铁掌帮飘等};
#VARIABLE {conf[pfm][normalbuff]} {
	perform suibo-zhuliu.wuzhuan
};

#NOP {战斗增幅指令，需要在战斗中使用的增幅类perform，上面的normalbuff指令最好也配置在此处，因为增幅类状态可随时消失。};
#NOP {战斗增幅指令会与下面的攻击类perform指令合并到wimpycmd使用的autopfm alias中，参考fight.tin脚本};
#VARIABLE {conf[pfm][fightbuff]} {
	perform suibo-zhuliu.wuzhuan
};

#NOP {通用攻击类perform指令，会与上面的战斗增幅指令合并到wimpycmd使用的autopfm alias中};
#VARIABLE {conf[pfm][attack]} {
	jiali max;
	perform xuanyin-jian.xuanyin;
	jiali 10
};
#NOP {化学类攻击perform指令，当遇到物免技能或者物免状态时会切换此处perform};
#VARIABLE {conf[pfm][dd]} {
};
#NOP {==============================================通用配置==============================================结束};

#NOP {==============================================预设配置==============================================开始};
#NOP {你可以在此设置若干个攻击perform组合指令，然后应用在下面定义的空手或者特定任务perform中};
#NOP {示例中p0，p1即为预定义的perform代码，do:使用该perform时的预备指令,cmd:该perform的实际执行指令};
#NOP {如该pfm是使用武器的，需在weapon字段中指定武器,为空表示空手技能(auto-指使用$conf[weapon][primary])};
#VARIABLE {conf[pfm][alias]} {
	{p0} {
		{do} {
			uwwp;
			bei none;
			jifa claw jiuyin-shenzhua;
			bei claw;
		}
		{weapon} {auto}
		{cmd} {
			jiali max;
			perform jiuyin-shenzhua.sanjue;
			jiali 10;
		}
	}
	{pls} {
		{do} {
			uwwp;
			bei none;
			jifa cuff lingshe-quanfa;
			jifa jifa parry lingshe-quanfa;
			bei cuff
		}
		{weapon} {}
		{cmd} {
			jiali max;
			perform lingshe-quanfa.lingshe;
			jiali 10;
		}
	}
};
#NOP {==============================================预设配置==============================================结束};

#NOP {==============================================场景配置==============================================开始};
#NOP {配置各种场景下使用的perform，配置内容为上面预设好的perform代码，未配置时统一使用通用攻击perform};
#NOP {空手，武器掉落时如未配置则会尝试捡武器};
#VARIABLE {conf[pfm][scene]} {};

#VARIABLE {conf[pfm][scene][unarmed]} {}

#NOP {长乐帮};
#VARIABLE {conf[pfm][scene][clb]} {};

#NOP {嵩山请人};
#VARIABLE {conf[pfm][scene][ssi]} {};

#NOP {嵩山杀人};
#VARIABLE {conf[pfm][scene][ssk]} {};

#NOP {送信1};
#VARIABLE {conf[pfm][scene][sx1]} {};

#NOP {送信2};
#VARIABLE {conf[pfm][scene][sx2]} {};

#NOP {华山1};
#VARIABLE {conf[pfm][scene][hs1]} {};

#NOP {华山2};
#VARIABLE {conf[pfm][scene][hs2]} {};

#NOP {武当};
#VARIABLE {conf[pfm][scene][wd]} {};

#NOP {雪山};
#VARIABLE {conf[pfm][scene][xs]} {};

#NOP {天地会};
#VARIABLE {conf[pfm][scene][tdh]} {};

#NOP {丐帮杀人};
#VARIABLE {conf[pfm][scene][gb]} {};

#NOP {丐帮颂摩崖};
#VARIABLE {conf[pfm][scene][smy]} {};

#NOP {七窍玲珑};
#VARIABLE {conf[pfm][scene][qqll]} {};

#NOP {护镖};
#VARIABLE {conf[pfm][scene][hb]} {};
#NOP {==============================================场景配置==============================================结束};

#NOP {==============================================特定NPC配置==============================================开始};
#NOP {指定雪山大内使用的技能，配置字段为大内门派+武器(参考前面大内放弃配置)，配置内容为前面alias预设的perform代码，};
#VARIABLE {conf[pfm][npc][xs]} {
	{大理天龙寺} {}
	{少林长鞭} {}
	{峨嵋长剑} {}
	{峨嵋钢刀} {}
	{明教} {}
	{明教一块铁令} {}
	{丐帮} {}
	{丐帮竹棒} {}
};

#NOP {其他任务特殊技能对应的pfm};
#VARIABLE {conf[pfm][npc][skill]} {
	{原始剑法} {}
	{古拙掌法} {}
	{降龙十八掌} {}
	{打狗棒法} {}
};

#NOP {送信2NPC技能使用的pfm，覆盖上方skill配置};
#VARIABLE {conf[pfm][skill][sx2]} {
	{原始剑法} {}
	{古拙掌法} {}
	{降龙十八掌} {}
	{打狗棒法} {}
};
#NOP {==============================================特定NPC配置==============================================结束};

#NOP {==============================================切换配置==============================================结束};
#NOP {定义可通过触发器调整的perform，应用场景为无法通过合并指令来完成的情况，如五轮大转会一直放};
#NOP {字段为要触发的内容，值为前面定义的预设perform代码，触发内容不能有通配符};
#NOP {condition项指要匹配的任务场景，如为空则匹配任意任务,perform指触发后执行的pfm alias};
#VARIABLE {conf[pfm][trigger]} {
	{结果%*被你杖头怪蛇一口喷中} {
		{condition} {雪山}
		{perform} {}
	}
};
#NOP {==============================================切换配置==============================================结束};
#NOP {==============================================技能配置==============================================结束};

#SHOWME {<faa>角色配置加载完毕};