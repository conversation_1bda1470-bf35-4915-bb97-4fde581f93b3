#NOP 金刀黑剑;
#alias goquest_jdhj {
  #VARIABLE {questmodule} {金刀黑剑};
  gotodo {绝情谷} {大厅} {jdhj_askgsz {%1}};
};
#ALIAS {jdhj_askgsz} {
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^^公孙止似乎不懂你的意思} {
    #CLASS questclass KILL;
    dohalt {gotonpc {裘千尺} {jdhj_story {%1}}};
  };
  #ACTION {^公孙止说道：「以你当前的经验恐怕还是难以领悟，还是抓紧去练功去吧。」} {
    questdelay {$questmodule} {0} {10800};
    dohalt {%1}
  };
  #ACTION {^公孙止对着你摇了摇头} {
    questdelay {$questmodule} {0} {10800}
    dohalt {%1}
  };
  #ACTION {^你脑海里似乎抓住了什么，可是依然不是很明白} {
    #CLASS questclass KILL;
    questfail {$questmodule};
    dohalt {%1};
  };
  #ACTION {^公孙止微笑着对你说道：「可要看仔细了!」,双手耍起手中的金刀和黑剑!} {
    pray pearl
  };
  #ACTION {^你听了公孙止的指点,终于领悟了金刀黑剑的精华所在} {
    #CLASS questclass KILL;
    questsuccess {$questmodule};
    dohalt {fulljdhj {%1}};
  };
  #CLASS questclass CLOSE;
  #VARIABLE {idle} {-30};
  ask zhi about 金刀黑剑
};
#ALIAS {jdhj_story} {
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^裘千尺说道：「哼，这恶贼害我到如此地步，不杀此贼我裘千尺誓不为人，你如果能将绝情丹找来，我就将告诉你一个秘密。」} {
    dohalt {ask qiu about 绝情丹}
  };
  #ACTION {^裘千尺说道：「你我在此相遇亦算有缘，绝情谷唯一一颗绝情丹我藏在大厅第五块青砖下。」} {
    dohalt {gotodo {绝情谷} {大厅} {wcwp;jie zhuan}};
  };
  #ACTION {^你数到第五块青砖} {
    dohalt {
      uwwp;
      gotonpc {裘千尺} {give dan to qiu}
    };
  };
  #ACTION {^你来晚了，绝情丹已给别人拿走了} {
    #VARIABLE {idle} {0};
    #DELAY {2} {
      wcwp;
      jie zhuan
    };
  };
  #ACTION {^你给裘千尺一枚绝情丹。} {
    dohalt {gotodo {桃花岛} {内室} {ask huang about 裘千丈}};
  };
  #ACTION {^黄蓉颜色凝重，慢慢回忆当年发生铁掌峰的经历!} {
    dohalt {
      uwwp;
      ask huang about 裘千尺
    };
  };
  #ACTION {^你将在绝情谷遇见裘千尺以及找黄蓉复仇的事情告诉黄蓉！} {
    dohalt {gotonpc {一灯大师} {ask dashi about 黄蓉}};
  };
  #ACTION {^你将在黄蓉求见大师的请求告诉了一灯大师!} {
    dohalt {gotodo {绝情谷} {大厅} {look}};
  };
  #ACTION {^公孙止%*道} {
    #VARIABLE {idle} {0};
  };
  #ACTION {^大家跟着黄蓉追了出去，你也跟着追了出去。} {
    loc {
      dohalt {
        wwp;
        gotodo {绝情谷} {断肠崖} {look}
      };
    };
  };
  #ACTION {^公孙止很惊恐的看着你} {
    #VARIABLE {workingflag} {1};
    #DELAY {1} {
      wwp;
      startfight;
    };
  };
  #ACTION {^公孙止痛哭流涕的哀求你的原谅。} {
    #CLASS questclass KILL;
    #VARIABLE {workingflag} {0};
    stopfight;
    dohalt {gotodo {绝情谷} {大厅} {jdhj_askgsz {%1}}};
  };
  #CLASS questclass CLOSE;
  ask qiu qianchi about 公孙止;
};