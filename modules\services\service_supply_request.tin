#NOP {请求获取物品,%1:物品名称,%2:成功指令,%3:失败指令};
#ALIAS {supply_call} {
  yz {supply_call_start {%1} {%2} {@iif{{"%3" == ""}{%2}{%3}}}}
};
#NOP {请求获取物品,先到位置再呼叫,%1:物品名称,%2:成功指令,%3:失败指令};
#ALIAS {supply_call_start} {
  #CLASS serviceclass KILL;
  #CLASS serviceclass OPEN;
  #ACTION {^没有这个人。} {
    #CLASS serviceclass KILL;
    #SHOWME {<faa>保姆$conf[nanny][supplier]人不在};
    #IF {"%3" != ""} {
      %3
    };
    #ELSE {
      %2
    };
  };
  #ACTION {%*(%*)告诉你：supply_wait} {
    #VARIABLE {idle} {0};
    #CLASS serviceclass KILL;
    #DELAY {5} {supply_call {%1} {%2} {%3}};
  };
  #ACTION {%*(%*)告诉你：supply_come} {
    #CLASS serviceclass KILL;
    #VARIABLE {waiter} {
      {id} {@lower{%%2}}
      {name} {%%1}
    };
    yz {supply_request_wait {%1} {%2} {%3}}
  };
  #CLASS serviceclass CLOSE;
  #IF {"$conf[nanny][supplier]" == ""} {
    #SHOWME {<faa>未配置supplier保姆,请自行处理};
    #IF {"%3" != ""} {
      %3
    };
    #ELSE {
      %2
    };
  };
  #ELSE {
    tell $conf[nanny][supplier] supply_request_%1
  };
};
#NOP {去银行等待碰头,%1:物品名称,%2:成功指令,%3:失败指令};
#ALIAS {supply_request_wait} {
  #VARIABLE {startts} {@now{}};
  #VARIABLE {arrived} {0};
  #CLASS serviceclass KILL;
  #CLASS serviceclass OPEN;
  #ACTION {^$waiter[name]轻轻地拍了拍你的头} {
    #VARIABLE {arrived} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkresponse\"|你设定checkresponse为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {@elapsed{$startts} > 120} {
      #CLASS serviceclass KILL;
      #NOP {超时直接溜};
      %3
    };
    #ELSEIF {$arrived == 1} {
      #CLASS serviceclass KILL;
      #IF {@carryqty{%1} == 0} {
        %3
      };
      #ELSE {
        %2
      };
    };
    #ELSE {
      #DELAY {2} {
        i;
        echo {checkresponse};
      };
    };
    #ELSE {commands};
  };
  #CLASS serviceclass CLOSE;
  i;
  echo {checkresponse};
};