#NOP {蛤蟆功,%1:后续指令};
#ALIAS {goquest_hubo} {
  #IF {@getSkillLevel{qimen-bagua} < 121} {
    thdprepare {learnbagua {goquest_hubo {%1}}}
  };
  #ELSE {
    #VARIABLE {questmodule} {左右互搏};
    gotodo {桃花岛} {岩洞} {hubo_fangyuan {%1}}
  };
};
#ALIAS {hubo_fangyuan} {
  #VARIABLE {huboflag} {0};
  #VARIABLE {askts} {@now{}};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^这里没有这个人} {
    #IF {@elapsed{$askts} > 60} {
      #CLASS questclass KILL;
      dohalt {%1}
    };
    #ELSE {
      #DELAY {3} {
        out;
        enter;
        pray pearl;
        ask zhou about 双手互搏;
      };
    };
  };
  #ACTION {^你向周伯通打听有关『双手互搏』的消息} {
    #CLASS questresponseclass KILL;
    #CLASS questresponseclass OPEN;
    #ACTION {^周伯通说道：「你不是已经学会了吗} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questsuccess {$questmodule};
      dohalt {%1};
    };
    #ACTION {^周伯通说道：「看来你现在还不适合学这门功夫} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questdelay {$questmodule} {0} {10800};
      dohalt {%1};
    };
    #ACTION {^周伯通说道：「你是谁啊，怎么莫名其妙地问我双手互搏的事啊？」} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questfail {$questmodule};
      dohalt {%1};
    };
    #ACTION {^周伯通说道：「{你先练练这第一课|接着练|让你练|怎么样，练不会}} {
      #CLASS questresponseclass KILL;
      dohalt {hua fang yuan}
    };
    #CLASS questresponseclass CLOSE;
  };
  #ACTION {^你伸出两根食指在地上划画，但画出来的方块有点象圆圈，圆圈又有点象方块} {
    #DELAY {0.5} {hua fang yuan}
  };
  #ACTION {^你又伸出两根食指在地上划画，但画出来的仍旧是方块有点象圆圈，圆圈又有点象方块} {
    #CLASS questclass KILL;
    questfail {$questmodule};
    dohalt {%1};
  };
  #ACTION {^你微微一笑，凝神守一，心地空明，随随便便的伸出双手手指，左手画了一个方块，右手画了一个圆圈} {
    #CLASS questclass KILL;
    questsuccess {$questmodule};
    dohalt {%1};
  };
  #CLASS questclass CLOSE;
  pray pearl;
  ask zhou about 双手互搏;
};