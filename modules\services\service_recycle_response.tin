#NOP {垃圾回收};
#ALIAS {initrecycleservice} {
  #CLASS servicerecycleclass KILL;
  #CLASS servicerecycleclass OPEN;
  #ACTION {^%*(%*)告诉你：recycle_request} {
    recycle_accept {@lower{%%2}} {%%1}
  };
  #ACTION {^! %*(%*)告诉你：recycle_request} {
    recycle_accept {@lower{%%2}} {%%1}
  };
  #ACTION {^%*(%*)告诉你：recycle_cancel} {
    #VARIABLE {caller} {};
  };
  #ACTION {^! %*(%*)告诉你：recycle_cancel} {
    #VARIABLE {caller} {};
  };
  #CLASS servicerecycleclass CLOSE;
};
#NOP {注册垃圾回收请求,%1:id,%2:name};
#ALIAS {recycle_accept} {
  #NOP {超过五分钟重置};
  #IF {"$caller" != "" && @elapsed{$caller[timestamp]} > 300} {
    #VARIABLE {caller} {};
  };
  #NOP {更新请求};
  #IF {"$caller" != "" && "$caller[id]" != "%1"} {
    tell %1 recycle_wait
  };
  #ELSE {
    #VARIABLE {caller} {
      {request} {recycle}
        {timestamp} {@now{}}
        {id} {%1}
        {name} {%2}
    };
    tell %1 recycle_come
  };
};
#NOP {去扬州钱庄响应,%1:后续指令};
#ALIAS {recycle_response} {
  yz {recycle_response_gold {%1}}
};
#NOP {回收钱,%1:后续指令};
#ALIAS {recycle_response_gold} {
  #VARIABLE {amount} {0};
  #VARIABLE {checkcount} {0};
  #VARIABLE {okflag} {0};
  #CLASS serviceclass KILL;
  #CLASS serviceclass OPEN;
  #ACTION {^你要对谁做这个动作？} {
    #MATH {checkcount} {$checkcount + 1};
  };
  #ACTION {^你轻轻地拍了拍$caller[name]的头。} {
    #VARIABLE {checkcount} {0};
  };
  #ACTION {^{设定环境变量：action \= \"checkcaller\"|你设定checkcaller为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {$checkcount == 0} {
    };
    #ELSEIF {$checkcount < 20} {
      #DELAY {2} {
        pat $caller[id];
        echo {checkcaller};
      };
    };
    #ELSE {
      #CLASS serviceclass KILL;
      %1;
    };
  };
  #ACTION {^$caller[name]给你} {
    #VARIABLE {okflag} {0};
    cun 100 gold;
    i;
    echo {checkcun};
  };
  #ACTION {^{设定环境变量：action \= \"checkcun\"|你设定checkcun为反馈信息}} {
    #VARIABLE {idle} {0};
    #DELAY {1} {
      #IF {@carryqty{gold} > 111} {
        cun 100 gold;
        i;
        echo {checkcun};
      };
      #ELSE {
        #MATH {amount} {$amount + 100};
        nod $caller[id]
      };
    }
  };
  #ACTION {^$caller[name]冲着你大喊：} {
    #CLASS serviceclass KILL;
    gotodo {扬州城} {杂货铺} {recycle_response_things {%1}}
  };
  #ACTION {^$caller[name]对你抱拳道：“青山不改，绿水常流，咱们后会有期！”} {
    #CLASS serviceclass KILL;
    #VARIABLE {caller} {};
    startjob
  };
  #CLASS serviceclass CLOSE;
  pat $caller[id];
  echo {checkcaller};
};
#NOP {回物品,%1:后续指令};
#ALIAS {recycle_response_things} {
  #VARIABLE {checkcount} {0};
  #VARIABLE {okflag} {0};
  #VARIABLE {favourite} {};
  #LIST {totalthings} {clear};
  #CLASS serviceclass KILL;
  #CLASS serviceclass OPEN;
  #ACTION {^你要对谁做这个动作？} {
    #MATH {checkcount} {$checkcount + 1};
  };
  #ACTION {^你轻轻地拍了拍$caller[name]的头。} {
    #VARIABLE {checkcount} {0};
  };
  #ACTION {^{设定环境变量：action \= \"checkcaller\"|你设定checkcaller为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {$checkcount == 0} {
    };
    #ELSEIF {$checkcount < 20} {
      #DELAY {2} {
        pat $caller[id];
        echo {checkcaller};
      };
    };
    #ELSE {
      #CLASS serviceclass KILL;
      %1;
    };
  };
  #ACTION {^$caller[name]给你} {
    nod $caller[id]
  };
  #ACTION {^$caller[name]对你抱拳道：“青山不改，绿水常流，咱们后会有期！”} {
    i;
    echo {checkthings};
  };
  #ACTION {^{设定环境变量：action \= \"checkthings\"|你设定checkthings为反馈信息}} {
    #VARIABLE {idle} {};
    #VARIABLE {favourite} {};
    #FOREACH {*id[things][]} {t} {
      #IF {"$t" == "weilan's hammer"} {
        #CONTINUE;
      };
      #IF {@contains{{common[jade]}{$t}} > 0} {
        #VARIABLE {favourite} {$t};
        #BREAK;
      };
      #ELSEIF {@contains{{common[valuable]}{$t}} > 0} {
        #VARIABLE {favourite} {$t};
        #BREAK;
      };
    };
    #IF {"$favourite" == ""} {
      #CLASS serviceclass KILL;
      #LIST {totalthings} {collapse} {,};
      servicelog {收到$caller[name]($caller[id])的遗产共计【$amount】gold和物品$totalthings。};
      #LOCAL {logname} {recycle_$caller[id]};
      #VARIABLE {caller} {};
      #IF {@carryqty{weilan's hammer} > 0} {
        giveweilan {%1}
      };
      #ELSE {
        logbuff {$logname};
        %1
      };
    };
    #ELSE {
      cun $favourite;
      #LIST {totalthings} {add} {$favourite};
      #VARIABLE {favourite} {};
      dohalt {
        i;
        echo {checkthings};
      };
    };
  };
  #CLASS serviceclass CLOSE;
  pat $caller[id];
  echo {checkcaller};
};
#ALIAS {giveweilan} {
  gotoroom {兵器铺} {giveweilan_start {%1}}
};
#ALIAS {giveweilan_start} {
  #CLASS serviceclass KILL;
  #CLASS serviceclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkthings\"|你设定checkthings为反馈信息}} {
    #DELAY {1} {
      #IF {@carryqty{weilan's hammer} > 0} {
        give chui to zhujian shi;
        i;
        echo {checkthings};
      };
      #ELSE {
        #CLASS serviceclass KILL;
        %1;
      };
    };
  };
  #CLASS serviceclass CLOSE;
  give chui to zhujian shi;
  i;
  echo {checkthings};
};
initrecycleservice