#NOP {五虎断门刀,%1:后续指令};
#ALIAS {goquest_whdmd} {
	#VARIABLE {questmodule} {五虎断门刀};
	gotodo {扬州城} {扬州盐局} {whdmd_shihui {%1}}
};
#ALIAS {whdmd_shihui} {
	#VARIABLE {shihuicount} {0};
	#CLASS questclass KILL;
	#CLASS questclass OPEN;
	#ACTION {^{设定环境变量：action \= \"checkid\"|你设定checkid为反馈信息}} {
		#IF {@carryqty{bu dai} == 0} {
			na 布袋;
		};
		#ELSE {
			zhua 石灰
		};
	};
	#ACTION {^突然，一个盐枭看见了你的动作，大声喝喊着：“有人偷袋子} {
		#CLASS questclass KILL;
		dopray {goquest_whdmd {%1}}
	};
	#ACTION {^你用脚拨了拨地上的布袋，顺手捡了起来} {
		zhua 石灰
	};
	#ACTION {^突然，一个盐枭看见了你的动作，大声喝喊着：“有人偷石灰} {
		#CLASS questclass KILL;
		dopray {goquest_whdmd {%1}}
	};
	#ACTION {^刚抓过石灰，小心盐局的人打你} {
		#VARIABLE {idle} {0};
		#DELAY {2} {zhua 石灰}
	};
	#ACTION {^{此地石灰已用完|人家盖房子正要用石灰呢，你偷完了怎么办}} {
		#IF {$shihuicount == 0} {
			#CLASS questclass KILL;
			quest_back {nextlogin {3600}}
		};
		#ELSE {
			gotoroom {508} {whdmd_chifan {%1}}
		};
	};
	#ACTION {^你趁人不注意，偷偷从地上抓了一把石灰放在身上的袋子里} {
		#MATH {shihuicount} {$shihuicount + 1};
		#DELAY {2} {zhua 石灰}
	};
	#CLASS questclass CLOSE;
	pray pearl;
	i;
	echo {checkid}
};
#ALIAS {whdmd_chifan} {
	#CLASS questclass KILL;
	#CLASS questclass OPEN;
	#ACTION {^{设定环境变量：action \= \"checktime\"|你设定checktime为反馈信息}} {
		#VARIABLE {idle} {0};
		#IF {@contains{{roomexits}{n}} > 0} {
			n;
			sit chair;
			dian mijiu;
			pay
		};
		#ELSE {
			#DELAY {2} {
				look;
				echo {checktime}
			};
		};
	};
	#ACTION {^酒保走过来对你说：“对不起您哪，现在不是用餐时间} {
		#DELAY {2} {
			sit chair;
			dian mijiu;
			pay
		};
	};
	#ACTION {^掌柜翻出菜单，拨打了一阵算盘后告诉你：“一共%*两白银} {
		give @eval{@ctd{%%1} + 1} silver to xian zhanggui
	};
	#ACTION {^冼掌柜满脸堆着笑，冲你哈腰道：“谢客官您的小费，老儿不客气就收下了，您走好} {
		ask xian zhanggui about rumors
	};
	#ACTION {^你向冼掌柜问道：这位壮士，不知最近有没有听说什么消息} {
		dohalt {ask xian zhanggui about 茅十八}
	};
	#ACTION {^你向冼掌柜打听有关『茅十八』的消息} {
		#CLASS questclass KILL;
		dohalt {loc {gotodo {扬州城} {丽春院} {whdmd_meetmao {%1}}}}
	};
	#CLASS questclass CLOSE;
	look;
	echo {checktime}
};
#NOP {遇茅十八};
#ALIAS {whdmd_meetmao} {
	#VARIABLE {startts} {@now{}};
	#VARIABLE {okflag} {0};
	#CLASS questclass KILL;
	#CLASS questclass OPEN;
	#ACTION {^你咳了几声，把肩耸了耸，向里屋望去} {
		#VARIABLE {okflag} {1};
	};
	#ACTION {^你突然全身发抖，吓的飞也似的逃了出去} {
		#VARIABLE {okflag} {2};
	};
	#ACTION {^{设定环境变量：action \= \"checkevent\"|你设定checkevent为反馈信息}} {
		#VARIABLE {idle} {0};
		#IF {$okflag == 1} {
			adjustgift {28} {10} {10} {32} {smile}
		};
		#ELSEIF {$okflag == 2} {
			#CLASS questclass KILL;
			loc {gotodo {扬州城} {丽春院} {whdmd_meetmao {%1}}}
		};
		#ELSEIF {@elapsed{$startts} >= 15} {
			s;
			n;
			whdmd_meetmao {%1}
		};
		#ELSE {
			#DELAY {1} {echo {checkevent}}
		};
	};
	#ACTION {^砰的一声，大门撞开，涌进十七八名短装结束，白布包头，青带缠腰的大汉} {
		#VARIABLE {idle} {0}
	};
	#ACTION {^那老者走上几步，向内张去，朦胧中见一名虬髯大汉坐在床上，头上包了白布，脸上} {
		#VARIABLE {idle} {0}
	};
	#ACTION {^你看的兀自生气，就冲上前去向着那个盐枭踢了一脚，刚好踢中那人的阴囊} {
		#VARIABLE {idle} {0}
	};
	#ACTION {^那髯须大汉说道：“看不出你这小朋友还满够义气。好，现下我要赶去城西得胜山} {
		#VARIABLE {idle} {0};
		#DELAY {0.5} {#SEND {yes};}
	};
	#ACTION {^你见到这锭金子，不禁咽了口唾沫，心中犹豫不定究竟是收还是不收(yes|no)} {
		#VARIABLE {idle} {0};
		#DELAY {0.5} {#SEND {no};}
	};
	#ACTION {^你们刚来到得胜山，却见一个秃顶汉子和一个白须老人已等待多时了，原来那两人便} {
		#VARIABLE {idle} {0};
		adjustgift {32} {10} {10} {28}
	};
	#ACTION {^茅十八拍了拍手中钢刀冲上前去，和史松打了起来} {
		#VARIABLE {idle} {0};
		#DELAY {1} {sa shi song}
	};
	#ACTION {^谁知却撒了个空，自己也摔了一交} {
		#VARIABLE {idle} {0};
		#DELAY {1} {sa shi song}
	};
	#ACTION {^茅十八道：“你为什么用石灰撒在那史松的眼里？”声音严厉，神态更是凶恶} {
		#CLASS questclass KILL;
		questsuccess {$questmodule};
		%1
	};
	#CLASS questclass CLOSE;
	#NOP {根骨太小可能被吓跑};
	adjustgift {10} {32} {10} {28} {echo {checkevent}}
}