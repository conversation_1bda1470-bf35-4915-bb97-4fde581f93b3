#NOP {快速行走模块};
#NOP {地址变量};
#VARIABLE {city} {};
#VARIABLE {lastcity} {};
#VARIABLE {room} {};
#VARIABLE {roomid} {};
#LIST {roomexits} {clear};
#LIST {roomways} {clear};
#VARIABLE {aimcity} {};
#VARIABLE {aimroom} {};
#VARIABLE {aimroomid} {};
#VARIABLE {aimdo} {};
#NOP {期望到达的房间,在checkroom中进行处理,用于记录将要到达的房间,用于处理被先手打晕或者打死后捡尸体的逻辑};
#VARIABLE {expectcity} {};
#VARIABLE {expectroomid} {};
#VARIABLE {errorcount} {0};
#VARIABLE {afterlocdo} {NULL};
#VARIABLE {idle} {0};
#VARIABLE {lastcmd} {};
#NOP {发呆房间统计};
#VARIABLE {idleroom} {
  {name} {}
  {count} {0}
}
#NOP {是否使用ado合并指令快速行走,当前仅紫檀站有效,启用ado时下面的max_walk_steps可以设置的大一点};
#NOP {太大的话实际上服务器也会截断执行,未设置ado时根据网络条件不同推荐设置15~20之间为宜。};
#VARIABLE {ado} {0};
#NOP {快速行走最一次执行的步骤长度};
#VARIABLE {max_walk_steps} {15};
#NOP {快速行走分段行走最大延时};
#VARIABLE {max_walk_delay} {1};
#VARIABLE {min_walk_delay} {0.5};
#NOP {快速行走每指令延时};
#VARIABLE {timepercmd} {0.1};
#NOP {上一次执行的指令数};
#VARIABLE {prevcmdcount} {0};
#VARIABLE {targetnpc} {NULL};

#NOP {是否特殊行走步骤};
#FUNCTION isSpecialStep {
  #IF {@startWiths{{%1}{river_}} == 1} {#RETURN {1};};
  #ELSEIF {@startWiths{{%1}{matrix_}} == 1} {#RETURN {1};};
  #ELSEIF {@startWiths{{%1}{killnpc}} == 1} {#RETURN {1};};
  #ELSEIF {@startWiths{{%1}{night_}} == 1} {#RETURN {1};};
  #ELSE {#RETURN {0};};
};
#NOP {回复定位信息,查询后任务完成去扬州钱庄等待};
#LIST {common[responseusers]} {create} {kimjun;morrison;xcjbast};
#VARIABLE {env[waitresponse]} {0};
#ACTION {^%*(%*)告诉你：where are you} {
  reply 当前位于:$city $room;
  #IF {@contains{{common[responseusers]}{@lower{%2}}}} {
    #VARIABLE {env[waitresponse]} {1};
  };
};
#NOP {捕捉房间出口};
#ACTION {^%u - %*$} {
  #VARIABLE {room} {%1};
  #LIST {roomexits} {clear};
  #IF {@len{%2} > 0} {
    #LIST {roomexits} {sort} {@formatExits{%2}};
  };
  #IF {@len{%2} == 0} {
    #SWITCH {"%1"} {
      #CASE {"天阁斋"} {balance};
      #CASE {"天音阁"} {balance};
      #CASE {"威信钱庄"} {balance};
      #CASE {"墨玉斋"} {balance};
      #CASE {"大理钱庄"} {balance};
      #CASE {"通宝斋"} {balance};
      #CASE {"金华斋"} {balance};
      #CASE {"勒马斋"} {balance};
      #CASE {"聚宝斋"} {balance};
      #CASE {"宝龙斋"} {balance};
      #CASE {"万宝斋"} {balance};
    };
  };
  #SWITCH {"%1"} {
    #CASE {"中央广场"} {#VARIABLE {city} {扬州城}};
    #CASE {"广场北"} {#VARIABLE {city} {扬州城}};
    #CASE {"广场南"} {#VARIABLE {city} {扬州城}};
    #CASE {"安定门"} {#VARIABLE {city} {长安城}};
    #CASE {"安远门"} {#VARIABLE {city} {长安城}};
    #CASE {"永宁门"} {#VARIABLE {city} {长安城}};
    #CASE {"长乐门"} {#VARIABLE {city} {长安城}};
    #CASE {"青龙门"} {#VARIABLE {city} {襄阳城}};
    #CASE {"白虎门"} {#VARIABLE {city} {襄阳城}};
    #CASE {"朱雀门"} {#VARIABLE {city} {襄阳城}};
    #CASE {"玄武门"} {#VARIABLE {city} {襄阳城}};
    #CASE {"白驼山"} {#VARIABLE {city} {白驼山}};
    #CASE {"百丈涧"} {#VARIABLE {city} {天山}};
    #CASE {"断魂崖"} {#VARIABLE {city} {天山}};
    #CASE {"报国寺山门"} {#VARIABLE {city} {峨嵋山}};
    #CASE {"草原边缘"} {#VARIABLE {city} {回疆}};
    #CASE {"长江北岸"} {#VARIABLE {city} {扬州城}};
    #CASE {"长江南岸"} {#VARIABLE {city} {扬州城}};
    #CASE {"崇圣寺"} {#VARIABLE {city} {天龙寺}};
    #CASE {"川西土路"} {#VARIABLE {city} {大雪山}};
    #CASE {"大东门"} {#VARIABLE {city} {成都城}};
    #CASE {"大西门"} {#VARIABLE {city} {成都城}};
    #CASE {"大南门"} {#VARIABLE {city} {成都城}};
    #CASE {"大北门"} {#VARIABLE {city} {成都城}};
    #CASE {"独峰岭"} {#VARIABLE {city} {绝情谷}};
    #CASE {"樊川"} {#VARIABLE {city} {终南山}};
    #CASE {"佛山广场"} {#VARIABLE {city} {佛山镇}};
    #CASE {"观胜峰下"} {#VARIABLE {city} {嵩山}};
    #CASE {"金龙峡"} {#VARIABLE {city} {恒山}};
    #CASE {"澜沧江边"} {#VARIABLE {city} {大理城}};
    #CASE {"六盘山"} {#VARIABLE {city} {兰州城}};
    #CASE {"明月屏"} {#VARIABLE {city} {苗疆}};
    #CASE {"南村口"} {#VARIABLE {city} {华山村}};
    #CASE {"南门吊桥"} {#VARIABLE {city} {福州城}};
    #CASE {"日月坪"} {#VARIABLE {city} {黑木崖}};
    #CASE {"汝州城"} {#VARIABLE {city} {嵩山少林}};
    #CASE {"莎萝坪"} {#VARIABLE {city} {华山}};
    #CASE {"苏州北郊"} {#VARIABLE {city} {苏州城}};
    #CASE {"玄岳门"} {#VARIABLE {city} {武当山}};
    #CASE {"岱宗坊"} {#VARIABLE {city} {泰山}};
    #CASE {"泸溪"} {#VARIABLE {city} {铁掌山}};
    #CASE {"泸溪北"} {#VARIABLE {city} {襄阳城}};
    #CASE {"林间道"} {#VARIABLE {city} {佛山镇}};
    #CASE {"山岗"} {#VARIABLE {city} {大理城}};
  };
};
#NOP {捕捉房间出口};
#ACTION {这里{唯一|明显}的出口是 %*。} {
  #LIST {roomexits} {clear};
  #LIST {roomexits} {sort} {@formatExits{%2}};
};
#NOP {捕捉房间出口};
#ACTION {^    这里没有任何明显的出路。} {
  #LIST {roomexits} {clear};
};
#ACTION {^【你现在正处于%1】} {
  #NOP {别名处理};
  #VARIABLE {lastcity} {$city};
  #SWITCH {"%1"} {
    #CASE {"襄阳郊外"} {#VARIABLE {city} {襄阳城}};
    #CASE {"大理城东"} {#VARIABLE {city} {大理城}};
    #CASE {"大理城西"} {#VARIABLE {city} {大理城}};
    #CASE {"大理城南"} {#VARIABLE {city} {大理城}};
    #CASE {"大理王府"} {#VARIABLE {city} {大理城}};
    #CASE {"大理皇宫"} {#VARIABLE {city} {大理城}};
    #CASE {"玉虚观"} {#VARIABLE {city} {大理城}};
    #CASE {"伊犁城"} {#VARIABLE {city} {星宿海}};
    #CASE {"大草原"} {#VARIABLE {city} {回疆}};
    #CASE {"武当后山"} {#VARIABLE {city} {武当山}};
    #CASE {"柳宗镇"} {#VARIABLE {city} {襄阳城}};
    #CASE {"萧府"} {#VARIABLE {city} {黄河流域}};
    #CASE {"极乐世界"} {#VARIABLE {city} {扬州城}};
    #CASE {"中原"} {#VARIABLE {city} {成都城}};
    #DEFAULT {#VARIABLE {city} {%1};}
  };

  #LIST {roomways} {clear};
  #LIST {entrylines} {clear};
  #CLASS captureclass KILL;
  #CLASS captureclass OPEN;
  #ACTION {%*} {
    #LIST {entrylines} {add} {%%1};
  };
  #NOP {这里通过房间名描述行结束出口捕捉，普通房间5行，无出口房间1行};
  #ACTION {^%u -} {
    #CLASS captureclass KILL;
    #SWITCH {"%%1"} {
      #CASE {"天阁斋"} {balance};
      #CASE {"天音阁"} {balance};
      #CASE {"威信钱庄"} {balance};
      #CASE {"墨玉斋"} {balance};
      #CASE {"大理钱庄"} {balance};
      #CASE {"通宝斋"} {balance};
      #CASE {"金华斋"} {balance};
      #CASE {"勒马斋"} {balance};
      #CASE {"聚宝斋"} {balance};
      #CASE {"宝龙斋"} {balance};
      #CASE {"万宝斋"} {balance};
    };
    #VARIABLE {room} {%%1};

    #NOP {处理出口数据，仅处理奇数行数据};
    #LOOP 1 &entrylines {i} {
      #IF {@eval{$i % 2} == 0} {
        #CONTINUE;
      };
      #IF {@len{$entrylines[+$i]} == 0} {
        #CONTINUE;
      };
      #LIST {templist} {create} {@fetchWays{@trim{$entrylines[+$i]}}};
      #NOP {普通房间3行,无出口房间1行才排除房间自身};
      #IF {(&entrylines[] == 5 && $i == 3) || &entrylines[] ==1} {
        #LOCAL {roomindex} {@contains{{templist}{$room}}};
        #IF {$roomindex > 0} {
          #LIST {templist} {delete} {$roomindex};
        };
      };
      #FOREACH {$templist[]} {w} {
        #LIST {roomways} {add} {$w};
      };
    };
  } {1};
  #CLASS captureclass CLOSE;
};

#VARIABLE {walkexception} {0};
#ACTION {^你从山上滚了下来，只觉得浑身无处不疼。} {
  #VARIABLE {walkexception} {1};
};
#NOP {天山异常需要丢掉铁链才能离开};
#ACTION {^你手上拿着铁链，怎么离开！} {
  drop tielian;
};
#NOP {防止指令输入过多，延迟执行,%1为要执行的动作};
#ALIAS {runwait} {
  #MATH {runtime} {$prevcmdcount * $timepercmd};
  #IF {$runtime > $max_walk_delay} {#VARIABLE {runtime} {$max_walk_delay};};
  #IF {$runtime < $min_walk_delay} {#VARIABLE {runtime} {$min_walk_delay};};
  #DELAY {$runtime} {
    %1;
  };
};
#NOP {发呆处理机制};
#VARIABLE {idle} {0};
#VARIABLE {interrupt} {0};
#UNTICKER {idleticker};
#TICKER {idleticker} {
  #IF {$interrupt == 0} {
    #MATH {idle} {$idle + 1};
    #IF {$idle > 60} {
      #VARIABLE {idle} {0};
      checkidle;
    };
  };
} {1};
#NOP {快速行走};
#ALIAS {walk} {
  #VARIABLE {walkexception} {0};
  #VARIABLE {idle} {0};
  #VARIABLE {interrupt} {0};

  #CLASS walkclass KILL;
  #CLASS boatclass KILL;
  #CLASS riverclass KILL;
  #CLASS matrixclass KILL;
  #CLASS fightclass KILL;
  #CLASS nightclass KILL;
  #CLASS fullclass KILL;
  #CLASS dzclass KILL;
  #class getbookclass kill;
  
  stopfight;
  #IF {"$aimcity" == ""} {
    #SHOWME {<ffa>目标城市不明确};
  };
  #ELSEIF {"$aimroom" == ""} {
    #SHOWME {<ffa>目标房间不明确};
  };
  #ELSEIF {"$city" == "$aimcity" && "$room" == "$aimroom" && $roomid == $aimroomid} {
    checkroom
  };
  #ELSE {
    #NOP {发呆处理};
    #LOCAL {tempaimroomid} {$aimroomid};
    #IF {"$city" != "$aimcity"} {
      #NOP {按区域行走};
      #LOCAL {nextcity} {@getNextCity{{$city}{$aimcity}}};
      #IF {"$nextcity" != "$aimcity"} {
        #LOCAL {tempaimroomid} {$cities[$nextcity][cityroomid]};
      };
    };
    #LOCAL {temppath} {@getWalkPath{{$roomid}{$tempaimroomid}}};
    #IF {"$temppath" != "NULL"} {
      #LIST {fullpath} {clear};
      #LIST {fullpath} {create} {$temppath};
      #SHOWME {<ffa>&fullpath[]};
      halt;
      #NOP {几个特殊位置};
      #IF {"$room" != "广场空地"} {
        yun qi;
        yun jing;
        yun jingli;
      };
      runpath
    };
  };
};

#NOP {执行快速行走};
#ALIAS {runpath} {
  #IF {&fullpath[] == 0} {
    #SHOWME {<faa>寻路失败};
  };
  #CLASS walkclass OPEN;
  #ACTION {^你太累了，休息一下再走吧} {
    #CLASS walkclass KILL;
    checkstamina;
  };
  #ACTION {^设定环境变量：action \= \"nextstep\"} {
    #IF {$walkexception == 1} {
      loc {walk};
    };
    #ELSE {
      runpath;
    };
  };
  #ACTION {^设定环境变量：action \= \"checkroom\"} {
    #CLASS walkclass KILL;
    loc {checkroom};
  };
  #ACTION {^这个方向没有出路。} {
    #MATH {errorcount} {$errorcount + 1};
    #IF {$errorcount > 10} {
      #CLASS walkclass KILL;
      checkerror;
    };
  };
  #CLASS walkclass CLOSE;
  #NOP {执行快速行走路径,遇到过河,迷宫,拦路或夜间关门路径截断};
  #VARIABLE {prevcmdcount} {0};
  #LOCAL {specialflag} {0};
  #LOCAL {pl} {&fullpath[]};
  #LIST {walkpath} {clear};
  #LOOP {1} {$pl} {i} {
    #MATH {prevcmdcount} {$prevcmdcount + 1};
    #LOCAL {cmd} {$fullpath[+1]};
    #LIST {fullpath} {delete} {1};
    #LIST {walkpath} {add} {$cmd};
    #MATH {prevcmdcount} {$prevcmdcount+1};
    #IF {@isSpecialStep{$cmd} == 1} {
      #LOCAL {specialflag} {1};
      #BREAK;
    };
    #ELSEIF {$i == $max_walk_steps} {
      #SHOWME {<ffa>$i == $max_walk_steps};
      #LOCAL {tempflag} {1};
      #BREAK;
    };
  };
  #SHOWME {<faa>&walkpath[]};
  #IF {$ado == 0 || @contains{{walkpath}{wwp}} > 0 || @contains{{walkpath}{uwwp}} > 0 || &walkpath[] < 2} {
    #LIST {walkpath} {collapse} {;};
    #IF {$__DEBUG__ == 1} {
      #SHOWME {<ffa>行走指令:$walkpath};
    };
    $walkpath;
  };
  #ELSE {
    #NOP {ado指令发送压缩指令,特殊指令为本地alias,服务器无法识别，需单独执行};
    #IF {$specialflag == 1} {
      #LOCAL {specialstep} {$walkpath[+&walkpath[]]};
      #LIST {walkpath} {delete} {&walkpath[]};
      #IF {&walkpath[] > 0} {
        #LIST {walkpath} {collapse} {|};
        #IF {$__DEBUG__ == 1} {
          #SHOWME {<ffa>行走指令:ado $walkpath};
        };
        #SEND {ado $walkpath};
      };
      $specialstep;
    };
    #ELSE {
      #LIST {walkpath} {collapse} {|};
      #IF {$__DEBUG__ == 1} {
        #SHOWME {<ffa>行走指令:ado $walkpath};
      };
      #SEND {ado $walkpath};
    };
  };
  #IF {$specialflag != 1} {
    #IF {&fullpath[] == 0} {
      runwait {
        set action checkroom;
      };
    };
    #ELSE {
      runwait {
        set action nextstep;
      };
    };
  };
};
#NOP {检查是否到达目的地};
#ALIAS {checkroom} {
  #VARIABLE {expectcity} {$aimcity};
  #VARIABLE {expectroomid} {$aimroomid};
  #IF {"$city" == "$aimcity" && "$room" == "$aimroom"} {
    #CLASS jobwalkclass KILL;
    #VARIABLE {errorcount} {0};
    #VARIABLE {roomid} {$aimroomid};
    #MAP goto {$roomid};
    #IF {$__DEBUG__ == 1} {
      #SHOWME {<ffa>执行$aimdo};
    };
    #IF {"$idleroom[name]" != "" && "$idleroom[name]" != "$room"} {
      #VARIABLE {idleroom} {
        {name} {}
        {count} {0}
      };
    };
    $aimdo
  };
  #ELSE {
    walk
  };
};
#NOP {发呆处理流程,如果在一个房间短期内数次触发发呆，则说明已经进入了某种错误逻辑,记录日志并重启};
#ALIAS {checkidle} {
  #IF {"$room" == "$idleroom[name]"} {
    #VARIABLE {idleroom[count]} {@eval{$idleroom[count] + 1}};
  };
  #ELSE {
    #VARIABLE {idleroom} {
      {name} {$room}
      {count} {0}
    };
  };
  #IF {$idleroom[count] >= 4} {
    logbuff {idle};
    loc {doabort};
  };
  #ELSE {
    loc {walk};
  };
};
#ALIAS {checkstamina} {
  #CLASS staminaclass KILL;
  #CLASS staminaclass OPEN;
  #ACTION {^设定环境变量：action \= \"checkstamina\"} {
    #IF {$hp[jingli] > 500} {
      #CLASS staminaclass KILL;
      loc {walk};
    };
    #ELSE {
      #DELAY {6} {
        hp;
        l;
        set action checkstamina;
      };
    };
  };
  #CLASS staminaclass CLOSE;
  stopwalk;
  #DELAY {6} {
    hp;
    set action checkstamina;
  };
};
#NOP {错误处理,这里加入一些无地图房间的渡船的处理};
#ALIAS {checkerror} {
  stopwalk;
  #VARIABLE {laststep} {};
  #VARIABLE {errorcount} {0};
  #LIST {boatroom} {create} {渡船;小舟;长江渡船;黄河渡船;木筏;竹篓};
  #CLASS walkerrorclass OPEN;
  #ACTION {^只听院内传来一个浑厚的声音说道} {
    #CLASS walkerrorclass KILL;
    #DELAY {1} {
      loc {walk}
    };
  };
  #ACTION {^设定环境变量：action \= \"checkerror\"} {
    #IF {@contains{{boatroom}{$room}} > 0} {
      #IF {@contains{{roomexits}{out}} > 0} {
        #CLASS walkerrorclass KILL;
        out;
        loc {
          walk
        };
      };
      #ELSE {
        #DELAY {2} {
          look;
          set action checkerror
        }
      };
    };
    #ELSEIF {"$room" == "瀑布中"} {
      #CLASS walkerrorclass KILL;
      dohalt {
        tiao anbian;
        loc {walk};
      };
    };
    #ELSEIF {"$room" == "铁舟上"} {
      wwp {tie jiang};
      hua boat;
      tiao shandong;
      #DELAY {1} {
        loc {walk};
      };
    };
    #ELSEIF {"$room" == "小木筏"} {
      hua mufa;
      #DELAY {1} {
        loc {walk};
      };
    };
    #ELSEIF {@isUniqueRoom{{$city}{$room}} == 1} {
      #CLASS walkerrorclass KILL;
      loc {walk}
    };
    #ELSE {
      #NOP {直接排除走过来的方向};
      #IF {"$laststep" != ""} {
        #LOCAL {tempindex} {@contains{{roomexits}{@reverseDir{$laststep}}}};
        #IF {$tempindex > 0} {
          #LIST {roomexits} {delete} {$tempindex};
        };
      };
      #LOCAL {tempindex} {@rnd{{1}{&roomexits[]}}};
      #VARIABLE {laststep} {$roomexits[+$tempindex]};
      $laststep;
      #DELAY {0.5} {
        loc {set action checkerror}
      };
    };
  };
  #CLASS walkerrorclass CLOSE;
  #DELAY {2} {
    look;
    set action checkerror;
  };
};
#NOP {停止所有触发};
#ALIAS {sta} {
  stopwalk;
  jobclear;
  commonclear;
  kungfuclear;
  questclear;
  #VARIABLE {interrupt} {1};
  #CLASS serviceclass KILL;
  #CLASS haltclass KILL;
};
#NOP {停止行走,关闭相关触发器类};
#ALIAS {stopwalk} {
  #CLASS walkclass KILL;
  #CLASS walkerrorclass KILL;
  #CLASS gpsclass KILL;
  #CLASS matrixclass KILL;
  #CLASS riverclass KILL;
  #CLASS boatclass KILL;
  #CLASS nightclass KILL;
  #CLASS haltclass KILL;
  #CLASS yidengclass KILL;
  #CLASS killstopclass KILL;
};
#NOP {停止行走};
#ALIAS {stop} {
  stopwalk
};
#NOP {发起定位};
#ALIAS {loc} {
  #CLASS gpsclass OPEN;
  #ACTION {^设定环境变量：action \= \"locate\"} {
    #CLASS gpsclass KILL;
    #LOCAL {tempid} {@getRoomId{{$city}{$room}{roomexits}{roomways}}};
    #IF {$tempid > 0 } {
      #VARIABLE {roomid} {$tempid};
      #MAP goto {$roomid};
    };
    #ELSE {
      #LOCAL {tempid} {@getRoomId{{}{$room}{roomexits}{roomways}}};
    };
    #IF {$tempid < 0} {
      checkerror
    };
    #ELSE {
      #VARIABLE {roomid} {$tempid};
      #VARIABLE {city} {@getRoomInfo{{$roomid}{ROOMAREA}}};
      #IF {$__DEBUG__ == 1} {
        #SHOWME {<ffa>定位后准备执行%1};
      };
      %1
    };
  };
  #CLASS gpsclass CLOSE;
  look;
  set action locate;
};
#NOP {杀拦路NPC,%1:npcid};
#ALIAS {killnpc} {
  #CLASS killstopclass open;
  #VARIABLE {targetnpc} {NULL};
  #ACTION {^你要对谁做这个动作？} {
    #CLASS killstopclass kill;
    set wimpy 100;
    runwait {set action nextstep}
  };
  #ACTION {^你双手抱拳，对%%*作了个揖道：} {
    #VARIABLE {targetnpc} {%%1};
    #NOP {默认未指定的拦路NPC参数};
    #LOCAL {passexp} {300000};
    #LOCAL {performexp} {2000000};
    #IF {"$common[blocker][%1]" != ""} {
      #LOCAL {passexp} {$common[blocker][%1][passexp]};
      #LOCAL {performexp} {$common[blocker][%1][performexp]};
    };
    #IF {$hp[exp] < $passexp} {
      #CLASS killstopclass kill;
      #DELAY {1} {
        loc {
          doabort
        }
      };
    };
    #ELSE {
      yield no;
      kill %1;
      startfight;
      #IF {$hp[exp] > $performexp} {
        unset wimpy
      };
    };
  };
  #ACTION {^这里没有这个人。} {
    #CLASS killstopclass kill;
    set wimpy 100;
    runwait {set action nextstep}
  };
  #ACTION {^$targetnpc神志迷糊，脚下一个不稳，倒在地上昏了过去。} {
    kill %1;
  };
  #ACTION {^{巨蟒|毒蟒}抽搐了几下，身体缩在一起，死了} {
    stopfight;
    get gold from corpse;
    get silver from corpse;
    loc {#SEND {hi %1}};
  };
  #ACTION {^$targetnpc「啪」的一声倒在地上，挣扎着抽动了几下就死了。} {
    stopfight;
    get gold from corpse;
    get silver from corpse;
    #SEND {hi %1}
  };
  #CLASS killstopclass close;
  #NOP {几个门派特殊情况处理};
  #LOCAL {passflag} {0};
  #LOCAL {passaction} {};
  #IF {"$hp[party]" == "昆仑派" && "%1" == "xi huazi"} {
    #LOCAL {passflag} {1};
  };
  #ELSEIF {"$hp[party]" == "铁掌帮" && ("%1" == "hong xiaotian" || "%1" == "huang lingtian" || "%1" == "ling zhentian")} {
    #LOCAL {passflag} {1};
  };
  #ELSEIF {"$hp[party]" == "嵩山派" && "%1" == "ding mian"} {
    #LOCAL {passflag} {1};
  };
  #ELSEIF {"$hp[party]" == "武当派" && ("%1" == "zhang songxi" || "%1" == "yu lianzhou" || "%1" == "yin liting")} {
    #LOCAL {passflag} {1};
  };
  #ELSEIF {"$hp[party]" == "姑苏慕容" && "%1" == "a bi"} {
    #LOCAL {passflag} {1};
  };
  #ELSEIF {"$hp[party]" == "大轮寺" && "%1" == "hu bayin"} {
    #LOCAL {passflag} {1};
  };
  #ELSEIF {"$hp[party]" == "明教" && ("%1" == "yin wushou" || "%1" == "yang xiao" || "%1" == "fan yao")} {
    #LOCAL {passflag} {1};
  };
  #ELSEIF {"$hp[party]" == "星宿派" "%1" == "caihua zi"} {
    #LOCAL {passflag} {1};
    #LOCAL {passaction} {give 1 silver to caihua zi};
  };
  #ELSEIF {"$hp[party]" == "星宿派" && ("%1" == "chuchen zi" || "%1" == "shihou zi")} {
    #LOCAL {passflag} {1};
  };
  #ELSEIF {"$hp[party]" == "天龙寺" && ("%1" == "liaokuan chanshi" || "%1" == "liaoxing chanshi" || "%1" == "benyin dashi" || "%1" == "liaoqing chanshi" || "%1" == "liaohuo chanshi")} {
    #LOCAL {passflag} {1};
  };
  #ELSEIF {"$hp[party]" == "峨嵋派" && "%1" == "jingfeng shitai"} {
    #LOCAL {passflag} {1};
  };
  #ELSEIF {"$hp[party]" == "少林派" && "%1" == "xuansheng dashi"} {
    #LOCAL {passflag} {1};
  };
  #ELSEIF {"$hp[shen]" == "戾气" && "%1" == "ding mian"} {
    #LOCAL {passflag} {1};
  };
  #IF {$passflag == 1} {
    #CLASS killstopclass kill;
    #DELAY {0.5} {
      #IF {"$passaction" != ""} {
        $passaction;
      };
      set action nextstep
    };
  };
  #ELSE {
    hi %1
  };
};
#NOP {去城市};
#ALIAS {gotocity} {
  #IF {"%1" == ""} {
    #SHOWME {<ffa>缺少目标城市};
  };
  #ELSE {
    #VARIABLE {aimcity} {%1};
    #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
    #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
    walk;
  };
};
#NOP {去房间，优先在当前城市搜索，如仅存在于一个城市则去第一个，否则中止,%2:做什么};
#ALIAS {gotoroom} {
  #IF {"%1" == ""} {
    #SHOWME {<ffa>缺少目标房间};
  };
  #ELSE {
    #LOCAL {temprooms} {};
    #MAP list {%1} {roomarea} {$city} {VARIABLE} {temprooms};
    #IF {&temprooms[] == 0} {
      #MAP list {%1} {VARIABLE} {temprooms};
      #IF {&temprooms[] == 0} {
        #SHOWME {<faa>未找到房间%1};
      };
      #ELSE {
        #FOREACH {*temprooms[]} {r} {
          #LOCAL {tempcities[@getRoomInfo{{$r}{ROOMAREA}}]} {1};
        };
        #IF {&tempcities[] > 1} {
          #SHOWME {<faa>房间%1存在于*tempcities[]中，请细化条件。};
        };
        #ELSE {
          #VARIABLE {aimroom} {@getRoomInfo{{*temprooms[+1]}{ROOMNAME}}};
          #VARIABLE {aimroomid} {*temprooms[+1]};
          #VARIABLE {aimcity} {*tempcities[+1]};
          #VARIABLE {aimdo} {%2};
          walk;
        };
      };
    };
    #ELSE {
      #VARIABLE {aimroom} {@getRoomInfo{{*temprooms[+1]}{ROOMNAME}}};
      #VARIABLE {aimroomid} {*temprooms[+1]};
      #VARIABLE {aimcity} {@getRoomInfo{{$aimroomid}{ROOMAREA}}};
      #VARIABLE {aimdo} {%2};
      walk;
    };
  };
};
#NOP {去NPC,%1npc名字,%2做什么};
#ALIAS {gotonpc} {
  #IF {"%1" == ""} {
    #SHOWME {<ffa>缺少目标NPC};
  };
  #ELSE {
    #LOCAL {location} {$mapnpcs[%1]};
    #IF {"$location" == ""} {
      #SHOWME {<faa>未找到NPC %1};
    };
    #ELSE {
      #VARIABLE {aimcity} {$location[city]};
      #VARIABLE {aimroom} {$location[room]};
      #VARIABLE {aimroomid} {$location[roomid]};
      #VARIABLE {aimdo} {%2};
      walk;
    };
  };
};
#NOP {去做某件事,%1城市,%2房间,%3动作,不保留现场};
#ALIAS {gotodo} {
  #IF {"%1" == ""} {
    #SHOWME {<ffa>缺少目标城市};
  };
  #ELSEIF {"%2" == ""} {
    #SHOWME {<ffa>缺少目标房间};
  };
  #LOCAL {temproomlist} {@findRooms{{%1}{%2}}};
  #IF {&temproomlist[] == 0} {
    #SHOWME {<faa>在%1中未检索到房间%2};
  };
  #ELSE {
    #VARIABLE {aimcity} {%1};
    #VARIABLE {aimroomid} {$temproomlist[+1]};
    #IF {@isNumber{%2}} {
      #VARIABLE {aimroom} {@getRoomInfo{{$aimroomid}{ROOMNAME}}};
    };
    #ELSE {
      #VARIABLE {aimroom} {%2};
    };
    #VARIABLE {aimdo} {%3};
    walk
  };
};
#NOP {去做某件事,%1城市,%2房间,%3动作,保留现场};
#ALIAS {gotofor} {
  #IF {"%1" == ""} {
    #SHOWME {<ffa>缺少目标城市};
  };
  #ELSEIF {"%2" == ""} {
    #SHOWME {<ffa>缺少目标房间};
  };
  #VARIABLE {aimbackup} {
    {city}{$aimcity}
    {room}{$aimroom}
    {roomid}{$aimroomid}
    {do}{$aimdo}
  };
  gotodo {%1} {%2} {%3}
};
#NOP {从备份的目标中恢复行走};
#ALIAS {restorewalk} {
  #SHOWME {$aimbackup};
  #VARIABLE {aimcity} {$aimbackup[city]};
  #VARIABLE {aimroom} {$aimbackup[room]};
  #VARIABLE {aimroomid} {$aimbackup[roomid]};
  #VARIABLE {aimdo} {$aimbackup[do]};
  #VARIABLE {aimbackup} {};
  walk
};
#NOP {去师门,%1:后续指令};
#ALIAS {gomaster} {
  #IF {"$mapnpcs[$hp[master][name]]" == ""} {
		#SHOWME {<faa>未找到师傅资料};
	};
  #ELSE {
    gotonpc {$hp[master][name]} {%1};
  };
};
#NOP {初始化加载个性化地图};
#ALIAS {setuppersonalmap} {
  #IF {"$hp[sex]" == "n"} {
    #NOP {割了的人不能进丽春院及内部房间};
    #MAP SET {roomdata} {l} {494};
    #MAP SET {roomdata} {l} {495};
    #MAP SET {roomdata} {l} {496};
  };
  #IF {"$hp[party]" == "姑苏慕容" | "$hp[party]" == "普通百姓"} {
    #NOP {开启慕容地道};
    #MAP AT {1964} {#MAP LINK {zuan didao} {2981}};
    #MAP AT {1915} {#MAP LINK {zuan didao} {2981}};
    #MAP AT {1870} {#MAP LINK {zuan didao} {2981}};
    #MAP AT {293} {#MAP LINK {push 桥栏;d} {2981}};
  };
};
#NOP {各区域ALIAS};
#ALIAS {bts} {
  #VARIABLE {aimcity} {白驼山};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {cz} {
  #VARIABLE {aimcity} {沧州城};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {ca} {
  #VARIABLE {aimcity} {长安城};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {clb} {
  #VARIABLE {aimcity} {长乐帮};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {cd} {
  #VARIABLE {aimcity} {成都城};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {dl} {
  #VARIABLE {aimcity} {大理城};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {dlhg} {
  #VARIABLE {aimcity} {大理城};
  #VARIABLE {aimroom} {皇宫正门};
  #VARIABLE {aimroomid} {@getRoomId{{$aimcity}{$aimroom}}};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {dlwf} {
  #VARIABLE {aimcity} {大理城};
  #VARIABLE {aimroom} {王府大厅};
  #VARIABLE {aimroomid} {@getRoomId{{$aimcity}{$aimroom}}};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {dxs} {
  #VARIABLE {aimcity} {大雪山};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {dls} {
  #VARIABLE {aimcity} {大雪山};
  #VARIABLE {aimroom} {大轮寺山门};
  #VARIABLE {aimroomid} {@getRoomId{{$aimcity}{$aimroom}}};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {ems} {
  #VARIABLE {aimcity} {峨嵋山};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {fs} {
  #VARIABLE {aimcity} {佛山镇};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {fz} {
  #VARIABLE {aimcity} {福州城};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {gb} {
  #VARIABLE {aimcity} {丐帮};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {};
  walk;
};
#ALIAS {gyz} {
  #VARIABLE {aimcity} {归云庄};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {hz} {
  #VARIABLE {aimcity} {杭州城};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {hmy} {
  #VARIABLE {aimcity} {黑木崖};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {};
  walk;
};
#ALIAS {hengs} {
  #VARIABLE {aimcity} {恒山};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {hh} {
  #VARIABLE {aimcity} {黄河流域};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {hs} {
  #VARIABLE {aimcity} {华山};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {hsc} {
  #VARIABLE {aimcity} {华山村};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {hdg} {
  #VARIABLE {aimcity} {蝴蝶谷};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {hj} {
  #VARIABLE {aimcity} {回疆};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {jx} {
  #VARIABLE {aimcity} {嘉兴城};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {jqg} {
  #VARIABLE {aimcity} {绝情谷};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {kls} {
  #VARIABLE {aimcity} {昆仑山};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {lz} {
  #VARIABLE {aimcity} {兰州城};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {mz} {
  #VARIABLE {aimcity} {梅庄};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {wdj} {
  #VARIABLE {aimcity} {苗疆};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {mj} {
  #VARIABLE {aimcity} {明教};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {mr} {
  #VARIABLE {aimcity} {姑苏慕容};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {ny} {
  #VARIABLE {aimcity} {南阳城};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {nb} {
  #VARIABLE {aimcity} {宁波城};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {njc} {
  #VARIABLE {aimcity} {牛家村};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {pdz} {
  #VARIABLE {aimcity} {平定州};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {ptsl} {
  #VARIABLE {aimcity} {莆田少林};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {qzj} {
  #VARIABLE {aimcity} {全真教};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {mg} {
  #VARIABLE {aimcity} {蒙古};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {mtl} {
  #VARIABLE {aimcity} {曼佗罗山庄};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {sld} {
  #VARIABLE {aimcity} {神龙岛};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {sls} {
  #VARIABLE {aimcity} {嵩山少林};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {sz} {
  #VARIABLE {aimcity} {苏州城};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {tais} {
  #VARIABLE {aimcity} {泰山};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {tg} {
  #VARIABLE {aimcity} {塘沽城};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {thd} {
  #VARIABLE {aimcity} {桃花岛};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {tyx} {
  #VARIABLE {aimcity} {桃源县};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {tls} {
  #VARIABLE {aimcity} {天龙寺};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {tians} {
  #VARIABLE {aimcity} {天山};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {tzs} {
  #VARIABLE {aimcity} {铁掌山};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {wds} {
  #VARIABLE {aimcity} {武当山};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {wls} {
  #VARIABLE {aimcity} {无量山};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {xxh} {
  #VARIABLE {aimcity} {星宿海};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {xy} {
  #VARIABLE {aimcity} {襄阳城};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {xyp} {
  #VARIABLE {aimcity} {逍遥派};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {yz} {
  #VARIABLE {aimcity} {扬州城};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {yzw} {
  #VARIABLE {aimcity} {燕子坞};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {zns} {
  #VARIABLE {aimcity} {终南山};
  #VARIABLE {aimroom} {$cities[$aimcity][cityroom]};
  #VARIABLE {aimroomid} {$cities[$aimcity][cityroomid]};
  #VARIABLE {aimdo} {%1};
  walk;
};
#ALIAS {zhp} {
  gotodo {扬州城} {杂货铺} {dlist}
};