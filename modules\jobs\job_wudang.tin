#NOP {武当任务模块};
#ALIAS {jobgo_wudang} {
  on_wudang_before_go {gotodo {武当山} {天乙真庆宫} {startfull {jobask_wudang}}}
};
#ALIAS {jobask_wudang} {
  on_wudang_before_ask {gotodo {武当山} {三清殿} {jobask_wudang_ask}};
};
#ALIAS {jobask_wudang_ask} {
  #NOP {询问结果,0:成功,1:放弃,2:busy,3:空挡,4:刚做完,5:其他};
  #VARIABLE {askresult} {0};
  #VARIABLE {joblocation} {};
  #VARIABLE {jobnpc_bastard} {};
  #VARIABLE {jobnpc_bastard_title} {};
  #VARIABLE {jobnpc_party} {};
  #VARIABLE {jobnpc_skill} {};
  #VARIABLE {jobnpc_desc} {};
  #CLASS jobrequestclass OPEN;
  #ACTION {^你向宋远桥打听有关『job』的消息。} {
    #VARIABLE {idle} {0};
    #CLASS jobresponseclass KILL;
    #CLASS jobresponseclass OPEN;
    #ACTION {^宋远桥在你的耳边悄声说道：据说%+4u%u正在%*周围%!*捣乱，你马上去给我巡视一圈} {
      #VARIABLE {jobnpc_bastard_title} {%%%1};
      #VARIABLE {jobnpc_bastard} {%%%2};
      #VARIABLE {joblocation} {%%%3};
    };
    #ACTION {^宋远桥在你的耳边悄声说道：据说%+4u%u正在%*捣乱，你马上去给我巡视一圈} {
      #VARIABLE {jobnpc_bastard_title} {%%%1};
      #VARIABLE {jobnpc_bastard} {%%%2};
      #VARIABLE {joblocation} {%%%3};
    };
    #ACTION {^宋远桥在你的耳边悄声说道：据门派弟子来报，此人是来自%*的%!*，尤为擅长%*的功夫} {
      #VARIABLE {jobnpc_party} {%%%1};
      #VARIABLE {jobnpc_skill} {%%%2};
      echo {checkask};
    };
    #ACTION {^宋远桥在你的耳边悄声说道：老头子已追查到此人是我武当出身，尤为擅长%*的功夫} {
      #VARIABLE {jobnpc_skill} {%%%1};
      echo {checkask} {2};
    };
    #ACTION {^宋远桥在你的耳边悄声说道：此人的武功%*，} {
      #NOP {当武当NPC等级底时是没有这句话的};
      #VARIABLE {jobnpc_desc} {%%%1};
    };
    #ACTION {^{设定环境变量：action \= \"checkask\"|你设定checkask为反馈信息}} {
      resonate {checkask};
      #VARIABLE {echots} {0};
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {
        #IF {@contains{{conf[fangqiparty]}{$jobnpc_party}} > 0 || @contains{{conf[fangqiskill]}{$jobnpc_skill}} > 0 || @contains{{conf[fangqidesc]}{$jobnpc_desc}} > 0} {
          joblog {<acf>主动放弃来自【$jobnpc_party】使用【$jobnpc_skill】描述为【$jobnpc_desc】的恶贼。};
          jobfangqi_wudang
        };
        #ELSE {
          #NOP {武当任务需要按距离从近至远访问,否则可能路过NPC所在房间导致失败};
          parsejoblocation {$joblocation} {jobadjust_wudang} {
            joblog {<acf>未能解析地址【$joblocation】。};
            jobfangqi_wudang
          } {2} {1}
        };
      };
    };
    #ACTION {^宋远桥说道：「现在暂时没有适合你的工作。」} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {on_wudang_wait};
    };
    #ACTION {^宋远桥说道：「我不是告诉你了吗，有人在捣乱。你就快去吧！」} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {jobfangqi_wudang};
    };
    #ACTION {^宋远桥说道：「你刚做完武当锄奸任务，还是先去休息一会吧。} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {jobprepare};
    };
    #ACTION {^宋远桥说道：「你刚做完%*任务，还是先休息一会吧。」} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {on_wudang_wait};
    };
    #ACTION {^宋远桥说道：「%*的正气还不够，我无法放心让你做这个任务！」} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {on_wudang_wait};
    };
    #ACTION {^宋远桥说道：「现在我这里没有给你的任务，你还是先处理好你其他事情再说吧。」} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {doquit};
    };
    #CLASS jobresponseclass CLOSE;
  };
  #CLASS jobrequestclass CLOSE;
  jobclear_wudang;
  execute {
    cond;
    jobtimes;
    time;
    ask song yuanqiao about job;
  };
};
#NOP {判定,%1:空};
#ALIAS {jobadjust_wudang} {
  #IF {"$jobnpc_desc" == "已入化境" || @contains{{common[dangerwdskills]}{$jobnpc_skill}} > 0} {
    jobfangqi_wudang_ask {1}
  };
  #ELSE {
    jobdo_wudang
  };
};
#NOP {做武当任务,%1:重复搜索标志};
#ALIAS {jobdo_wudang} {
  #VARIABLE {jobnpc_bastard_id} {none};
  #CLASS jobdoclass KILL;
  #CLASS jobdoclass OPEN;
  #NOP {被动式遇敌屏蔽掉};
  #NOP #ACTION {^$jobnpc_bastard对着你发出一阵阴笑} {
  #NOP  jobfight_wudang;
  #NOP } {1};

  #NOP {增加遇到恶贼的NPC};               
  #ACTION {$jobnpc_bastard_title $jobnpc_bastard(%*)} {
    jobfight_wudang;
  } {1};

  #ACTION {^$jobnpc_bastard{大喊一声：不好|「啪」的一声倒在地上}} {
    #NOP {未防止遇到NPC时出现吃指令，战斗结束触发放在这里};
    #CLASS jobdoclass KILL;
    #CLASS jobcheckclass KILl;
    #CLASS jobfightclass KILL;
    joblog {<acf>顺利杀死使用【$jobnpc_skill】描述为【$jobnpc_desc】的恶贼，耗时@elapsed{$jobfight_ts}秒。};
    #DELAY {1} {
      loc {
        dohalt {
          stopfight;
          on_wudang_down {jobfinish_wudang}
        };
      };
    };
  };
  #CLASS jobdoclass CLOSE;
  #VARIABLE {jobstart_ts} {@now{}};
  taskbegin;
  taskrecord;
  joblog {<acf>寻找位于【$joblocation】使用【$jobnpc_skill】的恶贼【$jobnpc_bastard】。};
  closewimpy;
  wwp;
  pfm_buff_normal;
  createpfm {@getFightPerform{$jobnpc_skill}} {1} {$jobnpc_bastard_id} {$jobnpc_bastard_id};
  jobnextroom {checkbastard {%1}} {jobfail_wudang {%1}};
};
#NOP {武当搜索失败,%1:重复搜索标识};
#ALIAS {jobfail_wudang} {
  #LOCAL {iter} {@eval{@eval{%1} + 1}};
  #LOCAL {wanderroom} {$common[wanderwheres][$jobcity$jobroom]};
  #IF {$iter == 1} {
    joblog {<acf>未能找到位于【$joblocation】的【$jobnpc_bastard】，扩大范围搜索。};
    parsejoblocation {$joblocation} {jobdo_wudang {$iter}} {jobfail_wudang {$iter}} {5};
  };
  #ELSEIF {$iter == 2 && "$wanderroom" != ""} {
    joblog {<acf>还是未能找到位于【$joblocation】的【$jobnpc_bastard】，开始漫游搜索。};
    loc {gotoroom {$wanderroom[roomid]} {jobwander_wudang {$wanderroom[roomname]} {$wanderroom[range]}}}
  };
  #ELSE {
    joblog {<acf>终究未能找到位于【$joblocation】的【$jobnpc_bastard】。};
    #NOP {不用去放弃了，已经放弃过了};
    dohalt {jobfangqi_wudang}
  };
};
#NOP {%1:房间名称,%2:步数};
#ALIAS {jobwander_wudang} {
  job_wander {checkbastard {} {jobwander_wudang {%1} {@eval{%2 - 1}}}} {jobfail_wudang {3}} {%1} {%2}
};
#NOP {确认恶贼,%1:重复搜索标志,%2:额外动作};
#ALIAS {checkbastard} {
  #VARIABLE {missingflag} {0};
  #CLASS jobcheckclass KILl;
  #CLASS jobcheckclass OPEN;
  #ACTION {^这里没有 $jobnpc_bastard_id} {
    #VARIABLE {missingflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkbastard\"|你设定checkbastard为反馈信息}} {
    resonate {checkbastard};
    #VARIABLE {echots} {0};
    #VARIABLE {idle} {0};
    #CLASS jobcheckclass KILL;
    #CLASS jobfightclass KILL;
    #IF {$walkstoppedts != 0} {
      #NOP {啥也不做};
      #CLASS jobcheckclass KILL;
    };
    #ELSEIF {$escapeflag == 1 || $dangerousflag == 1} {
      #IF {@elapsed{$escapetimestap} > 30} {
        runawayside {doabort}
      };
      #NOP {已启动逃跑，啥都不干};
    };
    #ELSEIF {"$jobnpc_bastard_id" == "none"} {
      #IF {"%2" != ""} {
        %2
      };
      #ELSE {
        pfm_buff_normal;
        closewimpy;
        runwait {jobnextroom {checkbastard {%1}} {jobfail_wudang {%1}} {1}}
      };
    };
    #ELSEIF {$missingflag == 1} {
      jobfangqi_wudang
    };
    #ELSE {
      jobfight_wudang
    };
  };
  #CLASS jobcheckclass CLOSE;
  ensure {look} {checkbastard}
};
#ALIAS {jobfight_wudang} {
  #VARIABLE {jobfight_ts} {@now{}};
  #CLASS jobfightclass KILL;
  #VARIABLE {nofightroom} {0};
  #VARIABLE {jobnpcmissed} {0};
  #CLASS jobfightclass OPEN;
  #ACTION {^这里不准战斗。} {
    #VARIABLE {nofightroom} {1};
  };
  #LINE ONESHOT #ACTION {$jobnpc_bastard_title $jobnpc_bastard(%*)} {
    #VARIABLE {jobnpc_bastard_id} {@lower{%%1}};
    stopwalk;
    #IF {$escapeflag != 1} {
      ensure {
        kill $jobnpc_bastard_id;
        startfight {} {$jobnpc_bastard};
        autopfm
      } {checkfight}
    };
  };
  #ACTION {^$jobnpc_bastard对着你说道：嘿嘿！有胆敢跟过来} {
    #VARIABLE {jobfight_ts} {@now{}};
    startfight {} {$jobnpc_bastard}
  };
  #ACTION {^{设定环境变量：action \= \"checkfight\"|你设定checkfight为反馈信息}} {
    resonate {checkfight};
    #VARIABLE {echots} {0};
    kill $jobnpc_bastard_id;
    #IF {$nofightroom == 1} {
      #VARIABLE {nofightroom} {0};
      follow $jobnpc_bastard_id;
      kick $jobnpc_bastard_id;
      #DELAY {1} {
        startfight;
        autopfm;
        echo {checkfight}
      }
    };
    #ELSEIF {$jobnpcmissed == 1} {
      stopfight;
      #DELAY {1} {
        loc {jobfangqi_wudang}
      };
    };
  };
  #ACTION {^这里没有 $jobnpc_bastard_id。} {
    #VARIABLE {jobnpcmissed} {1};
  };
  #CLASS jobfightclass CLOSE;
  #VARIABLE {jobfight_target} {$jobnpc_bastard};
  #VARIABLE {jobfight_ts} {@now{}};
};
#ALIAS {jobfinish_wudang} {
  taskstats {武当};
  gotodo {武当山} {三清殿} {jobfinish_wudang_ask};
};
#ALIAS {jobfinish_wudang_ask} {
  #NOP {询问结果,0:成功,1:busy,2:失败,3:没领任务};
  #VARIABLE {askresult} {0};
  #VARIABLE {jobreward_exp} {};
  #VARIABLE {jobreward_pot} {};
  #VARIABLE {jobreward_tongbao} {零};
  #CLASS jobrequestclass OPEN;
  #ACTION {^你向宋远桥打听有关『完成』的消息。} {
    #VARIABLE {idle} {0};
    #CLASS jobresponseclass OPEN;
    #ACTION {^宋远桥说道：「$hp[name]你怎么搞的，任务都没做,我看你是捣乱来了！」} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {jobgo_wudang};
    };
    #ACTION {^宋远桥说道：「$hp[name]你怎么搞的，居然让那恶贼给跑了！」} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {jobfangqi_wudang};
    };
    #ACTION {^恭喜你！你成功的完成了武当任务！你被奖励了：} {
      #VARIABLE {lastjob} {武当};
      #CLASS rewardclass OPEN;
      #ACTION {^%*点经验!} {
        #VARIABLE {jobreward_exp} {%%%%1};
      };
      #ACTION {^%*点潜能!} {
        #VARIABLE {jobreward_pot} {%%%%1};
      };
      #ACTION {^%*枚情怀币} {
        #VARIABLE {jobreward_tongbao} {%%%%1};
      };
      #CLASS rewardclass CLOSE;
      echo {checkover}
    };
    #ACTION {^宋远桥说道：「$hp[name]%*往常都是我们七个师兄弟集齐向师傅拜寿。」} {
      #VARIABLE {env[yttl]} {YES};
      set env_yttl;
    };
    #ACTION {^{设定环境变量：action \= \"checkover\"|你设定checkover为反馈信息}} {
      #VARIABLE {echots} {0};
      #CLASS rewardclass KILL;
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      joblog {<acf>成功完成，获得【$jobreward_exp】点经验【$jobreward_pot】点潜能【$jobreward_tongbao】枚情怀币，耗时@elapsed{$jobstart_ts}秒。};
      taskgain {@ctd{$jobreward_exp}} {@ctd{$jobreward_tongbao}};
      taskend;
      jobclear_wudang;
      dohalt {
        on_wudang_finish {jobprepare}
      };
    };
    #CLASS jobresponseclass CLOSE;
  };
  #CLASS jobrequestclass CLOSE;
  ask song yuanqiao about 完成;
};
#ALIAS {jobfangqi_wudang} {
  gotodo {武当山} {三清殿} {jobfangqi_wudang_ask};
};
#NOP {放弃,%1:是否真的放弃};
#ALIAS {jobfangqi_wudang_ask} {
  #CLASS jobrequestclass KILL;
  #NOP {询问结果,0:成功,1:busy};
  #VARIABLE {askresult} {0};
  #CLASS jobrequestclass OPEN;
  #ACTION {^你向宋远桥打听有关『放弃』的消息。} {
    #VARIABLE {idle} {0};
    #CLASS jobresponseclass OPEN;
    #ACTION {^宋远桥说道：「%*这个任务确实比较难完成，下次给你简单的，先退下吧！」} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      #IF {"%1" == ""} {
        joblog {<acf>未能完成任务。};
        cond;
        dohalt {jobprepare};
      };
      #ELSE {
        dohalt {jobdo_wudang}
      };
    };
    #ACTION {^宋远桥说道：「%*你太让我失望了，居然这么点活都干不好，先退下吧！」} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      #IF {"%1" == ""} {
        joblog {<acf>未能完成任务，耗时@elapsed{$jobstart_ts}秒。};
        dohalt {jobprepare};
      };
      #ELSE {
        jobdo_wudang
      };
    };
    #ACTION {^宋远桥说道：「%*你又没在我这里领任务，瞎放弃什么呀！」} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {jobprepare};
    };
    #CLASS jobresponseclass CLOSE;
  };
  #CLASS jobrequestclass CLOSE;
  ask song yuanqiao about 放弃;
};
#ALIAS {jobclear_wudang} {
  #VARIABLE {jobnpc_bastard} {};
  #VARIABLE {jobnpc_bastard_title} {};
  #VARIABLE {jobnpc_bastard_id} {};
  #VARIABLE {jobnpc_skill} {};
  #VARIABLE {jobnpc_desc} {};
  #VARIABLE {joblocation} {};
  #LIST {jobroomlist} {clear};
  #VARIABLE {jobfight_ts} {0};
  #VARIABLE {jobnpc_count} {0};
  #VARIABLE {jobstart_ts} {0};
};