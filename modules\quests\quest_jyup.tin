#NOP {九阴真经上,%1:后续指令};
#ALIAS {goquest_jyup} {
  #IF {@getSkillLevel{qimen-bagua} < 51} {
    #SHOWME {<faa>奇门八卦太低了，先读读书吧！};
    %1
  };
  #ELSEIF {@getSkillLevel{qimen-bagua} < 151} {
    learnbagua {goquest_jyup {%1}};
  };
  #ELSE {
    #VARIABLE {questmodule} {九阴上};
    gotonpc {黄药师} {jyup_ask_yaoshi {%1}}
  };
};
#ALIAS {jyup_ask_yaoshi} {
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^这里没有这个人。} {
    #CLASS questclass KILL;
    questdelay {$questmodule} {0} {3600};
    %1
  };
  #ACTION {^你向黄药师打听有关『周伯通』的消息。} {
    #CLASS questclass KILL;
    dohalt {
      gotonpc {黄蓉} {jyup_ask_huangrong {%1}}
    };
  };
  #CLASS questclass CLOSE;
  dohalt {ask huang yaoshi about 周伯通}
};
#ALIAS {jyup_ask_huangrong} {
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^这里没有这个人。} {
    #CLASS questclass KILL;
    questdelay {$questmodule} {0} {3600};
    %1
  };
  #ACTION {^你向黄蓉打听有关『周伯通』的消息。} {
    i;
    dohalt {
      gotodo {桃花岛} {岩洞} {jyup_ask_zhou {%1}}
    };
  };
  #CLASS questclass CLOSE;
  dohalt {ask huang rong about 周伯通}
};
#ALIAS {jyup_ask_zhou} {
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你要对谁做这个动作} {
    #VARIABLE {idle} {0};
    poor;
    #DELAY {6} {
      hi zhou botong
    };
  };
  #ACTION {^你双手抱拳，对周伯通作了个揖道} {
    #CLASS questclass KILL;
    dohalt {
      give fan he to zhou botong;
      jyup_ask_jiuyin {%1}
    };
  };
  #CLASS questclass CLOSE;
  hi zhou botong
};
#ALIAS {jyup_ask_jiuyin} {
  #VARIABLE {leaveflag} {0};
  #VARIABLE {askresult} {0};
  #VARIABLE {startts} {0};
  #VARIABLE {askflag} {0};
  #VARIABLE {missingflag} {0};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^这里没有这个人} {
    #CLASS questclass KILL;
    questupdate {$questmodule};
    dohalt {%1}
  };
  #NOP {周伯通说道：「我正忙着呢,你过一会儿再来吧。」};
  #NOP {看起来周伯通在二十六分三十秒之内不准备理你。};
  #ACTION {^你向周伯通打听有关『九阴真经』的消息。} {
    #CLASS questresponseclass KILL;
    #CLASS questresponseclass OPEN;
    #ACTION {^周伯通拉着你说道：“乖乖的听我说故事吧} {
      #CLASS questresponseclass KILL;
      dohalt {ask zhou botong about 故事}
    };
    #ACTION {^周伯通说道：「你没看见我正忙着么} {
      #CLASS questresponseclass KILL;
      #DELAY {5} {
        dohalt {ask zhou about 九阴真经}
      };
    };
    #ACTION {^周伯通说道：「我正忙着呢} {
      #CLASS questresponseclass KILL;
      questdelay {$questmodule} {0} {10800};
      dohalt {%1}
    };
    #ACTION {^周伯通似乎不懂你的意思} {
      #NOP {这里可能是上次zhou刷新跑了,这里直接问下一步};
      #CLASS questresponseclass KILL;
      dohalt {ask zhou botong about 功夫}
    };
    #CLASS questresponseclass CLOSE;
  };
  #ACTION {^你向周伯通打听有关『故事』的消息。} {
    #CLASS questresponseclass OPEN;
    #ACTION {^周伯通看你不问后来怎样，便赌气不说了} {
      #CLASS questresponseclass KILL;
      dohalt {ask zhou botong about 后来怎样}
    };
  };
  #ACTION {^你向周伯通打听有关『后来怎样』的消息。} {
    #VARIABLE {idle} {0};
    #VARIABLE {askresult} {0};
    #CLASS questresponseclass KILL;
    #CLASS questresponseclass OPEN;
    #ACTION {^周伯通讲完又开始对你说起他和全真教的故事} {
      dohalt {
        answer n
      }
    };
    #ACTION {^周伯通说道：「华山剑宗的高级剑法是？」} {
      #DELAY {0.5} {answer dugu-jiujian}
    };
    #ACTION {^周伯通说道：「有种剑法，练了使人绝子绝孙。这个剑法的名字是什么？」} {
      #DELAY {0.5} {answer pixie-jian}
    };
    #ACTION {^周伯通说道：「我求杨过这小子教我的掌法叫做什么名称？」} {
      #DELAY {0.5} {answer anran-zhang}
    };
    #ACTION {^周伯通说道：「峨嵋的灭绝师太曾经以那种功夫将张无忌打的倒地不起？」} {
      #DELAY {0.5} {answer jieshou-jiushi}
    };
    #ACTION {^周伯通说道：「少林和尚们的内功是什么？」} {
      #DELAY {0.5} {answer yijin-jing}
    };
    #ACTION {^周伯通说道：「我全真教的内功心法是什么？」} {
      #DELAY {0.5} {answer xiantian-gong}
    };
    #ACTION {^周伯通说道：「古墓派的内功心法是什么？」} {
      #DELAY {0.5} {answer yunu-xinjing}
    };
    #ACTION {^周伯通说道：「黄老邪的那厉害指法是什么？」} {
      #DELAY {0.5} {answer tanzhi-shentong}
    };
    #ACTION {^周伯通说道：「一灯大师的拿手绝学是什么？」} {
      #DELAY {0.5} {answer yiyang-zhi}
    };
    #ACTION {^周伯通说道：「桃花的狂风绝技要以什么和旋风扫叶腿配合施展？」} {
      #DELAY {0.5} {answer luoying-zhang}
    };
    #ACTION {^周伯通说道：「段家有门以指为剑的剑法，叫什么名字？」} {
      #DELAY {0.5} {answer liumai-shenjian}
    };
    #ACTION {^周伯通说道：「铁掌帮的著名轻功是？} {
      #DELAY {0.5} {answer shuishangpiao}
    };
    #ACTION {^周伯通说道：「神龙洪老头的暗器是叫做什} {
      #DELAY {0.5} {answer hansha-sheying}
    };
    #ACTION {^周伯通说道：「老毒物的奇怪内功是什么} {
      #DELAY {0.5} {answer hamagong}
    };
    #ACTION {^周伯通说道：「老叫化和郭兄弟都会的掌法是什么} {
      #DELAY {0.5} {answer xianglong-zhang}
    };
    #ACTION {^周伯通说道：「峨嵋的剑法是什么} {
      #DELAY {0.5} {answer huifeng-jian}
    };
    #ACTION {^周伯通说道：「武当张老头创出的软绵绵拳法叫什么名字} {
      #DELAY {0.5} {answer taiji-quan}
    };
    #ACTION {^周伯通说道：「明教张无忌那小子的内功是什么？} {
      #DELAY {0.5} {answer jiuyang-shengong}
    };
    #ACTION {^周伯通说道：「星宿派恶名昭彰的功夫是什么？} {
      #DELAY {0.5} {answer huagong-dafa}
    };
    #ACTION {^周伯通说道：「慕容家的家传特殊招架技能是？} {
      #DELAY {0.5} {answer douzhuan-xingyi}
    };
    #ACTION {^周伯通说道：「明教前教主阳顶天练什么功夫练到走火而死的？} {
      #DELAY {0.5} {answer qiankun-danuoyi}
    };
    #ACTION {^周伯通说道：「黄老邪的箫乐是由他的内功而来。这个内功的名字是？} {
      #DELAY {0.5} {answer bihai-chaosheng}
    };
    #ACTION {^周伯通说道：「金蛇郎君夏雪宜的剑法叫什么名称？} {
      #DELAY {0.5} {answer jinshe-jianfa}
    };
    #ACTION {^周伯通说道：「华山气宗的镇山之宝是什么} {
      #DELAY {0.5} {answer zixia-gong}
    };
    #ACTION {^周伯通说道：「我老顽童的自创拳招是什么啊} {
      #DELAY {0.5} {answer kongming-quan}
    };
    #ACTION {^周伯通说道：「老叫化教黄蓉那小丫头的是什么拳法} {
      #DELAY {0.5} {answer xiaoyaoyou}
    };
    #ACTION {^周伯通说道：「古墓一派的特殊拳法，叫什么名称} {
      #DELAY {0.5} {answer meinu-quan}
    };
    #ACTION {^周伯通说道：「黄老邪除了教杨过弹指神通，还教了什么武} {
      #DELAY {0.5} {answer yuxiao-jian}
    };
    #ACTION {^周伯通说道：「瑛姑的高明身法是什么？} {
      #DELAY {0.5} {answer niqiugong}
    };
    #ACTION {^周伯通说道：「黄蓉的家传手法叫什么名字} {
      #DELAY {0.5} {answer lanhua-shou}
    };
    #ACTION {^周伯通说道：「原来我们是同道中人啊。」} {
      #CLASS questresponseclass KILL;
    };
    #CLASS questresponseclass CLOSE;
    #NOP {问了后来怎样，即可进行回答并计时，周伯通2分钟后会进行下一阶段提示，这里直接通过时间来确定};
    #VARIABLE {startts} {@now{}};
    id here;
    echo {checknext};
  };
  #ACTION {^周伯通说道：「这样吧，我教你点好玩的功夫} {
  };
  #ACTION {^周伯通说道：「你这个人又笨又无聊} {
  };
  #ACTION {^周伯通说道：「怎么你到今天才来找我？！明天我就去和处机说让他打你屁股。」} {
  };
  #ACTION {^{设定环境变量：action \= \"checknext\"|你设定checknext为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {"$roomthings[周伯通]" == ""} {
      #IF {$missingflag == 0} {
        #NOP {房间刷新NPC溜回刷新点};
        #VARIABLE {missingflag} {1};
        out;
        eneter;
        id here;
        echo {checknext};
      };
      #ELSE {
        #CLASS questclass KILL;
        #NOP {出问题了，延时};
        questdelay {$questmodule} {1800};
        %1
      };
    };
    #ELSEIF {@elapsed{$startts} > 120} {
      pray pearl;
      ask zhou botong about 功夫;
      echo {checkask}
    };
    #ELSE {
      #DELAY {2} {
        id here;
        echo {checknext};
      };
    };
  };
  #ACTION {^你向周伯通打听有关『功夫』的消息。} {
    #VARIABLE {askflag} {1};
    #CLASS questresponseclass KILL;
    #CLASS questresponseclass OPEN;
    #ACTION {^周伯通露出迷惑} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      #NOP {流程异常只能退出};
      dohalt {doquit};
    };
    #ACTION {^你无论怎么练习就是无法成功} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questfail {$questmodule};
      dohalt {
        %1
      };
    };
    #ACTION {^周伯通在你练习纯熟後，突然哈哈大笑起来} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questsuccess {$questmodule};
      #SEND {shout 哈哈哈，我开了$questmodule，羡慕嫉妒恨吧！！！};
      dohalt {
        %1
      };
    };
    #CLASS questresponseclass CLOSE;
  };
  #ACTION {^{设定环境变量：action \= \"checkask\"|你设定checkask为反馈信息}} {
    #IF {$askflag == 0} {
      echo {checknext}
    };
  };
  #CLASS questclass CLOSE;
  dohalt {ask zhou about 九阴真经}
};