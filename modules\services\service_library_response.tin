#NOP {图书馆模块};
#ALIAS {initlibraryservice} {
  #CLASS servicelibraryclass KILL;
  #CLASS servicelibraryclass OPEN;
  #ACTION {^%*(%*)告诉你：library_request_%*} {
    library_accept {@lower{%%2}} {%%1} {%%3}
  };
  #ACTION {^! %*(%*)告诉你：library_request_%*} {
    library_accept {@lower{%%2}} {%%1} {%%3}
  };
  #ACTION {^%*(%*)告诉你：library_return_%*} {
    library_return_accept {@lower{%%2}} {%%1} {%%3}
  };
  #ACTION {^! %*(%*)告诉你：library_return_%*} {
    library_return_accept {@lower{%%2}} {%%1} {%%3}
  };
  #ACTION {^%*(%*)告诉你：library_cancel} {
    #VARIABLE {caller} {};
  };
  #ACTION {^! %*(%*)告诉你：library_cancel} {
    #VARIABLE {caller} {};
  };
  #CLASS servicelibraryclass CLOSE;
};
#NOP {响应书籍请求,%1:id,%2:name,%3:要拿的书籍};
#ALIAS {library_accept} {
  #IF {"$librarybooks[%1][party]" != "" && "$librarybooks[%1][party]" != "$hp[party]"} {
    tell %1 library_nop;
  };
  #ELSE {
    #NOP {超过五分钟重置};
    #IF {"$caller" != "" && @elapsed{$caller[timestamp]} > 300} {
      #VARIABLE {caller} {};
    };
    #NOP {更新请求};
    #IF {"$caller" != "" && "$caller[id]" != "%1"} {
      tell %1 library_wait
    };
    #ELSE {
      #VARIABLE {caller} {
        {request} {library}
        {timestamp} {@now{}}
        {id} {%1}
        {name} {%2}
        {target} {%3}
      };
      tell %1 library_come
    };
  };
};
#NOP {响应归还书籍请求,%1:id,%2:name,%3:要还的书籍};
#ALIAS {library_return_accept} {
  #IF {"$caller" != ""} {
    #NOP {五分钟超时取消};
    #IF {"$caller[id]" == "%1" && @elapsed{$caller[timestamp]} > 300} {
      #VARIABLE {caller} {
        {request} {library}
        {timestamp} {@now{}}
        {id} {%1}
        {name} {%2}
        {target} {%3}
        {return} {1}
      };
      tell %1 library_come
    };
    #ELSE {
      tell %1 library_wait
    };
  };
  #ELSE {
    #VARIABLE {caller} {
      {request} {library}
      {timestamp} {@now{}}
      {id} {%1}
      {name} {%2}
      {target} {%3}
      {return} {1}
    };
    tell %1 library_come
  };
};
#NOP {响应书籍请求,%1:后续指令};
#ALIAS {library_response} {
  #IF {"$caller[target]" == ""} {
    %1;
  };
  #ELSEIF {"$caller[return]" != ""} {
    yz {library_response_receive {%1}};
  };
  #ELSE {
    #SWITCH {"$caller[target]"} {
      #CASE {"易经"} {
        getbook_yijing {yz {library_response_give {yi jing} {%1}}} {
          #VARIABLE {caller} {};
          tell $caller[id] library_nop;
          %1;
        };
      };
      #CASE {"九宫八卦图谱"} {
        getbook_bagua {yz {library_response_give {jiugong tu} {%1}}} {
          #VARIABLE {caller} {};
          tell $caller[id] library_nop;
          %1;
        };
      };
      #DEFAULT {
        gotodo {扬州城} {杂货铺} {loadstockbook_start {$caller[target]} {%1}};
      };
    };
  };
};
#NOP {给书籍,%1:书籍,%2:后续指令};
#ALIAS {library_response_give} {
  #VARIABLE {checkcount} {0};
  #VARIABLE {okflag} {0};
  #CLASS serviceclass KILL;
  #CLASS serviceclass OPEN;
  #ACTION {^你要对谁做这个动作？} {
    #MATH {checkcount} {$checkcount + 1};
  };
  #ACTION {^你轻轻地拍了拍$caller[name]的头。} {
    #VARIABLE {checkcount} {0};
  };
  #ACTION {^{设定环境变量：action \= \"checkcaller\"|你设定checkcaller为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {$checkcount == 0} {
      #VARIABLE {okflag} {0};
      #VARIABLE {checkcount} {0};
      give %1 to $caller[id];
      echo {checkgive};
    };
    #ELSEIF {$checkcount < 60} {
      #DELAY {2} {
        pat $caller[id];
        echo {checkcaller};
      };
    };
    #ELSE {
      #CLASS serviceclass KILL;
      #VARIABLE {caller} {};
      %1;
    };
  };
  #ACTION {^$caller[name]对你抱拳道：“青山不改，绿水常流，咱们后会有期！”} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkgive\"|你设定checkgive为反馈信息}} {
    #VARIABLE {idle} {0};
    #MATH {checkcount} {$checkcount + 1};
    #IF {$okflag == 1} {
      #CLASS serviceclass KILL;
      #VARIABLE {caller} {};
      %2
    };
    #ELSEIF {$checkcount < 5} {
      #DELAY {2} {
        echo {checkgive};
      };
    };
    #ELSE {
      #CLASS serviceclass KILL;
      #VARIABLE {caller} {};
      %2
    };
  };
  #CLASS serviceclass CLOSE;
  pat $caller[id];
  echo {checkcaller};
};
#NOP {接收书籍};
#ALIAS {library_response_receive} {
  #VARIABLE {checkcount} {0};
  #VARIABLE {okflag} {0};
  #CLASS serviceclass KILL;
  #CLASS serviceclass OPEN;
  #ACTION {^你要对谁做这个动作？} {
    #MATH {checkcount} {$checkcount + 1};
  };
  #ACTION {^你轻轻地拍了拍$caller[name]的头。} {
    #VARIABLE {checkcount} {0};
  };
  #ACTION {^{设定环境变量：action \= \"checkcaller\"|你设定checkcaller为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {$checkcount == 0} {
      #VARIABLE {okflag} {0};
      #VARIABLE {checkcount} {0};
      echo {checkreceive};
    };
    #ELSEIF {$checkcount < 60} {
      #DELAY {2} {
        pat $caller[id];
        echo {checkcaller};
      };
    };
    #ELSE {
      #CLASS serviceclass KILL;
      #VARIABLE {caller} {};
      %1;
    };
  };
  #ACTION {^$caller[name]对你抱拳道：“青山不改，绿水常流，咱们后会有期！”} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkreceive\"|你设定checkreceive为反馈信息}} {
    #VARIABLE {idle} {0};
    #MATH {checkcount} {$checkcount + 1};
    #IF {$okflag == 1} {
      #LOCAL {bookid} {$caller[target]};
      #VARIABLE {caller} {};
      storethings {$bookid} {%1};
    };
    #ELSEIF {$checkcount < 5} {
      #DELAY {2} {
        echo {checkreceive};
      };
    };
    #ELSE {
      #VARIABLE {caller} {};
      %1
    };
  };
  #CLASS serviceclass CLOSE;
  pat $caller[id];
  echo {checkcaller};
};
#NOP {从杂货铺中取,%1:书名,%2:后续指令};
#ALIAS {loadstockbook_start} {
  #VARIABLE {failflag} {0};
  #CLASS serviceclass KILL;
  #CLASS serviceclass OPEN;
  #ACTION {^你并没有保存该物品} {
    #VARIABLE {failflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkthings\"|你设定checkthings为反馈信息}} {
    #DELAY {1} {
      dohalt {
        #IF {$failflag == 1} {
          #VARIABLE {caller} {};
          #NOP {补充书籍};
          tell $caller[id] library_nop;
          %2;
        };
        #ELSEIF {@carryqty{%1} == 1} {
          yz {library_response_give {%1} {%2}}
        };
        #ELSE {
          qu %1;
          i;
          echo {checkthings};
        };
      };
    };
  };
  #CLASS serviceclass CLOSE;
  qu %1;
  i;
  echo {checkthings};
};
initlibraryservice