#NOP {九阴真经下,%1:后续指令};
#ALIAS {goquest_jydown} {
  #VARIABLE {questmodule} {九阴下};
  gotodo {兰州城} {石洞} {jydown_kill {%1}}
};
#NOP {杀陈玄风,%1:后续指令};
#ALIAS {jydown_kill} {
  #VARIABLE {missflag} {0};
  #VARIABLE {askresult} {0};
  #CLASS questclass OPEN;
  #ACTION {^这里没有 mei chaofeng} {
    follow chen xuanfeng;
  };
  #ACTION {^你决定跟随梅超风一起行动} {
    #VARIABLE {askresult} {0};
    kill mei chao<PERSON>
  };
  #ACTION {^什么？梅超风还没醒过来呢。} {
    kill mei chao<PERSON>;
  };
  #ACTION {^什么？陈玄风还没醒过来呢。} {
    kill chen xuanfeng;
  };
  #ACTION {^这里没有 chen xuanfeng} {
    #IF {$missflag == 0} {
      #VARIABLE {missflag} {1};
      out;
      follow chen xuan<PERSON>;
    };
    #ELSE {
      #CLASS questclass KILL;
      questupdate {$questmodule};
      loc {
        runwait {%1}
      }
    };
  };
  #ACTION {^你决定跟随陈玄风一起行动} {
    #VARIABLE {askresult} {0};
    pray pearl;
    i;
    kill chen xuanfeng;
  };
  #ACTION {^梅超风「啪」的一声倒在地上，挣扎着抽动了几下就死了} {
    follow chen xuanfeng
  };
  #ACTION {^陈玄风志迷糊，脚下一个不稳，倒在地上昏了过去。} {
    kill chen xuanfeng
  };
  #ACTION {^陈玄风「啪」的一声倒在地上，挣扎着抽动了几下就死了} {
    echo {checkresult};
  };
  #ACTION {^这里没有这个人。} {
    #IF {"$room" == "石洞"} {
      halt;
      out;
      look;
      kill chen xuanfeng
    };
    #ELSE {
      #CLASS questclass KILL;
      questupdate {$questmodule};
      loc {%1};
    };
  };
  #ACTION {^你看到陈玄风身上有一片皮革，顺手拿了起来} {
    #VARIABLE {askresult} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkresult\"|你设定checkresult为反馈信息}} {
    #CLASS questclass KILL;
    #IF {$askresult == 1} {
      questsuccess {$questmodule}
    };
    #ELSE {
      questfail {$questmodule}
    };
    dohalt {
      loc {%1}
    };
  };
  #CLASS questclass CLOSE;
  openwimpy;
  follow none;
  follow mei chaofeng;
};