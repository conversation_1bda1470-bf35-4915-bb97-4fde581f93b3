#NOP {桃花岛守墓任务模块};
#ALIAS {jobgo_shoumu} {
  gotonpc {黄蓉} {
    startfull {jobask_shoumu}
  };
};
#NOP {桃花岛守墓接任务};
#ALIAS {jobask_shoumu} {
  #NOP {询问结果,0:成功,1:busy,2:问师母,3:换任务,4:查看时间,5:空挡};
  #VARIABLE {askresult} {0};
  #VARIABLE {askdo} {
    time;
    ask huang rong about 守墓;
    set action checkask
  };
  #CLASS jobrequestclass OPEN;
  #ACTION {^你向黄蓉打听有关『师母』的消息。} {
    #CLASS jobresponseclass OPEN;
    #ACTION {^您先歇口气再说话吧。} {
      dohalt {
        ask huang rong about 师母
      }
    };
    #ACTION {^黄蓉叹了口气，说道: 我母亲绝顶聪明，可惜她英年早逝，我爹爹将她葬在桃花岛上的一座墓里。} {
      #CLASS jobresponseclass KILL;
      #VARIABLE {askresult} {0};
      dohalt {
        $askdo
      }
    };
    #CLASS jobresponseclass CLOSE;
  };
  #ACTION {^你向黄蓉打听有关『守墓』的消息。} {
    #VARIABLE {askresult} {0};
    #CLASS jobresponseclass OPEN;
    #ACTION {^您先歇口气再说话吧。} {
      #VARIABLE {askresult} {1};
    };
    #ACTION {^黄蓉说道：「难得你有这份心意，但是我母亲的墓不是随便就能进去的。」} {
      #VARIABLE {askresult} {2};
    };
    #ACTION {^黄蓉说道：「你刚刚做好守墓任务，还是先休息一会吧。」} {
      #VARIABLE {askresult} {3};
    };
    #ACTION {^黄蓉疑惑的看着你,说道: 现在大白天的，守什么墓呀} {
      #VARIABLE {askresult} {4};
    };
    #ACTION {^对不起，现在这里没有什么可以给你做的} {
      #VARIABLE {askresult} {5};
    };
    #ACTION {^已经有人去守墓了，你下次再去吧} {
      #VARIABLE {askresult} {5};
    };
    #ACTION {^黄蓉瞪了你一眼,说道: 不是叫你去了吗，你还呆在这里干什么} {
      #VARIABLE {askresult} {5};
    };
    #ACTION {黄蓉说道：「已经有人去守墓了，你下次再去吧！」} {
      #VARIABLE {askresult} {5};
    };
    #ACTION {^黄蓉在你耳边悄悄说道: 你要好好守墓。如果有什么人进到墓里，你不要对他客气!} {
      #VARIABLE {askresult} {0};
    };
    #ACTION {^设定环境变量：action \= \"checkask\"} {
      #VARIABLE {echots} {0};
      #VARIABLE {idle} {0};
      #SHOWME {<ffa>询问结果$askresult};
      #CLASS jobresponseclass KILL;
      #DELAY {2} {
        #SWITCH {$askresult} {
          #CASE {0} {
            #CLASS jobrequestclass KILL;
            loc {jobdo_shoumu};
          };
          #CASE {1} {$askdo};
          #CASE {2} {
            #CLASS jobrequestclass KILL;
            jobask_shoumu
          };
          #CASE {3} {
            #CLASS jobrequestclass KILL;
            #VARIABLE {lastjob} {守墓};
            jobgo
          };
          #CASE {4} {
            #CLASS jobrequestclass KILL;
            #IF {@eval{17 - $env[gametime]} > 4} {
              #IF {"$hp[lastjob]" != "送信"} {
                jobgo_songxin
              };
              #ELSE {
                jobgo_clb
              };
            };
            #ELSE {
              jobgo_shoumu
            };
          };
          #CASE {5} {
            #CLASS jobrequestclass KILL;
            jobgo_shoumu
          };
        };
      }
    };
    #CLASS jobresponseclass CLOSE;
  };
  #CLASS jobrequestclass CLOSE;
  gotonpc {黄蓉} {ask huang rong about 师母}
};

#ALIAS {jobdo_shoumu} {
  #CLASS jobdoclass OPEN;
  #ACTION {^盗墓贼对着你「嘿嘿」笑了两声} {
    #CLASS jobdoclass KILL;
    jobfight_shoumu
  };
  #CLASS jobdoclass CLOSE;
  gotodo {桃花岛} {墓中圹室} {hi daomu zei}
};
#ALIAS {jobfight_shoumu} {
  #VARIABLE {tempdo} {
    get all from corpse 1;
    get all from corpse 2;
    get all from corpse 3;
    i;
    set action checktreasure
  };
  #VARIABLE {jobthing_treasure} {};
  #VARIABLE {jobthing_treasure_id} {};
  #VARIABLE {variable name} {text to fill variable};
  #CLASS jobfightclass OPEN;
  #ACTION {^盗墓贼「啪」的一声倒在地上，挣扎着抽动了几下就死了。} {
    kill zei
  };
  #ACTION {^这里没有这个人。} {
    stopfight;
    $tempdo
  };
  #ACTION {^你从盗墓贼的尸体身上搜出一{个|颗|块}%*。} {
    #VARIABLE {jobthing_treasure} {%%2};
  };
  #ACTION {$jobthing_treasure(%*)} {
    #IF {"$jobthing_treasure" != ""} {
      #VARIABLE {jobthing_treasure_id} {@lower{%%1}};
    };
  };
  #ACTION {^设定环境变量：action \= \"checktreasure\"} {
    #VARIABLE {echots} {0};
    #DELAY {2} {
      #IF {"$jobthing_treasure" == ""} {
        $tempdo
      };
      #ELSE {
        #CLASS jobfightclass KILL;
        drop cloth;
        jobfinish_shoumu
      };
    }
  };
  #CLASS jobfightclass CLOSE;
  createpfm;
  kill daomu zei;
  startfight
};
#NOP {桃花岛守墓完成};
#ALIAS {jobfinish_shoumu} {
  #CLASS jobrequestclass OPEN;
  #ACTION {^黄蓉{对着你竖起了|轻轻地拍了拍你的头|冲上前去，激动地紧紧握住你|对着你点}} {
    #CLASS jobresponseclass OPEN;
    #ACTION {^你获得了%*点经验，%*点潜能的奖励。} {
      
    };
    #ACTION {^你给黄蓉一} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      jobcheck
    };
    #CLASS jobresponseclass CLOSE;
  };
  #ACTION {^你身上没有这样东西。} {
    #CLASS jobrequestclass KILL;
    jobcheck
  };
  gotonpc {黄蓉} {give $jobthing_treasure_id to huang rong}
};