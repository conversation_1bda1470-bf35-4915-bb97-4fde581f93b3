#NOP {公共模块};
#NOP {计算表达式值};
#FUNCTION eval {
    #IF {"%1" == ""} {
        #RETURN {0};
    };
    #ELSE {
        #math result {%1}
    };
};
#NOP {取绝对值};
#FUNCTION abs {
    #IF {%1 < 0} {
        #MATH {result} {-%1};
    };
    #ELSE {
        #VARIABLE {result} {%1};
    };
};
#NOP {格式化字符串};;
#FUNCTION {string} {#FORMAT result {%s} {%1}};
#NOP {返回重复指定次数的字符串,%1:重复次数,%2:要重复的字符串,不传视为空格};
#FUNCTION repeat {
    #IF {"%2" == ""} {
        #FORMAT result {%+%1s} {}
    };
    #ELSE {
        #LOCAL {_tempstr} {};
        #IF {%1 >= 1} {
            #LOOP 1 %1 {i} {
                #LOCAL {_tempstr} {$_tempstr %2};
            };
        };
        #REPLACE {_tempstr} { } {};
        #RETURN {$_tempstr};
    };
};
#NOP {小写};
#FUNCTION {lower} {#FORMAT result {%l} {%1}};
#NOP {大写};
#FUNCTION {upper} {#FORMAT result {%u} {%1}};
#NOP {去空格};
#FUNCTION trim {#FORMAT result {%p} {%1}};
#NOP {是否是数字};
#FUNCTION {isNumber} {#REGEXP {%1} {^%d$} {#VARIABLE {result} {1};};};
#NOP {列表长度};
#FUNCTION {size} {#list %1 size result};
#NOP {将检测列表元素用指定字符连接起来,%1:列表变量};
#FUNCTION join {
    #LOCAL {_tempstr} {$%1[+1]};
    #LOOP 2 &%1[] {i} {
        #LOCAL {_tempstr} {$_tempstr $%1[+$i]}
    };
    #RETURN {$_tempstr};
};
#NOP {分支选择,%1:条件表达式,%2:true时结果,%2:false时结果};
#FUNCTION {iif} {
    #IF {%1} {
        #RETURN {%2};
    };
    #ELSE {
        #RETURN {%3};
    };
};
#NOP {分支动作，%1:条件表达式,%2:true时的指令,%3:false时的指令};
#ALIAS {ifdo} {
    #IF {%1} {
        %2;
    };
    #ELSE {
        %3;
    };
};
#NOP 字符串长度;
#FUNCTION {len} {#FORMAT result {%L} {%0}};
#NOP {取1~指定数字之间的随机数};
#function {rnd} {#MATH {result} {1 d (%2 - %1 + 1) + %1 - 1}};
#NOP {从左取字符串的指定长度,%1:字符串,%2:长度};
#FUNCTION left {#FORMAT {result} {%.%2s} {%1}};
#NOP {从右取字符串的指定长度,%1:字符串,%2:长度};
#FUNCTION right {
    #FORMAT {tempstr} {%r} {%1};
    #FORMAT {result} {%r} {@left{{$tempstr}{%2}}};
};
#NOP {比较两个字符串,%1-参数1,%2-参数2};
#FUNCTION equal {
    #NOP {排除转义};
    #LOCAL {_p1} {%1};
    #REPLACE {_p1} {\x7B} {<};
    #REPLACE {_p1} {\x7D} {>};

    #LOCAL {_p2} {%2};
    #REPLACE {_p2} {\x7B} {<};
    #REPLACE {_p2} {\x7D} {>};

    #IF {"$_p1" == "$_p2"} {
        #RETURN {1};
    };
    #ELSE {
        #RETURN {0};
    };
};
#NOP {当前时间,1979/01/01至今经过的秒数};
#FUNCTION now {#FORMAT {result} {%T}};
#NOP {当前时间,1979/01/01至今经过的毫秒数};
#FUNCTION nowms {#FORMAT {result} {%U};#MATH {result} {$result / 1000}};
#NOP {距离指定时间经过的秒数};
#FUNCTION elapsed {#RETURN {@eval{@now{} - @eval{%1}}}};
#NOP {距离指定时间经过的毫秒数};
#FUNCTION elapsedms {#RETURN {@eval{@nowms{} - %1}}};
#NOP {延时指定秒后的时间戳};
#FUNCTION delayed {#MATH {result} {@now{} + %1}};
#NOP {时间格式化,%1:秒};
#FUNCTION timeFormat {
    #LOCAL {h} {@eval{%1/3600}};
    #LOCAL {m} {@eval{(%1-$h*3600)/60}};
    #LOCAL {s} {@eval{%1-$h*3600-$m*60}};
    #RETURN {@padLeft{{$h}{2}{0}}:@padLeft{{$m}{2}{0}}:@padLeft{{$s}{2}{0}}};
};
#NOP {时间格式化,%1:秒};
#FUNCTION timeFormatCN {
    #LOCAL {olseconds} {%1};
    #LOCAL {d} {@eval{$olseconds / 86400}};
    #LOCAL {olseconds} {@eval{$olseconds % 86400}};
    #LOCAL {h} {@eval{$olseconds / 3600}};
    #LOCAL {olseconds} {@eval{$olseconds % 3600}};
    #LOCAL {m} {@eval{$olseconds / 60}};
    #LOCAL {s} {@eval{$olseconds % 60}};
    #RETURN {@padLeft{{$d}{2}{0}}天 @padLeft{{$h}{2}{0}}小时 @padLeft{{$m}{2}{0}}分钟 @padLeft{{$s}{2}{0}}秒};
};
#NOP {%1是否以%2开头};
#FUNCTION startWiths {
    #LOCAL {temp} {@left{{%1}{@len{%2}}}};
    #IF {"$temp" == "%2"} {
        #RETURN {1}
    };
    #ELSE {
        #RETURN {0}
    };
};
#NOP {%1是否以%2结尾,仅支持中文};
#FUNCTION endWiths {
    #LOCAL {templength} {@eval{@len{%1} -  @len{%2}}};
    #LOCAL {temp} {@left{{%1}{$templength}}};
    #IF {"$temp%2" == "%1"} {
        #RETURN {1};
    };
    #ELSE {
        #RETURN {0};
    };
};
#NOP {左补齐,%1:要补齐的字符,%2:长度,%3:补齐的字符,默认为空格};
#FUNCTION padLeft {
    #LOCAL {_padcount} {@eval{%2 - @len{%1}}};
    #RETURN {@repeat{{$_padcount}{%3}}%1};
};
#NOP {右补齐,%1:要补齐的字符,%2:长度,%3:补齐的字符,默认为空格};
#FUNCTION padRight {
    #LOCAL {_padcount} {@eval{%2 - @len{%1}}};
    #RETURN {%1@repeat{{$_padcount}{%3}}};
};
#NOP {%1是否包含%2};
#FUNCTION instr {
    #REGEXP {%1} {%2} {#VARIABLE {result} {1};} {#VARIABLE {result} {0};};
};
#NOP {是否是列表成员};
#FUNCTION {contains} {#LIST {%1} {find} {%2} result};
#NOP {检索指定元素在列表中出现的次数,%1:列表,%2:元素};
#FUNCTION {count} {
    #VARIABLE {result} {0};
    #LOOP {1} {&%1[]} {i} {
        #IF {"$%1[$i]" == "%2"} {
            #MATH {result} {$result + 1};
        };
    };
};
#NOP {对指令列表进行筛选，列表的成员必须为复杂对象。%1:列表变量名，%2:筛选的字段，%3:条件表达式，&为字段值变量，为真返回};
#FUNCTION filter {
    #IF {&%1[] == 0} {
        #RETURN {};
    };
    #LOCAL {newlist} {};
    #LOOP 1 &%1[] {i} {
        #LOCAL {expr} {%3};
        #REPLACE {expr} {&} {$%1[+$i][%2]};
        #IF {$expr} {
            #LOCAL {newlist[*%1[+$i]]} {$%1[+$i]};
        };
    };

    #RETURN {$newlist};
};
#NOP {对列表进行排序，列表的成员必须为复杂对象，成员遍历的字段值保存在排序后成员的sidx字段内。%1:列表变量名，%2:排序的字段，%3:空或asc为升序,desc为降序，否则为降序};
#FUNCTION sort {
    #LOCAL {templist} {create} {};
    #LOOP 1 &%1[] {i} {
        #LOCAL {sidx} {$%1[+$i][%2]};
        #LOCAL {iter} {@eval{&templist[$sidx][] + 1}};
        #LOCAL {templist[$sidx][$iter]} {$%1[+$i]};
        #LOCAL {templist[$sidx][$iter][sidx]} {*%1[+$i]};
    };
    #LOCAL {sortlist} {clear} {};
    #FOREACH {*templist[]} {k} {
        #LOOP 1 &templist[$k][] {i} {
            #LOCAL {iter} {@eval{&sortlist[] + 1}};
            #IF {"%3" == "desc"} {
                #LOCAL {iter} {@eval{&%1[] - &sortlist[]}};
            };
            #LOCAL {sortlist[$iter]} {$templist[$k][+$i]};
        };
    };

    #RETURN {$sortlist};
};
#NOP 中文数字转阿拉伯数字
#FUNCTION ctd {
    #LOCAL {expr} {%1};
    #REPLACE {expr} {亿} {;亿;};
    #REPLACE {expr} {万} {;万;};
    #LIST {segments} {create} {$expr};
    #LOCAL {ubound} {@eval{&segments[]/2}};
    #LOCAL {form} {};
    #LOCAL {idx} {0};
    #WHILE {$idx < $ubound} {
        #LOCAL {sindex} {@eval{($idx+1)*2}};
        #IF {"$segments[+$sindex]" == "亿"} {
            #CAT {form} {+@ctdbase{$segments[+$sindex-1]}*100000000};
        };
        #ELSE {
            #CAT {form} {+@ctdbase{$segments[+$sindex-1]}*10000};
        };
        #MATH {idx} {$idx + 1};
    };
    #IF {&segments[] > @eval{$ubound*2}} {
        #CAT {form} {+@ctdbase{$segments[+&segments[]]}};
    };
    #MATH {result} {$form};
};
#NOP {中文货币描述转数字};
#FUNCTION transMoney {
    #VARIABLE {cm_gold} {};
    #VARIABLE {cm_silver} {};
    #VARIABLE {cm_coin} {};
    #VARIABLE {cm_expr} {%*锭黄金};
    #REGEXP {%1} {$cm_expr} {#VARIABLE {cm_gold} {&1}};
    #VARIABLE {cm_expr} {%*两白银};
    #IF {"$cm_gold" != ""} {
        #VARIABLE {cm_expr} {$cm_gold锭黄金$cm_expr};
    };
    #REGEXP {%1} {$cm_expr} {#VARIABLE {cm_silver} {&1}};
    #VARIABLE {cm_expr} {%*文铜钱};
    #IF {"$cm_silver" != ""} {
        #VARIABLE {cm_expr} {$cm_silver两白银$cm_expr};
    };
    #IF {"$cm_gold" != ""} {
        #VARIABLE {cm_expr} {$cm_gold锭黄金$cm_expr};
    };
    #REGEXP {%1} {$cm_expr} {#VARIABLE {cm_coin} {&1}};
    #RETURN {@eval{@ctd{$cm_gold}*10000 + @ctd{$cm_silver}*100 + @ctd{$cm_coin}}};
};

#NOP {最小表达式拆分};
#FUNCTION ctdbase {
    #LOCAL {expr} {%1};
    #REGEXP {$expr} {^十} {#replace expr {十} {一十}};
    #REGEXP {$expr} {零十} {#replace expr {十} {一十}};
    #REPLACE expr {零} {};
    #REPLACE expr {千} {*1000}; 
    #REPLACE expr {百} {*100};
    #REPLACE expr {十} {*10}; 
    #REPLACE expr {一} {+1}; 
    #REPLACE expr {二} {+2}; 
    #REPLACE expr {三} {+3}; 
    #REPLACE expr {四} {+4}; 
    #REPLACE expr {五} {+5}; 
    #REPLACE expr {六} {+6}; 
    #REPLACE expr {七} {+7}; 
    #REPLACE expr {八} {+8}; 
    #REPLACE expr {九} {+9};
    #MATH {result} {$expr};
};
#NOP {变量是否为数字};
#FUNCTION isNumber {
    #IF {@eval{1 + %1} == 0} {#RETURN {0};};
    #ELSE {#RETURN {1};};
};
#NOP {反向反转};
#FUNCTION reverseDir {
    #IF {@startWiths{{%1}{matrix_}} > 0 || @startWiths{{%1}{river_}} > 0} {
        #RETURN {};
    };
    #SWITCH {"%1"} {
        #CASE {"e"} {#LOCAL reversedir {w}};
        #CASE {"w"} {#LOCAL reversedir {e}};
        #CASE {"n"} {#LOCAL reversedir {s}};
        #CASE {"s"} {#LOCAL reversedir {n}};        
        #CASE {"ne"} {#LOCAL reversedir {sw}};
        #CASE {"se"} {#LOCAL reversedir {nw}};
        #CASE {"nw"} {#LOCAL reversedir {se}};
        #CASE {"sw"} {#LOCAL reversedir {ne}};
        #CASE {"nu"} {#LOCAL reversedir {sd}};
        #CASE {"nd"} {#LOCAL reversedir {su}};
        #CASE {"su"} {#LOCAL reversedir {nd}};
        #CASE {"sd"} {#LOCAL reversedir {nu}};
        #CASE {"wu"} {#LOCAL reversedir {ed}};
        #CASE {"wd"} {#LOCAL reversedir {eu}};
        #CASE {"eu"} {#LOCAL reversedir {wd}};
        #CASE {"ed"} {#LOCAL reversedir {wu}};
        #CASE {"u"} {#LOCAL reversedir {d}};
        #CASE {"d"} {#LOCAL reversedir {u}};
        #CASE {"enter"} {#LOCAL reversedir {out}};
        #CASE {"out"} {#LOCAL reversedir {enter}};
        #DEFAULT {#LOCAL reversedir {%1}};
    };
    #RETURN $reversedir;
};
#NOP {路径反转};
#FUNCTION reversePath {
    #LIST {templists} {create} {%1};
    #IF {&templists[] == 0} {
        #RETURN {};
    };
    #LIST {rvlist} {clear} {};
    #LOOP 1 &templists[] {i} {
        #LOCAL {rvdir} {@reverseDir{$templists[+$i]}};
        #IF {"$rvdir" == ""} {
            #CONTINUE;
        };
        #LIST {rvlist} {add} {$rvdir};
    };
    #LIST {rvlist} {collapse} {;};
    #RETURN {$rvlist};
};
#NOP {获取路径长名称};
#FUNCTION longDir {
    #SWITCH {"%1"} {
        #CASE {"e"} {#LOCAL tempdir {east}};
        #CASE {"w"} {#LOCAL tempdir {west}};
        #CASE {"n"} {#LOCAL tempdir {north}};
        #CASE {"s"} {#LOCAL tempdir {south}};        
        #CASE {"ne"} {#LOCAL tempdir {northeast}};
        #CASE {"se"} {#LOCAL tempdir {southeast}};
        #CASE {"nw"} {#LOCAL tempdir {northwest}};
        #CASE {"sw"} {#LOCAL tempdir {southwest}};
        #CASE {"nu"} {#LOCAL tempdir {northup}};
        #CASE {"nd"} {#LOCAL tempdir {northdown}};
        #CASE {"su"} {#LOCAL tempdir {southup}};
        #CASE {"sd"} {#LOCAL tempdir {southdown}};
        #CASE {"wu"} {#LOCAL tempdir {westup}};
        #CASE {"wd"} {#LOCAL tempdir {westdown}};
        #CASE {"eu"} {#LOCAL tempdir {eastup}};
        #CASE {"ed"} {#LOCAL tempdir {eastdown}};
        #CASE {"u"} {#LOCAL tempdir {up}};
        #CASE {"d"} {#LOCAL tempdir {down}};
        #CASE {"enter"} {#LOCAL tempdir {enter}};
        #CASE {"out"} {#LOCAL tempdir {out}};
        #DEFAULT {#LOCAL tempdir {%1}};
    };
    #RETURN $tempdir;
};
#NOP {获取路径短名称};
#FUNCTION shortDir {
    #SWITCH {"%1"} {
        #CASE {"east"} {#LOCAL tempdir {e}};
        #CASE {"west"} {#LOCAL tempdir {w}};
        #CASE {"north"} {#LOCAL tempdir {n}};
        #CASE {"south"} {#LOCAL tempdir {s}};        
        #CASE {"northeast"} {#LOCAL tempdir {ne}};
        #CASE {"southeast"} {#LOCAL tempdir {se}};
        #CASE {"northwest"} {#LOCAL tempdir {nw}};
        #CASE {"southwest"} {#LOCAL tempdir {sw}};
        #CASE {"northup"} {#LOCAL tempdir {nu}};
        #CASE {"northdown"} {#LOCAL tempdir {nd}};
        #CASE {"southup"} {#LOCAL tempdir {su}};
        #CASE {"southdown"} {#LOCAL tempdir {sd}};
        #CASE {"westup"} {#LOCAL tempdir {wu}};
        #CASE {"westdown"} {#LOCAL tempdir {wd}};
        #CASE {"eastup"} {#LOCAL tempdir {eu}};
        #CASE {"eastdown"} {#LOCAL tempdir {ed}};
        #CASE {"up"} {#LOCAL tempdir {u}};
        #CASE {"down"} {#LOCAL tempdir {d}};
        #CASE {"enter"} {#LOCAL tempdir {enter}};
        #CASE {"out"} {#LOCAL tempdir {out}};
        #DEFAULT {#LOCAL tempdir {%1}};
    };
    #RETURN $tempdir;
};
#NOP {格式化房间出口};
#FUNCTION formatExits {
    #LOCAL dir %1;
    #REPLACE dir { }{};
    #REPLACE dir {、}{;};
    #REPLACE dir {和}{;};
    #REPLACE dir {northeast} {ne};
    #REPLACE dir {northwest}{nw};
    #REPLACE dir {southeast}{se};
    #REPLACE dir {southwest}{sw};
    #REPLACE dir {northdown}{nd};
    #REPLACE dir {southdown}{sd};
    #REPLACE dir {eastdown}{ed};
    #REPLACE dir {westdown}{wd};
    #REPLACE dir {southup}{su};
    #REPLACE dir {northup}{nu};
    #REPLACE dir {eastup}{eu};
    #REPLACE dir {westup}{wu};
    #REPLACE dir {south}{s};
    #REPLACE dir {north}{n};
    #REPLACE dir {down}{d};
    #REPLACE dir {east}{e};
    #REPLACE dir {west}{w};
    #REPLACE dir {up}{u};
    #RETURN $dir;    
};
#NOP {格式化房间出口名字};
#FUNCTION fetchWays {
    #LOCAL {way} {%1};
    #REPLACE way {-}{8};
    #REPLACE way {→}{8};
    #REPLACE way {←}{8};
    #REPLACE way {∨}{8};
    #REPLACE way {∧}{8};
    #REPLACE way { }{8};
    #LOCAL {loopflag} {1};
    #WHILE {$loopflag} {
        #REGEXP {$way} {{\d+}} {#REPLACE way {&1} {;};} {#LOCAL {loopflag} {0};};
    };
    #RETURN $way;
};
#NOP {获取一个随机颜色表达式};
#FUNCTION getRandomColor {
    #LIST {colorlist} {create} {a;b;c;d;e;f};

    #RETURN {$colorlist[+@rnd{{1}{6}}]$colorlist[+@rnd{{1}{6}}]$colorlist[+@rnd{{1}{6}}]};
};
#NOP {打印变量内容,%1:变量名称,%2:缩进空格数,%3:是否初次调用};
#ALIAS {printvar} {
    #LOCAL {indent} {@eval{%2}};
    #IF {&{%1} == 0} {
        #LINE IGNORE {#SHOWME {%1 is undefined}};
    };
    #ELSEIF {&%1[] == 0} {
        #SHOWME {@repeat{$indent}$%1};
    };
    #ELSE {
        #IF {"%3" == ""} {
            #LINE IGNORE {#SHOWME {----------------------------------------%1 start----------------------------------------}};
        };
        #LOCAL {maxlength} {0};
        #FOREACH {*%1[]} {k} {
            #IF {@len{$k} > $maxlength} {
                #LOCAL {maxlength} {@len{$k}};
            };
        };
        #MATH {maxlength} {$maxlength + 2};
        #FOREACH {*%1[]} {k} {
            #IF {&%1[$k][] == 0} {
                #LINE IGNORE {#SHOWME {@repeat{$indent}@padRight{{\x7B$k\x7D}{$maxlength}}\x7B$%1[$k]\x7D}};
            };
            #ELSE {
                #LINE IGNORE {#SHOWME {@repeat{$indent}@padRight{{\x7B$k\x7D}{$maxlength}}\x7B}};
                printvar {%1[$k]} {@eval{$indent + 2}} {1};
                #LINE IGNORE {#SHOWME {@repeat{$indent}\x7D}}
            };
        };
        #IF {"%3" == ""} {
            #LINE IGNORE {#SHOWME {----------------------------------------%1 end----------------------------------------}};
        };
    };
};
#SHOWME {<fac>@padRight{{通用函数}{12}}<fac> <cfa>模块加载完毕<cfa>};