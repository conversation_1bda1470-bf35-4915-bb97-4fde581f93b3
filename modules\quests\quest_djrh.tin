#NOP {刀剑融合,%1:后续指令};
#ALIAS {goquest_djrh} {
  #VARIABLE {questmodule} {刀剑融合};
  gotonpc {苗人凤} {djrh_askmiao {%1}}
};
#ALIAS {djrh_askmiao} {
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你向苗人凤打听有关『刀剑融合』的消息。} {
    #CLASS questresponseclass OPEN;
    #ACTION {^苗人凤说道：「不是刚学完吗，还是以后切磋吧} {
      #NOP {刚招待过玩家,等会再来};
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questupdate {$questmodule};
      dohalt {%1}
    };
    #ACTION {^苗人凤说道：「现在时间紧急，这刀法还是以后切磋吧} {
      #NOP {复仇篇开始,解药篇未结束};
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questupdate {$questmodule};
      dohalt {%1}
    };
    #ACTION {^苗人凤说道：「{今天先切磋到这里吧，明天吧|以你当前的经验恐怕还是难以领悟}} {
      #NOP {经验或时间不足,一般不会出现};
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questdelay {$questmodule} {0} {10800};
      dohalt {%1}
    };
    #ACTION {^苗人凤说道：「你已经刀剑融合，可惜缺少内功支持。传说：冷泉神功可能在闯王宝藏里} {
      #NOP {没撑住};
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questsuccess {$questmodule};
      dohalt {%1}
    };
    #ACTION {^你于%*解开雪山飞狐刀剑融合的奥秘} {
      #NOP {成功};
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questsuccess {$questmodule};
      dohalt {%1}
    };
    #ACTION {^你听了苗人凤的指点，脑海里似乎抓住了什么，可是依然不是很明白} {
      #NOP {失败};
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      questfail {$questmodule};
      dohalt {%1}
    };
    #CLASS questresponseclass CLOSE;
  };
  #CLASS questclass CLOSE;
  pray pearl;
  dohalt {
    ask miao renfeng about 刀剑融合
  };
};