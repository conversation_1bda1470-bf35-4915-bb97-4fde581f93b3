#NOP {少林护送任务模块};
#NOP {护送路径的关键节点};
#VARIABLE {jiaotucheck} {0};
#ALIAS {jobgo_husong} {
  gotonpc {玄慈大师} {
    startfull {jobask_husong}
  }
};
#ALIAS {jobask_husong} {
  #VARIABLE {joblocation} {};
  #VARIABLE {jobnpc_dashi} {};
  #LIST {husongpath} {create} {};
  #CLASS jobrequestclass KILL;
  #CLASS jobrequestclass OPEN;
  #ACTION {^你向玄慈大师打听有关『job』的消息。} {
    #VARIABLE {idle} {0};
    #CLASS jobresponseclass KILL;
    #CLASS jobresponseclass OPEN;     
    #ACTION {^玄慈大师说道：「我这里现在没有什么任务给你} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {
        #IF {@eval{$hp[busy][公共]} > 20} {
          waitlian;
          startfull {jobask_husong} {2}
        };
        #ELSEIF {@eval{$hp[busy][公共]} > 0} {
          waitlian;
          startfull {jobask_husong} {1}
        };
        #ELSE {
          #VARIABLE {env[husong]} {@eval{@now{} - 1200 + 120}};
          jobprepare;
        };
      };
    };
    #ACTION {^玄慈大师说道：「现在我这里没有给你的任务} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      #VARIABLE {env[husong]} {@eval{@now{} - 1200 + 120}};
      dohalt {jobprepare}
    };
    #ACTION {^玄慈大师说道：「你刚才不是已经问过了吗} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {doquit};
    };
    #ACTION {^玄慈大师说道：「你%*武功精进} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      #NOP {武当和送信2};
      #VARIABLE {env[husong]} {@eval{@now{} - 1200 + 120}};
      dohalt {jobprepare}
    };
    #ACTION {^玄慈大师说道：「嗯，已经有人在帮我了，你还是去忙点别的什么吧} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      #NOP {武当和送信2};
      #VARIABLE {env[husong]} {@eval{@now{} - 1200 + 120}};
      dohalt {jobprepare}
    };
    #ACTION {^玄慈大师说道：「你这位%*眼露凶光，我少林名门正派} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {
        gozshen {10000} {jobgo_husong};
      };
    };
    #ACTION {^玄慈大师说道：「就请各位随同%*前去恒山，一路小心} {
      #VARIABLE {jobnpc_dashi} {%%%1};
    };
    #ACTION {^玄慈大师说道：「%*大师现在%*。} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      #IF {"%%%1大师" == "$jobnpc_dashi"} {
        #VARIABLE {joblocation} {%%%2};
        #VARIABLE {jobstart_ts} {@now{}};
        joblog {寻找位于【$joblocation】的【$jobnpc_dashi】};
        dohalt {
          gotoroom {$fangzhengwheres[$joblocation]} {jobhusong_askdashi};
        }
      };
    };
    #CLASS jobresponseclass CLOSE;     
  };
  #CLASS jobrequestclass CLOSE;
  cond;
  ask xuanci dashi about job;
};
#ALIAS {jobhusong_askdashi} {
  #VARIABLE {jobjiaotu_count} {0};
  #CLASS jobdoclass KILL;
  #CLASS jobdoclass OPEN;
  #ACTION {^你决定跟随$jobnpc_dashi一起行动} {
    dohalt {ask $hp[id]'s dashi about 救援};
  };
  #ACTION {^$jobnpc_dashi说道：「{好的，就请这位|你刚才不是问过了吗}} {
    #CLASS jobdoclass KILL;
    joblog {开始护送【$jobnpc_dashi】至【白云庵】};
    #NOP {关闭汝州至黄河的路径};
    #MAP AT {781} {#MAP UNLINK {e} {782}};
    dohalt {
      loc {jobdo_husong};
    };
  };
  #CLASS jobdoclass CLOSE;
  follow none;
  follow $hp[id]'s dashi;
};
#NOP {开始护送，先到蓝田，再到白云庵};
#ALIAS {jobdo_husong} {
  #VARIABLE {jobcheck_count} {0};
  #VARIABLE {jobflag_jiaotu} {0};
  #VARIABLE {jobflag_escape} {0};
  #VARIABLE {jobflag_arrived} {0};
  #VARIABLE {dashiexits} {};
  #VARIABLE {dashifound} {0};
  #VARIABLE {dashistep} {};
  #VARIABLE {dashishowup} {0};
  #VARIABLE {fallback} {0};
  #LIST {husongpath} {create} {@getWalkPath{{$roomid}{白云庵}}};
  #CLASS jobdoclass KILL;
  #CLASS jobdoclass OPEN;
  #ACTION {^你双手抱拳，对$jobnpc_dashi作了个揖道} {
    #VARIABLE {dashishowup} {1};
    #VARIABLE {fallback} {0};
  };
  #ACTION {^突然从路边冲出一个魔教教徒，一声不吭的向$jobnpc_dashi冲去} {
    #VARIABLE {jobflag_jiaotu} {1};
  };
  #ACTION {^$jobnpc_dashi高喊一声：情况紧急，我撤退，你掩护} {
    #VARIABLE {jobflag_escape} {1};
  };
  #ACTION {^$jobnpc_dashi说道：「好了，历尽艰险，终于到了恒山} {
    #VARIABLE {jobflag_arrived} {1};
  };
  #ACTION {^$jobnpc_dashi在你的耳边悄声说道%*刚才我碰到方生师弟} {
    #VARIABLE {env[wxjz]} {YES};
    set env_wxjz
  };
  #ACTION {^好，任务完成，你得到了%*点实战经验和%*点潜能。} {
    #CLASS jobfinishclass KILL;
    #VARIABLE {env[husong]} {@now{}};
    #NOP {开启汝州至黄河的路径};
    #MAP AT {781} {#MAP LINK {e} {782}};
    joblog {成功护送大师到恒山，获得%%1点经验%%2点潜能，共计杀死$jobjiaotu_count个魔教教徒，耗时@elapsed{$jobstart_ts}秒。};
  };
  #ACTION {^{设定环境变量：action \= \"checkjiaotu\"|你设定checkjiaotu为反馈信息}} {
    #VARIABLE {echots} {0};
    #VARIABLE {aimdo} {jobdo_husong};
    #MATH {jiaotucheck} {$jiaotucheck + 1};
    #VARIABLE {idle} {0};
    #IF {$jobflag_jiaotu == 0} {
      #IF {$jiaotucheck > 4} {
        #VARIABLE {dashishowup} {0};
        hi $hp[id]'s dashi;
        echo {checkdashi};
      };
      #ELSE {
        #DELAY {1} {
          echo {checkjiaotu}
        };
      };
    };
    #ELSE {
      #IF {$jobflag_escape == 0} {
        jobfight_husong;
      };
      #ELSE {
        #VARIABLE {dashiexits} {$roomexits};
        #VARIABLE {dashifound} {0};
        #VARIABLE {dashistep} {};
        jobfinddashi_husong;
      };
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkdashi\"|你设定checkdashi为反馈信息}} {
    #VARIABLE {echots} {0};
    #IF {$dashishowup == 0} {
      #IF {$fallback == 0} {
        #VARIABLE {fallback} {1};
        #VARIABLE {jiaotucheck} {5};
        @reverseDir{$lastdashistep};
        echo {checkjiaotu};
      };
      #ELSE {
        #CLASS jobdoclass KILL;
        joblog {丟失【$jobnpc_dashi】，任务失败};
        logbuff {missdashi};
        dohalt {jobprepare}
      };
    };
    #ELSEIF {"$room" != "白云庵"} {
      #NOP {设置目的信息防止异常发呆};
      #VARIABLE {aimcity} {$city};
      #VARIABLE {aimroom} {$room};
      #VARIABLE {aimroomid} {$roomid};
      executecmd {jobstep_husong};
    };
    #ELSE {
      #VARIABLE {jobcheck_count} {0};
      #DELAY {1} {echo {checkfinish}};
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkfinish\"|你设定checkfinish为反馈信息}} {
    #VARIABLE {echots} {0};
    #MATH {jobcheck_count} {$jobcheck_count + 1};
    #IF {$jobflag_arrived == 1} {
      #CLASS jobdoclass KILL;
      loc {
        dohalt {jobprepare};
      };
    };
    #ELSEIF {$jobcheck_count > 3} {
      joblog {未能领取任务奖励，任务失败};
      logbuff {noreward};
    };
    #ELSE {
      #DELAY {1} {echo {checkfinish}};
    };
  };
  #CLASS jobdoclass CLOSE;
  jobstep_husong;
};
#NOP {走下一步,%1:后续指令};
#ALIAS {jobstep_husong} {
  #VARIABLE {idle} {0};
  #VARIABLE {jiaotucheck} {0};
  #VARIABLE {lastdashistep} {};

  #IF {"$room" == "白云庵"} {
    jobfinish_husong;
  };
  #ELSEIF {&husongpath[] == 0} {
    loc {jobdo_husong};
  };
  #ELSE {
    #VARIABLE {lastdashistep} {$husongpath[+1]};
    #LIST {husongpath} {delete} {1};
    #IF {"$lastdashistep" == "river_he"} {
      #MATH {jiaotucheck} {$jiaotucheck + 1};
      dohalt {river_he {1} {echo {checkjiaotu}}};
    };
    #ELSEIF {"$lastdashistep" == "open gate"} {
      #VARIABLE {lastdashistep} {$husongpath[+1]};
      #LIST {husongpath} {delete} {1};
      dohalt {
        open gate;
        $lastdashistep;
        echo {checkjiaotu}
      };
    };
    #ELSE {
      #NOP {判定下要走的路径在当前房间出口是否存在，不存在重新定位并启动};
      #IF {@contains{{roomexits}{$lastdashistep}} == 0} {
        dohalt {
          loc {jobdo_husong}
        };
      };
      #ELSE {
        dohalt {
          $lastdashistep;
          echo {checkjiaotu}
        };
      };
    };
  };
};
#NOP {寻找逃跑的大师};
#ALIAS {jobfinddashi_husong} {
  #VARIABLE {dashidirect} {};
  #CLASS jobfightclass KILL;
  #CLASS jobfightclass OPEN;
  #ACTION {^%!s少林派第三十五代弟子「少林长老」$jobnpc_dashi(%*'s dashi)} {
    #IF {"@lower{%%1}" == "$hp[id]"} {
      #VARIABLE {dashifound} {1};
    };
  };
  #ACTION {^{设定环境变量：action \= \"checklocale\"|你设定checklocale为反馈信息}} {
    #VARIABLE {echots} {0};
    #IF {$dashifound == 1} {
      #CLASS jobfightclass KILL;
      jobfight_husong;
    };
    #ELSE {
      #VARIABLE {dashistep} {$dashiexits[+1]};
      #LIST {dashiexits} {delete} {1};
      dohalt {
        $dashistep;
        echo {checkfindstart}
      };
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkfindstart\"|你设定checkfindstart为反馈信息}} {
    #VARIABLE {echots} {0};
    #IF {$dashifound == 1} {
      #CLASS jobfightclass KILL;
      jobfight_husong;
    };
    #ELSE {
      #FOREACH {$roomexits[]} {d} {
        #IF {"$d" == "@reverseDir{$dashistep}"} {
          #CONTINUE;
        };
        look @longDir{$d};
        echo {checkdashi$d};
      };
      echo {checkfindover};
    };
  };
  #ACTION {^设定环境变量：action \= \"checkdashi%*\"} {
    #VARIABLE {echots} {0};
    #IF {$dashifound == 1} {
      #VARIABLE {dashifound} {0};
      #VARIABLE {dashidirect} {%%1};
    };
  };
  #ACTION {^你设定checkdashi%*为反馈信息} {
    #IF {$dashifound == 1} {
      #VARIABLE {dashifound} {0};
      #VARIABLE {dashidirect} {%%1};
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkfindover\"|你设定checkfindover为反馈信息}} {
    #VARIABLE {echots} {0};
    #IF {"$dashidirect" != ""} {
      #DELAY {1} {
        #CLASS jobfightclass KILL;
        dohalt {
          $dashidirect;
          follow $hp[id]'s dashi;
          jobfight_husong
        };
      };
    };
    #ELSE {
      #CLASS jobfightclass KILL;
      #DELAY {1} {
        dohalt {
          @reverseDir{$dashistep};
          jobfinddashi_husong {1}
        };
      }
    };
  };
  #CLASS jobfightclass CLOSE;
  #IF {&dashiexits[] == 0} {
    #CLASS jobfightclass KILL;
    #CLASS jobdoclass KILL;
    joblog {丟失【$jobnpc_dashi】，任务失败};
    logbuff {missing}
    dohalt {jobprepare}
  };
  #ELSE {
    look;
    echo {checklocale};
  };
};
#NOP {杀教徒};
#ALIAS {jobfight_husong} {
  #CLASS jobfightclass KILL;
  #CLASS jobfightclass OPEN;
  #ACTION {^魔教教徒神志迷糊，脚下一个不稳，倒在地上昏了过去} {
    kill $hp[id]'s jiaotu;
  };
  #ACTION {^魔教教徒「啪」的一声倒在地上，挣扎着抽动了几下就死了。} {
    #MATH {jobjiaotu_count} {$jobjiaotu_count + 1};
    kill $hp[id]'s jiaotu; 
  };
  #ACTION {^这里没有这个人。} {
    #CLASS jobfightclass KILL;
    #VARIABLE {jobflag_jiaotu} {0};
    dohalt {
      get gold from corpse;
      get silver from corpse;
      doheal {
        #IF {$hp[neili] < $hp[neili_max]} {
          startfull {loc {jobdo_husong}};
        };
        #ELSE {
          loc {jobdo_husong}
        };
      };
    };
  };
  #CLASS jobfightclass CLOSE;
  openwimpy;
  pfm_buff_normal;
  createpfm {@getFightPerform{}} {1};
  startfight;
  wwp;
  kill $hp[id]'s jiaotu;
};
#NOP {放弃任务};
#ALIAS {jobfangqi_husong} {
  jobprepare;
};