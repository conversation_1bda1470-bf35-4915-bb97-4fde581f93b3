#nop {刀舞剑舞;%1:后续指令};
#ALIAS goquest_dwjw
{
	#CLASS questclass KILL;
	#CLASS questclass OPEN;
	#IF {"$hp[party]" == "灵鹫宫"} {#VARIABLE {questmodule} {灵鹫剑舞}};
	#IF {"$hp[party]" == "逍遥派"} {#VARIABLE {questmodule} {逍遥刀舞}};
	#VARIABLE {okflag} {0};

	#ACTION {^不由得心下大奇，想要上去看个明白。} 
	{
		#VARIABLE {okflag} {1}; 
	};

	#ACTION {^{设定环境变量：action \= \"checktime\"|你设定checktime为反馈信息}} 
	{
		#VARIABLE {idle} {0};
		#IF {$env[gametime] < 9} 
		{
		  #DELAY {10} 
		  {
			time -s;
			look;
			echo {checktime};
		  };
		};

		#elseif {$env[gametime]>=10}
		{
			#CLASS questclass kill;
			questdelay {$questmodule} {0} {1200};
			dohalt {%1};
		};

		#else
		{
			pray pearl;
			climb cliff
		};

	};

	#ACTION {^你见这山崖离地数十丈，犹豫了半天，想想还是回去练好轻功再来爬吧} 
	{
        #CLASS questclass kill;
        questfail {$questmodule};
        dohalt {%1};
	} {5};
	
	#ACTION {^终于爬到了孔洞处,伸手取出了一副帛卷。} 
	{
		#CLASS questclass kill;
		questsuccess {$questmodule};
		dohalt {%1};
	} {5};
	
	#action {^还好你掉进了大水潭没有摔死..你挣扎着爬到岸边，心力交瘁，晕了过去。}
	{
        #CLASS questclass kill;
        questfail {$questmodule};
        dohalt {%1};
	};

	#ACTION {^你熟门熟路，穿越无量剑宗，很快就来到了山中密林。} 
	{
		dohalt {echo {checktime};}
	};

	#CLASS questclass close;
	gotodo {无量山} {无量剑宗} {cross wuliang};
};
