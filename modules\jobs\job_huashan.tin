#NOP {华山任务模块，%1:是否需要pray，参数不为空不执行on_huashan_before_go};
#ALIAS {jobgo_huashan} {
  #IF {"%1" == ""} {
    on_huashan_before_go {gotodo {华山} {前厅} {startfull {jobask_huashan}}}
  };
  #ELSEIF {$env[pray] == 0 && (@isQuestActivate{独孤九剑} == 1 || @isQuestActivate{吸星大法} == 1)} {
    dopray {storethings {pearl} {jobgo_huashan {%1}}}
  };
  #ELSE {
    gotodo {华山} {前厅} {startfull {jobask_huashan}}
  };
};
#ALIAS {jobask_huashan} {
  on_huashan_before_ask {gotodo {华山} {正气堂} {jobask_huashan_ask}}
};
#ALIAS {jobask_huashan_ask} {
  #NOP {询问结果,0:成功,1:放弃,2:busy,3:空挡,4:继续任务,5:其他选项};
  #VARIABLE {askresult} {0};
  #CLASS jobrequestclass OPEN;
  #ACTION {^你向岳不群打听有关『惩恶扬善』的消息。} {
    #VARIABLE {idle} {0};
    #CLASS jobresponseclass OPEN;
    #ACTION {^岳不群给了你一块令牌。} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      #VARIABLE {waittimes} {0};
      joblog {等待蒙面人。};
      dohalt {jobwait_huashan};
    };
    #ACTION {^岳不群说道：「你不能光说呀，倒是做出点成绩给我看看！」} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {jobfangqi_huashan};
    };
    #ACTION {^岳不群说道：「你现在正忙着做其他任务呢！」} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {
        waitlian;
        gotodo {华山} {前厅} {startfull {jobask_huashan} {1}};
      };
    };
    #ACTION {^岳不群说道：「现在没有听说有恶人为害百姓，你自己去修习武功去吧} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {jobprepare}；
    };
    #ACTION {^岳不群说道：「你眼露凶光, 还想去惩恶扬善? 」} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {gozshen {10000} {jobgo_huashan}};
    };
    #ACTION {^岳不群说道：「现在我这里没有给你的任务，你还是先处理好你其他事情再说吧。」} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {doquit};
    };
    #ACTION {^岳不群说道：「你还是先去思过崖面壁思过去吧} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      #VARIABLE {env[dgjj]} {YES};
      set env_dgjj;
      dohalt {jobprepare}
    };
    #CLASS jobresponseclass CLOSE;
  };
  #CLASS jobrequestclass CLOSE;
  jobclear_huashan;
  cond;
  ask yue buqun about 惩恶扬善;
};
#NOP {等蒙面人,%1:阶段,2为hs2};
#ALIAS {jobwait_huashan} {
  #VARIABLE {joblocation} {};
  #VARIABLE {jobnpc_mmr} {};
  #CLASS jobwaitclass OPEN;
  #ACTION {^{突然|忽然|冷不丁|猛地|冷不防}%!*一个蒙面人%!*你的令牌，向%*{处|方向}} {
    #IF {"%%2" == "菜地" || "%%2" == "碎石路" || "%%2" == "玄坛庙"} {
      #VARIABLE {joblocation} {华山村%%2};
    };
    #ELSEIF {@getRoomId{{华山}{%%2}} != -1} {
      #VARIABLE {joblocation} {华山%%2};
    };
    #ELSE {
      #VARIABLE {joblocation} {%%2};
    };
  };
  #ACTION {^你一把抓向蒙面人试图抢回令牌%!*发现原来是曾经名震江湖的%*。} {
    #VARIABLE {jobnpc_mmr} {%%1};
  };
  #ACTION {^{设定环境变量：action \= \"waitmmr\"|你设定waitmmr为反馈信息}} {
    #VARIABLE {echots} {0};
    #VARIABLE {idle} {0};
    #MATH {waittimes} {$waittimes + 1};
    #CLASS jobwaitclass KILL;
    #IF {"$jobnpc_mmr" != ""} {
      runwait {
        parsejoblocation {$joblocation} {jobdo_huashan} {
          joblog {无法解析地址【$joblocation】。};
          jobfangqi_huashan;
        } {2};
      };
    };
    #ELSEIF {$waittimes > 5} {
      jobfangqi_huashan
    };
    #ELSE {
      runwait {
        e;
        loc {jobwait_huashan};
      };
    };
  };
  #CLASS jobwaitclass CLOSE;
  #VARIABLE {jobstart_ts} {@now{}};
  #IF {"%1" == ""} {
    createpfm {@getFightPerform{{}{华山}}} {1};
  };
  #ELSE {
    createpfm {@getFightPerform{{}{华山2}}} {1};
  };
  gotodo {华山} {树林} {
    time;
    echo {waitmmr};
  };
};
#ALIAS {jobdo_huashan} {
  joblog {寻找位于【$joblocation】的【$jobnpc_mmr】。};
  wwp;
  pfm_buff_normal;
  jobnextroom {checkmmr};
};
#ALIAS {checkmmr} {
  #VARIABLE {jobnpc_mmr_id} {};
  #CLASS jobcheckclass OPEN;
  #ACTION {$jobnpc_mmr(%*)} {
    #VARIABLE {jobnpc_mmr_id} {@lower{%%1}};
  };
  #ACTION {^{设定环境变量：action \= \"checkmmr\"|你设定checkmmr为反馈信息}} {
    #VARIABLE {echots} {0};
    #VARIABLE {idle} {0};
    #CLASS jobcheckclass KILL;
    #IF {"$jobnpc_mmr_id" == ""} {
      jobnextroom {checkmmr}
    };
    #ELSE {
      jobfight_huashan;
    };
  };
  #CLASS jobcheckclass CLOSE;
  look;
  echo {checkmmr};
};

#ALIAS {jobfight_huashan} {
  #VARIABLE {trycount} {0};
  #VARIABLE {cuthead} {0};
  #CLASS jobfightclass KILL;
  #CLASS jobfightclass OPEN;
  #ACTION {^$jobnpc_mmr神志迷糊，脚下一个不稳，倒在地上昏了过去。} {
  };
  #ACTION {^这里没有这个人。} {
    joblog {未能找到位于【$joblocation】的【$jobnpc_mmr】。};
    jobfangqi_huashan
  };
  #ACTION {^$jobnpc_mmr「啪」的一声倒在地上，挣扎着抽动了几下就死了。} {
    joblog {顺利杀死【$jobnpc_mmr】，耗时【@elapsed{$jobfight_ts}】秒。};
    dohalt {
      stopfight;
      wcwp;
      execute {
        get silver from corpse 1;
        get ling pai from corpse 1;
        qie corpse 1;
        get silver from corpse 2;
        get ling pai from corpse 2;
        qie corpse 2
      };
      echo {checkhead}
    };
  };
  #ACTION {^你扬起%*，对准$jobnpc_mmr的尸体的脖子比了比，猛斩了下去！} {
    #VARIABLE {cuthead} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkhead\"|你设定checkhead为反馈信息}} {
    #VARIABLE {echots} {0};
    #VARIABLE {idle} {0};
    #IF {$cuthead == 1} {
      #CLASS jobfightclass KILL;
      dohalt {jobhead_huashan}
    };
    #ELSE {
      #MATH {trycount} {$trycount + 1};
      #DELAY {2} {
        #IF {$trycount >= 5} {
          jobfangqi_huashan
        };
        #ELSE {
          wcwp;
          execute {
            get silver from corpse 1;
            get ling pai from corpse 1;
            qie corpse 1;
            get silver from corpse 2;
            get ling pai from corpse 2;
            qie corpse 2
          };
          echo {checkhead}
        };
      }
    };
  };
  #CLASS jobfightclass CLOSE;
  #VARIABLE {jobfight_ts} {@now{}};
  kill $jobnpc_mmr_id;
  startfight
};
#ALIAS {jobhead_huashan} {
  gotodo {华山} {祭坛} {jobhead_huashan_give};
};
#ALIAS {jobhead_huashan_give} {
  #CLASS jobfinishclass OPEN;
  #ACTION {^你身上没有这样东西。} {
    #CLASS jobfinishclass KILL;
    runwait {jobfangqi_huashan}
  };
  #ACTION {^岳灵珊在你的令牌上写下了一个 %* 字。} {
    dohalt {
      #IF {"%%1" == "一"} {
        #NOP {如果有请求则不继续};
        #IF {"$currentjob" != "华山2" || "$caller" != ""} {
          ask yue lingshan about 力不从心
        };
        #ELSE {
          #CLASS jobfinishclass KILL;
          jobwait_huashan {2}
        };
      };
      #ELSE {
        #CLASS jobfinishclass KILL;
        jobfinish_huashan
      };
    }
  };
  #ACTION {^你向岳灵珊打听有关『力不从心』的消息} {
    #LINE ONESHOT #ACTION {^岳灵珊说道：%*回去复命} {
      #CLASS jobfinishclass KILL;
      dohalt {jobfinish_huashan};
    };
  };
  #CLASS jobfinishclass CLOSE;
  give head to yue lingshan
};
#ALIAS {jobfinish_huashan} {
  gotodo {华山} {正气堂} {jobfinish_huashan_ask};
};
#ALIAS {jobfinish_huashan_ask} {
  #VARIABLE {questflag} {0};
  #CLASS jobfinishclass KILL;
  #CLASS jobfinishclass OPEN;
  #ACTION {^你身上没有这样东西。} {
    #CLASS jobfinishclass KILL;
    runwait {jobfangqi_huashan}
  };
  #ACTION {^恭喜你！你成功的完成了华山任务！你被奖励了：} {
    #VARIABLE {lastjob} {华山};
    #CLASS rewardclass OPEN;
    #ACTION {^%*点经验!} {
      #VARIABLE {jobreward_exp} {%%%1};
    };
    #ACTION {^%*点潜能!} {
      #VARIABLE {jobreward_pot} {%%%1};
    };
    #ACTION {^岳不群说道：%*你杀了不少恶人，未免杀气过重不如上思过崖面壁忏悔吧} {
      #VARIABLE {questflag} {1};
    };
    #ACTION {^岳不群说道：%*听说魔教教主被关在杭州西湖湖底，你去把他杀了，我就让你入五岳剑派。} {
      #VARIABLE {questflag} {2};
    };
    #ACTION {^你给岳不群一块令牌。} {
      #CLASS rewardclass KILL;
      #CLASS jobfinishclass KILL;
      joblog {成功完成，获得【$jobreward_exp】点经验【$jobreward_pot】点潜能，耗时【@elapsed{$jobstart_ts}】秒。};
      jobclear_huashan;
      #VARIABLE {jobnpc_mmr} {};
      #VARIABLE {joblocation} {};
      #IF {$questflag == 1} {
        #VARIABLE {env[dgjj]} {YES};
        set env_dgjj;
      };
      #IF {$questflag == 2} {
        #VARIABLE {env[xxdf]} {YES};
        set env_xxdf;
      };
      runwait {jobprepare}
    };
    #CLASS rewardclass CLOSE;
  };
  #CLASS jobfinishclass CLOSE;
  give ling pai to yue buqun;
};
#ALIAS {jobfangqi_huashan} {
  gotodo {华山} {正气堂} {jobfangqi_huashan_ask};
};
#ALIAS {jobfangqi_huashan_ask} {
  #NOP {询问结果,0:成功,1:busy};
  #VARIABLE {askresult} {0};
  #CLASS jobrequestclass OPEN;
  #ACTION {^你向岳不群打听有关『失败』的消息。} {
    #CLASS jobresponseclass OPEN;
    #ACTION {^岳不群说道：「既然如此，也不能勉强，你就先退下吧。」} {
      joblog {未能完成任务，耗时@elapsed{$jobstart_ts}秒};
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {jobprepare};
    };
    #ACTION {^岳不群说道：「你又没领过任务，何谓失败啊？」} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {jobprepare};
    };
    #CLASS jobresponseclass CLOSE;
  };
  #CLASS jobrequestclass CLOSE;
  ask yue buqun about 失败;
};
#ALIAS {jobclear_huashan} {
  #VARIABLE {jobnpc_mmr} {};
  #VARIABLE {jobnpc_mmr_id} {};
  #VARIABLE {joblocation} {};
  #LIST {jobroomlist} {clear};
  #VARIABLE {jobfight_ts} {0};
  #VARIABLE {jobnpc_count} {0};
  #VARIABLE {jobstart_ts} {0};
};