#VARIABLE {librarybooks} {
  {易经} {
    {party} {桃花岛}
    {timestamp} {0}
  }
  {九宫八卦图谱} {
    {party} {桃花岛}
    {timestamp} {0}
  }
  {kejin jijie} {
    {timestamp} {0}
  }
  {douzhen dinglun} {
    {timestamp} {0}
  }
  {boji xidoufang} {
    {timestamp} {0}
  }
  {she jing} {
    {timestamp} {0}
  }
};
#NOP {图书模块,%1:书籍名称,%2:后续指令,%3:失败的指令};
#ALIAS {library_call} {
  yz {library_call_start {%1} {%2} {%3}}
};
#NOP {开始请求书籍,%1:书籍名称,%2:后续指令};
#ALIAS {library_call_start} {
  #CLASS serviceclass KILL;
  #CLASS serviceclass OPEN;
  #ACTION {^没有这个人。} {
    #CLASS serviceclass KILL;
    #VARIABLE {librarybooks[%1][timestamp]} {@now{}};
    #SHOWME {<faa>保姆$conf[nanny][library]人不在};
    %3
  };
  #ACTION {%*(%*)告诉你：library_wait} {
    #CLASS serviceclass KILL;
    #VARIABLE {idle} {0};
    #DELAY {5} {library_call {%1} {%2} {%3}};
  };
  #ACTION {%*(%*)告诉你：library_come} {
    #CLASS serviceclass KILL;
    #VARIABLE {waiter} {
      {id} {@lower{%%2}}
      {name} {%%1}
    };
    #NOP {扬州钱庄交易};
    library_request_wait {%1} {%2} {%3};
  };
  #CLASS serviceclass CLOSE;
  #IF {"$conf[nanny][library]" == ""} {
    #SHOWME {<faa>未配置保姆,请自行处理};
    #VARIABLE {librarybooks[%1][timestamp]} {@now{}};
    %2
  };
  #ELSE {
    tell $conf[nanny][library] library_request_%1
  };
};
#ALIAS {library_request_wait} {
  #VARIABLE {startts} {@now{}};
  #VARIABLE {okflag} {0};
  #VARIABLE {arrived} {0};
  #CLASS serviceclass KILL;
  #CLASS serviceclass OPEN;
  #ACTION {^$waiter[name]轻轻地拍了拍你的头} {
    #VARIABLE {arrived} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkresponse\"|你设定checkresponse为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {@elapsed{$startts} > 600} {
      #CLASS serviceclass KILL;
      loc {
        library_call {%1} {%2} {%3};
      };
    };
    #ELSEIF {$arrived == 0} {
      #DELAY {5} {
        echo {checkresponse};
      }
    };
  };
  #ACTION {^$waiter[name]给你%*} {
    #CLASS serviceclass KILL;
    bye $waiter[id];
    %2;
  };
  #ACTION {%*(%*)告诉你：library_nop} {
    #CLASS serviceclass KILL;
    #VARIABLE {librarybooks[%1][timestamp]} {@now{}};
    %3;
  };
  #CLASS serviceclass CLOSE;
  echo {checkresponse};
};
#NOP {取消};
#ALIAS {library_cancel} {
  #IF {"$conf[nanny][library]" != ""} {
    tell $conf[nanny][library] library_cancel
  };
};
#NOP {还书,%1:书籍,%2:后续指令};
#ALIAS {library_return} {
  yz {library_return_start {%1} {%2}}
};
#ALIAS {library_return_start} {
  #CLASS serviceclass KILL;
  #CLASS serviceclass OPEN;
  #ACTION {^没有这个人。} {
    #CLASS serviceclass KILL;
    #VARIABLE {librarybooks[%1][timestamp]} {@now{}};
    #SHOWME {<faa>保姆$conf[nanny][library]人不在};
    %2
  };
  #ACTION {%*(%*)告诉你：library_wait} {
    #CLASS serviceclass KILL;
    #VARIABLE {idle} {0};
    #DELAY {5} {library_return {%1} {%2} {%3}};
  };
  #ACTION {%*(%*)告诉你：library_come} {
    #CLASS serviceclass KILL;
    #VARIABLE {waiter} {
      {id} {@lower{%%2}}
      {name} {%%1}
    };
    #NOP {扬州钱庄交易};
    library_return_wait {%1} {%2};
  };
  #CLASS serviceclass CLOSE;
  #IF {"$conf[nanny][library]" == ""} {
    #SHOWME {<faa>未配置保姆,请自行处理};
    #VARIABLE {librarybooks[%1][timestamp]} {@now{}};
    %2
  };
  #ELSE {
    tell $conf[nanny][library] library_return_%1
  };
};
#ALIAS {library_return_wait} {
  #VARIABLE {okflag} {0};
  #VARIABLE {arrived} {0};
  #CLASS serviceclass KILL;
  #CLASS serviceclass OPEN;
  #ACTION {^$waiter[name]轻轻地拍了拍你的头} {
    #VARIABLE {arrived} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkresponse\"|你设定checkresponse为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {$arrived == 0} {
      #DELAY {5} {
        echo {checkresponse};
      }
    };
    #ELSE {
      #CLASS serviceclass KILL;
      give %1 to $waiter[id];
      i;
      bye $waiter[id];
      %2;
    };
  };
  #CLASS serviceclass CLOSE;
  echo {checkresponse};
};