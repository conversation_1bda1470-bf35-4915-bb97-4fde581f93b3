#nop {神照经;%1:后续指令};
#ALIAS goquest_szj {
	#VARIABLE {questmodule} {神照经};
	#SWITCH {$questlist[$questmodule][laststep]} {
    #CASE {0} {
			gotonpc {丁典} {szj_askdingdian {%1}};
    };
    #CASE {1} {
      gotonpc {狄云} {szj_askdiyun {%1}};
    };
    #DEFAULT {
      questupdate {$questmodule} {2};
    };
  };
};
#ALIAS {szj_askdingdian} {
	#CLASS questclass KILL;
	#CLASS questclass OPEN;
	#ACTION {^丁典说道：「今天先教到到这里吧，明天吧} {
		#CLASS questclass KILL;
		#NOP {时间不够};
		questdelay {$questmodule} {0} {3600};
		dohalt {%1;};
	};
	#ACTION {^丁典说道：「以%*当前的经验恐怕还是难以领悟} {
		#CLASS questclass KILL;
		#NOP {经验不够};
		questdelay {$questmodule} {} {7200};
		dohalt {%1;};
	};
	#ACTION {^你听了丁典的指点，对神照经的奥妙似乎有些心得} {
		#CLASS questclass KILL;
		#NOP {开了};
		questupdate {$questmodule} {1};
		dohalt {
			fangqi shenzhao-jing;
			%1;
		};
	};
	#ACTION {^你听了丁典的指点，可是对神照经的奥妙全然不得要领} {
		#CLASS questclass KILL;
		#NOP {失败};
		questfail {$questmodule};
		dohalt {%1;};
	};
	#CLASS questclass CLOSE;
	pray pearl;
	dohalt {
		ask ding dian about 神照经
	};
};
#ALIAS {szj_askdiyun} {
	#CLASS questclass KILL;
	#CLASS questclass OPEN;
	#ACTION {^狄云说道：「今天先教到到这里吧，明天吧} {
		#CLASS questclass KILL;
		#NOP {时间不够};
		questdelay {$questmodule} {0} {3600};
		dohalt {%1;};
	};
	#ACTION {^狄云说道：「以%*当前的经验恐怕还是难以领悟} {
		#CLASS questclass KILL;
		#NOP {经验不够};
		questdelay {$questmodule} {} {7200};
		dohalt {%1;};
	};
	#ACTION {^狄云说道：「你最好去丁大哥那里，其实我对神照经有些地方还是不能融会贯通} {
		#CLASS questclass KILL;
		#NOP {开了};
		questupdate {$questmodule} {2};
		dohalt {%1;};
	};
	#ACTION {^你试图将内息冲到百会穴中，突然只觉颜面上一阵清凉} {
		#CLASS questclass KILL;
		#NOP {开了};
		questupdate {$questmodule} {2};
		dohalt {
			fangqi shenzhao-jing;
			%1
		};
	};
	#ACTION {^你听了狄云的指点，可是对神照经的奥妙全然不得要领} {
		#CLASS questclass KILL;
		#NOP {失败};
		questfail {$questmodule};
		dohalt {%1;};
	};
	#CLASS questclass CLOSE;
	pray pearl;
	dohalt {
		ask di yun about 神照经
	};
};