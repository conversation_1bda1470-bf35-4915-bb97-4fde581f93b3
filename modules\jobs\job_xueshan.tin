#NOP {雪山任务模块};
#ALIAS {jobgo_xueshan} {
  on_xueshan_before_go {gotodo {大雪山} {风见台} {startfull {jobask_xueshan}}}
};
#ALIAS {jobask_xueshan} {
  dohalt {on_xueshan_before_ask {gotodo {大雪山} {入幽口} {jobask_xueshan_ask}}}
};
#ALIAS {jobask_xueshan_ask} {
  #NOP {询问结果,0:成功,1:空挡,2:刚做完雪山};
  #VARIABLE {askresult} {0};
  #VARIABLE {joblocation} {};
  #VARIABLE {jobnpc_bastard} {};
  #VARIABLE {jobnpc_skill} {};
  #VARIABLE {jobnpc_desc} {};
  #VARIABLE {jobnpc_superguard} {0};
  #CLASS jobrequestclass OPEN;
  #ACTION {^你向宝象打听有关『job』的消息。} {
    #CLASS jobresponseclass OPEN;
    #ACTION {^宝象在你的耳边悄声说道：听说最近%u附近来了个漂亮的小妞，你去给我弄来。} {
      #VARIABLE {joblocation} {%%%1};
    };
    #ACTION {宝象说道：「我不是叫你到%u去给老祖爷爷抢美女了嘛！」} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      #VARIABLE {joblocation} {%%%1};
      dohalt {
        parsejoblocation {$joblocation} {jobdo_xueshan} {joblog {未能解析地址【$joblocation】。};jobfangqi_xueshan} {2};
      };
    };
    #ACTION {^宝象在你的耳边悄声说道：这个任务比较困难，不行不要勉强} {
      #VARIABLE {jobnpc_superguard} {1};
    };
    #ACTION {^宝象说道：「给老祖爷爷干活，速去速回} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      #VARIABLE {jobstart_ts} {@now{}};
      dohalt {
        parsejoblocation {$joblocation} {on_xueshan_go {jobdo_xueshan}} {joblog {未能解析地址【$joblocation】。};jobfangqi_xueshan} {2};
      };
    };
    #ACTION {^宝象说道：「身体是革命的本钱啊} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {on_xueshan_wait};
    };
    #ACTION {^宝象说道：「你要累死你老祖爷爷啊！一边呆着去！」} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {jobprepare};
    };
    #ACTION {^宝象说道：「我看你不够心狠手辣，爷爷我不喜欢} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {gofshen {10000} {jobgo_xueshan}};
    };
    #ACTION {^宝象说道：「现在我这里没有给你的任务，你还是先处理好你其他事情再说吧。」} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      doquit;
    };
    #CLASS jobresponseclass CLOSE;
  };
  #CLASS jobrequestclass CLOSE;
  jobclear_xueshan;
  execute {
    jobtimes;
    cond;
    time;
    ask bao xiang about job
  };
};
#NOP {做雪山任务};
#ALIAS {jobdo_xueshan} {
  #VARIABLE {jobstart_ts} {@now{}};
  taskbegin;
  taskrecord;
  joblog {寻找位于【$joblocation】的【小妞】。};
  wwp;
  pfm_buff_normal;
  createpfm {$conf[pfm][scene][xs]};
  jobnextroom {checkbeauty {0}} {jobfail_xueshan {0}};
};
#NOP {雪山搜索失败，%1:重复搜索标识};
#ALIAS {jobfail_xueshan} {
  #LOCAL {iter} {@eval{@eval{%1} + 1}};
  #LOCAL {wanderroom} {$common[wanderwheres][$jobcity$jobroom]};
  #IF {$iter == 1} {
    joblog {未能找到位于【$joblocation】的【小妞】，扩大范围搜索。};
    parsejoblocation {$joblocation} {jobnextroom {checkbeauty {$iter}} {jobfail_xueshan {$iter}}} {jobfail_xueshan {$iter}} {5};
  };
  #ELSEIF {$iter == 2 && "$wanderroom" != ""} {
    joblog {还是未能找到位于【$joblocation】的【小妞】，开始漫游搜索。};
    loc {gotoroom {$wanderroom[roomid]} {jobwander_xueshan {$wanderroom[roomname]} {$wanderroom[range]}}}
  };
  #ELSE {
    joblog {终究未能找到位于【$joblocation】的【小妞】。};
    jobfangqi_xueshan;
  };
};
#NOP {%1:房间名称,%2:步数};
#ALIAS {jobwander_xueshan} {
  job_wander {checkbeauty {} {jobwander_xueshan {%1} {@eval{%2 - 1}}}} {jobfail_xueshan {3}} {%1} {%2}
};
#NOP {检查美女，%1:重搜索标志,%2:额外动作};
#ALIAS {checkbeauty} {
  #VARIABLE {checkresult} {0};
  #CLASS jobcheckclass KILL;
  #CLASS jobcheckclass OPEN;
  #ACTION {^你要看什么} {
    #VARIABLE {checkresult} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkbeauty\"|你设定checkbeauty为反馈信息}} {
    #VARIABLE {echots} {0};
    #CLASS jobcheckclass KILL;
    #IF {$checkresult == 1} {
      #IF {"%2" != ""} {
        %2
      };
      #ELSE {
        runwait {jobnextroom {checkbeauty {%1}} {jobfail_xueshan {%1}}}
      };
    };
    #ELSE {
      follow $hp[id]'s beauty;
      checkguard
    };
  };
  #CLASS jobcheckclass CLOSE;
  look $hp[id]'s beauty;
  echo {checkbeauty};
};
#NOP {检查guard};
#ALIAS {checkguard} {
  #VARIABLE {okflag} {0};
  #VARIABLE {guardflag} {0};
  #VARIABLE {guarddesc} {};
  #VARIABLE {superguard} {0};
  #VARIABLE {jobnpc_guard_name} {};
  #VARIABLE {jobnpc_guard_id} {};
  #VARIABLE {jobnpc_guard_party} {};
  #VARIABLE {jobnpc_guard_weapon} {};
  #CLASS jobcheckclass KILL;
  #CLASS jobcheckclass OPEN;
  #ACTION {^%+2u{镖头|大侠|捕头|护院|保镖} %u(%*)} {
    #VARIABLE {guardflag} {1};
    #VARIABLE {jobnpc_guard_name} {%%3};
    #VARIABLE {jobnpc_guard_id} {@lower{%%4}};
  };
  #ACTION {^这位高手似乎来自%*。} {
    #VARIABLE {jobnpc_guard_party} {%%1};
  };
  #ACTION {^{他|她}装备着} {
    #CLASS weaponcapclass OPEN;
    #ACTION {^  □%*(%*)} {
      #CLASS weaponcapclass KILL;
      #VARIABLE {jobnpc_guard_weapon} {%%%1};
    };
    #CLASS weaponcapclass CLOSE;
  };
  #ACTION {^{设定环境变量：action \= \"checkguard\"|你设定checkguard为反馈信息}} {
    #VARIABLE {echots} {0};
    #CLASS weaponcapclass KILL;
    #VARIABLE {guarddesc} {$jobnpc_guard_party$jobnpc_guard_weapon};
    #IF {$guardflag == 0}{
      sa $hp[id]'s beauty;
    };
    #ELSEIF {$jobnpc_superguard == 1 && (@contains{{conf[fangqidesc]}{大内高手}} > 0 || @contains{{conf[fangqiguard]}{$guarddesc}} > 0)} {
      #CLASS jobcheckclass KILL;
      joblog {主动放弃来自【$jobnpc_guard_party】装备了【$jobnpc_guard_weapon】的【大内高手】【$jobnpc_guard_name】。};
      runwait {jobfangqi_xueshan};
    };
    #ELSE {
      #CLASS jobcheckclass KILL;
      #DELAY {0.5} {fightguard};
    };
  };
  #ACTION {^人家有保镖在呢！你这么干太冒险了吧} {
    #CLASS jobcheckclass KILL;
    #NOP {保镖不在，也撒不了，直接溜吧};
    joblog {未找到小妞保镖};
    jobfangqi_xueshan
  };
  #ACTION {^你嘿嘿阴笑了几声，用指甲向} {
    #CLASS jobcheckclass KILL;
    #DELAY {1} {on_xueshan_down {jobfinish_xueshan}}
  };
  #CLASS jobcheckclass CLOSE;
  checkdanger {
    look $hp[id]'s guard;
    echo {checkguard};
  };
};
#NOP {尝试杀guard};
#ALIAS {fightguard} {
  #VARIABLE {checkresult} {0};
  #VARIABLE {performcd} {0};
  #CLASS jobcheckclass KILL;
  #CLASS jobcheckclass OPEN;
  #ACTION {^这里不准战斗} {
    #VARIABLE {checkresult} {1};
  };
  #ACTION {^$jobnpc_guard_name说道：「快逃啊！」} {
    #VARIABLE {checkresult} {2};
  };
  #ACTION {^$jobnpc_guard_name说道：「光天化日也敢抢劫！」} {
    #VARIABLE {checkresult} {0};
  };
  #ACTION {^这里没有这个人} {
    #VARIABLE {checkresult} {3};
  };
  #ACTION {^{设定环境变量：action \= \"checkkill\"|你设定checkkill为反馈信息}} {
    resonate {checkkill};
    #CLASS jobcheckclass KILL;
    #IF {$checkresult == 1} {
      stopfight;
      #DELAY {1} {
        jobfangqi_xueshan;
      };
    };
    #ELSEIF {$checkresult == 3} {
      stopfight;
      checkbeauty {1};
    };
    #ELSE {
      jobfight_xueshan
    };
  };
  #CLASS jobcheckclass CLOSE;
  #CLASS haltclass KILL;
  #SHOWME {<faa>设置战斗目标为$jobnpc_guard_name};
  joblog {开始与来自【$jobnpc_guard_party】装备了【$jobnpc_guard_weapon】的【$jobnpc_guard_name】决斗。};
  #VARIABLE {jobfight_target} {$jobnpc_guard_name};
  #VARIABLE {jobfight_ts} {@now{}};
  #IF {$jobnpc_superguard == 1 && "$conf[pfm][npc][xs][$guarddesc]" != ""} {
    #NOP {使用特定NPC的perform};
    createpfm {@getFightPerform{$guarddesc}} {1} {$hp[id]'s guard};
  };
  #ELSE {
    createpfm {@getFightPerform{}} {1} {$hp[id]'s guard}
  };
  #DELAY {1} {
    ensure {
      kill $hp[id]'s guard;
      autopfm;
      startfight {} {$jobnpc_guard_name}
    } {checkkill}
  }
};
#NOP {雪山任务战斗};
#ALIAS {jobfight_xueshan} {
  #CLASS jobfightclass OPEN;
  #ACTION {^$jobnpc_guard_name神志迷糊，脚下一个不稳，倒在地上昏了过去} {
    takedown;
  };
  #ACTION {^$jobnpc_guard_name「啪」的一声倒在地上，挣扎着抽动了几下就死了} {
    takedown;
    #IF {$jobnpc_superguard == 0} {
      joblog {顺利杀死来自【$jobnpc_guard_party】的【$jobnpc_guard_name】，耗时@elapsed{$jobfight_ts}秒。};
    };
    #ELSE {
      joblog {顺利杀死来自【$jobnpc_guard_party】使用【$jobnpc_guard_weapon】的【大内高手】【$jobnpc_guard_name】，耗时@elapsed{$jobfight_ts}秒。};
    };
    dohalt {
      stopfight;
      execute {
        get gold from corpse;
        get silver from corpse;
        get zhuanji;
        sa $hp[id]'s beauty
      };
    };
  };
  #ACTION {^你嘿嘿阴笑了几声，用指甲向} {
    #CLASS jobfightclass KILL;
    #DELAY {1} {on_xueshan_down {jobfinish_xueshan};}
  };
  #CLASS jobfightclass CLOSE;
};
#ALIAS {jobfinish_xueshan} {
  taskstats {雪山};
  gotodo {大雪山} {入幽口} {jobfinish_xueshan_ask}
};
#ALIAS {jobfinish_xueshan_ask} {
  #CLASS jobrequestclass OPEN;
  #ACTION {^你向宝象打听有关『完成』的消息。} {
    #CLASS jobresponseclass OPEN;
    #ACTION {^宝象似乎不懂你的意思} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {jobask_xueshan};
    };
    #ACTION {^宝象开始考虑是否要杀了你} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {jobfangqi_xueshan};
    };
    #ACTION {^恭喜你！你成功的完成了雪山任务！你被奖励了：} {
      #CLASS jobresponseclass KILL;
        #CLASS jobrequestclass KILL;
      #VARIABLE {lastjob} {雪山};
      #CLASS rewardclass OPEN;
      #ACTION {^%*点经验!} {
        #VARIABLE {jobreward_exp} {%%%%1};
      };
      #ACTION {^%*点潜能!} {
        #VARIABLE {jobreward_pot} {%%%%1};
      };
      #ACTION {^%*个通宝} {
        #VARIABLE {jobreward_tongbao} {%%%%1};
        #SHOWME {<faa>获得通宝 $jobreward_tongbao};
      };
      #ACTION {^宝象说道：「好好干，老祖爷爷会喜欢你的} {
        #CLASS rewardclass KILL;
        joblog {成功完成，获得$jobreward_exp点经验$jobreward_pot点潜能，【$jobreward_tongbao】个通宝，耗时@elapsed{$jobstart_ts}秒。};
        taskgain {@ctd{$jobreward_exp}} {@ctd{$jobreward_tongbao}};
        taskend;
        jobclear_xueshan;
      };
      #CLASS rewardclass CLOSE;
      dohalt {
        on_xueshan_finish {jobprepare}
      };
    };
    #CLASS jobresponseclass CLOSE;
  };
  #CLASS jobrequestclass CLOSE;
  ask bao xiang about 完成;
};
#ALIAS {jobfangqi_xueshan} {
  gotodo {大雪山} {入幽口} {jobfangqi_xueshan_ask};
};
#ALIAS {jobfangqi_xueshan_ask} {
  #CLASS jobdoclass KILL;
  #NOP {询问结果,0:成功,1:busy};
  #VARIABLE {askresult} {0};
  #CLASS jobrequestclass OPEN;
  #ACTION {^你向宝象打听有关『失败』的消息。} {
    #CLASS jobresponseclass OPEN;
    #ACTION {^宝象说道：「这么简单的任务你怎么可以放弃呢！快去干！」} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {jobfangqi_xueshan};
    };
    #ACTION {^宝象说道：「你根本就没任务，失败什么呀} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {jobprepare};
    };
    #ACTION {^宝象对你失望极了：“你没救了。} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      joblog {未能完成任务，耗时@elapsed{$jobstart_ts}秒。};
      dohalt {jobprepare};
    };
    #ACTION {^宝象说道：「这个任务是比较困难，你完不成也不能全怪你} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      joblog {未能完成任务，耗时@elapsed{$jobstart_ts}秒。};
      dohalt {jobprepare};
    };
    #CLASS jobresponseclass CLOSE;
  };
  #CLASS jobrequestclass CLOSE;
  ask bao xiang about 失败;
};
#ALIAS {jobclear_xueshan} {
  #VARIABLE {jobnpc_guard_name} {};
  #VARIABLE {jobnpc_guard_id} {};
  #VARIABLE {jobnpc_guard_party} {};
  #VARIABLE {jobnpc_guard_weapon} {};
  #VARIABLE {joblocation} {};
  #LIST {jobroomlist} {clear};
  #VARIABLE {jobfight_ts} {0};
  #VARIABLE {jobnpc_count} {0};
  #VARIABLE {jobstart_ts} {0};
};