#NOP {门童模块，主要负责蝴蝶谷开门(必须明教)和搬运林退思保证闺房入口};
#ALIAS {jobgo_doorman} {
  gotodo {蝴蝶谷} {山壁} {jobdoorman_killling}
};
#ALIAS {jobdoorman_killling} {
  #VARIABLE {okflag} {1};
  #CLASS jobcheckclass KILL;
  #CLASS jobcheckclass OPEN;
  #ACTION {^这里没有这个人} {
    #CLASS jobcheckclass KILL;
    dohalt {gotoroom {378} {jobdoorman_ansuanling}}
  };
  #ACTION {^凌退思「啪」的一声倒在地上，挣扎着抽动了几下就死了} {
    #CLASS jobcheckclass KILL;
    dohalt {gotoroom {378} {jobdoorman_ansuanling}}
  };
  #CLASS jobcheckclass CLOSE;
  yield no;
  kill ling tuisi
};
#NOP {检查凌退思是否在山壁};
#ALIAS {jobdoorman_checkling} {
  #VARIABLE {okflag} {1};
  #VARIABLE {called} {0};
  #VARIABLE {attackts} {0};
  #LIST {emotes} {create} {18mo;9191;9494;accuse;addoil;admire;admire2;admit;afraid;agree;ah;angry;angry3;beg1;beg2;laugh;haha;hehe;smile};
  #CLASS jobcheckclass KILL;
  #CLASS jobcheckclass OPEN;
  #ACTION {^你想攻击谁} {
    #VARIABLE {okflag} {0};
  };
  #ACTION {^结果对你没有造成任何伤害} {
    #VARIABLE {attackts} {@now{}}
  };
  #ACTION {^看起来凌退思想杀死你} {
    #VARIABLE {attackts} {@now{}}
  };
  #ACTION {^凌退思已经无法还手了} {
    #VARIABLE {okflag} {2};
  };
  #ACTION {^%*说道：「芝麻开门} {
    bo huacong
  };
  #ACTION {^%*巡捕对你说道：杀了人，就要偿命} {
    yield no;
    openwimpy;
    kill xunbu;
  };
  #ACTION {^看起来%*巡捕想杀死你} {
    yield no;
    openwimpy;
    kill xunbu;
  };
  #ACTION {^%*捕头恶狠狠地盯着你%*} {
    yield no;
    openwimpy;
    kill butou;
  };
  #ACTION {^看起来%*捕头想杀死你} {
    yield no;
    openwimpy;
    kill butou;
  };
  #ACTION {^{设定环境变量：action \= \"checkling\"|你设定checkling为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {$okflag == 0} {
      #CLASS jobcheckclass KILL;
      gotoroom {378} {jobdoorman_ansuanling {1}}
    };
    #ELSEIF {$okflag == 2} {
      #VARIABLE {okflag} {1};
      #DELAY {2} {
        hit ling tuisi;
        echo {checkling}
      };
    };
    #ELSEIF {@elapsed{$attackts} >= 10} {
      #VARIABLE {okflag} {1};
      #DELAY {2} {
        hit ling tuisi;
        echo {checkling}
      };
    };
    #ELSE {
      #DELAY {6} {
        $emotes[+@rnd{{1}{&emotes[]}}] ling tuisi;
        echo {checkling}
      };
    };
  };
  #CLASS jobcheckclass CLOSE;
  closewimpy;
  dohalt {
    yield yes;
    drop ling tuisi;
    hit ling tuisi;
    echo {checkling}
  }
};
#NOP {暗算凌退思并抱走,%1:是否必须等到凌退思};
#ALIAS {jobdoorman_ansuanling} {
  #VARIABLE {okflag} {0};
  #CLASS jobcheckclass KILL;
  #CLASS jobcheckclass OPEN;
  #ACTION {^凌退思都已经这样了，你还用得着暗算吗} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^你摒息静气，纵身而起，向北面后院的凌退思扑去} {
    #VARIABLE {okflag} {2};
  };
  #ACTION {^{设定环境变量：action \= \"checkansuan\"|你设定checkansuan为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {$okflag == 0} {
      #IF {"%1" == ""} {
        #CLASS jobcheckclass KILL;
        loc {gotodo {蝴蝶谷} {山壁} {jobdoorman_checkling}}
      };
      #ELSE {
        #DELAY {2} {
          yun jing;
          dohalt {
            ansuan ling tuisi at north;
            echo {checkansuan}
          }
        }
      };
    };
    #ELSEIF {$okflag == 1} {
      #DELAY {2} {
        yun jing;
        dohalt {
          ansuan ling tuisi at north;
          echo {checkansuan}
        }
      }
    };
    #ELSE {
      #CLASS jobcheckclass KILL;
      doorman_call {tong yaoshi} {smile};
      dohalt {
        n;
        yield yes;
        get ling tuisi;
        loc {gotodo {蝴蝶谷} {山壁} {jobdoorman_checkling}}
      };
    };
  };
  #CLASS jobcheckclass CLOSE;
  yield no;
  uwwp;
  jiali 0;
  dohalt {
    ansuan ling tuisi at north;
    echo {checkansuan}
  }
};