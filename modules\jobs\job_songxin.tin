#NOP {送信任务模块};
#VARIABLE {sxwaitonce} {0};
#ALIAS {jobgo_songxin} {
  on_songxin_before_go {gotodo {大理城} {马房} {startfull {jobask_songxin}}};
};
#ALIAS {jobask_songxin} {
  on_songxin_before_ask {gotodo {大理城} {驿站} {jobask_songxin_ask}};
};
#ALIAS {jobask_songxin_ask} {
  #VARIABLE {jobkiller_contact} {0};
  #VARIABLE {jobkiller_timpstamp} {@now{}};
  #VARIABLE {job_killer_expectts} {0};
  #VARIABLE {job_killer_killed} {0};
  #VARIABLE {askresult} {0};
  #CLASS jobrequestclass OPEN;
  #ACTION {^你向褚万里打听有关『job』的消息。} {
    #VARIABLE {idle} {0};
    #CLASS jobresponseclass OPEN;
    #ACTION {^褚万里说道：「%*，你不是本王府随从，此话从何说起？」} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {joingf {jobask_songxin}};
    };
    #ACTION {^褚万里(<PERSON> wanli)告诉你：%!*送到「%*」的「%*」手上。} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      #VARIABLE {askresult} {0};
      #VARIABLE {joblocation} {%%%1};
      #VARIABLE {jobnpc_receiver} {%%%2};
      dohalt {
        parsejoblocation {$joblocation} {taskbegin;jobdo_songxin} {
          joblog {未能解析地址【$joblocation】。} {送信1};;
          jobfangqi_songxin
        } {2};
      };
    };
    #ACTION {^褚万里说道：「你不是已经领了送信的任务吗？还不快去做。」} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {jobfangqi_songxin};
    };
    #ACTION {^褚万里说道：「你刚做完大理送信任务，还是去休息一会吧。} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {jobprepare};
    };
    #ACTION {^褚万里说道：「{你先去休息一会吧|现在暂时没有给你的任务}} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {on_songxin_wait};
    };
    #ACTION {^褚万里说道：「现在我这里没有给你的任务，你还是先处理好你其他事情再说吧。」} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      doquit;
    };
    #CLASS jobresponseclass CLOSE;
  };
  #CLASS jobrequestclass CLOSE;
  #VARIABLE {jobstart_ts} {0};
  #VARIABLE {jobsongxin_ts} {0};
  jobtimes;
  time;
  cond;
  ask zhu wanli about job;
};
#NOP {用户是否选择的复合任务(正负神)};
#FUNCTION isComplicatedJobs {
  #IF {@contains{{conf[joblist]}{武当}} > 0 && @contains{{conf[joblist]}{雪山}} > 0} {
    #RETURN {1};
  };
  #ELSE {
    #RETURN {0};
  };
};
#NOP {送信，%1:是否二段,%2:是否重复搜索标志};
#ALIAS {jobdo_songxin} {
  #VARIABLE {jobkiller_tick} {0};
  #VARIABLE {jobnpc_killer} {};
  #VARIABLE {checkcount} {0};
  #VARIABLE {job_receiver_roomid} {0};
  #VARIABLE {jobnpc_receiver_id} {none};
  #VARIABLE {job_killer_timestamp} {0};
  #CLASS jobdoclass KILL;
  #CLASS jobdoclass OPEN;
  #ACTION {^{糟糕！有人要抢信|你觉得有些不妙}} {
    #CLASS jobdoclass KILL;
    #CLASS jobcheckclass KILL;
    #CLASS dangercheckclass KILL;
    #VARIABLE {jobkiller_contact} {1};
    #VARIABLE {jobkiller_tick} {@elapsed{$jobkiller_timpstamp}};
    #VARIABLE {workingflag} {0};
    #VARIABLE {job_killer_timestampms} {@nowms{}};
    stopwalk;
    halt;
    jobfight_songxin {%1}
  } {1};
  #LINE ONESHOT #ACTION {^{设定环境变量：action \= \"checkgo\"|你设定checkgo为反馈信息}} {
    resonate {checkgo};
    #VARIABLE {echots} {0};
    taskrecord;
    #IF {"%1" == "" || $job_killer_killed == 1} {
      jobnextroom {checkreceiver {%1} {%2}} {jobfail_songxin {%1} {%2}};
    };
    #ELSE {
      #NOP {二段送信杀手来的很快，为了减少不必要的意外情况，直接在当地城市房间等待};
      #NOP {在神龙岛上的情况需要单独处理下，如果目的地不是神龙岛则先出去，否则原地等};
      #IF {"$city" == "神龙岛" && "$jobcity" != "神龙岛"} {
        gotodo {$city} {$cities[$city][cityroom]} {jobcheck_songxin {%1}}
      };
      #ELSE {
        jobcheck_songxin {%1}
      };
    };
  };
  #CLASS jobdoclass CLOSE;
  #IF {"%1" == ""} {
    #VARIABLE {jobsongxin_ts} {@now{}};
  };
  #VARIABLE {jobstart_ts} {@now{}};
  #NOP {如果是复合任务，那么在送信等待前补充正气丹};
  #IF {"%1" =="" && @isReceiveSongxin{$joblocation} == 0} {
    #CLASS jobdoclass KILL;
    jobfangqi_songxin {%1};
  };
  #ELSEIF {@isComplicatedJobs{} == 1 && @carryqty{zhengqi dan} < 4} {
    #CLASS jobdoclass KILL;
    buymedicine_dl {zhengqi dan} {10} {jobdo_songxin}
  };
  #ELSE {
    joblog {寻找位于【$joblocation】的收信人【$jobnpc_receiver】。} {送信@iif{{"%1"==""}{1}{2}}};
    time;
    wwp;
    pfm_buff_normal;
    #IF {"%1" == ""} {
      createpfm {$conf[pfm][scene][sx1]} {1};
    };
    #ELSE {
      createpfm {$conf[pfm][scene][sx2]} {1};
    };
    echo {checkgo} {2}
  };
};
#NOP {送信搜索失败,%1:是否二段,%2:重复搜索标志};
#ALIAS {jobfail_songxin} {
  #LOCAL {iter} {@eval{@eval{%2} + 1}};
  #LOCAL {wanderroom} {$common[wanderwheres][$jobcity$jobroom]};
  #IF {$iter == 1} {
    joblog {未能找到位于【$joblocation】的收信人【$jobnpc_receiver】，扩大范围搜索。} {送信@iif{{"%1"==""}{1}{2}}};
    parsejoblocation {$joblocation} {jobdo_songxin {%1} {$iter}} {jobfail_songxin {%1} {$iter}} {5};
  };
  #ELSEIF {$iter == 2 && "$wanderroom" != ""} {
    joblog {还是未能找到位于【$joblocation】的收信人【$jobnpc_receiver】，开始漫游搜索。};
    loc {gotoroom {$wanderroom[roomid]} {jobwander_songxin {%1} {$wanderroom[roomname]} {$wanderroom[range]}}}
  };
  #ELSE {
    joblog {终究未能找到位于【$joblocation】的收信人【$jobnpc_receiver】。};
    jobfangqi_songxin {%1};
  };
};
#NOP {%1:是否二段,%2:房间名称,%3:步数};
#ALIAS {jobwander_songxin} {
  job_wander {checkreceiver {%1} {jobwander_songxin {%1} {%2} {@eval{%3 - 1}}}} {jobfail_songxin {%1} {3}} {%2} {%3}
};
#NOP {查看收信人,%1:是否二段,%2:重复搜索标志,%3:额外动作};
#ALIAS {checkreceiver} {
  #VARIABLE {okflag} {0};
  #VARIABLE {checkcount} {0};
  #VARIABLE {jobnpc_receiver_id} {};
  #CLASS jobcheckclass OPEN;
  #ACTION {$jobnpc_receiver(%*)} {
    #VARIABLE {jobnpc_receiver_id} {@lower{%%1}};
  };
  #ACTION {^{设定环境变量：action \= \"checkreceiver\"|你设定checkreceiver为反馈信息}} {
    #CLASS jobcheckclass KILL;
    resonate {checkreceiver};
    #VARIABLE {echots} {0};
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #NOP {啥也不做};
      #CLASS jobcheckclass KILL;
    };
    #ELSEIF {"$jobnpc_receiver_id" == ""} {
      #IF {"%3" != ""} {
        %3
      };
      #ELSE {
        runwait {jobnextroom {checkreceiver {%1} {%2}} {jobfail_songxin {%1} {%2}}};
      };
    };
    #ELSE {
      #VARIABLE {job_receiver_roomid} {$roomid};
      checkdanger {
        dohalt {
          #IF {$sxwaitonce == 1 || $job_killer_killed == 1 || ($conf[waitkiller] == 0 && "%1" == "")} {
            #VARIABLE {sxwaitonce} {0};
            jobfinish_songxin {%1};
          };
          #ELSE {
            jobcheck_songxin {%1};
          };
        };
      } {$job_killer_killed};
    };
  };
  #CLASS jobcheckclass CLOSE;
  ensure {look} {checkreceiver}
};
#NOP {检查信件，%1:是否二段};
#ALIAS {jobcheck_songxin} {
  #VARIABLE {checkts} {@now{}};
  #VARIABLE {lastjingli} {0};
  #CLASS jobcheckclass KILL;
  #CLASS jobcheckclass OPEN;
  #ACTION {^这里不准战斗，也不准打坐。} {
    #CLASS jobcheckclass KILL;
    jobfinish_songxin {%1};
  };
  #ACTION {^{$dazuo_over}} {
    #IF {$jobkiller_contact == 0} {
      i;
      echo {checksongxin};
    };
  };
  #ACTION {^你吐纳完毕，睁开双眼，站了起来} {
    #IF {$jobkiller_contact == 0} {
      i;
      echo {checksongxin};
    };
  };
  #ACTION {^{设定环境变量：action \= \"checksongxin\"|你设定checksongxin为反馈信息}} {
    resonate {checksongxin};
    #VARIABLE {lastjingli} {$hp[jingli]};
    #VARIABLE {echots} {0};
    #VARIABLE {idle} {0};
    #NOP {最大内力超过6000，等待时一直打坐，且不在积蓄};
    pfm_buff_normal;
    #IF {$hp[neili_max] >= 6000} {
      closesaving;
    };
    #IF {"$id[things][letter]" == "" && 1 == 2} {
      #CLASS jobdoclass KILL;
      #CLASS jobcheckclass KILL;
      jobfangqi_songxin {%1}
    };
    #ELSEIF {@elapsed{$checkts} >= 120} {
      #CLASS jobdoclass KILL;
      #CLASS jobcheckclass KILL;
      jobfinish_songxin {%1}
    };
    #ELSE {
      #IF {"%1" == ""} {
        #IF {"@getLianSkill{2}" != ""} {
          waitlian;
        };
        dzn {echo {checksongxin}} {2}
      };
      #ELSEIF {@now{} < $job_killer_expectts} {
        #NOP {对于送信2打坐要保证安全，由于sx2杀手至少会等一个tick后才可能刷新，一个tick至少15秒};
        #NOP {我们从接收sx2任务开始算，尝试打坐至15秒后中止，此后根据精力的变化判定是否进行了心跳刷新};
        #NOP {第一个心跳后每次心跳都有可能刷新杀手，所以在心跳刚刷新时可以打坐，但是不要超过15秒时间};
        #NOP {过了打坐时间应一直检测直到心跳(须有超时机制防止误判)心跳或杀手到来。};
        #SHOWME {<faa>可打坐@eval{$job_killer_expectts - @now{}}秒};
        jobsx2_consumejingli;
        dzn {echo {checksongxin} {2}} {2} {{timestamp}{$job_killer_expectts}}
      };
      #ELSE {
        #SHOWME {<faa>开始心跳检测};
        yun qi;
        pfm_buff_normal;
        echo {checkheartbeat} {2}
      };
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkheartbeat\"|你设定checkheartbeat为反馈信息}} {
    resonate {checkheartbeat};
    #VARIABLE {idle} {0};
    #IF {@elapsed{$job_killer_expectts} >= 90} {
      #NOP {时间太长还不来放弃了};
      #CLASS jobdoclass KILL;
      #CLASS jobcheckclass KILL;
      #SHOWME {<faa>超时跑路};
      jobfangqi_songxin {%1}
    };
    #ELSE {
      #SHOWME {<faa>未心跳继续等待};
      pfm_buff_normal;
      #DELAY {1} {jobsx2_heartbeat}
    };
  };
  #CLASS jobcheckclass CLOSE;
  #MATH {checkcount} {$checkcount + 1};
  #IF {$checkcount >= 4} {
    jobclear;
    jobfangqi
  };
  #ELSE {
    ensure {
      wwp;
      pfm_buff_normal
    } {checksongxin}
  };
};
#NOP {消耗一下精力};
#ALIAS {jobsx2_consumejingli} {
  yun jingli;
  #IF {"$id[weapon]" != ""} {
    lian @getWeaponSkill{$id[weapon]} 10;
  };
  #ELSE {
    lian *kungfu[bei][+1] 10;
  };
  hp
};
#NOP {打坐时计时的回调，用于更新是否心跳过};
#ALIAS {jobsx2_heartbeat} {
  #SHOWME {<ffa>$lastjingli != $hp[jingli]};
  #IF {$lastjingli != $hp[jingli] || $lastjingli == $hp[jingli_max]} {
    #SHOWME {<faa>已心跳，更新预期时间};
    #VARIABLE {job_killer_expectts} {@eval{@now{} + 15}};
    #VARIABLE {checkcount} {0};
    echo {checksongxin} {2}
  };
  #ELSE {
    hp;
    echo {checkheartbeat} {2}
  };
};
#NOP {打坐完成后的预期的操作，打坐倒15秒的阈值重置变量并通过其他触发器等待心跳刷新};
#ALIAS {jobsx2_expectdo} {
  #SHOWME {<faa>jobsx2_expectdo};
  jobsx2_heartbeat
};
#NOP {送信杀杀手};
#NOP {对于初期的化学pfm太猛,可能第二个杀手还没上来就弄死了第一个杀手,这里根据经验进行判定一下,要稍微等一下第二个杀手};
#ALIAS {jobfight_songxin} {
  #VARIABLE {tempdo} {
    get letter from corpse 1;
    get silver from corpse 1;
    get gold from corpse 1;
    l letter;
    set action checkletter
  };
  #VARIABLE {pfmtimestamp} {0};
  #VARIABLE {jobnpc_killer} {};
  #VARIABLE {jobnpc_killer_count} {1};
  #IF {$hp[exp] > 300000} {
    #VARIABLE {jobnpc_killer_count} {2};
  };
  #VARIABLE {jobnpc_killer_arrived} {0};
  #VARIABLE {jobnpc_killer_party} {};
  #VARIABLE {jobnpc_killer_skill} {};
  #VARIABLE {letterflag} {0};
  #VARIABLE {checkcount} {0};
  #VARIABLE {corpseidnex} {1};
  #CLASS jobkiller KILL;
  #CLASS jobkiller OPEN;
  #ACTION {^你隐约感觉到有人围了过来} {
    #CLASS jobkillercap1 OPEN;
    #ACTION {^%*说道：「%*乖乖把密函交出来吧} {
      #CLASS jobkillercap1 KILL;
      #MATH {jobnpc_killer_arrived} {$jobnpc_killer_arrived + 1};
      #VARIABLE {jobnpc_killer[%%%1]} {NULL};
      ensure {look} {checkkiller}
    };
    #ACTION {^你定睛一看，原来是%*，而且此人武功%*，似乎用的是%*的%*！} {
      #CLASS jobkillercap1 KILL;
      #VARIABLE {jobfight_target} {%%%1};
      #VARIABLE {jobnpc_killer_count} {1};
      #MATH {jobnpc_killer_arrived} {$jobnpc_killer_arrived + 1};
      #VARIABLE {jobnpc_killer_party} {%%%3};
      #VARIABLE {jobnpc_killer_skill} {%%%4};
      #VARIABLE {jobnpc_killer[%%%1]} {NULL};
      settarget {$jobfight_target};
      #IF {@contains{{conf[fangqiparty]}{$jobnpc_killer_party}} > 0 || @contains{{conf[fangqiskill]}{$jobnpc_killer_skill}} > 0} {
        #CLASS jobkiller KILL;
        #CLASS jobfightclass KILL;
        runawayside {
          loc {jobfangqi_songxin {1}}
        } {3};
      };
      #ELSE {
        ensure {look} {checkkiller}
      };
    };
    #CLASS jobkillercap1 CLOSE;
  };
  #ACTION {^糟糕，又冲上来了个人} {
    #CLASS jobkillercap2 OPEN;
    #ACTION {^%*说道：「{师兄|想跑}} {
      #CLASS jobkillercap2 KILL;
      #MATH {jobnpc_killer_arrived} {$jobnpc_killer_arrived + 1};
      #VARIABLE {jobnpc_killer[%%%1]} {NULL};
    };
    #CLASS jobkillercap2 CLOSE;
  };
  #ACTION {{地煞|天杀}门%!*杀手 %*(%*)} {
    #IF {"$jobnpc_killer[%%2]" != ""} {
      #VARIABLE {jobnpc_killer[%%2]} {@lower{%%3}};
      kill @lower{%%3}
    };
  };
  #CLASS jobkiller CLOSE;
  #CLASS jobfightclass KILL;
  #CLASS jobfightclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkkiller\"|你设定checkkiller为反馈信息}} {
    resonate {checkkiller};
    #VARIABLE {echots} {0};
    #VARIABLE {idle} {0};
    #MATH {checkcount} {$checkcount + 1};
    #LOCAL {targetkiller} {};
    #IF {$__DEBUG__ == 1} {
      #SHOWME {<ffa>杀手列表:$jobnpc_killer};
    };
    #FOREACH {*jobnpc_killer[]} {sxk} {
      #IF {"$jobnpc_killer[$sxk]" != "NULL"} {
        #LOCAL {targetkiller} {$sxk};
        #BREAK;
      };
    };
    #IF {"$targetkiller" != ""} {
      #IF {"$jobnpc_killer[$targetkiller]" != "NULL"} {
        #NOP {创建pfm并叫杀};
        #IF {"%1" != ""} {
          joblog {开始与使用【$jobnpc_killer_skill】描述为【$jobnpc_killer_desc】的杀手对决。};
          createpfm {@getFightPerform{{$jobnpc_killer_skill}{送信2}}} {1} {$jobnpc_killer[$targetkiller]};
        };
        #ELSE {
          createpfm {@getFightPerform{{}{送信}}} {1} {$jobnpc_killer[$targetkiller]};
        };
        kill $jobnpc_killer[$targetkiller];
        #IF {"%1" == ""} {
          autopfm
        };
        #ELSE {
          #VARIABLE {pfmtimestamp} {@now{}};
          #TICKER {sx2pfm} {
            #IF {@elapsed{$pfmtimestamp} >= 2} {
              #UNTICKER {sx2pfm};
            };
            autopfm
          } {0.3};
        };
      };
      #ELSE {
        #NOP {再look一下};
        look;
        echo {checkkiller}
      };
    };
    #ELSEIF {"%1" != "" || $jobnpc_count >= 2 || $checkcount >= 3} {
      #CLASS jobkiller KILL;
      #NOP {送信2或者杀了俩};
      #IF {"%1" != ""} {
        joblog {顺利杀死使用【$jobnpc_killer_skill】描述为【$jobnpc_killer_desc】的杀手，耗时@elapsed{$jobfight_ts}秒，杀手在$jobkiller_tick秒后出现。} {送信2};;
      };
      #ELSE {
        joblog {顺利杀死$jobnpc_count个杀手，耗时@elapsed{$jobfight_ts}秒，杀手在$jobkiller_tick秒后出现。} {送信1};;
      };
      #VARIABLE {checkcount} {0};
      dohalt {
        stopfight;
        execute {
          get letter from corpse $corpseidnex;
          get silver from corpse $corpseidnex;
          get gold from corpse $corpseidnex;
          get letter from corpse @eval{$corpseidnex + 1};
          get silver from corpse @eval{$corpseidnex + 1};
          get gold from corpse @eval{$corpseidnex + 1};
          get zhuanji;
          cond;
          l letter;
        };
        echo {checkletter} {2};
      };
    };
    #ELSE {
      #DELAY {1} {look;cond;echo {checkkiller}}
    };
  };
  #ACTION {^%*神志迷糊，脚下一个不稳，倒在地上昏了过去} {
    #IF {"$jobnpc_killer[%%1]" != ""} {
      #IF {"%1" != ""} {
        takedown;
      };
      #IF {"$jobnpc_killer[%%1]" != "NULL"} {
        kill $jobnpc_killer[%%1]
      };
    };
  };
  #ACTION {^%*「啪」的一声倒在地上，挣扎着抽动了几下就死了} {
    #IF {"$jobnpc_killer[%%1]" != ""} {
      #UNVARIABLE {jobnpc_killer[%%1]};
      #IF {"%1" != ""} {
        takedown;
      };
      #MATH {jobnpc_count} {$jobnpc_count + 1};
      ensure {look} {checkkiller}
    };
  };
  #ACTION {^这是一封由大理国镇南王发出的信件，上面写着} {
    #VARIABLE {letterflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkletter\"|你设定checkletter为反馈信息}} {
    resonate {checkletter};
    #VARIABLE {echots} {0};
    #VARIABLE {idle} {0};
    #IF {$letterflag == 1} {
      #VARIABLE {job_killer_killed} {1};
      #VARIABLE {jobkiller_contact} {0};
      #CLASS jobkiller KILL;
      #CLASS jobfightclass KILL;
      #SHOWME {<faa>job_receiver_roomid = $job_receiver_roomid,p1 = %1};
      #NOP {如果有怪蛇毒或者星宿毒需要及时处理，其他的先去完成任务};
      #IF {"$hp[condition][怪蛇]" != "" || "$hp[condition][星宿掌]" != ""} {
        doqudu {jobtakedown_songxin {%1}}
      };
      #ELSE {
        jobtakedown_songxin {%1}
      };
    };
    #ELSE {
      #DELAY {1} {
        #MATH {corpseidnex} {$corpseidnex + 1};
        #IF {$corpseidnex > 3} {
          #CLASS jobkiller KILL;
          #CLASS jobfightclass KILL;
          joblog {信件丢失。};
          loc {jobfangqi};
        };
        #ELSE {
          execute {
            get letter from corpse $corpseidnex;
            get silver from corpse $corpseidnex;
            get gold from corpse $corpseidnex;
            get letter from corpse @eval{$corpseidnex + 1};
            get silver from corpse @eval{$corpseidnex + 1};
            get gold from corpse @eval{$corpseidnex + 1};
            l letter
          };
          echo {checkletter} {2};
        };
      }
    };
  };
  #CLASS jobfightclass CLOSE;
  #VARIABLE {jobfight_ts} {@now{}};
  #VARIABLE {jobnpc_count} {0};
  startfight
};
#NOP {送信处理完杀手后的动作,%1-二段标识};
#ALIAS {jobtakedown_songxin} {
  #IF {$job_receiver_roomid == 0} {
    #IF {"%1" != ""} {
      loc {jobnextroom {checkreceiver {%1} {0}} {jobfail_songxin {%1} {0}}}
    };
    #ELSE {
      loc {gotoroom {$nextroom} {checkreceiver {%1}}}
    };
  };
  #ELSE {
    loc {jobfinish_songxin {%1}}
  };
};
#NOP {是否继续做送信一段};
#FUNCTION isReceiveSongxin {
  #IF {@startWiths{{$joblocation}{桃源县}} > 0 && "$city" != "桃源县"} {
    #RETURN {1};
  };
  #IF {@startWiths{{$joblocation}{神龙岛}} > 0 && "$city" != "神龙岛"} {
    #RETURN {1};
  };
  #IF {@startWiths{{$joblocation}{燕子坞}} > 0 && "$hp[party]" != "姑苏慕容" && "$city" != "燕子坞"} {
    #RETURN {1};
  };
  #IF {@startWiths{{$joblocation}{曼佗罗山庄}} > 0 && "$hp[party]" != "姑苏慕容" && "$city" != "曼佗罗山庄"} {
    #RETURN {0};
  };
  #RETURN {1};
};
#NOP {是否继续做送信二段，%1:目的地};
#FUNCTION isReceiveSongxin2 {
  #IF {"$currentjob" != "送信2"} {
    #RETURN {0};
  };
  #NOP {如果有请求则不继续};
  #IF {"$caller" != ""} {
    #RETURN {0};
  };
  #NOP {如果做官府任务那么优先做官府};
  #IF {"$hp[condition][双倍经验]" == "" && @contains{{conf[extendjob]}{官府}} > 0 && @elapsed{$env[wanted]} < 120} {
    #RETURN {0};
  };
  #IF {@startWiths{{%1}{桃源县}} > 0 && "$city" != "桃源县"} {
    #RETURN {0};
  };
  #IF {@startWiths{{%1}{神龙岛}} > 0 && "$city" != "神龙岛"} {
    #RETURN {0};
  };
  #IF {@startWiths{{%1}{燕子坞}} > 0 && "$hp[party]" != "姑苏慕容" && "$city" != "燕子坞"} {
    #RETURN {0};
  };
  #IF {@startWiths{{%1}{曼佗罗山庄}} > 0 && "$hp[party]" != "姑苏慕容" && "$city" != "曼佗罗山庄"} {
    #RETURN {0};
  };
  #NOP {判定独孤九剑解谜，如果下次华山完成可以整除50，则不进行二段送信};
  #IF {@isQuestActivate{独孤九剑} == 1} {
    #LOCAL {totaltimes} {@eval{$hp[jobtimes][华山]} + @eval{$hp[jobtimes][送信]} + @eval{$hp[jobtimes][丐帮]}};
    #IF {($totaltimes % 50) == 49} {
      #RETURN {0};
    };
  };
  #RETURN {1};
};
#NOP {完成送信，%1:是否二段};
#ALIAS {jobfinish_songxin} {
  #VARIABLE {jobdone} {0};
  #VARIABLE {tempreceiver} {};
  #VARIABLE {jobreward_exp} {};
  #VARIABLE {jobreward_pot} {};
  #VARIABLE {jobreward_tongbao} {零};
  #VARIABLE {jobflag_songxin2} {0};
  #VARIABLE {jobnpc_killer_desc} {};
  #CLASS jobfinishclass OPEN;
  #ACTION {^恭喜你！你成功的完成了送信任务！你被奖励了：} {
    #VARIABLE {jobdone} {1};
    #CLASS jobdoclass KILL;
    #CLASS rewardclass OPEN;
    #VARIABLE {lastjob} {送信};
    #ACTION {^%*点经验!} {
      #VARIABLE {jobreward_exp} {%%%1};
    };
    #ACTION {^%*点潜能!} {
      #VARIABLE {jobreward_pot} {%%%1};
    };
    #ACTION {^%*个通宝} {
      #VARIABLE {jobreward_tongbao} {%%%1};
      #SHOWME {<faa>获得通宝 $jobreward_tongbao};
    };
    #ACTION {^$jobnpc_receiver在你的耳边悄声说道：你赶紧把它送到「%*」的「%*」手上。} {
      #VARIABLE {joblocation} {%%%1};
      #VARIABLE {tempreceiver} {%%%2};
    };
    #ACTION {^$jobnpc_receiver在你的耳边悄声说道：可能有个武功和你相比%*的家伙要来抢你的信} {
      #VARIABLE {jobnpc_receiver} {$tempreceiver};
      #VARIABLE {jobnpc_killer_desc} {%%%1};
    };
    #ACTION {^你可以选择同意或者放弃不做(no)。} {
      #VARIABLE {jobflag_songxin2} {1};
      #VARIABLE {job_killer_expectts} {@eval{15 + @now{}}};
    };
    #CLASS rewardclass CLOSE;
  };
  #ACTION {^{设定环境变量：action \= \"checkdone\"|你设定checkdone为反馈信息}} {
    resonate {checkdone};
    #VARIABLE {echots} {0};
    #CLASS rewardclass KILL;
    #CLASS jobfinishclass KILL;
    #DELAY {2} {
      #IF {$jobdone == 1} {
        #VARIABLE {jobkiller_contact} {0};
        #VARIABLE {jobkiller_timpstamp} {@now{}};
        #VARIABLE {job_killer_killed} {0};
        
        #IF {"%1" == ""} {
          joblog {成功完成，获得$jobreward_exp点经验$jobreward_pot点潜能，耗时@elapsed{$jobstart_ts}秒。} {送信1};
          taskgain {@ctd{$jobreward_exp}} {0};
          taskstats {送信1};
          #IF {@isReceiveSongxin2{$joblocation} == 0} {
            joblog {决定不接受位于【$joblocation】存在【$jobnpc_killer_desc】描述劫匪的送信任务。} {送信2};
            joblog {全部完成，一共耗时@elapsed{$jobsongxin_ts}秒。} {送信2};
            taskend;
            jobclear_songxin;
            no;
            jobprepare
          };
          #ELSE {
            parsejoblocation {$joblocation} {jobdo_songxin {1}} {
              joblog {未能解析地址【$joblocation】。} {送信2};;
              jobclear_songxin;
              no;
              jobprepare
            } {2};
          };
        };
        #ELSE {
          joblog {成功完成，获得$jobreward_exp点经验$jobreward_pot点潜能，【$jobreward_tongbao】个通宝，耗时@elapsed{$jobstart_ts}秒。} {送信2};
          taskgain {@ctd{$jobreward_exp}} {@ctd{$jobreward_tongbao}};
          joblog {全部完成，一共耗时@elapsed{$jobsongxin_ts}秒。} {送信2};
          taskend;
          taskstats {送信2};
          jobclear_songxin;
          dohalt {
            on_songxin_finish {jobprepare}
          }
        };
      };
      #ELSE {
        jobfangqi_songxin {%1}
      };
    };
  };
  #CLASS jobfinishclass CLOSE;
  ensure {
    songxin $jobnpc_receiver_id;
    time;
    jobtimes;
  } {checkdone}
};
#NOP {放弃送信，%1:标识，为空为二段放弃};
#ALIAS {jobfangqi_songxin} {
  openwimpy;
  gotodo {大理城} {驿站} {checkrequest {jobfangqi_songxin_ask {%1}}};
};
#NOP {放弃送信};
#ALIAS {jobfangqi_songxin_ask} {
  #CLASS jobdoclass KILL;
  #CLASS jobrequestclass KILL;
  #NOP {询问结果,0:成功,1:busy};
  #VARIABLE {askresult} {0};
  #CLASS jobrequestclass OPEN;
  #ACTION {^你向褚万里打听有关『fangqi』的消息。} {
    #CLASS jobrequestclass KILL;
    jobclear_songxin;
    #IF {"%1" == "" || 1 == 1} {
      dohalt {jobprepare};
    };
  };
  #CLASS jobrequestclass CLOSE;
  ask zhu wanli about fangqi;
  joblog {未能完成任务，耗时@elapsed{$jobstart_ts}秒。}
};
#ALIAS {jobclear_songxin} {
  #VARIABLE {jobnpc_receiver} {};
  #VARIABLE {jobnpc_receiver_id} {};
  #VARIABLE {job_receiver_roomid} {0};
  #VARIABLE {jobnpc_killer_desc} {};
  #VARIABLE {jobflag_songxin2} {0};
  #VARIABLE {joblocation} {};
  #LIST {jobroomlist} {clear};
  #VARIABLE {jobfight_ts} {0};
  #VARIABLE {jobnpc_count} {0};
  #VARIABLE {jobstart_ts} {0};
};