#NOP {九阳神功，%1:后续指令};
#ALIAS {goquest_jysg} {
  #VARIABLE {questmodule} {九阳神功};
  #IF {@getSkillLevel{medicine} < 122} {
    fullmedicine {goquest_jysg {%1}} {122}
  };
  #ELSE {
    gotonpc {张无忌} {jysg_askzhang {%1}}
  };
};
#ALIAS {jysg_askzhang} {
  #VARIABLE {questmodule} {九阳神功};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你向张无忌问道：这位壮士，不知最近有没有听说什么消息？} {
    #CLASS questresponseclass KILL;
    #CLASS questresponseclass OPEN;
    #ACTION {^张无忌说道：「最近明教繁荣昌盛，也未听闻有什么别的消息} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      #NOP {没开，继续任务吧};
      unset env_jysg;
      #VARIABLE {env[jysg]} {};
      dohalt {jobprepare};
    };
    #ACTION {^张无忌说道：「{想当年太师傅在少林曾听闻昆仑派“昆仑三圣”何足道带话到少林说“经在油中”|你知道“经在油中”此话的含义了么}} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      #NOP {解谜去};
      #NOP {准备点蟒蛇胆};
      dohalt {reducefood {gotodo {昆仑山} {洞天福地} {jysg_prepare {%1}}}}
    };
    #CLASS questresponseclass CLOSE;
  };
  #CLASS questclass CLOSE;
  ask zhang wuji about 传闻;
};
#NOP {准备材料};
#ALIAS {jysg_prepare} {
  #VARIABLE {questmodule} {九阳神功};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkrock\"|你设定checkrock为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {@carryqty{jian shi} == 0} {
      #DELAY {2} {
        shuai rock;
        get sharp rock;
        i;
        echo {checkrock};
      };
    };
    #ELSE {
      #DELAY {1} {
        #3 n;
        i;
        echo {checkzhitiao};
      };
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkzhitiao\"|你设定checkzhitiao为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {@carryqty{zhi tiao} < 2} {
      #DELAY {2} {
        dohalt {
          zhe zhi;
          i;
          echo {checkzhitiao}
        };
      };
    };
    #ELSE {
      i;
      echo {checkxisi}
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkxisi\"|你设定checkxisi为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {@carryqty{xi si} < 2} {
      #DELAY {2} {
        dohalt {
          si bark;
          i;
          echo {checkxisi}
        };
      };
    };
    #ELSE {
      #DELAY {1} {
        dohalt {
          #3 s;
          eu;
          ed;
          echo {checkyaocao};
        };
      };
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkyaocao\"|你设定checkyaocao为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {@carryqty{yao cao} == 0} {
      #DELAY {2} {
        dohalt {
          get yao cao;
          i;
          echo {checkyaocao};
        };
      };
    };
    #ELSE {
      #CLASS questclass KILL;
      wu;
      jysg_heal {%1}
    };
  };
  #CLASS questclass CLOSE;
  i;
  echo {checkrock};
};
#NOP {治疗猴子和大白猿，在这里等猴子出事，出事后再吃蛇胆吃鱼骨};
#ALIAS {jysg_heal} {
  #VARIABLE {xiaohouflag} {0};
  #VARIABLE {baiyuanflag} {0};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^一个小猴忽然从陡峭的山壁上失足掉了下来，后腿被一块石头压住了，动弹不得} {
    #VARIABLE {xiaohouflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkxiaohou\"|你设定checkxiaohou为反馈信息}} {
    #VARIABLE {idle} {0};
    i;
    hp;
    #IF {$xiaohouflag == 0} {
      #DELAY {2} {
        echo {checkxiaohou}
      };
    };
    #ELSE {
      #NOP {吃蟒蛇胆，吃鱼};
      echo {checkfood};
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkfood\"|你设定checkfood为反馈信息}} {
    #VARIABLE {idle} {0};
    #DELAY {1} {
      #IF {$hp[food] > 50} {
        #CLASS questclass KILL;
        reducefood {gotodo {昆仑山} {洞天福地} {jysg_prepare {%1}}}
      };
      #ELSE {
        wd;#3 w;
        echo {checkbone};
      };
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkbone\"|你设定checkbone为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {@carryqty{yu gu} == 0} {
      #DELAY {2} {
        #IF {@carryqty{da baiyu} == 0} {
          zhua yu;
        };
        #6 eat fish;
        i;
        hp;
        echo {checkbone};
      };
    };
    #ELSE {
      #3 e;eu;
      move rock;
      heal xiao hou;
    };
  };
  #ACTION {^你用两根枝条作为夹板，替小猴子续上断骨，把草药嚼烂了给它敷在伤处。} {
    dohalt {
      wd;
      eu;
      echo {checkbaiyuan}
    };
  };
  #ACTION {^那只你救过的小猴忽然跑了过来，吱吱喳喳，叫个不停} {
    #VARIABLE {baiyuanflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkbaiyuan\"|你设定checkbaiyuan为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {$baiyuanflag == 0} {
      #DELAY {2} {
        echo {checkbaiyuan}
      };
    };
    #ELSE {
      dohalt {
        heal da baiyuan;
      };
    };
  };
  #ACTION {^你用鱼骨做针，树皮撮成的细丝作线，补好了伤口，最后把草药嚼烂了给它敷在伤处} {
    #CLASS questclass KILL;
    #SHOWME {<faa>失败了};
  };
  #ACTION {^你用尖石慢慢割开白猿肚腹上缝补过之处，只见里面竟藏着一个油布包裹，打开一看，是一套经书} {
    #CLASS questclass KILL;
    questsuccess {$questmodule};
    i;
    dohalt {
      drop yao cao;
      drop yu gu;
      drop jian shi;
      #2 drop xian guo;
      eat pan tao;
      loc {gotoroom {3748} {fulljiuyang_read {%1}}};
    };
  };
  #CLASS questclass CLOSE;
  echo {checkxiaohou}
};
#ALIAS {jysg_reducefood} {
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkjumang\"|你设定checkjumang为反馈信息}} {
  };
  #CLASS questclass CLOSE;
  doheal {
    startfull {echo {checkjumang}} {3}
  }
};