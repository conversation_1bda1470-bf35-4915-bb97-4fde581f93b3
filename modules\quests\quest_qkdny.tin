#NOP {九阳神功，%1:后续指令};
#ALIAS {goquest_qkdny} {
  #VARIABLE {questmodule} {乾坤大挪移};
  gotodo {灵蛇岛} {山峰} {qkdny_getling {%1}}
};
#ALIAS {qkdny_getling} {
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^{妙风使|辉月使|流云使}「啪」的一声倒在地上，挣扎着抽动了几下就死了} {
    kill shi;
  };
  #ACTION {^这里没有这个人} {
    stopfight;
    get sheng<PERSON><PERSON> ling from corpse 1;
    get sheng<PERSON><PERSON> ling from corpse 2;
    get sheng<PERSON><PERSON> ling from corpse 3;
    i;
    echo {checkling};
  };
  #ACTION {^{设定环境变量：action \= \"checkling\"|你设定checkling为反馈信息}} {
    #IF {@carryqty{shenghuo ling} == 0} {
      #DELAY {6} {
        startfight;
        kill shi;
      };
    };
    #ELSE {
      #CLASS questclass KILL;
      loc {
        gotonpc {张无忌} {qkdny_giveling {%1}};
      }
    };
  };
  #CLASS questclass CLOSE;
  startfight;
  kill shi;
};
#ALIAS {qkdny_giveling} {
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^张无忌说道：「上代阳教主有遗命，寻回圣物者当可修习本教神功，你去秘道中试试吧} {
    #CLASS questclass KILL;
    dohalt {
      gotodo {明教} {藏金室} {qkdny_dafu {%1}};
    };
  };
  #CLASS questclass CLOSE;
  give shenghuo ling to zhang wuji
};
#ALIAS {qkdny_dafu} {
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkdafu\"|你设定checkdafu为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {@carryqty{dafu} == 0} {
      #DELAY {2} {
        get dafu;
        echo {checkdafu}
      };
    };
    #ELSE {
      gotodo {明教} {3852} {qkdny_mishi {%1}}
    };
  };
  #CLASS questclass CLOSE;
  get dafu;
  echo {checkdafu}
};
#ALIAS {qkdny_mishi} {
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkpi\"|你设定checkpi为反馈信息}} {
    #IF {@carryqty{yang pi} == 0} {
      #DELAY {2} {
        get all from haigu;
        get all from haigu 2;
        i;
        echo {checkpi};
      }
    };
    #ELSE {
      questsuccess {乾坤大挪移};
      drop duanjian;
      hp;
      executecmd {echo {checkhp}}
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkhp\"|你设定checkhp为反馈信息}} {
    #DELAY {0.5} {
      #IF {@getSkillLevel{qiankun-danuoyi} >= 60} {
        #CLASS questclass KILL;
        river_mj_midao_out {goreadbook {pi} {golearn {%1}}}
      };
      #ELSEIF {$hp[neili] < 500} {
        #CLASS questclass KILL;
        startfull {qkdny_mishi {%1}} {3}
      };
      #ELSE {
        yun jing;
        hp;
        #10 read pi;
        echo {checkhp};
      };
    };
  };
  #CLASS questclass CLOSE;
  echo {checkpi};
};