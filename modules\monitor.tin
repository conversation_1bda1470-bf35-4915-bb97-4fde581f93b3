/*
 * @Author: maguangquan <EMAIL>
 * @Date: 2023-12-21 11:42:44
 * @LastEditors: maguangquan <EMAIL>
 * @LastEditTime: 2024-02-29 17:00:52
 * @FilePath: \sjgame\zitan\shujian\modules\monitor.tin
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#NOP {状态模块};
#CLASS monitormodule KILL;
#CLASS monitormodule OPEN;
#VARIABLE {wdplayers} {};
#ACTION {^%*向宋远桥打听有关『{job|任务}』的消息} {
	#VARIABLE {wdplayers[%1]} {
		{ask} {@now{}}
	};
};
#ACTION {^%*向宋远桥打听有关『{fangqi|放弃}』的消息} {
	#IF {"$wdplayers[%1]" != ""} {
		#VARIABLE {wdplayers[%1][fangqi]} {@now{}};
	};
};
#ACTION {^%*向宋远桥打听有关『{finish|完成}』的消息} {
	#IF {"$wdplayers[%1][ask]" != "" && "$wdplayers[%1][fangqi]" != ""} {
		#LOCAL {info} {玩家【%1】接受任务并在【@eval{$wdplayers[%1][fangqi] - $wdplayers[%1][ask]}】秒内放弃，【@elapsed{$wdplayers[%1][ask]}】秒后完成。};
		#SHOWME {<faa>$info};
		joblog {$info} {武当监控}
	};
};
#VARIABLE {xsplayers} {};
#ACTION {^%*向宝象打听有关『{job|任务}』的消息} {
	#VARIABLE {xsplayers[%1]} {
		{ask} {@now{}}
	};
};
#ACTION {^%*向宝象打听有关『{fangqi|放弃}』的消息} {
	#IF {"$xsplayers[%1][ask]" != ""} {
		#LOCAL {info} {玩家【%1】接受任务并在【@elapsed{$xsplayers[%1][ask]}】秒内放弃。};
		#SHOWME {<faa>$info};
		joblog {$info} {雪山监控}
	};
};
#ACTION {^%*向宝象打听有关『{finish|完成}』的消息} {
	#IF {"$xsplayers[%1][ask]" != ""} {
		#LOCAL {info} {玩家【%1】接受任务并在【@elapsed{$xsplayers[%1][ask]}】秒内完成。};
		#SHOWME {<faa>$info};
		joblog {$info} {雪山监控}
	};
};
#CLASS monitormodule CLOSE;
#SHOWME {<fac>@padRight{{监控}{12}}<fac> <cfa>模块加载完毕<cfa>};