#NOP {武馆任务模块};
#ALIAS {jobwuguan_start} {
  #VARIABLE {checkcount} {0};
  #CLASS wuguanclass OPEN;
  #ACTION {^狄云对着你点了点头。} {
    #CLASS wuguanclass KILL;
    jobwuguan_guide
  };
  #ACTION {^{设定环境变量：action \= \"checkwuguan\"|你设定checkwuguan为反馈信息}} {
    #MATH {checkcount} {$checkcount + 1};
    answer y;
    #IF {$checkcount > 3} {
      #CLASS wuguanclass KILL;
      #VARIABLE {wuguantool} {};
      #IF {"$id[things][piao]" != ""} {
        #VARIABLE {wuguantool} {piao};
      };
      #ELSEIF {"$id[things][shui tong]" != ""} {
        #VARIABLE {wuguantool} {shui tong};
      };
      #ELSEIF {"$id[things][chai dao]" != ""} {
        #VARIABLE {wuguantool} {chai dao};
      };
      #ELSEIF {"$id[things][ju zi]" != ""} {
        #VARIABLE {wuguantool} {ju zi};
      };
      #ELSEIF {"$id[things][sao zhou]" != ""} {
        #VARIABLE {wuguantool} {sao zhou};
      };
      #ELSEIF {"$id[things][chu tou]" != ""} {
        #VARIABLE {wuguantool} {chu tou};
      };
      #ELSEIF {"$id[weapon]" != ""} {
        #VARIABLE {wuguantool} {$id[weapon]};
      };
      #IF {"$wuguantool" != ""} {
        loc {
          jobdo_wuguan
        }
      };
      #ELSE {
        loc {
          jobgo_wuguan
        }
      };
    };
    #ELSE {
      answer y;
      #DELAY {2} {
        echo {checkwuguan}
      }
    };
  };
  #CLASS wuguanclass CLOSE;
  echo {checkwuguan}
};
#ALIAS {jobwuguan_guide} {
  #VARIABLE {guideflag} {0};
  #VARIABLE {checkcount} {0};
  #CLASS guideclass OPEN;
  #ACTION {现在你可以去找孙均} {
    #VARIABLE {guideflag} {1};
  };
  #ACTION {周圻师兄常年闯荡} {
    #VARIABLE {guideflag} {2};
  };
  #ACTION {我沈城师兄学习} {
    #VARIABLE {guideflag} {3};
  };
  #ACTION {我师妹最爱唠叨} {
    #VARIABLE {guideflag} {4};
  };
  #ACTION {我鲁坤师兄正缺人手} {
    #VARIABLE {guideflag} {5};
  };
  #ACTION {^{设定环境变量：action \= \"checkguide\"|你设定checkguide为反馈信息}} {
    #DELAY {2} {
      #SWITCH {$guideflag} {
        #CASE {1} {
          #CLASS guideclass KILL;
          jobwuguan_sunjun
        };
        #CASE {2} {
          #CLASS guideclass KILL;
          jobwuguan_zhouqi
        };
        #CASE {3} {
          #CLASS guideclass KILL;
          jobwuguan_shencheng
        };
        #CASE {4} {
          #CLASS guideclass KILL;
          jobwuguan_qifang
        };
        #CASE {5} {
          #CLASS guideclass KILL;
          jobgo_wuguan
        };
        #DEFAULT {
          #MATH {checkcount} {$checkcount + 1};
          #IF {$checkcount > 3} {
            #CLASS guideclass KILL;
            loc {jobgo_wuguan}
          };
          #ELSE {
            echo {checkguide}
          };
        };
      };
    }
  };
  #CLASS guideclass CLOSE;
  echo {checkguide}
};
#ALIAS {jobwuguan_sunjun} {
  #VARIABLE {respflag} {0};
  #CLASS newbieclass OPEN;
  #ACTION {^你听了%*的讲解} {
    #VARIABLE {respflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkresponse\"|你设定checkresponse为反馈信息}} {
    #CLASS newbieclass KILL;
    dohalt {
      n;n;
      #IF {$respflag == 1} {
        jobwuguan_guide
      };
      #ELSE {

      };
    }
  };
  #CLASS guideclass CLOSE;
  execute {s;s;ask sun jun about 学习};
  echo {checkresponse}
};
#ALIAS {jobwuguan_zhouqi} {
  #VARIABLE {respflag} {0};
  #CLASS newbieclass OPEN;
  #ACTION {^你听了%*的讲解} {
    #VARIABLE {respflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkresponse\"|你设定checkresponse为反馈信息}} {
    #CLASS newbieclass KILL;
    dohalt {
      s;wu;sd;
      #IF {$respflag == 1} {
        jobwuguan_guide
      };
      #ELSE {

      };
    }
  };
  #CLASS guideclass CLOSE;
  execute {nu;ed;n;ask zhou qi about 学习};
  echo {checkresponse}
};
#ALIAS {jobwuguan_shencheng} {
  #VARIABLE {respflag} {0};
  #CLASS newbieclass OPEN;
  #ACTION {^你听了%*的讲解} {
    #VARIABLE {respflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkresponse\"|你设定checkresponse为反馈信息}} {
    #CLASS newbieclass KILL;
    dohalt {
      n;w;
      #IF {$respflag == 1} {
        jobwuguan_guide
      };
      #ELSE {

      };
    }
  };
  #CLASS guideclass CLOSE;
  execute {e;s;ask shen cheng about 学习};
  echo {checkresponse}
};
#ALIAS {jobwuguan_qifang} {
  #VARIABLE {respflag} {0};
  #CLASS newbieclass OPEN;
  #ACTION {^你听了%*的讲解} {
    #VARIABLE {respflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkresponse\"|你设定checkresponse为反馈信息}} {
    #CLASS newbieclass KILL;
    dohalt {
      execute {w;w;s;out;sd};
      #IF {$respflag == 1} {
        jobwuguan_guide
      };
      #ELSE {

      };
    }
  };
  #CLASS guideclass CLOSE;
  execute {nu;enter;n;e;e;ask qi fang about 学习};
  echo {checkresponse}
};
#ALIAS {jobgo_wuguan} {
  #IF {$hp[food] < 20 || $hp[water] < 20} {
    gotodo {武馆} {厨房} {wuguan_eat}
  };
  #ELSE {
    gotodo {武馆} {武馆大厅}{jobask_wuguan}
  };
};
#ALIAS {jobask_wuguan} {
  #VARIABLE {askresult} {0};
  #CLASS jobrequestclass OPEN;
  #ACTION {^你向鲁坤打听有关『工作』的消息。} {
    #CLASS jobresponseclass OPEN;
    #ACTION {^鲁坤说道：「狄云兄弟有事找你，你还是先去找他吧。」} {
      #VARIABLE {askresult} {1};
    };
    #ACTION {^鲁坤说道：「你功夫已经挺高了} {
      #VARIABLE {askresult} {2};
    };
    #CLASS jobresponseclass CLOSE;
  };
  #ACTION {^{设定环境变量：action \= \"checkask\"|你设定checkask为反馈信息}} {
    #CLASS jobresponseclass KILL;
    #CLASS jobrequestclass KILL;
    #SWITCH {$askresult} {
      #CASE {0} {
        dohalt {gotodo {武馆} {物品房} {jobasktool_wuguan}}
      };
      #CASE {1} {
        dohalt {gotodo {武馆} {武馆前院} {wuguan_inquiry}}
      };
      #CASE {2} {
        dohalt {gotodo {武馆} {冬暖阁} {wuguan_leave}}
      };
    };
  };
  #CLASS jobrequestclass CLOSE;
  ask lu kun about 工作;
  echo {checkask};
};
#ALIAS {jobasktool_wuguan} {
  #VARIABLE {askresult} {0};
  #VARIABLE {wuguantool} {};
  #CLASS jobrequestclass OPEN;
  #ACTION {^你向吴坎打听有关『工具』的消息。} {
  };
  #ACTION {^{设定环境变量：action \= \"checkask\"|你设定checkask为反馈信息}} {
    #CLASS jobrequestclass KILL;
    #IF {"$id[things][piao]" != ""} {
      #VARIABLE {wuguantool} {piao};
    };
    #ELSEIF {"$id[things][shui tong]" != ""} {
      #VARIABLE {wuguantool} {shui tong};
    };
    #ELSEIF {"$id[things][chai dao]" != ""} {
      #VARIABLE {wuguantool} {chai dao};
    };
    #ELSEIF {"$id[things][ju zi]" != ""} {
      #VARIABLE {wuguantool} {ju zi};
    };
    #ELSEIF {"$id[things][sao zhou]" != ""} {
      #VARIABLE {wuguantool} {sao zhou};
    };
    #ELSEIF {"$id[things][chu tou]" != ""} {
      #VARIABLE {wuguantool} {chu tou};
    };
    #ELSE {
      #VARIABLE {wuguantool} {$id[weapon]};
    };
    dohalt {
      jobdo_wuguan
    }
  };
  #CLASS jobrequestclass CLOSE;
  ask wu kan about 工具;
  i;
  echo {checkask};
};
#ALIAS {jobdo_wuguan} {
  #VARIABLE {wuguando} {};
  #SWITCH {"$wuguantool"} {
    #CASE {"piao"} {
      #VARIABLE {wuguando} {jiao 水};
      gotodo {武馆} {菜园} {jobfight_wuguan}
    };
    #CASE {"shui tong"} {
      #VARIABLE {wuguando} {tiao 水};
      gotodo {武馆} {水房} {jobfight_wuguan}
    };
    #CASE {"chai dao"} {
      #VARIABLE {wuguando} {pi 柴};
      gotodo {武馆} {柴房} {jobfight_wuguan}
    };
    #CASE {"ju zi"} {
      #VARIABLE {wuguando} {ju 木头};
      gotodo {武馆} {木房} {jobfight_wuguan}
    };
    #CASE {"sao zhou"} {
      #VARIABLE {wuguando} {sao 马房};
      gotodo {武馆} {马厩} {jobfight_wuguan}
    };
    #CASE {"chu tou"} {
      #VARIABLE {wuguando} {chu 草};
      gotodo {武馆} {菜地} {jobfight_wuguan}
    };
    #ELSE {
      #SHOWME {<faa>工具异常};
    };
  };
};
#ALIAS {jobfight_wuguan} {
  #CLASS jobfightclass OPEN;
  #ACTION {^你{用瓢|找了个|慢慢将水桶|挥起锄头|摆正}} {
    #VARIABLE {idle} {0};
    dohalt {
      $wuguando
    }
  };
  #ACTION {^一股暖流发自丹田流向全身} {
    $wuguando
  };
  #ACTION {^%*管事说道：「干的不错，好了} {
    #CLASS jobfightclass KILL;
    gotodo {武馆} {武馆大厅} {jobfinish_wuguan}
  };
  #CLASS jobfightclass CLOSE;
  wield $wuguantool;
  $wuguando
};
#ALIAS {jobfinish_wuguan} {
  #CLASS jobrequestclass OPEN;
  #ACTION {^鲁坤说道：「没给你工作，你怎么跑来覆命了？」} {
  };
  #ACTION {^鲁坤给你一本} {
    drop book
  };
  #ACTION {^你被奖励了：%*点经验和%*点潜能。} {
  };
  #ACTION {^{设定环境变量：action \= \"checktask\"|你设定checktask为反馈信息}} {
    #CLASS jobrequestclass KILL;
    dohalt {
      wuguan_learn
    }
  };
  #CLASS jobrequestclass CLOSE;
  execute {
    se;
    give $wuguantool to wu kan;
    nw;
    task ok;
  };
  echo {checktask}
};
#ALIAS {wuguan_learn} {
  #VARIABLE {learnflag} {0};
  #VARIABLE {learndo} {
    execute {
      #10 xue jiaotou force;
      hp
    };
    #DELAY {0.5} {echo {checklearn}}
  };
  #CLASS learnclass OPEN;
  #ACTION {^你一觉醒来} {
    #CLASS learnclass KILL;
    jobgo_wuguan
  };
  #ACTION {^你要向谁求教} {
    #VARIABLE {learnflag} {1};
  };
  #ACTION {^也许是缺乏实战经验} {
    #VARIABLE {learnflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checklearn\"|你设定checklearn为反馈信息}} {
    #IF {$learnflag == 0 && $hp[pot] > 1 && $hp[jing] > 10} {
      $learndo
    };
    #ELSE {
      #IF {"$hp[sex]" == "m"} {
        gotodo {武馆} {男休息室} {sleep}
      };
      #ELSE {
        gotodo {武馆} {女休息室} {sleep}
      };
    };
  };
  #CLASS learnclass CLOSE;
  gotodo {武馆} {东练武场} {$learndo}
};
#ALIAS {wuguan_eat} {
  #CLASS foodclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkfood\"|你设定checkfood为反馈信息}} {
    #IF {$hp[food] < 90} {
      ask wang about food;
      dohalt {
        execute {
          #5 eat fan;
          drop fan;
          hp
        };
        echo {checkfood}
      };
    };
    #ELSEIF {$hp[water] < 90} {
      ask wang about water;
      dohalt {
        execute {
          #5 drink cha;
          drop cha;
          hp
        };
        echo {checkfood}
      };
    };
    #ELSE {
      #CLASS foodclass KILL;
      #DELAY {2} {
        jobgo_wuguan
      }
    };
  };
  #CLASS foodclass CLOSE;
  execute {
    get all;
    #5 eat fan;
    #5 drink cha;
    drop fan;
    drop cha;
    drop cha;
    hp
  };
  echo {checkfood}
};
#ALIAS {wuguan_inquiry} {
  #VARIABLE {checkcount} {0};
  #VARIABLE {checkresult} {0};
  #VARIABLE {answer} {};
  #CLASS inquiryclass OPEN;
  #ACTION {^如果确认回答问题，请输入} {
    #VARIABLE {checkresult} {1};
  };
  #ACTION {^你先回答了冯坦的问题再离开也不迟啊！} {
    #VARIABLE {checkresult} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkinquiry\"|你设定checkinquiry为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {$checkresult == 1} {
      answer y
    };
    #ELSEIF {$checkcount > 10} {
      #CLASS inquiryclass KILL;
      jobgo_wuguan
    };
    #ELSE {
      #MATH {checkcount} {$checkcount + 1};
      #DELAY {2} {
        nu;sd;
        echo {checkinquiry}
      }
    };
  };
  #ACTION {准备好了吧！那我们就开始了} {
    #CLASS questionclass OPEN;
    #ACTION {^请问：如何查看书剑的预设表情动作} {#variable {answer} {semote}};
    #ACTION {^请问：工匠可以去扬州东门附近的龙门镖局雇佣保镖} {#variable {answer} {否}};
    #ACTION {^请问：下面那一项不是昆仑三圣何足道的绰号} {#variable {answer} {赌圣}};
    #ACTION {^请问：请问如何查看角色的本次连线时} {#variable {answer} {time}};
    #ACTION {^请问：如果你不希望看到闲聊频道的说话} {#variable {answer} {tune chat}};
    #ACTION {^请问：在河边或者江边叫船的指令} {#variable {answer} {yell boat}};
    #ACTION {^请问：如果你想给玩家%*疗伤} {#variable {answer} {yun lifeheal char}};
    #ACTION {^请问：温家五老中，愿意把五行阵传授给外人} {#variable {answer} {温方山}};
    #ACTION {^请问：用%*渡过汉水,需要轻功有效等级大于等于} {#variable {answer} {80}};
    #ACTION {^请问：笑傲江湖中风清阳传给令狐冲的剑法是} {#variable {answer} {dugu-jiujian}};
    #ACTION {^请问：当你想把身上的衣服%*穿上时，应该输入的指令是} {#variable {answer} {wear cloth}};
    #ACTION {^请问：如果有玩家企图杀害一个工匠，且此工匠不是通缉犯} {#variable {answer} {否}};
    #ACTION {^请问：帮派系统中每个帮派都有自己的实力值和名望值} {#variable {answer} {是}};
    #ACTION {^请问：射雕英雄传里梅超风传给杨康的爪法是} {#variable {answer} {jiuyin-baiguzhua}};
    #ACTION {^请问：当你想要了解某一门派%*的说明时} {#variable {answer} {help party mingjiao}};
    #ACTION {^请问：学习技能将会消耗的}  {#variable {answer} {精血}};
    #ACTION {^请问：领悟一次得到的点数取决于：} {#variable {answer} {读书写字}};
    #ACTION {^请问：加力%*的多少直接影响攻击力} {#variable {answer} {是}};
    #ACTION {^请问：下面这些人不曾断指的是} {#variable {answer} {赵志敬}};
    #ACTION {^请问：使内力增加的最常用途径是} {#variable {answer} {dazuo}};
    #ACTION {^请问：如果你想跟玩家%*组队，应该使用的命令} {#variable {answer} {team with char}};
    #ACTION {^请问：屠龙刀中藏的是} {#variable {answer} {武穆遗书}};
    #ACTION {^请问：在三十岁前精的最大值会逐年增长，四十岁后则会逐年减少} {#variable {answer} {否}};
    #ACTION {^请问：帮派系统中帮主可以用解散(gdismiss)命令来解散帮派，也可以} {#variable {answer} {是}};
    #ACTION {^请问：自己建立帮派的命令是} {#variable {answer} {gcreate}};
    #ACTION {^请问：如果你不想与玩家%*私聊，应当如何进行} {#variable {answer} {set block char}};
    #ACTION {^请问：如果你到了一个出售商品的NPC面前，看商品目录的指令} {#variable {answer} {list}};
    #ACTION {^请问：用内力给自己疗伤的指令} {#variable {answer} {yun heal}};
    #ACTION {^请问：「那少年约莫十五六岁年纪，头上歪戴著一顶黑黝黝的破石帽} {#variable {answer} {黄蓉}};
    #ACTION {^请问：打坐需要你的精的当前值大于最大精} {#variable {answer} {80%}};
    #ACTION {^请问：如果你在游戏中想跟随某个玩家或者NPC一起行动} {#variable {answer} {follow}};
    #ACTION {^请问：在帮派争斗期间，争斗双方及其各自结盟帮派的成员在争斗双方} {#variable {answer} {否}};
    #ACTION {^请问：在帮派系统中,如果要对一个帮派开战，那么宣战的指令是} {#variable  {answer} {declare}};
    #ACTION {^请问：在帮派系统中，结盟的指令是} {#variable {answer} {ally}};
    #ACTION {^请问：如果你想装备剑} {#variable {answer} {wield sword}};
    #ACTION {^请问：在聊天系统中，用 say  命令说出来的话，只能被同一个场景} {#variable {answer} {是}};
    #ACTION {^请问：帮派系统中帮主可以用解散} {#variable {answer} {是}};
    #ACTION {^请问：气是一个人的力气大小的标准，也分为最大值，有效值和当前值} {#variable {answer} {否}};
    #ACTION {^请问：恢复精血的指令是} {#variable {answer} {yun jing}};
    #ACTION {^请问：你希望在自己的角色上增加一些个性的描述说明，该通过哪个指令} {#variable {answer} {describe}};
    #ACTION {^请问：在任何地方都可以对通缉犯叫杀} {#variable {answer} {是}};
    #ACTION {^请问：正气或戾气是衡量一个人物正直或是邪恶的标尺} {#VARIABLE {answer} {是}};
    #ACTION {^请问：玩家可以通过提高特殊轻功的等级来提高后天身法} {#VARIABLE {answer} {否}};
    #ACTION {^请问：当你想从玩家%*身上偷取黄金%*时，正确的指令是} {#VARIABLE {answer} {steal gold from char}};
    #ACTION {^请问：人物有悟性、根骨、身法、膂力四项基本天赋} {#VARIABLE {answer} {是}};
    #ACTION {^请问：下面哪种频道解释是错误的} {#VARIABLE {answer} {party 帮派频道}};
    #ACTION {^请问：神雕侠侣中小龙女传给杨过抓麻雀的手法是什么} {#VARIABLE {answer} {tianluo-diwang}};
    #ACTION {^请问：如果你想布五行阵%*,该使用的命令是} {#VARIABLE {answer} {lineup form wuxing-zhen}};
    #ACTION {^请问：算命得知：「看……五官挪位，印堂发暗，……。」可知其容貌小于 15} {#VARIABLE {answer} {是}};
    #ACTION {^请问：如果一个游戏者的最高工匠技能级别高于他本身的最高武学技能} {#VARIABLE {answer} {是}};
    #ACTION {^请问：在游戏中建立聊天室的命令是} {#VARIABLE {answer} {irc /create}};
    #ACTION {^请问：不属于铸剑师应该拥有的技能是} {#VARIABLE {answer} {修理术}};
    #ACTION {^请问：玩家死亡后可以复活，但是损失所有的钱财} {#VARIABLE {answer} {否}};
    #ACTION {^请问：『九阴真经』和『九阳真经』作者是同一个人} {#VARIABLE {answer} {否}};
    #ACTION {^请问：使用什么指令可以让在游戏中的所有人肯定可以看到你所说的话} {#VARIABLE {answer} {shout}};
    #ACTION {^请问：当你的气血上限不是} {#VARIABLE {answer} {食物吃得太多}};
    #ACTION {^请问：人物有福缘、纯朴、容貌、精神四项隐藏天赋} {#VARIABLE {answer} {否}};
    #ACTION {^请问：潜能的上限与受经验限制的武功级别有关} {#VARIABLE {answer} {是}};
    #ACTION {^请问：恢复气血的指令是} {#VARIABLE {answer} {yun qi}};
    #ACTION {^请问：不决定铸造兵器基本威力的是} {#VARIABLE {answer} {铸造时间}};
    #ACTION {^请问：请问如何设定当自己气血不足 30% 的时候自动逃离战场？} {#VARIABLE {answer} {set wimpy 30}};
    #ACTION {^请问：工匠在钱庄中可以比武士存更多的钱，但相对而言取钱的手续费} {#VARIABLE {answer} {是}};
    #ACTION {^请问：工匠技能以四百级为极限，并且受经验限制} {#VARIABLE {answer} {否}};
    #ACTION {^请问：玩家可以通过提高读书写字的等级来提高后天悟性} {#VARIABLE {answer} {是}};
    #ACTION {^请问：请问如何把玄铁剑法} {#VARIABLE {answer} {jifa sword xuantie-jianfa}};
    #ACTION {^请问：当你想脱掉身上的衣服%*时，应该输入的指令是} {#VARIABLE {answer} {remove cloth}};
    #ACTION {^请问：当你想把手中的剑%*放下时，应该输入的指令是} {#VARIABLE {answer} {unwield sword}};
    #ACTION {^请问：小郡主沐剑屏是其父唯一的孩子} {#VARIABLE {answer} {否}};
    #ACTION {^请问：帮派争斗中攻击对方的物品或房间时，同一房间的所有和攻击者} {#VARIABLE {answer} {是}};
    #ACTION {^请问：吐呐需要你的气的当前值大于最大气的} {#VARIABLE {answer} {70%}};
    #ACTION {^请问：鹿鼎记中韦小宝使用的轻功是什么} {#VARIABLE {answer} {shenxing-baibian}};
    #ACTION {^请问：ALIAS是指MUD里的别名，用简单的几个字母代替} {#VARIABLE {answer} {是}};
    #ACTION {^请问：如果你想解除跟随某个玩家或NPC的指令，则应输入} {#VARIABLE {answer} {follow none}};
    #ACTION {^请问：下面不属于私人聊天的指令是} {#VARIABLE {answer} {rumor}};
    #ACTION {^请问：讨价还价%*是你做买卖讲价的水平，级别越高则在买卖中的} {#VARIABLE {answer} {是}};
    #ACTION {^请问：读书时，每一次读书都要消耗一些精血，悟性高的人消耗的精血} {#VARIABLE {answer} {是}};
    #ACTION {^请问：经验大于 5,000,000 的玩家就能建立自己的帮派} {#VARIABLE {answer} {是}};
    #ACTION {^请问：恢复精力的指令是} {#VARIABLE {answer} {yun jingli}};
    #ACTION {^请问：精是一个人的精神状况的标准，分为最大值，有效值} {#VARIABLE {answer} {是}};
    #ACTION {^请问：学习一次得到的点数约等于后天悟性的} {#VARIABLE {answer} {1/2}};
    #ACTION {^请问：不属于织造师应该拥有的技能是} {#VARIABLE {answer} {修补术}};
    #ACTION {^请问：使用什么指令可以解散一个你已经建立的队伍} {#VARIABLE {answer} {team dismiss}};
    #ACTION {^请问：飞身渡长江需要最大内力大于等于} {#VARIABLE {answer} {3500}};
    #ACTION {^请问：如果在短时间内发大量信息到公聊频道的玩家} {#VARIABLE {answer} {是}};
    #ACTION {^请问：使用什么指令可以让你从战斗中安全脱离出来} {#VARIABLE {answer} {halt}};
    #ACTION {^请问：当%*的任何一种小于零的时候人都会昏迷} {#VARIABLE {answer} {否}};
    #ACTION {^请问：当你想要了解某一区域%*的地图的时候} {#VARIABLE {answer} {help map dali}};
    #ACTION {^请问：下列行为中，不会被通缉的是} {#VARIABLE {answer} {杀官兵}};
    #ACTION {^请问：当你想把身上的衣服%*丢弃的时候} {#VARIABLE {answer} {drop cloth}};
    #ACTION {^请问：请问如何给自己起一个「nickname」的外号？} {#VARIABLE {answer} {nick nickname}};
    #ACTION {^请问：玩家%*在公开频道中发布不适当言论，你希望发布} {#VARIABLE {answer} {vote chblk char}};
    #ACTION {^请问：不影响膂力大小的是} {#VARIABLE {answer} {基本招架}};
    #ACTION {^请问：内力可以运用于恢复，包括} {#VARIABLE {answer} {是}};
    #ACTION {^请问：当你身处茶馆，想要买花生} {#VARIABLE {answer} {buy huasheng}};
    #ACTION {^请问：不受根骨影响的是} {#VARIABLE {answer} {饭量的大小}};
    #ACTION {^请问：算命得知：「……，不过你前生一定行了善事，一生大富大贵} {#VARIABLE {answer} {否}};
    #ACTION {^请问：请问怎样可以将你的好友%*加入你的好友列表} {#VARIABLE {answer} {finger -a friend}};
    #ACTION {^请问：在游戏中，查看自己有何武功的指令} {#VARIABLE {answer} {skills(cha)}};
    #ACTION {^请问：如果要销毁自己帮派里的物品或房间} {#VARIABLE {answer} {destory}};
    #ACTION {^请问：%*血缘上讲是姐妹} {#VARIABLE {answer} {否}};
    #ACTION {^请问：工匠所拥有的所有武学技能都不能超过二百级} {#VARIABLE {answer} {是}};
    #ACTION {^请问：武功的有效等级等于基本武功跟特殊武功之和} {#VARIABLE {answer} {否}};
    #ACTION {^请问：玩家可以通过提高基本内功的等级来提高后天根骨} {#VARIABLE {answer} {是}}; 
    #ACTION {^请问：玩家可以通过提高特殊内功的等级来提高后天根骨} {#VARIABLE {answer} {否}};
    #ACTION {^请问：为了角色安全需要时常修改登陆密码} {#VARIABLE {answer} {passwd}};
    #ACTION {^请问：帮派系统中帮派成员分为%*四个等级} {#VARIABLE {answer} {是}};
    #ACTION {^请问：帮派系统中帮派的首领可以在自己的帮派里命令等级比自己} {#VARIABLE {answer} {是}};
    #ACTION {^请问：玩家可以通过提高基本轻功的等级来提高后天身法} {#VARIABLE {answer} {是}};
    #ACTION {^请问：练习技能一次得到的点数约等于该项基本武功的级别} {#VARIABLE {answer} {1/5}};
    #ACTION {^请问：读书一次得到的点数约等于读书写字级别的} {#VARIABLE {answer} {1/5}};
    #ACTION {^请问：当地上有黄金%*，你想捡起来，应输入的指令是} {#VARIABLE {answer} {get gold}};
    #ACTION {^请问：辟邪剑法源自} {#VARIABLE {answer} {是}};
    #ACTION {^请问：不属于药师应该拥有的技能是} {#VARIABLE {answer} {种植术}};
    #ACTION {^请问：离开游戏的正确指令是} {#VARIABLE {answer} {quit}};
    #ACTION {^请问：下面哪个不是看守谢逊的三大神僧} {#VARIABLE {answer} {空见}};
    #ACTION {^请问：飞身渡黄河需要轻功有效等级大于等于} {#VARIABLE {answer} {250}};
    #ACTION {^请问：在游戏中，查看自己身上有何物品的指令是？} {#VARIABLE {answer} {inventory(i)}};
    #ACTION {^请问：工匠在钱庄中可以比武士存更多的钱，但相对而言取钱的手续费比武士} {#VARIABLE {answer} {是}};
    #ACTION {^请问：请问：不属于药师应该拥有的技能是：} {#VARIABLE {answer} {种植术}};
    #ACTION {^请问：帮派系统中帮派成员分为帮主、副帮主、堂主、帮众四个等级？} {#VARIABLE {answer} {是}};
    #ACTION {^请问：当你的气血上限不是 100% 时，哪种原因是错误的？} {#VARIABLE {answer} {食物吃得太多}};

    #action {^A、%%%1%sB、%%%2%s$} {
      #VARIABLE {answers[%%%1]} {A};   
      #VARIABLE {answers[%%%2]} {B};
      #DELAY {0.5} {
        answer $answers[$answer];
        #LIST {answers} {clear}
      }
    };
  
    #action {^C、%%%1%sD、%%%2%s$} {
      #variable {answers[%%%1]} {C};   
      #variable {answers[%%%2]} {D};
    } ;
  
    #action {^C、%%%1%s$} {
      #variable {answers[%%%1]} {C}; 
    }{9};
  
    #action {^E、%%%1%s$} {
      #variable {answers[%%%1]} {E};   
    };
    #CLASS questionclass CLOSE;
  };
  #ACTION {^狄云对你说道：“恭喜！恭喜！”} {
    #CLASS questionclass KILL;
    #CLASS inquiryclass KILL;
    gotodo {武馆} {冬暖阁} {wuguan_leave}
  };
  #CLASS inquiryclass CLOSE;
  set action checkinquiry
};
#ALIAS {wuguan_leave} {
  #VARIABLE {tempdo} {ask wan zhenshan about 离馆};
  #CLASS jobrequestclass OPEN;
  #ACTION {^你向万震山打听有关『离馆』的消息。} {
    #CLASS jobresponseclass OPEN;
    #ACTION {^万震山给你一封「付韩兄书」。} {
      #CLASS jobresponseclass KILL;
      dohalt {
        gotodo {武馆} {门廊} {ask sun jun about 离馆}
      }
      
    };
    #CLASS jobresponseclass CLOSE;
  };
  #ACTION {^你向孙均打听有关『离馆』的消息。} {
    #CLASS jobresponseclass OPEN;
    #ACTION {^您先歇口气再说话吧。} {
      dohalt {
        $tempdo
      }
    };
    #ACTION {^孙均对你说道：“恭喜！恭喜！} {
      #CLASS jobresponseclass KILL;
      #CLASS jobrequestclass KILL;
      dohalt {
        out;
        eat yuji;
        credit vip;
        loc {
          gotodo {扬州城} {当铺} {checkpur}
        }
      }
    };
    #CLASS jobresponseclass CLOSE;
  };
  #CLASS jobrequestclass CLOSE;
  $tempdo
};
#ALIAS {checkpur} {
  #VARIABLE {per} {0};
  #VARIABLE {suicideflag} {0};
  #VARIABLE {checkcount} {0};
  #CLASS checkclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkhp\"|你设定checkhp为反馈信息}} {
    dohalt {
      #IF {$hp[neili_max] < 600} {
        execute {
          duihuan yuji wan;
          fu yuji;
          hp
        };
        echo {checkhp};
      };
      #ELSE {
        duihuan tianlong;
        read book;
        echo {checkpur};
      };
    }
  };
  #ACTION {^你当前的淳朴值为:%*} {
    #VARIABLE {per} {%%1};
  };
  #ACTION {^本周你已经不能使用天龙八部了} {
    #VARIABLE {suicideflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkpur\"|你设定checkpur为反馈信息}} {
    #VARIABLE {idle} {0};
    #MATH {checkcount} {$checkcount + 1};
    #IF {$checkcount >= 10} {
      #VARIABLE {suicideflag} {1};
    };
    dohalt {
      #IF {$suicideflag == 1} {
        #CLASS checkclass KILL;
        n;
        suicide -f;
        $user_password
      };
      #ELSEIF {$per < 30} {
        dohalt {
          duihuan tianlong;
          read book;
          echo {checkpur};
        }
      };
      #ELSE {
        #CLASS checkclass KILL;
        gotodo {扬州城} {当铺} {checkper}
      };
    }
  };
  #CLASS checkclass CLOSE;
  hp;
  set action checkhp
};

#ALIAS {checkper} {
  #VARIABLE {perflag} {0};
  #VARIABLE {tempdo} {
    duihuan youtan;
    dohalt {
      #VARIABLE {perflag} {0};
      fu youtan;
      echo {checkper}
    };
  };
  #CLASS checkclass OPEN;
  #ACTION {^你服下一朵幽昙奇花觉得一股清凉地感觉流遍了你的全身。} {
  };
  #ACTION {^你的幽昙奇花已经用完了!} {
  };
  #ACTION {^你已经很漂亮了} {
    #VARIABLE {perflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkper\"|你设定checkper为反馈信息}} {
    #IF {$perflag == 1} {
      dohalt {
        duihuan ebook;
        i;
        echo {checkbook}
      };
    };
    #ELSE {
      $tempdo
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkbook\"|你设定checkbook为反馈信息}} {
    #IF {@carryqty{ebook} == 0} {
      #DELAY {1} {
        duihuan ebook;
        i;
        echo {checkbook}
      };
    };
    #ELSE {
      #CLASS checkclass KILL;
      dohalt {
        read book;
        jobgo_xuncheng
      };
    };
  };
  #CLASS checkclass CLOSE;
  $tempdo
};