#NOP {蛤蟆功3,%1:后续指令};
#ALIAS {goquest_hmg3} {
  #VARIABLE {questmodule} {蛤蟆功三};
  gotodo {华山} {舍身崖} {hmg3_findhong {%1}}
};
#NOP {获取下一步，%1:要排除的方向};
#FUNCTION getNextDirect {
  #LIST {directions} {create} {left;right;up};
  #IF {"%1" != ""} {
    #LIST {directions} {delete} {@contains{{directions}{%1}}};
  };
  #RETURN {$directions[+@rnd{{1}{&directions[]}}]};
};
#ALIAS {hmg3_findhong} {
  #VARIABLE {nextstep} {};
  #VARIABLE {prestep} {};
  #VARIABLE {okcount} {0};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你已经见过你义父最后一面了} {
    #CLASS questclass KILL;
    questsuccess {$questmodule};
    runwait {%1};
  };
  #ACTION {^你来的太勤了些吧。} {
    #CLASS questclass KILL;
    questdelay {$questmodule} {0} {10800};
    runwait {%1};
  };
  #ACTION {^你手脚并用，使出了吃奶的力气终于爬了上来。} {
    #VARIABLE {idle} {0};
    #VARIABLE {okcount} {0};
    #VARIABLE {nextstep} {@getNextDirect{$nextstep}};
    dohalt {
      yun jingli;
      hp;
      climb $nextstep;
    }
  };
  #ACTION {^{$dazuo_over}} {
    #VARIABLE {idle} {0};
    #IF {$hp[neili] > $threshold_neili} {
      yun jingli;
      hp;
      climb $nextstep;
    };
    #ELSE {
      dzn
    };
  };
  #ACTION {^你费了很大力气爬了过去，却发现爬错了方向，只好退回了原地} {
    #VARIABLE {idle} {0};
    #VARIABLE {okcount} {0};
    #VARIABLE {nextstep} {@getNextDirect{$nextstep}};
    dohalt {
      #IF {$hp[neili] < 1000} {
        dzn
      };
      #ELSE {
        yun jingli;
        hp;
        climb $nextstep;
      };
    }
  };
  #ACTION {^你找到了一个避风之处，准备歇口气再爬} {
    #VARIABLE {idle} {0};
    #MATH {okcount} {$okcount + 1};
    dohalt {
      #IF {$okcount >= 3} {
        #CLASS questclass KILL;
        startfull {hmg3_chaikai {%1}};
      };
      #ELSE {
        yun jingli;
        hp;
        climb up;
      };
    }
  };
  #CLASS questclass CLOSE;
  climb up;
};
#NOP {拆开,%1:后续指令};
#NOP {洪七公说道：「你们这么人上华山干什么来了！」};
#ALIAS {hmg3_chaikai} {
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^洪七公说道：「你们%*上华山干什么来了} {
    #CLASS questclass KILL;
    #VARIABLE {idle} {-60};
    questdelay {$questmodule} {0} {1200};
    restoregift {
      climb down;
      wd;
      loc {%1};
    };
  };
  #ACTION {^你觉得天地之大，却无可去之处，忍不住放声大哭。} {
    #VARIABLE {idle} {-30};
  };
  #ACTION {^头，却没一个敢跟我老叫化吃一条蜈蚣。小娃娃，你敢不敢吃} {
    #VARIABLE {idle} {-30};
    chi wugong;
  };
  #ACTION {^同归于尽不可，若是上前拆开（chaikai），自己功力与他们相差太远} {
    pray pearl;
    chaikai;
  };
  #ACTION {^你成功领会义父的蛤蟆功,你的功夫提高了} {
    #CLASS questclass KILL;
    questsuccess {$questmodule};
    #SEND {shout 哈哈哈，我开了$questmodule，羡慕嫉妒恨吧！！！};
    #DELAY {2} {
      restoregift {
        climb down;
        wd;
        loc {%1};
      };
    }
  };
  #ACTION {^象断了线的风筝般直坠入山下。} {
    questfail {$questmodule};
  };
  #ACTION {^一股暖流发自丹田流向全身，慢慢地你又恢复了知觉……} {
    #CLASS questclass KILL;
    questfail {$questmodule};
    climb down;
    wd;
    loc {%1};
  } {1};
  #CLASS questclass CLOSE;
  cry;
};