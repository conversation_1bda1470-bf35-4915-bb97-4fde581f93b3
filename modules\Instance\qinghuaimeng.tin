#CLASS {qinghuaimeng} {kill}
#CLASS {qinghuaimeng} {open}

/* 
 * 情怀梦副本脚本
 * 该脚本实现了情怀梦副本的自动化功能
 */

/* 全局变量定义 */
#VAR {killer_master} {0}
#VAR {qinghuaimeng_box} {0}
#VAR {qinghuaimeng_killer_num_1} {10}
#VAR {qinghuaimeng_killer_num} {0}

#ALIAS {otherquest_qinghuaimeng} {
    #VAR killer_master 0
    #VAR qinghuaimeng_box $qinghuaimeng_box
    #VAR qinghuaimeng_killer_num_1 $qinghuaimeng_killer_num_1
    open_qinghuaimeng_triggers
    
    #ECHO {<解密>:%t <玩家:${char_id:-none}>：挑战情怀梦副本开始} {cyan}
    
    #VAR now_time {@time_in_seconds{} + 24 * 60 * 60 + 300}
    #VAR qinghuaimeng_start_time $now_time
    #ALIAS {挑战情怀梦} {$now_time}
    #VAR qinghuaimeng_killer_num 0
    
    send {i}
    
    #IF {$qhm_da_huandan == 1} {
        #DELAY {1} {
            #TRIGGER {^\s+(\S+)盒大还丹\(盒\)\(Da huandan\)$} {
                #VAR da_huandan %1
                #IF {$da_huandan < $qhm_da_huandan_number} {
                    #DELAY {1} {
                        duihuan da huandan;
                        i;
                    };
                } {
                    #UNTRIGGER {otherquest_qinghuaimeng_5};
                    #DELAY {1} {
                        #DELAY {3} {
                            #DELAY {1} {
                                qinghuaimeng;
                                up;
                                mazemap;
                                set wimpy 100;
                                #DELAY {3} {
                                    qinghuaimeng_skills_1;
                                    #DELAY {3} {
                                        close_fight;
                                        hp $char_id;
                                    };
                                };
                            };
                        };
                    };
                };
            } {otherquest_qinghuaimeng_5};
            
            #IF {$da_huandan < $qhm_da_huandan_number} {
                duihuan da huandan;
                i;
            } {
                #UNTRIGGER {otherquest_qinghuaimeng_5};
                #DELAY {1} {
                    #DELAY {2} {
                        #DELAY {1} {
                            qinghuaimeng;
                            up;
                            mazemap;
                            set wimpy 100;
                            #DELAY {3} {
                                qinghuaimeng_skills_1;
                                #DELAY {3} {
                                    close_fight;
                                    hp $char_id;
                                };
                            };
                        };
                    };
                };
            };
        };
    } {
        #DELAY {1} {
            qinghuaimeng;
            up;
            #DELAY {3} {
                qinghuaimeng_skills_1;
                #DELAY {3} {
                    close_fight;
                    #DELAY {2} {
                        mazemap;
                    };
                };
            };
        };
    };
    
    /* 触发器设置 */
    #TRIGGER {^\s*看起来(.*)想杀死你！$} {
        #VAR idle 0;
    } {otherquest_qinghuaimeng_2};
    
    #TRIGGER {^\s*(.*)一个闪身就不见了。$} {
        #IF {$killer_master > 0} {
            alias action 准备挑战;
        };
        
        #MATH qinghuaimeng_killer_num {$qinghuaimeng_killer_num + 1};
        
        #DELAY {1} {
            #IF {"$yun_pfm" != ""} {
                $yun_pfm;
                #DELAY {1} {
                    yun qi;
                    yun jing;
                    yun jingli;
                    mazemap;
                };
            };
        };
    } {otherquest_qinghuaimeng_3};
    
    #TRIGGER {^\s*(.*)神志迷糊，脚下一个不稳，倒在地上昏了过去。$} {
        kill wanderer;
    } {otherquest_qinghuaimeng_4};
    
    #TRIGGER {^\s*这里没有这个人。$} {
        #DELAY {1} {
            mazemap;
        };
    } {otherquest_qinghuaimeng_12};
    
    #TRIGGER {^\s*总步数: 0$} {
        #IF {$killer_master > 0} {
            #ECHO {内存清理} {cyan};
            leave;
            #ECHO {<解密>:%t <玩家:${char_id:-none}>：挑战情怀梦共杀killer【$qinghuaimeng_killer_num】个。} {yellow};
            #VAR qinghuaimeng_killer_num 0;
        };
    } {otherquest_qinghuaimeng_6};
    
    #TRIGGER {^\s*情怀梦境 - leave$} {
        #DELAY {1} {
            qinghuaimeng_skills_2;
            look;
        };
    } {otherquest_qinghuaimeng_7};
    
    #TRIGGER {^\s(.*)梦境掌控者\s+(\S+)\((.*)\\)} {
        #UNTRIGGER {otherquest_qinghuaimeng_8};
        #VAR killer_name %2;
        #VAR killer_id %3;
        #FORMAT killer_id {%l} {$killer_id};
        #VAR pfm_id $killer_id;
        #VAR killer_master 1;
        
        #DELAY {1} {
            #IF {"$yun_pfm" != ""} {
                $yun_pfm;
                #DELAY {3} {
                    yun qi;
                    yun jing;
                    yun jingli;
                    kill $killer_id;
                };
            };
        };
    } {otherquest_qinghuaimeng_8};
    
    #TRIGGER {^\s*你把\s+\"action\"\s+设定为\s+\"准备挑战} {
        #TRIGGER {otherquest_qinghuaimeng_8} {ON};
        #IF {$killer_master == 1} {
            look;
        };
    } {otherquest_qinghuaimeng_9};
    
    #TRIGGER {^\s*HP\:(.+)} {
        #REGEX {%1} {^(.%d*)/(%d-)/(%d-)%%/(.%d*)/(%d-)/%d-/(.%d*)/(%d-)/(%d-)%%/(.%d*)/(%d-)/%+(%d-)/(.%d-)/%d-/%d-/(%d-)%.%d%d%%/(%d-)/(%d-)/(%d-)%.%d%d%%/(%d-)/%d$} {
            #VAR jing &1;
            #VAR maxjing &2;
            #VAR hurtjing &3;
            #VAR jingli &4;
            #VAR maxjingli &5;
            #VAR qi &6;
            #VAR maxqi &7;
            #VAR hurtqi &8;
            #VAR neili &9;
            #VAR maxneili &10;
            #VAR jiali &11;
            #VAR shen &12;
            #VAR food &13;
            #VAR pot &14;
            #VAR maxpot &15;
            #VAR water &16;
            #VAR exp &17;
            
            #IF {$hurtqi < 80 || $neili < 5000} {
                fu dan;
            };
        };
    } {otherquest_qinghuaimeng_10};
    
    #TRIGGER {^\s*你被奖励了(.*)枚情怀币！} {
        close_qinghuaimeng_triggers;
        #VAR qinghuaimeng_tongbao %1;
        #ECHO {<解密>:%t <玩家:${char_id:-none}>：完成情怀梦副本，获得$qinghuaimeng_tongbao情怀币。} {yellow};
        #ECHO {<解密>:%t <玩家:${char_id:-none}>：挑战情怀梦副本结束} {white};
        
        #VAR now_time {@time_in_seconds{} + 24 * 60 * 60 + 300};
        #VAR qinghuaimeng_start_time $now_time;
        #ALIAS {挑战情怀梦} {$now_time};
        changejob;
    } {otherquest_qinghuaimeng_11};
    
    #TRIGGER {^\s*请明天再来情怀梦境吧。} {
        close_qinghuaimeng_triggers;
        #ECHO {<解密>:%t <玩家:${char_id:-none}>：情怀梦副本，时间间隔不够。} {red};
        
        #VAR now_time {@time_in_seconds{} + 24 * 60 * 60 + 300};
        #VAR qinghuaimeng_start_time $now_time;
        #ALIAS {挑战情怀梦} {$now_time};
        changejob;
    } {otherquest_qinghuaimeng_13};
    
    #TRIGGER {^\s*你还是打发了游荡者再说吧!} {
        #TICKER {timer} {
            search;
        } {1};
    } {otherquest_qinghuaimeng_18};
    
    #TRIGGER {^\s*你满头大汉} {
        #TICKER {timer} {
            search;
        } {1};
    } {otherquest_qinghuaimeng_14};
    
    #TRIGGER {^\s*宝藏之地都被你破坏了，还找什么呢!} {
        #KILL {timer};
        #VAR qinghuaimeng_box 0;
        mazemap;
    } {otherquest_qinghuaimeng_15};
    
    #TRIGGER {^\s*你得到了(.*)枚情怀币,但是好像也触碰到了什么机关！} {
        #VAR qinghuaimeng_box 0;
        #VAR qinghuaimeng_tongbao_1 %1;
        #ECHO {<解密>:%t <玩家:${char_id:-none}>：挖到宝箱，获得$qinghuaimeng_tongbao_1情怀币。} {yellow};
    } {otherquest_qinghuaimeng_16};
    
    #TRIGGER {^\s*你从宝藏中得到了一个(.*)。} {
        #VAR qinghuaimeng_tongbao_2 %1;
        #ECHO {<解密>:%t <玩家:${char_id:-none}>：挖到宝箱，获得$qinghuaimeng_tongbao_2。} {yellow};
    } {otherquest_qinghuaimeng_17};
    
    #TRIGGER {^\s*情怀梦境迷宫第一层行走方向提示：(.*)$} {
        /* 这里需要处理迷宫路径分析和导航 */
        #VAR path_directions %1;
        
        /* 以下为简化版本，实际需要根据迷宫地图数据进行处理 */
        #REPLACE {path_directions} {north} {n};
        #REPLACE {path_directions} {south} {s};
        #REPLACE {path_directions} {east} {e};
        #REPLACE {path_directions} {west} {w};
        
        #IF {$qinghuaimeng_killer_num <= $qinghuaimeng_killer_num_1} {
            #TRIGGER {otherquest_qinghuaimeng_12} {ON};
            #DELAY {1} {
                /* 处理单步移动 */
                go $path_directions;
                kill wanderer;
            };
        } {
            #UNTRIGGER {otherquest_qinghuaimeng_12};
            #DELAY {2} {
                /* 处理多步移动 */
                #FOREACH {$path_directions} {direction} {
                    #DELAY {2} {
                        go $direction;
                    };
                };
            };
        };
    } {otherquest_qinghuaimeng_1};
}

#ALIAS {qinghuaimeng_skills_1} {
    #ALIAS {bei_skills} {$qhm_beiskills_1};
    #ALIAS {pfm} {$qhm_pfm_1};
    set wimpycmd hp ${char_id}\pfm;
    bei_skills;
}

#ALIAS {qinghuaimeng_skills_2} {
    #ALIAS {bei_skills} {$qhm_beiskills_2};
    #ALIAS {pfm} {$qhm_pfm_2};
    set wimpycmd hp ${char_id}\pfm;
    bei_skills;
}

#ALIAS {close_qinghuaimeng_triggers} {
    #FOREACH {otherquest_qinghuaimeng_1;otherquest_qinghuaimeng_2;otherquest_qinghuaimeng_3;otherquest_qinghuaimeng_4;otherquest_qinghuaimeng_5;otherquest_qinghuaimeng_6;otherquest_qinghuaimeng_7;otherquest_qinghuaimeng_8;otherquest_qinghuaimeng_9;otherquest_qinghuaimeng_10;otherquest_qinghuaimeng_11;otherquest_qinghuaimeng_12;otherquest_qinghuaimeng_13;otherquest_qinghuaimeng_14;otherquest_qinghuaimeng_15;otherquest_qinghuaimeng_16;otherquest_qinghuaimeng_17;otherquest_qinghuaimeng_18} {trigger} {
        #UNTRIGGER {$trigger};
    };
}

#ALIAS {open_qinghuaimeng_triggers} {
    #FOREACH {otherquest_qinghuaimeng_1;otherquest_qinghuaimeng_2;otherquest_qinghuaimeng_3;otherquest_qinghuaimeng_4;otherquest_qinghuaimeng_5;otherquest_qinghuaimeng_6;otherquest_qinghuaimeng_7;otherquest_qinghuaimeng_8;otherquest_qinghuaimeng_9;otherquest_qinghuaimeng_10;otherquest_qinghuaimeng_11;otherquest_qinghuaimeng_12;otherquest_qinghuaimeng_13;otherquest_qinghuaimeng_14;otherquest_qinghuaimeng_15;otherquest_qinghuaimeng_16;otherquest_qinghuaimeng_17;otherquest_qinghuaimeng_18} {trigger} {
        #TRIGGER {$trigger} {ON};
    };
}

/* 添加辅助函数，模拟Lua中的time()函数 */
#FUNCTION {time_in_seconds} {
    #FORMAT result {%T};
    #RETURN $result;
}

#CLASS {qinghuaimeng} {close}
