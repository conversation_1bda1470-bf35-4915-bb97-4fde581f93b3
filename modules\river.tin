#NOP {过河或者特别路径模块};
#NOP {飞渡标识};
#VARIABLE {river_du_flag} {0};
#NOP {过河的动作};
#VARIABLE {river_do} {dujiang};
#NOP {艄公们把踏脚板收起来，说了一声“坐稳喽”，长篙一点，渡船向江心驶去。(长江船上)};
#NOP {艄公们把踏脚板收了起来，长篙一点，渡船向江心驶去。(长江岸边)};
#NOP {艄公们把踏脚板收了起来，长篙一点，渡船向河心驶去。(黄河船上)};
#NOP {艄公们把踏脚板收起来，说了一声“坐稳喽”，长篙一点，渡船向河心驶去。(黄河岸边)};
#NOP {日月教众喊了一声“坐稳喽”，绞盘开始转动，竹篓缓缓移动。(竹篓外)};
#NOP {铜锣三响，崖顶的绞盘开始转动，竹篓缓缓移动。(竹篓内)};
#NOP {过河等船,%1:空-未定义,1-船在这边岸边,2-船在对面岸边};
#ALIAS {waitboat} {
  #LIST {rivercmds} {create} {$river_do};
  #CLASS boatclass KILL;
  #CLASS boatclass open;
  #ACTION {^{一艘渡船缓缓地驶了过来|一个大竹篓缓缓地降了下来|艄公们把踏脚板收了起来，长篙一点，渡船向江心驶去|艄公们把踏脚板收起来，说了一声“坐稳喽”，长篙一点，渡船向河心驶去|日月教众喊了一声“坐稳喽”}} {
    #VARIABLE {idle} {0};
    dohalt {$river_do}
  };
  #ACTION {^你一提内息} {
    #CLASS boatclass kill;
  };
  #ACTION {^你掏出%*递给船家，纵身跃上了渡船。} {
    #VARIABLE {idle} {0};
    #VARIABLE {workingflag} {1};
    #CLASS boatclass kill;
    i
  };
  #ACTION {^铜锣三响，崖顶的绞盘开始转动，竹篓缓缓移动。} {
    #VARIABLE {idle} {0};
    #VARIABLE {workingflag} {1};
    #CLASS boatclass kill;
    i
  };
  #CLASS boatclass close;
  #VARIABLE {dztick} {5};
  openwimpy;
  #IF {$hp[exp] < 2000000} {
    opensaving
  };
  #ELSE {
    closesaving
  };
  #IF {"%1" == "2"} {
    #DELAY {0.5} {
      waitlian;
      dzn {river_waitdo} {} {{timestamp}{@delayed{$dztick}}};
    };
  };
  #ELSE {
    #VARIABLE {workingflag} {1};
    #DELAY {0.5} {
      waitlian;
      dzn {river_waitdo};
    };
  };
};
#ALIAS {river_waitdo} {
  #VARIABLE {workingflag} {0};
  dohalt {
    $river_do;
    #IF {@contains{{rivercmds}{enter}} > 0} {
      echo {checkboat};
    };
  };
};
#NOP {过江};
#ALIAS {river_jiang} {
  #VARIABLE {river_do} {dujiang};
  #CLASS riverclass KILL;
  #CLASS boatclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkshore\"|你设定checkshore为反馈信息}} {
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"$room" != "长江北岸" && "$room" != "长江南岸" && "$room" != "澜沧江边"} {
      #CLASS riverclass KILL;
      dohalt {loc {walk}}
    };
    #ELSE {
      #DELAY {0.5} {        
        yell boat;
        $river_do;       
        look
      };
    };
  };
  #ACTION {^{你的修为不够！|你的内力修为不够，怎能支持！？}} {
    #VARIABLE {river_do} {enter};
    enter;
    look;
    echo {checkboat};
  };
  #ACTION {^{设定环境变量：action \= \"checkboat\"|你设定checkboat为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {@contains{{boatrooms}{$room}} == 0} {
      waitboat;
    };
    #ELSE {
      #VARIABLE {workingflag} {1};
    };
  };
  #ACTION {^{有船不坐，你想扮|你的真气不够}} {
    waitboat {1};
  };
  #ACTION {^{江|河}面太宽了，如果没有中途借力的地方根本没法飞越过去！} {
    waitboat {2};
  };
  #ACTION {^你在%*中渡船上轻轻一点，又提气飞纵向} {
    #CLASS boatclass KILL;
    #CLASS riverclass kill;
    i;
    dohalt {loc {walk}}
  };
  #ACTION {^渡船猛地一震，已经靠岸，船夫说道：“请大伙儿下船吧！} {
    #CLASS boatclass KILL;
    #CLASS riverclass kill;
    #VARIABLE {workingflag} {0};
    out;
    loc {walk}
  };
  #CLASS riverclass close;
  #DELAY {0.5} {
    echo {checkshore};
  };
};
#NOP {过河，%1:非空强制坐船,%2:后续指令};
#ALIAS {river_he} {
  #VARIABLE {river_do} {duhe};
  #IF {"%1" != ""} {
    #VARIABLE {river_do} {enter};
  };
  #CLASS riverclass KILL;
  #CLASS boatclass KILL;
  #CLASS riverclass open;
  #ACTION {^{设定环境变量：action \= \"checkshore\"|你设定checkshore为反馈信息}} {
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"$room" != "西夏渡口" && "$room" != "陕晋渡口" && "$room" != "大渡口"} {
      #CLASS riverclass KILL;
      dohalt {loc {walk}}
    };
    #ELSE {
      yell boat;
      $river_do;
      look;
      #IF {"%1" != ""} {
        echo {checkboat};
      };
    };
  };
  #ACTION {^{你的修为不够！|你的内力修为不够，怎能支持！？}} {
    #VARIABLE {river_do} {enter};
    enter;
    look;
    echo {checkboat};
  };
  #ACTION {^{设定环境变量：action \= \"checkboat\"|你设定checkboat为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {@contains{{boatrooms}{$room}} == 0} {
      waitboat;
    };
    #ELSE {
      #VARIABLE {workingflag} {1};
    };
  };
  #ACTION {^{有船不坐，你想扮|你的真气不够}} {
    waitboat {1};
  };
  #ACTION {^{江|河}面太宽了，如果没有中途借力的地方根本没法飞越过去！} {
    waitboat {2};
  };
  #ACTION {^你在%*中渡船上轻轻一点，又提气飞纵向} {
    #CLASS riverclass kill;
    #CLASS boatclass KILL;
    i;
    dohalt {loc {walk}}
  };
  #ACTION {^渡船猛地一震，已经靠岸，船夫说道：“请大伙儿下船吧！} {
    #CLASS riverclass kill;
    #CLASS boatclass KILL;
    #VARIABLE {workingflag} {0};
    out;
    #IF {"%2" != ""} {
      %2;
    };
    #ELSE {
      loc {walk}
    };
  };
  #CLASS riverclass close;
  #DELAY {0.5} {
    echo {checkshore};
  };
};
#NOP {坐船};
#NOP {只听得湖面不远处隐隐传来：“别急嘛，这儿正忙着呐……”};
#ALIAS river_boat {
  #VARIABLE {river_do} {
    yell boat;
    enter
  };
  #CLASS riverclass KILL;
  #CLASS riverclass open;
  #ACTION {^{设定环境变量：action \= \"checkshore\"|你设定checkshore为反馈信息}} {
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"$room" != "岸边" && "$room" != "码头" && "$room" != "小岛边" && "$room" != "汉水东岸" && "$room" != "汉水西岸"} {
      #CLASS riverclass KILL;
      dohalt {loc {walk}}
    };
    #ELSE {
      #VARIABLE {hasboat} {1};
      $river_do;
      look;
      echo {checkboat};
    };
  };
  #ACTION {^艄公说“到啦，上岸吧”，随即把一块踏脚板搭上堤岸。} {
    #CLASS riverclass kill;
    #VARIABLE {workingflag} {0};
    out;
    loc {walk};
  };
  #ACTION {^艄公轻声说道：“都下船吧，我也要回去了。”} {
    #CLASS riverclass kill;
    #VARIABLE {workingflag} {0};
    out;
    loc {walk};
  };
  #ACTION {^{设定环境变量：action \= \"checkboat\"|你设定checkboat为反馈信息}} {
    #IF {@contains{{boatrooms}{$room}} == 0} {
      waitboat;
    };
    #ELSE {
      #VARIABLE {workingflag} {1};
    };
  };
  #CLASS riverclass close;
  echo {checkshore};
};
#NOP {等待不忙,%1:后续指令};
#ALIAS {river_halt} {
  #CLASS riverclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkhalt\"|你设定checkhalt为反馈信息}} {
    #CLASS riverclass KILL;
    dohalt {
      #IF {"%1" != ""} {
        %1
      };
      echo {nextstep}
    }
  };
  #CLASS riverclass CLOSE;
  echo {checkhalt}
};
#NOP {慕容地图qu};
#ALIAS {river_qu} {
  #CLASS riverclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkshore\"|你设定checkshore为反馈信息}} {
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"$room" != "岸边" && "$room" != "码头" && "$room" != "小岛边"} {
      #CLASS riverclass KILL;
      dohalt {loc {walk}}
    };
    #ELSE {
      qu %1;
    };
  };
  #ACTION {^你把钱交给船家，船家领你上了一条小舟。} {
    #VARIABLE {workingflag} {1};
    i;
    look
  };
  #ACTION {^{终于到了小岛边，船夫把小舟靠在岸边，快下船吧。|船夫把小舟靠在岸边，快下船吧。}} {
    #CLASS riverclass kill;
    #VARIABLE {workingflag} {0};
    out;
    loc {walk}
  };
  #CLASS riverclass close;
  echo {checkshore};
};
#NOP {小筑弹琴去山庄};
#ALIAS {river_tanqin} {
  #CLASS riverclass KILL;
  #CLASS riverclass open;
  #ACTION {^{设定环境变量：action \= \"checkshore\"|你设定checkshore为反馈信息}} {
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"$room" != "听雨居"} {
      #CLASS riverclass KILL;
      dohalt {loc {walk}}
    };
    #ELSE {
      tan qin;
    };
  };
  #ACTION {^你弹了一下琴，突然脚下一空，掉了下去。} {
    #VARIABLE {workingflag} {1};
    i
  };
  #ACTION {^终于到了岸边，船夫把小舟靠在岸边，快下船吧。} {
    #CLASS riverclass kill;
    #VARIABLE {workingflag} {0};
    out;
    loc {walk};
  };
  #CLASS riverclass close;
  echo {checkshore};
};
#NOP {进桃花岛};
#ALIAS {river_thd_in} {
  #LOCAL {qmbglv} {0};
  #IF {"$kungfu[know][qimen-bagua]" != ""} {
    #LOCAL {qmbglv} {$kungfu[know][qimen-bagua][lv]};
  };
  #CLASS riverclass KILL;
  #CLASS riverclass open;
  #ACTION {^{设定环境变量：action \= \"checkshore\"|你设定checkshore为反馈信息}} {
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"$room" != "海滨"} {
      #CLASS riverclass KILL;
      dohalt {loc {walk}}
    };
    #ELSE {
      #VARIABLE {workingflag} {1};
      l rock;
      jump boat
    };
  };
  #ACTION {^船「咣」的一声撞到了岸边，你急忙纵身跳上了岸。} {
    #CLASS riverclass KILL;
    #VARIABLE {workingflag} {0};
    loc {walk}
  };
  #CLASS riverclass close;
  #IF {"$hp[party]" != "桃花岛" && @getSkillLevel{qimen-bagua} < 50} {
    #CLASS riverclass KILL;
    doabort
  };
  #ELSE {
    echo {checkshore};
  };
};
#NOP {出桃花岛};
#ALIAS {river_thd_out} {
  #CLASS riverclass KILL;
  #CLASS riverclass open;
  #ACTION {^你向黄药师打听有关『leave』的消息。} {
    #CLASS riverclass KILL;
    loc {dohalt {walk}}
  };
  #ACTION {^这里没有这个人。} {
    #CLASS riverclass KILL;
    #DELAY {2} {
      matrix_thd_out
    };
  };
  #CLASS riverclass close;
  ask yaoshi about leave;
};
#NOP {进天山};
#ALIAS {river_ts_in} {
  #VARIABLE {checkcount} {0};
  #CLASS riverclass KILL;
  #CLASS riverclass open;
  #ACTION {^{设定环境变量：action \= \"checkshore\"|你设定checkshore为反馈信息}} {
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"$room" != "百丈涧"} {
      #CLASS riverclass KILL;
      dohalt {loc {walk}}
    };
    #ELSE {
      wcwp;
      ge tielian;
    };
  };
  #ACTION {^你{得用件锋利的器具才能砍断铁链|手上这件兵器无锋无刃}} {
    #MATH {checkcount} {$checkcount + 1};
    #IF {$checkcount >= 5} {
      #IF {"$conf[weapon][chop]" != "" && @carryqty{$conf[weapon][chop]} == 0} {
        #CLASS riverclass KILL;
        doabort;
      };
    };
    #ELSE {
      #DELAY {1} {
        dohalt {
          wcwp;
          ge tielian;
        };
      };
    };
  };
  #ACTION {^{你握紧手中|铁链不是已经斩断了吗}} {
    dohalt {
      tiao duimian;
    };
  };
  #ACTION {^你手握铁链，把心一横冲着对面悬崖跳了过去。人在半空，真气一浊，身形急速下坠,} {
    #CLASS riverclass kill;
    dohalt {
      wwp;
      loc {walk};
    }
  };
  #CLASS riverclass close;
  echo {checkshore};
};
#NOP {出天山};
#ALIAS {river_ts_out} {
  #CLASS riverclass KILL;
  #CLASS riverclass open;
  #ACTION {^{设定环境变量：action \= \"checkshore\"|你设定checkshore为反馈信息}} {
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"$room" != "仙愁门"} {
      #CLASS riverclass KILL;
      dohalt {loc {walk}}
    };
    #ELSE {
      tiao duimian
    };
  };
  #ACTION {^你向后退了几步，往前急速越了几步，借着上下坡的重心下移，人在半空} {
    #CLASS riverclass kill;
    dohalt {
      loc {walk}
    }
  };
  #CLASS riverclass close;
  echo {checkshore};
};
#NOP {进绝情谷};
#ALIAS {river_jqg_in} {
  #CLASS riverclass open;
  #ACTION {^{设定环境变量：action \= \"checkshore\"|你设定checkshore为反馈信息}} {
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"$room" != "小溪边"} {
      #CLASS riverclass KILL;
      dohalt {loc {walk}}
    };
    #ELSE {
      l boat;
      jump boat;
    };
  };
  #ACTION {^你要看什么} {
    #DELAY {2} {
      l boat;
      jump boat;
    }
  };
  #ACTION {^你吸了口气，纵身向小舟上跳将过去} {
    #VARIABLE {workingflag} {1};
  };
  #ACTION {^又划出三四里，溪心忽有九块大石迎面耸立，犹如屏风一般，挡住了来船去路。} {
    #CLASS riverclass kill;
    #VARIABLE {workingflag} {0};
    out;
    loc {walk};
  };
  #CLASS riverclass close;
  echo {checkshore};
};
#NOP {出绝情谷};
#ALIAS {river_jqg_out} {
  #CLASS riverclass KILL;
  #CLASS riverclass open;
  #ACTION {^{设定环境变量：action \= \"checkshore\"|你设定checkshore为反馈信息}} {
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"$room" != "独峰岭"} {
      #CLASS riverclass KILL;
      dohalt {loc {walk}}
    };
    #ELSE {
      uwwp;
      l boat;
      tui boat;
      jump boat;
    };
  };
  #ACTION {^你拿着兵刃怎么推动小舟？} {
    #DELAY {0.5} {
      uwwp;
      l boat;
      tui boat;
      jump boat
    }
  };
  #ACTION {^你要看什么} {
    #DELAY {2} {
      l boat;
      jump boat;
    }
  };
  #ACTION {^你吸了口气，纵身向小舟上跳将过去} {
    #VARIABLE {workingflag} {1};
  };
  #ACTION {^又划出三四里，溪流曲折，小舟经划过了几个弯后又回到溪边。} {
    #CLASS matrixclass KILL;
    #VARIABLE {workingflag} {0};
    out;
    wwp;
    loc {walk}
  };
  #CLASS riverclass close;
  echo {checkshore};
};
#NOP {绝情谷内谷};
#ALIAS {river_jqg_inside} {
  #CLASS riverclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^这里没有这个人。} {
    #CLASS riverclass KILL;
  };
  #ACTION {^公孙止%*既然对绝情谷甚有兴趣，就请随便看看吧} {
    dohalt {
      xian hua;
      zuan dao
    }
  };
  #ACTION {^公孙止说道：「%*功夫还太弱，绝情谷中甚为危险，还是不要在此久留了。」} {
    dohalt {
      doabort
    };
  };
  #ACTION {^这里是绝情谷的禁地，没有庄主的命令，外来弟子不能进入！！} {
    ask gongsun zhi about 绝情谷;
  };
  #ACTION {^你弓身钻进密道。} {
    #CLASS riverclass KILL;
    loc {walk};
  };
  #CLASS riverclass CLOSE;
  xian hua;
  zuan dao;
};
#NOP {进神龙岛};
#ALIAS {river_sld_in} {
  #VARIABLE {boatflag} {0};
  #VARIABLE {riverdo} {
    wcwp;
    chop tree;
    wwp;
    bang mu tou;
    zuo mufa;
    echo {checkboat};
  };
  #CLASS riverclass KILL;
  #CLASS riverclass open;
  #ACTION {^{设定环境变量：action \= \"checkshore\"|你设定checkshore为反馈信息}} {
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"$room" != "海滩"} {
      #CLASS riverclass KILL;
      dohalt {loc {walk}}
    };
    #ELSEIF {"$id[things][cu shengzi]" == ""} {
      #CLASS riverclass KILL;
      gotodo {塘沽城} {钱庄} {
        balanceex {2} {50} {
          gotodo {$aimcity} {$aimroomid} {$aimdo};
        };
      };
    };
    #ELSE {
      $riverdo;
    };
  };
  #ACTION {^只见你轻轻一跃，已坐在木筏上。} {
    #VARIABLE {boatflag} {1};
    #VARIABLE {workingflag} {1};
  };
  #ACTION {^小木筏顺着海风，一直向东飘去。} {
    #VARIABLE {idle} {0};
    hua mufa
  };
  #ACTION {^你回头一看，小木筏撞得散架，沉到海里了。} {
    #VARIABLE {workingflag} {0};
    dohalt {
      wwp;
      loc {walk}
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkboat\"|你设定checkboat为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {$boatflag == 0} {
      #DELAY {1} {$riverdo};
    };
    #ELSE {
      hua mufa
    };
  };
  #CLASS riverclass close;
  #IF {"$id[things][cu shengzi]" == ""} {
    buy shengzi;
    i;
  };
  echo {checkshore};
};
#NOP {出神龙岛};
#ALIAS {river_sld_out} {
  #VARIABLE {checkcount} {0};
  #VARIABLE {boatflag} {0};
  #CLASS riverclass KILL;
  #CLASS riverclass open;
  #ACTION {^{设定环境变量：action \= \"checklu\"|你设定checklu为反馈信息}} {
    resonate {checklu};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"$room" != "陆府正厅"} {
      #CLASS riverclass KILL;
      #DELAY {1} {loc {walk}}     
    };
    #ELSE {
      #DELAY {1} {steal lingpai}      
    };
  };
  #ACTION {^你{成功地偷到了|已经有了令牌}} {
    ensure {
      out;
      #3 s;
      give ling pai to chuan fu;
    } {checkboat}
  };
  #ACTION {^陆高轩一脚把{$hp[name]}踢了出去！} {
    yun qi;
    hp;
    enter;
    #DELAY {0.5} {steal lingpai};
  };
  #ACTION {^你给船夫一块通行令牌} {
    #VARIABLE {boatflag} {1};
  };
  #ACTION {^你身上没有这样东西。} {
    #VARIABLE {boatflag} {2};
  };
  #ACTION {^{设定环境变量：action \= \"checkboat\"|你设定checkboat为反馈信息}} {
    resonate {checkboat};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {$boatflag != 1} {
      #DELAY {0.5} {
        loc {walk};
      };
    };
    #ELSE {
      #VARIABLE {checkcount} {0};
      #VARIABLE {boatflag} {0};
      #DELAY {1} {
        echo {checkorder};
      };
    };
  };
  #ACTION {^你轻轻一跃，上了小船。} {
    #VARIABLE {boatflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkorder\"|你设定checkorder为反馈信息}} {
    #MATH {checkcount} {$checkcount + 1};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {$boatflag == 1} {
      #VARIABLE {workingflag} {1};
      order 开船
    };
    #ELSEIF {$checkcount < 5} {
      #DELAY {1} {
        echo {checkorder};
      };
    };
    #ELSE {
      #CLASS riverclass KILL;
      loc {dohalt {walk}};
    };
  };
  #ACTION {^你轻轻一跃，下了船。} {
    #CLASS riverclass KILL;
    #VARIABLE {workingflag} {0};
    loc {walk}
  };
  #CLASS riverclass close;
  #3 n;
  enter;
  echo {checklu} {2};
};
#NOP {黑木崖耳语};
#ALIAS {river_hmy_flatter} {
  #VARIABLE {okflag} {0};
  #VARIABLE {jiabuid} {jia};
  #class riverclass KILL;
  #class riverclass open;
  #ACTION {^你要对谁耳语？} {
    #VARIABLE {okflag} {2};
  };
  #ACTION {^你在%*的耳边悄声说道：教主千秋万载，一统江湖} {
    #IF {"%%1" != "贾布"} {
      #IF {"$jiabuid" == "jia"} {
        #VARIABLE {jiabuid} {bu};
      };
      #ELSE {
        #VARIABLE {jiabuid} {jia};
      };
    };
  };
  #ACTION {^那道大石门缓缓移了开来，一个日月教徒说道：“请进。”} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkwhisper\"|你设定checkwhisper为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {$okflag == 2} {
      #class riverclass KILL;
      loc {abort}
    };
    #ELSEIF {$okflag == 1} {
      #class riverclass KILL;
      dohalt {
        wu;
        loc {walk}
      }
    };
    #ELSE {
      whisper $jiabuid 教主文成武德，一统江湖;
      whisper $jiabuid 教主千秋万载，一统江湖;
      whisper $jiabuid 属下忠心为主，万死不辞;
      whisper $jiabuid 教主令旨英明，算无遗策;
      whisper $jiabuid 教主烛照天下，造福万民;
      whisper $jiabuid 教主战无不胜，攻无不克;
      whisper $jiabuid 日月神教文成武德、仁义英明;
      whisper $jiabuid 教主中兴圣教，泽被苍生;
      #DELAY {0.5} {echo {checkwhisper}}
    };
  };
  #class riverclass close;
  echo {checkwhisper}
};
#NOP {上黑木崖};
#ALIAS {river_hmy_up} { 
  #VARIABLE {river_do} {zong};
  #class riverclass KILL;
  #CLASS riverclass open;
  #ACTION {^{你的修为不够！|你的内力修为不够，怎能支持！？}} {
    #VARIABLE {river_do} {enter};
    enter;
    echo {checkboat};
  };
  #ACTION {^你一提内息，看准了崖间竹篓位置} {
    #CLASS riverclass KILL;
    dohalt {loc {walk}}
  } {1};
  #ACTION {^{设定环境变量：action \= \"checkboat\"|你设定checkboat为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {@contains{{boatrooms}{$room}} == 0} {
      waitboat;
    };
    #ELSE {
      #VARIABLE {workingflag} {1};
    };
  };
  #ACTION {^峭壁实在太陡了，如果没有中途借力的地方根本没法纵身{上|下}去！} {
    waitboat {2}
  };
  #ACTION {^有竹篓就坐上去吧} {
    waitboat {1}
  };
  #ACTION {^竹篓晃了几下，在一间石屋之内停了下来。} {
    #CLASS riverclass kill;
    #VARIABLE {workingflag} {0};
    out;
    loc {walk}
  };
  #CLASS riverclass close;
  yell shangya;
  $river_do
};
#NOP {下黑木崖};
#ALIAS {river_hmy_down} {
  #VARIABLE {river_do} {zong};
  #class riverclass KILL;
  #CLASS riverclass open;
  #ACTION {^{你的修为不够！|你的内力修为不够，怎能支持！？}} {
    #VARIABLE {river_do} {enter};
    enter;
    echo {checkboat};
  };
  #ACTION {^你一提内息，看准了崖间竹篓位置} {
    #CLASS riverclass KILL;
    dohalt {loc {walk}}
  } {1};
  #ACTION {^{设定环境变量：action \= \"checkboat\"|你设定checkboat为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {@contains{{boatrooms}{$room}} == 0} {
      waitboat;
    };
    #ELSE {
      #VARIABLE {workingflag} {1};
    };
  };
  #ACTION {^峭壁实在太陡了，如果没有中途借力的地方根本没法纵身{上|下}去！} {
    waitboat
  };
  #ACTION {^有竹篓就坐上去吧} {
    waitboat
  };
  #ACTION {^竹篓晃了几下，在一间石屋之内停了下来。} {
    #CLASS riverclass kill;
    #VARIABLE {workingflag} {0};
    out;
    loc {walk}
  };
  #CLASS riverclass close;
  yell xiaya;
  $river_do
};
#NOP {宝象拦路};
#ALIAS {river_baoxiang} {
  #CLASS riverclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkhalt\"|你设定checkhalt为反馈信息}} {
    #CLASS riverclass KILL;
    dohalt {echo {nextstep}};
  };
  #CLASS riverclass CLOSE;
  #IF {"$hp[sex]" != "f"} {
    runwait {
      echo {nextstep};
    };
  };
  #ELSE {
    echo {checkhalt};
  };
};
#NOP {古墓从卧室出门};
#ALIAS {river_gm_wsout} {
  #CLASS riverclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkcondition\"|你设定checkcondition为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {@getSkillLevel{force} < 100 && $hp[exp] < 165000} {
      #CLASS riverclass KILL;
      #NOP {这里走不了，直接自杀吧};
      #DELAY {1} {
        #VARIABLE {afterdeath} {
          gotodo {$aimcity} {$aimroomid} {$aimdo}
        };
        s;w;w;out;s;#4 tiao xuanya;
      };
    };
    #ELSEIF {$hp[neili] < 500} {
      #DELAY {6} {
        hp;
        echo {checkcondition};
      };
    };
    #ELSE {
      tang bed;
      ban shiban
    };
  };
  #ACTION {^你使出吃奶的劲力，但石板却纹丝不动。} {
    #VARIABLE {idle} {0};
    #DELAY {5} {
      hp;
      tang bed;ban shiban
    }
  };
  #ACTION {^你用力扳动突起的石板，只听得轧轧几响，石床已落入下层石室。} {
    #CLASS riverclass KILL;
    #DELAY {0.5} {
      loc {
        walk
      };
    };
  };
  #CLASS riverclass close;
  hp;
  skills;
  echo {checkcondition};
};
#NOP {古墓进入暗河};
#ALIAS {river_gm_anhe} {
  #CLASS riverclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^你凝望著那幅图，心中不由大喜，原来那绘的正是出墓的秘道。} {
    #CLASS riverclass KILL;
    dohalt {
      #SEND {walk down};
      loc {walk}
    }
  };
  #CLASS riverclass close;
  l map
};
#NOP {大轮寺山门};
#ALIAS {river_dls} {
  #CLASS riverclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkgate\"|你设定checkgate为反馈信息}} {
    dohalt {
      nu;
      loc {walk}
    };
  };
  #CLASS riverclass CLOSE;
  knock gate;
  echo {checkgate};
};

#NOP {绝情谷鳄鱼潭};
#ALIAS {river_jqg_eyutan} {
  #VARIABLE {okflag} {0};
  #class riverclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^你要对谁做这个动作？} {
    #VARIABLE {idle} {0};
    #DELAY {5} {hi e yu;}
  };
  #ACTION {^左首一条鳄鱼怪嗷一声，一个筋斗翻入渊中。} {
    kill e yu;
  };
  #ACTION {^鳄鱼「啪」的一声倒在地上，挣扎着抽动了几下就死了。} {
    kill e yu;
  };
  #ACTION {^这里没有这个人。} {
    dohalt {
      ta corpse;
      echo {checkok};
    };
  };
  #ACTION {^你右足踏在死鳄肚上，借劲跃起，接著左足在鳄鱼的背上一点} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkok\"|你设定checkok为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {$okflag == 1} {
      #DELAY {1} {
        loc {walk};
      };
    };
    #ELSE {
      hi e yu;
    };
  };
  #CLASS riverclass CLOSE;
  #DELAY {1} {
    dohalt {
      ta corpse;
      echo {checkok};
    };
  };
};

#NOP {绝情谷山洞};
#ALIAS {river_jqg_shandong} {
  pa down;
  dohalt {
    loc {walk};
  };
};

#NOP {绝情谷石窟};
#ALIAS {river_jqg_shiku} {
  #VARIABLE {riverdo} {zhe shugan};
  #class riverclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^你把树皮搓绞成索，费尽了力气，才把树皮搓成一条极长的索子。} {
    dohalt {zhe shugan}
  };
  #ACTION {^你折下一根枣树的枝干，长约一丈五尺。} {
    dohalt {climb shibi}
  };
  #ACTION {^你屏住呼吸，纵上石壁，一路向上攀援。} {
    #CLASS riverclass KILL;
    loc {walk};
  };
  #CLASS riverclass CLOSE;
  dohalt {
    drop shupi suo;
    drop zaoshu gan;
    #10 bo shupi;
    cuo shupi;
  };
};

#NOP {绝情谷石窟};
#ALIAS {river_jqg_shibi} {
  #class riverclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^你双足使出千斤坠功夫，牢牢踏在石壁之上，双臂运劲，将树干摔出洞穴。} {
    dohalt {climb up}
  };
  #ACTION {^你双手抓着绳索，交互上升，上升得更快了，不一会儿已然爬出洞穴。} {
    #CLASS riverclass KILL;
    loc {walk};
  };
  #CLASS riverclass CLOSE;
  fu shugan;
  shuai shugan;
};
#NOP {绝情谷峭壁};
#ALIAS {river_jqg_qiaobi} {
  #class riverclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^{你仔仔细细地把树藤|绳子已经绑好了}} {
    pa down
  };
  #ACTION {^你颤颤噤噤地爬了下去。} {
    #CLASS riverclass KILL;
    dohalt {
      loc {walk}
    }
  };
  #CLASS riverclass CLOSE;
  #DELAY {1} {
    #9 cuo shupi;
    bang song;
  }
};
#NOP {绝情谷崖壁,%1:方向};
#ALIAS {river_jqg_yabi_pa} {
  #CLASS riverclass OPEN;
  #ACTION {^你颤颤噤噤地爬了{上|下}去。} {
    #CLASS riverclass KILL;
    dohalt {
      loc {walk}
    }
  };
  #CLASS riverclass CLOSE;
  pa %1
};
#NOP {绝情谷跳水潭};
#ALIAS {river_jqg_tiaotan} {
  #class riverclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkshore\"|你设定checkshore为反馈信息}} {
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"$room" != "水潭岸边" && "$room" != "谷底"} {
      #CLASS riverclass KILL;
      dohalt {loc {walk}}
    };
    #ELSE {
      i;
      echo {checkweight};
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkweight\"|你设定checkweight为反馈信息}} {
    #VARIABLE {idle} {0};
    #DELAY {0.2} {
      #IF {$id[weight] >= 100} {
        drop stone;
        i;
        echo {checkweight};
      };
      #ELSEIF {$id[weight] < 70} {
        jian shi;
        i;
        echo {checkweight};
      };
      #ELSE {
        tiao tan
      };
    };
  };
  #ACTION {^你扑通一声，涌身跳入了水潭。} {
    #CLASS riverclass KILL;
    dohalt {
      loc {walk}
    }
  };
  #CLASS riverclass CLOSE;
  echo {checkshore};
};
#NOP {绝情谷水潭潜,%1:方向};
#NOP {由于重力不够，你无法继续下潜! };
#ALIAS {river_jqg_shuitan_qian} {
  #class riverclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^你{一个猛栽|两腿用力一蹬|手脚齐划}} {
    #CLASS riverclass KILL;
    dohalt {
      loc {walk};
    };
  };
  #ACTION {^由于重力不够，你无法继续下潜!} {
    qian up;
    dohalt {
      pa up;
      loc {walk};
    }
  };
  #ACTION {^你身子沉重，用尽全力也无法} {
    drop silver;
    #5 drop stone;
    drop coin;
    walk;
  };
  #ACTION {^你要往哪里潜？} {
    #CLASS riverclass KILL;
    #IF {"%1" == "up"} {
      pa up;
      loc {walk}
    };
    #ELSE {
      #IF {($aimroomid >= 3320 && $aimroomid <= 3326) || ($aimroomid >= 3346 && $aimroomid <= 3347)} {
        river_jqg_shuitan_qian {zuoshang}
      };
      #ELSE {
        river_jqg_shuitan_qian {up}
      };
    };
  };
  #CLASS riverclass CLOSE;
  #IF {"%1" != "down"} {
    #10 drop stone;
    i
  };
  qian %1
};
#NOP {逍遥派直达成都};
#ALIAS {river_xytocd} {
  #CLASS riverclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkcd\"|你设定checkcd为反馈信息}} {
    #CLASS riverclass KILL;
    #IF {"$room" == "高山脚下"} {
      #VARIABLE {city} {成都城};
      #VARIABLE {roomid} {1021};
    };
    dohalt {
      walk;
    };
  };
  push door;
  echo {checkcd};
};
#NOP {归云庄过河};
#ALIAS {river_gyz_xiaohe} {
  #CLASS riverclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^你踩着水底的暗桩，慢慢的走过了小河。} {
    #CLASS riverclass KILL;
    dohalt {
      loc {walk}
    };
  };
  #CLASS riverclass CLOSE;
  ask lao zhe about 裘千丈;
  jump river;
};
#NOP {武当后山茅屋};
#ALIAS {river_wd_maowu} {
  #CLASS riverclass OPEN;
  #ACTION {^你拨开树枝，一弯腰，钻了进去。} {
    #CLASS riverclass KILL;
    loc {walk};
  };
  #ACTION {^你打听清楚再钻吧！} {
    #CLASS riverclass KILL;
    dohalt {
      openwudangmaowu {gotodo {$aimcity} {$aimroomid} {$aimdo}} {doabort};
    };
  };
  #CLASS riverclass CLOSE;
  zuan shulin
};
#NOP {武当后山进丛林,这里要判断是否满足条件进去,不然摔伤};
#ALIAS {river_wd_houshan} {
  #IF {@getSkillLevel{dodge} < 100} {
    doabort
  };
  #ELSEIF {@getSkillLevel{hand} < 80} {
    #NOP {补手法技能};
    #VARIABLE {aimbackup} {
      {city}{$aimcity}
      {room}{$aimroom}
      {roomid}{$aimroomid}
      {do}{$aimdo}
    };
    runwait {loc {learnhand {restorewalk}}}
  };
  #ELSE {
    runwait {
      hold teng;
      tiao down;
      #DELAY {0.5} {
        loc {walk}
      };
    }
  };
};
#NOP {武当后山丛林出来};
#ALIAS {river_wd_conglin} {
  #class riverclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^你随波逐流，终于发现了岸边，浑身湿漉漉的爬上了汉水西岸} {
    #CLASS riverclass KILL;
    loc {walk}
  };
  #CLASS riverclass CLOSE;
  tiao river
};
#NOP {见一灯-渔};
#ALIAS {river_yideng_yu} {
  #VARIABLE {okflag} {0};
  #CLASS riverclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^你向渔人打听有关『一灯大师』的消息。} {
    #CLASS yidengclass KILL;
    #CLASS yidengclass OPEN;
    #ACTION {^渔人说道：「我不是已经告诉你上山的方法了么} {
      #CLASS yidengclass KILL;
      #CLASS riverclass KILL;
      dohalt {
        #IF {@carryqty{tie zhou} == 1 && @carryqty{tie jiang} == 1} {
          zhi tie zhou;
          wield jiang;
          hua boat;
        };
        #ELSE {
          doquit;
        };
      };
    };
    #ACTION {^{渔人说道：「要见我师傅到也不难|渔人说道：「我让你去找的金娃娃呢？」}} {
      #CLASS yidengclass KILL;
      dohalt {
        l pubu;
        tiao pubu;
        echo {checkpubu}
      };
    };
    #CLASS yidengclass CLOSE;
  };
  #ACTION {^瀑布中已经有人了，你再下去太危险了} {
    #VARIABLE {okflag} {2};
  };
  #ACTION {^你的身法不够，跳下去太危险了！} {
    #VARIABLE {okflag} {3};
  };
  #ACTION {^只见那瀑布奔腾而去，水沫四溅，你不由看得目眩心惊} {
    #VARIABLE {okflag} {4};
  };
  #ACTION {^你双足使劲，以千斤坠功夫牢牢站稳石上，恰以中流砥柱，屹立不动，闭气凝息。} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkpubu\"|你设定checkpubu为反馈信息}} {
    #VARIABLE {idle} {0};
    #SWITCH {$okflag} {
      #CASE {1} {
        echo {checkyideng};
      };
      #CASE {3} {
        #CLASS yidengclass KILL;
        #CLASS riverclass KILL;
        doabort;
      };
      #CASE {4} {
        #NOP {这里可能是没有问过渔人或已经抓到金娃娃了};
        i;
        echo {checkwawa}
      };
      #DEFAULT {
        #DELAY {2} {
          dohalt {
            l pubu;
            tiao pubu;
            echo {checkpubu}
          };
        };
      };
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkwawa\"|你设定checkwawa为反馈信息}} {
    #IF {@carryqty{jin wawa} == 1} {
      give jin wawa to yu ren
    };
    #ELSE {
      uwwp;
      i;
      ask yu ren about 一灯大师
    };
  };
  #ACTION {^你实在支撑不下去了，纵身跳回了瀑布岸边。} {
    #CLASS yidengclass KILL;
    #CLASS riverclass KILL;
    #DELAY {2} {
      doabort;
    };
  };
  #ACTION {^你用%*来抓金娃娃么} {
    uwwp
  };
  #ACTION {^{设定环境变量：action \= \"checkyideng\"|你设定checkyideng为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {"$id[things][jin wawa]" == ""} {
      #DELAY {2} {
        yun qi;
        zhua yu;
        i;
        echo {checkyideng}
      }
    };
    #ELSE {
      dohalt {
        tiao anbian;
        give jin wawa to yu ren
      };
    };
  };
  #ACTION {^渔人给了你一柄铁桨。} {
    #CLASS yidengclass KILL;
    #CLASS riverclass KILL;
    river_yideng_boat;
  };
  #CLASS riverclass CLOSE;
  uwwp;
  i;
  ask yu ren about 一灯大师
};
#ALIAS {river_yideng_boat} {
  #VARIABLE {checkcount} {0};
  #VARIABLE {boatflag} {0};
  #VARIABLE {river_do} {
    zhi tie zhou;
    wwp {tie jiang};
    hua boat;
    echo {checkboat};
  };
  #CLASS riverclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^你左手铁桨急忙挥出} {
    #VARIABLE {boatflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkboat\"|你设定checkboat为反馈信息}} {
    #VARIABLE {idle} {-30};
    #IF {$boatflag == 0} {
      #MATH {checkcount} {$checkcount + 1};
      #IF {$checkcount < 10} {
        #DELAY {2} {
          $river_do;
        };
      };
      #ELSE {
        #NOP {如果是解谜则两小时后再来，可能有人一直卡在里面};
        #IF {"$questmodule" != ""} {
          questdelay {$questmodule} {0} {7200}
        };
        loc {
          doquit;
        };
      };
    };
  };
  #ACTION {^那铁舟缓缓向前驶去，绿柳丛间时有飞鸟鸣啭，忽然钻入了一个山洞。} {
    #CLASS riverclass KILL;
    tiao shandong;
    wwp;
    loc {walk}
  };
  #CLASS riverclass CLOSE;
  $river_do;
};
#NOP {见一灯-樵};
#ALIAS {river_yideng_qiao} {
  #VARIABLE {checkcount} {0};
  #VARIABLE {riverdo} {
    answer 青山相待，白云相爱。梦不到紫罗袍共黄金带。一茅斋，野花开，管甚谁家兴废谁成败？陋巷单瓢亦乐哉。贫，气不改！达，志不改！;
    pa teng;
    echo {checkyideng};
  };
  #CLASS riverclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkshore\"|你设定checkshore为反馈信息}} {
    #IF {"$room" != "岸边"} {
      #CLASS riverclass KILL;
      dohalt {loc {walk}};
    };
    #ELSE {
      $riverdo;
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkyideng\"|你设定checkyideng为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {"$room" != "山顶"} {
      #MATH {checkcount} {$checkcount + 1};
      #IF {$checkcount <= 5} {
        #DELAY {2} {
          $riverdo
        };
      };
      #ELSE {
        #VARIABLE {checkcount} {0};
        enter;
        out;
        $riverdo;
      };
    };
    #ELSE {
      #CLASS riverclass KILL;
      loc {walk}
    };
  };
  #CLASS riverclass CLOSE;
  echo {checkshore};
};
#NOP {见一灯-耕};
#ALIAS {river_yideng_geng} {
  #CLASS riverclass OPEN;
  #ACTION {^{农夫双手托住大石|你已经帮农夫托起石块了}} {
    #CLASS riverclass KILL;
    #DELAY {2} {
      dohalt {
        e;
        loc {walk}
      };
    }
  };
  #CLASS riverclass CLOSE;
  #VARIABLE {idle} {-30};
  startfull {tuo shi}
};
#NOP {见一灯-石梁};
#ALIAS {river_yideng_liang} {
  #VARIABLE {riverdo} {
    tiao front;
    hp;
    echo {checkyideng};
  };
  #class riverclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkyideng\"|你设定checkyideng为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {"$room" == "石梁尽头"} {
      #CLASS riverclass KILL;
      loc {walk};
    };
    #ELSEIF {$hp[neili] < $threshold_neili} {
      #CLASS riverclass KILL;
      startfull {loc {walk}}
    };
    #ELSE {
      $riverdo
    };
  };
  #CLASS riverclass CLOSE;
  $riverdo
};
#NOP {见一灯-读};
#ALIAS {river_yideng_du} {
  #class riverclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^你向书生打听有关『一灯大师』的消息。} {
    #CLASS yidengclass KILL;
    #CLASS yidengclass OPEN;
    #ACTION {^书生说道：「{我出三道题目考考你|你可回答的出我的问题}} {
      #CLASS yidengclass KILL;
      dohalt {
        ask shu sheng about 题目;
      };
    };
    #ACTION {^书生说道：「%*我师傅就在前面不远处的石屋里} {
      #CLASS yidengclass KILL;
      #CLASS riverclass KILL;
      dohalt {
        n;
        loc {walk}
      };
    };
    #CLASS yidengclass CLOSE;
  };
  #ACTION {^你向书生打听有关『题目』的消息。} {
    dohalt {
      answer 辛未状元;
      answer 霜凋荷叶，独脚鬼戴逍遥巾;
      answer 魑魅魍魉，四小鬼各自肚肠
    };
  };
  #ACTION {^书生大惊，站起身来，长袖一挥，向你一揖到地} {
    #CLASS riverclass KILL;
    dohalt {
      n;
      loc {walk}
    };
  };
  #CLASS riverclass CLOSE;
  ask shu sheng about 一灯大师;
};
#NOP {见东方不败};
#ALIAS {river_hmy_dfbb} {
  #class riverclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkkey\"|你设定checkkey为反馈信息}} {
    #VARIABLE {idle} {0};
    #CLASS riverclass KILL;
    #IF {$hp[exp] < 1500000} {
      doabort
    };
    #ELSEIF {@carryqty{hei yaoshi} == 0} {
      runwait {
        loc {
          goheiyaoshi {gotodo {$aimcity} {$aimroom} {$aimdo}}
        }
      }
    };
    #ELSE {
      tui qiang;
      open men;
      d;
      loc {walk}
    };
  };
  #CLASS riverclass CLOSE;
  i;
  echo {checkkey};
};
#NOP {黄河萧府};
#ALIAS {river_hh_xiaofu} {
  #VARIABLE {okflag} {1};
  #VARIABLE {checkcount} {0};
  #class riverclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^张妈一手把你抓住，说道：“一点规矩也不懂！”} {
    #VARIABLE {okflag} {0};
  };
  #ACTION {^{设定环境变量：action \= \"checkok\"|你设定checkok为反馈信息}} {
    #IF {$okflag == 1} {
      loc {
        walk
      };
    };
    #ELSE {
      se;sw;kill ren feiyan;
    };
  };
  #ACTION {^这里没有这个人。} {
    echo {checkchai};
  };
  #ACTION {^任飞燕「啪」的一声倒在地上} {
    dohalt {
      get jin chai from corpse;
      i;
      echo {checkchai};
    }
  };
  #ACTION {^{设定环境变量：action \= \"checkchai\"|你设定checkchai为反馈信息}} {
    #class riverclass KILL;
    #IF {@carryqty{jin chai} == 0} {
      #NOP {请求援助};
      loc {supply_call {jin chai} {gotoroom {$aimroomid} {$aimdo}} {doabort}}
    };
    #ELSE {
      loc {walk}
    };
  };
  #CLASS riverclass CLOSE;
  give jin chai to zhang;
  n;
  echo {checkok};
};
#NOP {明教监狱};
#ALIAS {river_mj_jianyu} {
  #class riverclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^只觉被人连抬带拖，你昏昏沉沉地被扔到了沙漠之中} {
    #CLASS riverclass KILL;
    loc {
      walk
    }
  };
  #CLASS riverclass CLOSE;
};
#NOP {白驼山铁门进};
#ALIAS {river_bts_tiemen_in} {
  #class riverclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^你身在半空双臂一挥，身体借力又直飞出去，跳出了蛇潭！} {
    #CLASS riverclass KILL;
    dohalt {
      loc {walk};
    };
  };
  #ACTION {^铁门突然打开，你一没留神滚了进去。铁门却又合上了} {
    #CLASS riverclass KILL;
    dohalt {
      loc {walk};
    };
  };
  #CLASS riverclass CLOSE;
  #IF {@getSkillLevel{hamagong} > 0} {
    push tiemen
  };
  #ELSE {
    fan
  };
};
#NOP {白驼山铁门出};
#ALIAS {river_bts_tiemen_out} {
  #class riverclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^你双手在矮墙上一按，一翻身便纵了过去。} {
    #CLASS riverclass KILL;
    dohalt {
      loc {walk};
    };
  };
  #ACTION {^铁门突然打开，你一没留神滚了进去。铁门却又合上了} {
    #CLASS riverclass KILL;
    dohalt {
      loc {walk};
    };
  };
  #CLASS riverclass CLOSE;
  #IF {@getSkillLevel{hamagong} > 0} {
    push tiemen
  };
  #ELSE {
    fan
  };
};
#ALIAS {river_bts_stone_up} {
  #VARIABLE {okflag} {0};
  #CLASS riverclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^你手脚并用，顺着凹洞爬了上去。} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkstone\"|你设定checkstone为反馈信息}} {
    #IF {$okflag != 1} {
      dohalt {
        climb stone;
        echo {checkstone};
      };
    };
    #ELSE {
      #CLASS riverclass KILL;
      loc {walk}
    };
  };
  #CLASS riverclass CLOSE;
  #IF {@getSkillLevel{hamagong} == 0} {
    doabort
  };
  #ELSE {
    climb stone;
    echo {checkstone};
  };
};
#ALIAS {river_bts_stone_down} {
  #VARIABLE {okflag} {0};
  #CLASS riverclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^你手脚并用，爬上了巨石去，翻身一纵，已经跳了出去。} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkstone\"|你设定checkstone为反馈信息}} {
    dohalt {
      #IF {$okflag != 1} {
        climb stone;
        echo {checkstone};
      };
      #ELSE {
        #CLASS riverclass KILL;
        loc {walk}
      };
    };
  };
  #CLASS riverclass CLOSE;
  climb stone;
  echo {checkstone};
};
#NOP {过河模板};
#ALIAS {river_tz_sgjn} {
  #CLASS riverclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^你有病呀！没事推墓碑做什么} {
    #CLASS riverclass KILL;
    runwait {
      tznaogui {gotoroom {$aimroomid} {$aimdo}};
    }
  };
  #ACTION {^你扎下马步，深深的吸了口气，将墓碑缓缓的向旁推开} {
    #CLASS riverclass KILL;
    enter;
    loc {
      walk
    };
  };
  #CLASS riverclass CLOSE;
  move bei;
};
#NOP {意外进武馆出来};
#ALIAS {river_wg_out} {
  #CLASS riverclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkhp\"|你设定checkhp为反馈信息}} {
    #IF {$hp[exp] > 16500} {
      #CLASS riverclass KILL;
      #DELAY {1} {
        loc {
          gotonpc {万震山} {ask wan about 狄云};
        };
      };
    };
  };
  #CLASS riverclass CLOSE;
  hp;
  echo {checkhp};
};
#NOP {星宿练毒山洞进};
#ALIAS {river_zuancave_in} {
  #CLASS riverclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^你点燃了火折，发现} {
    #DELAY {2} {
      zuan;
      id here;
      echo {checkresult};
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkresult\"|你设定checkresult为反馈信息}} {
    #VARIABLE {idle} {0};
    #NOP {进去如果房间物品有神木王鼎，表明到达};
    #IF {"$roomthings[神木王鼎]" != ""} {
      #CLASS riverclass KILL;
      loc {walk};
    };
    #ELSE {
      #DELAY {2} {
        zuan;
        id here;
        echo {checkresult};
      };
    };
  };
  #CLASS riverclass CLOSE;
  use fire;
};
#NOP {星宿练毒山洞出};
#ALIAS {river_zuancave_out} {
  #CLASS riverclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^你点燃了火折，发现} {
    #DELAY {2} {
      zuan;
      id here;
      echo {checkresult};
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkresult\"|你设定checkresult为反馈信息}} {
    #VARIABLE {idle} {0};
    #NOP {进去如果房间物品有神木王鼎，表明到达};
    #IF {"$roomthings[神木王鼎]" == ""} {
      #CLASS riverclass KILL;
      loc {walk};
    };
    #ELSE {
      #DELAY {2} {
        zuan;
        id here;
        echo {checkresult};
      };
    };
  };
  #CLASS riverclass CLOSE;
  use fire;
};
#NOP {玄铁剑山洞->深谷};
#ALIAS {river_xtj_shandong} {
  #CLASS riverclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkid\"|你设定checkid为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {@carryqty{xiao shuzhi} == 0} {
      #CLASS riverclass KILL;
      #DELAY {1} {
        loc {
          findweapon_xsz {gotodo {$aimcity} {$aimroomid} {$aimdo}};
        };
      };
    };
    #ELSE {
      dian shuzhi;
    };
  };
  #ACTION {^你点燃了小树枝，周围马上亮了起来！} {
    dohalt {
      look qingtai;
      mo qingtai;
    };
  };
  #ACTION {^你抹去青苔，现出三行字来，字迹笔划甚细，入石却是极深，显是用极锋利的兵刃划成。} {
    dohalt {
      look zi;
      look mu;
      kneel mu;
    };
  };
  #ACTION {^你出了一会神，不自禁的在石墓之前跪拜，拜了四拜。} {
    dohalt {
      zuan dong;
    };
  };
  #ACTION {^你一矮身穿过小洞来到洞後，只见树木苍翠，山气清佳。} {
    #CLASS riverclass KILL;
    dohalt {
      loc {walk};
    };
  };
  #CLASS riverclass CLOSE;
  i;
  echo {checkid};
};
#NOP {玄铁剑峭壁->平台};
#ALIAS {river_xtj_qiaobi} {
  #VARIABLE {riverflag} {0};
  #CLASS riverclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^你凝神瞧了一阵，突见峭壁上每隔数尺便生著一丛青苔，数十丛笔直排列而上。} {
    #VARIABLE {riverflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checklook\"|你设定checklook为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {$riverflag == 0} {
      #DELAY {2} {
        #11 look shibi;
        echo {checklook};
      }
    };
    #ELSE {
      dohalt {
        mo qingtai;
        cuan up;
      };
    };
  };
  #ACTION {^你紧一紧腰带，提了一口气，窜高数尺，左足踏在第一个小洞之中，跟著窜起} {
    #CLASS riverclass KILL;
    dohalt {
      loc {walk};
    };
  };
  #CLASS riverclass CLOSE;
  #11 look shibi;
  echo {checklook};
};
#ALIAS {river_pxj_press} {
  #CLASS riverclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^一个声音提醒你：记住你的密码，以后你就靠它来进出这里了} {
    #CLASS riverclass KILL;
    dohalt {
      enter;
      loc {walk};
    };
  };
  #ACTION {^你心想：老天保佑这次能蒙着} {
    #NOP {没密码};
    #CLASS riverclass KILL;
    #DELAY {1} {
      loc {walk};
    };
  };
  #CLASS riverclass CLOSE;
  press $env[pxjpwd];
};
#NOP {思过崖进洞};
#ALIAS {river_sgy_wait} {
  #CLASS riverclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkenter\"|你设定checkenter为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {@contains{{roomexits}{enter}} > 0} {
      #CLASS riverclass KILL;
      enter;
      loc {walk}
    };
    #ELSE {
      #CLASS riverclass KILL;
      startfull {river_sgy_wait} {2}
    };
  };
  #CLASS riverclass CLOSE;
  look;
  echo {checkenter}
};
#NOP {思过崖面壁破洞};
#ALIAS {river_sgy_break} {
  #VARIABLE {mianbiok} {0};
  #CLASS riverclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^你忽然产生一种破壁的欲望，不禁站了起来} {
    #VARIABLE {mianbiok} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkmianbi\"|你设定checkmianbi为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {$mianbiok == 0} {
      dohalt {
        mianbi;
        echo {checkmianbi}
      };
    };
    #ELSE {
      dohalt {
        wcwp;
        break;
      };
    };
  };
  #ACTION {^你只听一声轰响} {
    #CLASS riverclass KILL;
    %1;
    loc {walk}
  };
  #CLASS riverclass CLOSE;
  #IF {"%1" == "out"} {
    wcwp;
    break;
  };
  #ELSEIF {@carryqty{fire} == 0} {
    #CLASS riverclass KILL;
    buyfire {gotoroom {$aimroomid} {$aimdo}}
  };
  #ELSE {
    echo {checkmianbi}
  };
};
#NOP {密道杀剑宗三剑客};
#ALIAS {river_hshs_sjk} {
  #LIST {sjklist} {create} {cheng buyou;cong buqi;feng buping};
  #VARIABLE {sjkindex} {1};
  #CLASS riverclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^%*「啪」的一声倒在地上，挣扎着抽动了几下就死了} {
    #VARIABLE {sjkindex} {1};
    kill $sjklist[+$sjkindex]
  };
  #ACTION {^这里没有这个人。} {
    #VARIABLE {idle} {0};
    #IF {$sjkindex >= 3} {
      #DELAY {1} {
        pray pearl;
        s;
        echo {checkmidong}
      };
    };
    #ELSE {
      #MATH {sjkindex} {$sjkindex + 1};
      kill $sjklist[+$sjkindex]
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkmidong\"|你设定checkmidong为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {"$room" != "秘洞"} {
      #CLASS riverclass KILL;
      loc {walk}
    };
    #ELSE {
      #DELAY {0.2} {
        s;
        echo {checkmidong};
      }
    };
  };
  #CLASS riverclass CLOSE;
  kill $sjklist[+$sjkindex]
};
#NOP {少林后山};
#ALIAS {river_slhs} {
  #CLASS riverclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkid\"|你设定checkid为反馈信息}} {
    #IF {@carryqty{nao gou} == 0 || @carryqty{tao suo} == 0} {
      s;
      ask fangsheng dashi about 挠钩;
    };
    #ELSE {
      echo {checkweight}
    };
  };
  #ACTION {^你向方生大师打听有关『挠钩』的消息。} {
    dohalt {ask fangsheng dashi about 套索}
  };
  #ACTION {^你向方生大师打听有关『套索』的消息。} {
    i;
    dohalt {
      n;
      echo {checkid};
    }
  };
  #ACTION {^{设定环境变量：action \= \"checkweight\"|你设定checkweight为反馈信息}} {
    #VARIABLE {idle} {0};
    #CLASS riverclass KILL;
    execute {
      da nao gou;
      shuai tao suo;
      climb up
    };
    loc {walk}
  };
  #CLASS riverclass CLOSE;
  drop silver;
  i;
  echo {checkid};
};
#NOP {昆仑山洞天福地pa，%1:方向};
#ALIAS {river_jysg_pa} {
  #CLASS riverclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkhalt\"|你设定checkhalt为反馈信息}} {
    #CLASS riverclass KILL;
    dohalt {
      echo {nextstep}
    };
  };
  #CLASS riverclass CLOSE;
  pa %1;
  echo {checkhalt};
};
#NOP {海船回溪口，w15s15};
#ALIAS {river_xikou} {
  #NOP {w15时无法往南开，所以先走到中间位置在开};
  river_driveboat {0} {-15} {river_driveboat {-15} {-15}}
};
#NOP {去灵蛇岛，坐标e100s5};
#ALIAS {river_lsd} {
  river_sea {100} {-5}
};
#NOP {去冰火岛，坐标n950e200};
#ALIAS {river_bhd} {
  river_sea {200} {950}
};
#NOP {溪口出海，%1:X坐标,%2:Y坐标};
#ALIAS {river_sea} {
  #VARIABLE {targetX} {100};
  #VARIABLE {targetY} {-5};
  #CLASS riverclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkshore\"|你设定checkshore为反馈信息}} {
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSEIF {"$room" != "溪口"} {
      #CLASS riverclass KILL;
      dohalt {loc {walk}};
    };
    #ELSEIF {@carryqty{gold} < 2} {
      #CLASS riverclass KILL;
      loc {
        hz {balanceex {10} {} {
          gotodo {$aimcity} {$aimroomid} {$aimdo};
        }};
      }
    };
    #ELSE {
      ask shao gong about 出海
    };
  };
  #ACTION {^这里没有这个人} {
    #DELAY {6} {
      ask shao gong about 出海
    };
  };
  #ACTION {^你向艄公打听有关『出海』的消息} {
    dohalt {
      give 1 gold to shao gong
    };
  };
  #ACTION {^艄公在你的耳边悄声说道：你要去哪里，就叫(yell)我的海船(chuan)吧} {
    yell chuan;
    #IF {@carryqty{luo pan} == 0} {
      kill shao gong;
      startfight;
    };
    #ELSE {
      dohalt {
        enter
      }
    };
  };
  #ACTION {^艄公「啪」的一声倒在地上，挣扎着抽动了几下就死了。} {
    stopfight;
    dohalt {
      get luo pan from corpse;
      enter
    };
  };
  #ACTION {^你象猴子一样蹦得老高，两下就窜上了帆船} {
    #CLASS riverclass KILL;
    river_driveboat {%1} {%2};
  };
  #CLASS riverclass CLOSE;
  i;
  echo {checkshore};
};
#NOP {溪口出海开船,%1:X坐标,%2:Y坐标,%3:后续指令};
#ALIAS {river_driveboat} {
  #VARIABLE {targetx} {%1};
  #VARIABLE {targety} {%2};
  #VARIABLE {currentx} {0};
  #VARIABLE {currenty} {0};
  #VARIABLE {coorw} {0};
  #VARIABLE {coore} {0};
  #VARIABLE {coorn} {0};
  #VARIABLE {coors} {0};
  #VARIABLE {arrived} {0};
  #CLASS riverclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^罗盘显示你现在的位于黄道带%*的地方} {
    #VARIABLE {coorw} {0};
    #VARIABLE {coore} {0};
    #VARIABLE {coorn} {0};
    #VARIABLE {coors} {0};
    #REGEXP {%%1} {以东%*个距离} {
      #VARIABLE {coore} {@ctd{&1}};
    };
    #REGEXP {%%1} {以东负%*个距离} {
      #VARIABLE {coore} {@eval{@ctd{&1}} * -1};
    };
    #REGEXP {%%1} {以西%*个距离} {
      #VARIABLE {coorw} {@ctd{&1} * -1};
    };
    #REGEXP {%%1} {以西负%*个距离} {
      #VARIABLE {coorw} {@@eval{@ctd{&1}}};
    };
    #REGEXP {%%1} {以北%*个距离} {
      #VARIABLE {coorn} {@ctd{&1}};
    };
    #REGEXP {%%1} {以北负%*个距离} {
      #VARIABLE {coorn} {@eval{@ctd{&1}} * -1};
    };
    #REGEXP {%%1} {以南%*个距离} {
      #VARIABLE {coors} {@ctd{&1} * -1};
    };
    #REGEXP {%%1} {以南负%*个距离} {
      #VARIABLE {coors} {@@eval{@ctd{&1}}};
    };
    #MATH {currentx} {$coore + $coorw};
    #MATH {currenty} {$coorn + $coors};
  };
  #ACTION {^罗盘显示你现在的位于黄道带正中的地方} {
    #VARIABLE {currentx} {0};
    #VARIABLE {currenty} {0};
  };
  #ACTION {^%*渐渐地在宁波溪口靠了岸，你可以上岸(out)了} {
    #VARIABLE {arrived} {1};
  };
  #ACTION {^%*渐渐地在一个不大的小岛旁靠了岸，你可以上岸(out)了。} {
    #VARIABLE {arrived} {1};
  };
  #ACTION {^只听嘣地一声，%*碰上了一座冰山，所有人都从船舱中被甩了出去} {
    #VARIABLE {arrived} {2};
    #LINE ONESHOT #ACTION {^一股暖流发自丹田流向全身，慢慢地你又恢复了知觉} {
      #CLASS riverclass KILL;
      #VARIABLE {idle} {0};
      loc {walk};
    } {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkdingwei\"|你设定checkdingwei为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {$arrived == 2} {
    };
    #IF {$arrived == 1} {
      #CLASS riverclass KILL;
      dohalt {
        out;
        loc {walk}
      };
    };
    #ELSE {
      #DELAY {1} {
        #IF {$currentx == $targetx && $currenty == $targety && "%3" != ""} {
          #CLASS riverclass KILL;
          %3
        };
        #ELSEIF {$currentx < $targetx} {
          turn east;
        };
        #ELSEIF {$currentx > $targetx} {
          turn west;
        };
        #ELSEIF {$currenty < $targety} {
          turn north;
        };
        #ELSEIF {$currenty > $targety} {
          turn south;
        };
        dingwei;
        echo {checkdingwei}
      };
    };
  };
  #CLASS riverclass CLOSE;
  start;
  dingwei;
  echo {checkdingwei}
};
#NOP {冰火岛冰山跳浮冰};
#ALIAS {river_bhd_bingshan} {
  #VARIABLE {fubingflag} {0};
  #VARIABLE {bingkuaiok} {0};
  #CLASS riverclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^%*一纵身，整个人已到了浮冰上} {
    #VARIABLE {fubingflag} {1};
  };
  #ACTION {^结果你一不小心脚下一滑，仰天一个大摔跤} {
    #VARIABLE {fubingflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkfubing\"|你设定checkfubing为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {$fubingflag == 0 && "$room" == "冰山"} {
      #DELAY {2} {
        jump fubing;
        look;
        echo {checkfubing}
      };
    };
    #ELSE {
      jump bing;
      look;
      echo {checkbingkuai}
    };
  };
  #ACTION {^%*张手抱向小冰块， 扑通一声跳入海中} {
    #VARIABLE {bingkuaiok} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkbingkuai\"|你设定checkbingkuai为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {$bingkuaiok == 0 || "$room" == "冰山"} {
      #DELAY {2} {
        jump bing;
        look;
        echo {checkbingkuai}
      };
    };
    #ELSE {
      #CLASS riverclass KILL;
      loc {walk};
    };
  };
  #CLASS riverclass CLOSE;
  jump fubing;
  look;
  echo {checkfubing}
};
#NOP {冰火岛浮冰跳冰块};
#ALIAS {river_bhd_bingkuai} {
  #VARIABLE {yanshiok} {0};
  #CLASS riverclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^浮冰终于被你划到达岩石的旁边。} {
    #VARIABLE {yanshiok} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkyanshi\"|你设定checkyanshi为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {$yanshiok == 0} {
      dohalt {
        hua;
        pa yanshi;
        echo {checkyanshi}
      };
    };
    #ELSE {
      #CLASS riverclass KILL;
      dohalt {
        loc {walk};
      }
    };
  };
  #CLASS riverclass CLOSE;
  hua;
  pa yanshi;
  echo {checkyanshi}
};
#NOP {冰火岛回来};
#ALIAS {river_bhd_boat} {
  wwp;
  gotoroom {3775} {river_bhd_boat_wood};
};
#ALIAS {river_bhd_boat_wood} {
  #VARIABLE {woodok} {0};
  #CLASS riverclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^看清楚一点，那并不是活物。} {
    dohalt {
      get xiong pi;
      get xiong zhang;
      kill xiong
    };
  };
  #ACTION {^大白熊惨嚎一声，慢慢倒下死了} {
    kill xiong
  };
  #ACTION {^这里没有这个人} {
    #VARIABLE {woodok} {0};
    wcwp;
    echo {checkwood}
  };
  #ACTION {^只听哗啦一声，这棵树木总算被你砍倒了} {
    #VARIABLE {woodok} {1};
  };
  #ACTION {^剩下的树木都太大了，你还是再找找吧} {
    #VARIABLE {woodok} {2};
  };
  #ACTION {^{设定环境变量：action \= \"checkwood\"|你设定checkwood为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {@carryqty{shu gan} < 5} {
      #IF {$woodok == 0} {
        #DELAY {1} {
          dohalt {
            chop tree;
            echo {checkwood}
          };
        };
      };
      #ELSEIF {$woodok == 1} {
        #VARIABLE {woodok} {0};
        dohalt {
          get shu gan;
          i;
          echo {checkwood}
        }
      };
      #ELSE {
        #VARIABLE {woodok} {0};
        s;
        #4 drop xiong pi;
        #2 drop xiong zhang;
        n;
        #DELAY {2} {
          kill xiong
        };
      };
    };
    #ELSE {
      #CLASS riverclass KILL;
      dohalt {gotoroom {3767} {river_bhd_boat_make}};
    };
  };
  #CLASS riverclass CLOSE;
  kill xiong
};
#ALIAS {river_bhd_boat_make} {
  #VARIABLE {okflag} {0};
  #CLASS riverclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^你将帆支起，又加固了主桅，一条船总算是做好了} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkchuan\"|你设定checkchuan为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {$okflag == 1} {
      dohalt {
        tui chuan;
      };
    };
    #ELSE {
      dohalt {
        make chuan;
        echo {checkchuan}
      };
    };
  };
  #ACTION {^{你使出吃奶的劲，将用树干制成的帆船推进了大海|岸边有艘帆船呢，你再推下去一艘，它们会相撞的}} {
    #CLASS riverclass KILL;
    dohalt {
      enter;
      river_xikou
    };
  };
  #CLASS riverclass CLOSE;
  echo {checkchuan}
};
#NOP {仅梅庄};
#ALIAS {river_mz} {
  #CLASS riverclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^你抓住门上的大铜环，敲了四下} {
    dohalt {qiao gate 2 times}
  };
  #ACTION {^你抓住门上的大铜环，敲了二下} {
    dohalt {qiao gate 5 times}
  };
  #ACTION {^你抓住门上的大铜环，敲了五下} {
    dohalt {qiao gate 3 times}
  };
  #ACTION {^你抓住门上的大铜环，敲了三下} {
    #VARIABLE {idle} {0};
    #VARIABLE {workingflag} {1};
  };
  #ACTION {^过了半晌，大门缓缓打开，并肩走出两个家人装束的老者} {
    dohalt {answer 拜见江南四友};
  };
  #ACTION {^左首的家人对你道：“我家主人向不见客。”说着便欲关门。} {
    show wuyue lingqi;
  };
  #ACTION {^家人让在两旁，对你一拱手道：“里边请。”} {
    #CLASS riverclass KILL;
    dohalt {
      s;
      loc {walk}
    };
  };
  #CLASS riverclass CLOSE;
  #IF {"$hp[party]" == "日月神教"} {
    qiao gate;
    s;
    loc {walk};
  };
  #ELSE {
    qiao gate 4 times
  };
};
#NOP {明教乾坤大挪移密室进};
#ALIAS {river_mj_midao_in} {
  #VARIABLE {okflag} {0};
  #CLASS riverclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkdafu\"|你设定checkdafu为反馈信息}} {
    #IF {@carryqty{dafu} == 0} {
      #DELAY {2} {
        get dafu;
        i;
        echo {checkdafu}
      };
    };
    #ELSE {
      s;
      echo {checkzhuan};
    };
  };
  #ACTION {^你慢慢的从石壁上取下四块石砖，露出了一个洞口，恰好能融入一人进入} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkzhuan\"|你设定checkzhuan为反馈信息}} {
    #IF {@carryqty{dafu} == 0} {
      s;
      get dafu;
      i;
      echo {checkdafu}
    };
    #ELSEIF {$okflag == 0} {
      dohalt {
        pull zhuan down;
        echo {checkzhuan}
      };
    };
    #ELSE {
      #CLASS riverclass KILL;
      dohalt {
        enter;
        loc {walk}
      };
    };
  };
  #CLASS riverclass CLOSE;
  i;
  echo {checkzhuan};
};
#NOP {明教乾坤大挪移密室出，%1:后续指令};
#ALIAS {river_mj_midao_out} {
  #VARIABLE {okflag} {0};
  #CLASS riverclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^你默念乾坤大挪移习法，一个挪移乾坤，石门发出“吱吱”的声音} {
    dohalt {
      e;nu;e;out;loc {%1}
    };
  };
  #CLASS riverclass CLOSE;
  wwp {dafu};
  gua wall;
  push men;
};
#NOP {牛家村密室};
#ALIAS {river_njc_ms} {
  #VARIABLE {okflag} {0};
  #CLASS riverclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^只听“喀啦”一声巨响，你的眼前出现了一道门。你毫不犹豫的走了进去} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkdest\"|你设定checkdest为反馈信息}} {
    #IF {$okflag == 0} {
      #DELAY {1} {
        execute {
          move wan;
          #3 zhuan tiewan zuo;
          #3 zhuan tiewan right;
        };
        echo {checkdest}
      };
    };
    #ELSE {
      #CLASS riverclass KILL;
      #VARIABLE {okflag} {0};
      #DELAY {1} {
        loc {walk};
      };
    };
  };
  #CLASS riverclass CLOSE;
  echo {checkdest}
};
#NOP {给钱住店，%1:进驻的方向};
#ALIAS {river_hotel} {
  #VARIABLE {okflag} {1};
  #CLASS riverclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^{怎么着，想白住啊|你身上没有这样东西}} {
    #VARIABLE {okflag} {0};
  };
  #ACTION {^{设定环境变量：action \= \"checkok\"|你设定checkok为反馈信息}} {
    #CLASS riverclass KILL;
    #IF {$okflag == 1} {
      %1;
      loc {walk}
    };
    #ELSE {
      #VARIABLE {bank} {@getLocalBank{}};
      #DELAY {1} {
        gotodo {$bank[city]} {$bank[room]} {balance {gotodo {$city} {$roomid} {$aimdo}}}
      };
    };
  };
  #CLASS riverclass CLOSE;
  loc {
    give 5 silver to xiao er;
    i;
    echo {checkok};
  };
};
#NOP {长按洗澡};
#ALIAS {river_shower} {
  #VARIABLE {okflag} {0};
  #VARIABLE {showerpath} {};
  #IF {"$hp[sex]" == "m"} {
    #VARIABLE {showerpath} {w}
  };
  #ELSE {
    #VARIABLE {showerpath} {e};
  };
  #CLASS riverclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^啊！你身中剧毒还去洗澡，可能有危险哦！} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^小丫环一把拦住你说道：“要付钱的啊！一两黄金！”} {
    #VARIABLE {okflag} {0};
  };
  #ACTION {^{设定环境变量：action \= \"checkshower\"|你设定checkshower为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {$okflag == 1} {
      #DELAY {6} {
        cond;
        $showerpath;
        echo {checkshower};
      }
    };
    #ELSEIF {"$room" != "清池"} {
      #CLASS riverclass KILL;
      loc {walk};
    };
    #ELSEIF {@carryqty{gold} < 1} {
      #CLASS riverclass KILL;
      #DELAY {seconds} {
        gotodo {长安城} {威信钱庄} {balanceex {10} {} {gotodo {$aimcity} {$aimroom} {$aimdo}}};
      };
    };
    #ELSE {
      #DELAY {0.5} {
        give 1 gold to yahuan;
        i;
        cond;
        $showerpath;
        echo {checkshower};
      };
    };
  };
  #CLASS riverclass CLOSE;
  echo {checkshower};
};
#NOP {刘好弈呕血谱};
#ALIAS {river_oxp} {
  #VARIABLE {okflag} {1};
  #CLASS riverclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^刘好弈拦住你说：“我这个书房不同一般} {
    #VARIABLE {okflag} {0};
  };
  #ACTION {^{设定环境变量：action \= \"checkquestion\"|你设定checkquestion为反馈信息}} {
    #CLASS riverclass KILL;
    #IF {$okflag == 1} {
      loc {walk}
    };
    #ELSE {
      oxp_guess
    };
  };
  #CLASS riverclass CLOSE;
  w;
  echo {checkquestion}
};
#NOP {尝试猜测一个答案};
#ALIAS {oxp_guess} {
  #VARIABLE {observed} {};
  #VARIABLE {guess} {};
  ask liu haoyi about question;
  #VARIABLE {answer} {@oxpCreateAnswer{}};
  dohalt {oxp_answer {answer} {oxp_test {1} {2}}}
};
#NOP {回单并返回结果，%1:答案序列，%2:处理指令};
#ALIAS oxp_answer {
  #CLASS riverclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^刘好弈道：“你猜的是%*，全对的有%*个，只有书架对的有%*个} {
    #CLASS riverclass KILL;
    #IF {@ctd{%%2} + @ctd{%%3} == 4} {
      #VARIABLE {precount} {$okcount};
      #VARIABLE {okcount} {@ctd{%%2}};
      #VARIABLE {guess[%%1]} {@ctd{%%2}};
      %2
    };
    #ELSE {
      #VARIABLE {answer} {@oxpCreateAnswer{}};
      oxp_answer {answer} {oxp_test {1} {2}}
    };
  };
  #ACTION {^刘好弈在墙上按了几下，道：“好了，你可以进书房去了} {
    #CLASS riverclass KILL;
    w;
    loc {walk}
  };
  #ACTION {^刘好弈生气地说：“你可真笨！七次机会都没猜中} {
    #CLASS riverclass KILL;
    oxp_guess
  };
  #CLASS riverclass CLOSE;
  guess $%1[+1]$%1[+2]$%1[+3]$%1[+4]
};
#NOP {测试交换两个位置检查变化，%1:原位置，%2:目的位置};
#ALIAS {oxp_test} {
  #VARIABLE {srctest} {%1};
  #VARIABLE {dsttest} {%2};
  #VARIABLE {testanswer} {@oxpSwapAnswer{{answer}{$srctest}{$dsttest}}};
  #LOCAL {mgq} {$answer[+1]$answer[+2]$answer[+3]$answer[+4]};
  #VARIABLE {okcount} {$guess[$mgq]};
  oxp_answer {testanswer} {oxp_test_result}
};
#NOP {处理测试交换结果};
#ALIAS {oxp_test_result} {
  #VARIABLE {testcount} {$okcount};
  #VARIABLE {testdiff} {@eval{$okcount - $precount}};
  #IF {$testdiff == 2} {
    #NOP {交换后的两个位置正确，在此基础上进一步交换测试，基本是再进行一步就结束了};
    #SHOWME {$srctest -> y};
    #SHOWME {$dsttest -> y};
    #VARIABLE {observed[$srctest]} {y};
    #VARIABLE {observed[$dsttest]} {y};
    #VARIABLE {answer} {$testanswer};
    #VARIABLE {tempslot} {@oxpGetOtherSlots{}};
    oxp_test {$tempslot[+1]} {$tempslot[+2]}
  };
  #ELSEIF {$testdiff == 1} {
    #NOP {以交换后的状态为基础，对进行交换的两个位置其中之一与未交换过的位置之一进行交换观察};
    #VARIABLE {answer} {$testanswer};
    oxp_observe {$srctest} {$dsttest}
  };
  #ELSEIF {$testdiff == 0} {
    #NOP {没有变化，选择刚交换的之一与剩余未观察的两个之一进行交换测试};
    #VARIABLE {tempslot} {@oxpGetOtherSlots{{$srctest}{$dsttest}}};
    oxp_test {$srctest} {$tempslot[+@rnd{{1}{2}}]}
  };
  #ELSEIF {$testdiff == -1} {
    #NOP {以交换前的状态为基础，对进行交换的两个位置其中之一与未交换过的位置之一进行交换观察};
    oxp_observe {$srctest} {$dsttest}
  };
  #ELSEIF {$testdiff == -2} {
    #NOP {交换前的两个位置正确，在原有基础上进一步交换测试，基本是再进行一步就结束了};
    #SHOWME {$srctest -> y};
    #SHOWME {$dsttest -> y};
    #VARIABLE {observed[$srctest]} {y};
    #VARIABLE {observed[$dsttest]} {y};
    #VARIABLE {tempslot} {@oxpGetOtherSlots{}};
    oxp_test {$tempslot[+1]} {$tempslot[+2]}
  };
};
#NOP {观察交换两个位置检查变化，%1:观察选取，%2:观察剩下};
#ALIAS {oxp_observe} {
  #VARIABLE {tempslots} {@oxpGetOtherSlots{{%1}{%2}}};
  #LOCAL {tarslot} {$tempslots[+@rnd{{1}{&tempslots[]}}]};
  #SHOWME {$tarslot of $tempslots};
  #VARIABLE {observeanswer} {@oxpSwapAnswer{{answer}{%1}{$tarslot}}};
  #LOCAL {mgq} {$answer[+1]$answer[+2]$answer[+3]$answer[+4]};
  #VARIABLE {okcount} {$guess[$mgq]};
  oxp_answer {observeanswer} {oxp_observe_result {%1} {%2} {$tarslot}}
};
#NOP {处理观察交换结果，%1:观察选取，%2:观察剩下，%3:观察交换};
#ALIAS {oxp_observe_result} {
  #VARIABLE {observecount} {$okcount};
  #VARIABLE {observediff} {@eval{$okcount - $precount}};
  #IF {$observediff == 1} {
    #NOP {未观察的位置正确，且观察交换的两个位置有一个正确，进一步进行检查交换};
    #SHOWME {%2 -> y};
    #VARIABLE {observed[%2]} {y};
    #VARIABLE {answer} {$observeanswer};
    oxp_observe {%1} {%3}
  };
  #ELSEIF {$observediff == 0} {
    #NOP {未观察的正确，观察交换的两个位置之一与剩余位置进行交换测试};
    #SHOWME {%2 -> y};
    #VARIABLE {observed[%2]} {y};
    #VARIABLE {tempslot} {@oxpGetOtherSlots{{%1}{%3}}};
    #VARIABLE {answer} {$observeanswer};
    oxp_test {%1} {$tempslot[+1]}
  };
  #ELSEIF {$observediff == -1} {
    #NOP {被观察的位置正确，剩下的交换测试};
    #SHOWME {%1 -> y};
    #VARIABLE {observed[%1]} {y};
    #VARIABLE {tempslot} {@oxpGetOtherSlots{}};
    #LOCAL {s1} {$tempslot[+1]};
    #LIST {tempslot} {delete} {1};
    #LOCAL {s2} {$tempslot[+@rnd{{1}{&tempslot[]}}]};
    oxp_test {$s1} {$s2}
  };
  #ELSE {
    #SHOWME {<faa>未预期的观察结果$observecount，重新启动};
    oxp_guess
  };
};
#NOP {获取其他可观察的位置，%1:排除位置，%2:排除位置};
#FUNCTION oxpGetOtherSlots {
  #LIST {result} {clear} {};
  #LOOP 1 4 {i} {
    #IF {"$observed[$i]" == "y"} {
      #CONTINUE;
    };
    #IF {"$i" == "%1" || "$i" == "%2"} {
      #CONTINUE;
    };
    #LIST {result} {add} {$i};
  };
};
#NOP {交换位置，%1:原答案，%2:源位置，%3:目的位置};
#FUNCTION oxpSwapAnswer {
  #LOCAL {templist} {$%1};
  #LOCAL {tempitem} {$%1[+%2]};
  #LOCAL {templist[+%2]} {$%1[+%3]};
  #LOCAL {templist[+%3]} {$tempitem};

  #RETURN {$templist};
};
#NOP {创建随机答案};
#FUNCTION oxpCreateAnswer {
  #LIST {shelf} {create} {1;2;3;4;5;6;7};
  #LIST {answer} {clear};
  #WHILE {&answer[] < 4} {
    #LOCAL {index} {@rnd{{1}{&shelf[]}}};
    #LIST {answer} {add} {$shelf[+$index]};
    #LIST {shelf} {delete} {$index};
  };
  #RETURN {$answer};
};
#NOP {练毒山洞进出，%1:非空表示出};
#ALIAS {river_xx_cave} {
  #VARIABLE {okflag} {0};
  #CLASS riverclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^什么} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkzuan\"|你设定checkzuan为反馈信息}} {
    #VARIABLE {idle} {0};
    #DELAY {1} {
      #IF {$okflag == 1} {
        #VARIABLE {okflag} {0};
        zuan;
        echo {checkzuan}
      };
      #ELSE {
        #CLASS riverclass KILL;
        loc {walk}
      };
    }
  };
  #CLASS riverclass CLOSE;
  #IF {@carryqty{fire} == 0} {
    #CLASS riverclass KILL;
    #IF {"%1" != ""} {
      get fire;
      i;
      #DELAY {6} {walk}
    };
    #ELSE {
      buyfire {gotoroom {$aimroomid} {$aimdo}}
    };
  };
  #ELSE {
    use fire;
    zuan;
    echo {checkzuan}
  };
};
#NOP {明教蝴蝶谷};
#ALIAS {river_hdg} {
  #VARIABLE {riverts} {@now{}};
  #CLASS riverclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkdoor\"|你设定checkdoor为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {@contains{{roomexits}{right}} > 0} {
      #CLASS riverclass KILL;
      right;
      loc {walk}
    };
    #ELSEIF {@elapsed{$riverts} >= 5} {
      #CLASS riverclass KILL;
      #IF {"$currentjob" == ""} {
        #SHOWME {<faa>进不去，别折腾了。};
      };
      #ELSE {
        loc {doabort}
      };
    };
    #ELSE {
      bo huacong;
      say 芝麻开门;
      #DELAY {1} {look;echo {checkdoor}}
    };
  };
  #CLASS riverclass CLOSE;
  bo huacong;
  say 芝麻开门;
  #DELAY {1} {look;echo {checkdoor}}
};
#NOP {苏州后院去闺房};
#ALIAS {river_sz_kaimen} {
  #CLASS riverclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkdoor\"|你设定checkdoor为反馈信息}} {
    #CLASS riverclass KILL;
    #IF {"$roomthings[凌退思]" != ""} {
      #NOP {如果不是解谜，不推荐书杀人，会被通缉};
      #IF {"$questmodule" == "连城诀"} {
        killnpc {ling tuisi}
      };
      #ELSEIF {"$currentjob" == ""} {
        #SHOWME {<faa>杀人要通缉，自己决定。};
      };
      #ELSE {
        loc {doabort}
      };
    };
    #ELSEIF {@contains{{roomexits}{n}} > 0} {
      n;
      loc {walk}
    };
    #ELSEIF {@carryqty{tong yaoshi} > 0} {
      kai men;
      n;
      loc {walk}
    };
    #ELSEIF {"$currentjob" != ""} {
      loc {doabort}
    };
    #ELSE {
      #SHOWME {<faa>没钥匙你过来干嘛的！};
    };
  };
  #CLASS riverclass CLOSE;
  look;
  i;
  id here;
  echo {checkdoor}
};
#NOP {苏州闺房去隔壁房间};
#ALIAS {river_sz_guifang} {
  #VARIABLE {okflag} {0};
  #CLASS riverclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^你向凌霜华打听有关『丁典』的消息} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkask\"|你设定checkask为反馈信息}} {
    #CLASS riverclass KILL;
    #IF {$okflag == 0} {
      #IF {"$currentjob" == ""} {
        #SHOWME {<faa>不能进去，不然暴毙！};
      };
      #ELSE {
        dohalt {loc {doabort}}
      };
    };
    #ELSE {
      dohalt {
        w;
        loc {walk}
      }
    };
  };
  #CLASS riverclass CLOSE;
  ask ling shuanghua about 丁典;
  echo {checkask}
};
#ALIAS {river_sl_talin} {
  #CLASS riverclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkcmd\"|你设定checkcmd为反馈信息}} {
    #CLASS riverclass KILL;
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSE {
      s;ne;se;n;e;sw;e;ne;se;s;se;
      #DELAY {0.5} {loc {walk}};
    };
  };
  #CLASS riverclass CLOSE;
  #DELAY {0.5} {echo {checkcmd}}
};
#ALIAS {river_jsjf_paya} {
  #CLASS riverclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkweight\"|你设定checkweight为反馈信息}} {
    #VARIABLE {idle} {0};
    resonate {checkweight};
    #IF {$id[weight] >= 10} {
      #DELAY {1} {
        drop silver;
        drop icon;
        echo {checkweight}
      };
    };
    #NOP {判断身上有没有小树枝};
    #IF {@carryqty{xiao shuzhi} == 0} {
      #CLASS riverclass KILL;
      #DELAY {1} {
        loc {
          findweapon_xsz {gotodo {$aimcity} {$aimroomid} {$aimdo}};
        };
      };
    };
    #ELSE {
      #CLASS riverclass KILL;
      l tieyuan;
      pa ya;
      loc {walk}
    };
  };
  #CLASS riverclass CLOSE;
  drop jinshe sword;
  ensure {
    drop jinshe sword;
    i
  } {checkweight}
};
#NOP {金蛇剑法进洞};
#ALIAS {river_jsjf_down} {
  #CLASS riverclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkid\"|你设定checkid为反馈信息}} {
    #VARIABLE {idle} {0};
    resonate {checkid};
    #IF {@carryqty{teng} == 0} {
      #DELAY {2} {
        break shuteng;
        i;
        echo {checkid}
      };
    };  
    #ELSE {
      #DELAY {1} {
        bian kuang;
        chan shu;
        dian fire;
        bo zhui;
        climb down;
        loc {walk}
      };
    };
  };
  #CLASS riverclass CLOSE;
  ensure {
    break shuteng;
    i
  } {checkid}
};
#NOP {快速路径,%1:指令};
#ALIAS {river_cmd} {
  #CLASS riverclass KILL;
  #CLASS riverclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkcmd\"|你设定checkcmd为反馈信息}} {
    #IF {$walkstoppedts != 0} {
      #CLASS matrixclass KILL;
      #VARIABLE {walkstoppedts} {0}
    };
    #ELSE {
      #CLASS riverclass KILL;
      %1;
      #DELAY {0.2} {loc {walk}}
    }
  };
  #CLASS riverclass CLOSE;
  echo {checkcmd}
};
#NOP {过河模板};
#ALIAS {river_template} {
  #CLASS riverclass KILL;
  #CLASS riverclass OPEN;
  #CLASS riverclass CLOSE;
};
#SHOWME {<fac>@padRight{{过河}{12}}<fac> <cfa>模块加载完毕<cfa>};