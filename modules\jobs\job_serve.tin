#VARIABLE {conf[wxbcategory]} {};
#NOP {备用铜钥匙};
#VARIABLE {stockitems} {
  {jin chai} {0}
  {tong yaoshi} {0}
};
#VARIABLE {keystockcount} {0};
#VARIABLE {jinchaistockcount} {0};
#VARIABLE {itemts} {
  {flower} {0}
  {jinchai} {0}
  {lingtuisi} {0}
  {tiebagua} {0}
};
#NOP {服务模块};
#NOP {你从任飞燕的尸体身上搜出一支金钗。};
#ALIAS {jobgo_serve} {
  wwp;
  openwimpy;
  gotodo {扬州城} {杂货铺} {jobserve_checkthings}
};
#NOP {检查重要物品};
#ALIAS {jobserve_checkthings} {
  #CLASS jobcheckclass KILL;
  #CLASS jobcheckclass OPEN;
  #ACTION {┃    jin chai           金钗} {
    #VARIABLE {stockitems[jin chai]} {@eval{$stockitems[jin chai] + 1}}
  };
  #ACTION {^┃   tong yaoshi        铜钥匙} {
    #VARIABLE {stockitems[tong yaoshi]} {@eval{$stockitems[tong yaoshi] + 1}}
  };
  #ACTION {^{设定环境变量：action \= \"checkthings\"|你设定checkthings为反馈信息}} {
    #CLASS jobcheckclass KILL;
    #DELAY {1} {gotoroom {424} {jobserve_start}}
  };
  #CLASS jobcheckclass CLOSE;
  #VARIABLE {stockitems[jin chai]} {0};
  #VARIABLE {stockitems[tong yaoshi]} {0};
  dlist;
  echo {checkthings}
};
#ALIAS {jobserve_start} {
  #VARIABLE {drugscount} {0};
  #LIST {flowers} {create} {野菊花;黄鹤翎;白菊花;美人红;金孔雀;莺羽黄;玉楼春;桃花菊};
  #LIST {emotes} {create} {18mo;9191;9494;accuse;addoil;admire;admire2;admit;afraid;agree;ah;angry;angry3;beg1;beg2;laugh;haha;hehe;smile};
  #CLASS jobcheckclass KILL;
  #CLASS jobcheckclass OPEN;
  #ACTION {^%*(%*)告诉你：suanming_support_%*} {
    #IF {"$caller" == "" || "$caller[id]" == "@lower{%%2}"} {
      #VARIABLE {caller} {
        {request} {suanming}
        {timestamp} {@now{}}
        {id} {@lower{%%2}}
        {name} {%%1}
        {roomid} {%%3}
      };
    };
  };
  #ACTION {^%!*(%*)告诉你：drugs_%*_%*} {
    #%%3 give %%2 to @lower{%%1};
    ok @lower{%%1}
  };
  #ACTION {^{设定环境变量：action \= \"checkhp\"|你设定checkhp为反馈信息}} {
    #VARIABLE {idle} {};
    #VARIABLE {drugscount} {0};
    #FOREACH {*id[things][]} {t} {
      #IF {@contains{{common[tradedrugs]}{$t}} == 0} {
        #CONTINUE;
      };
      #MATH {drugscount} {$drugscount + $id[things][$t][qty]};
    };
    #IF {"$caller" != ""} {
      #CLASS jobcheckclass KILL;
      checkrequest {gotoroom {424} {jobserve_start}}
    };
    #ELSEIF {(@contains{{conf[services]}{zhanbu}} > 0 || @contains{{conf[services]}{assist}} > 0) && @carryqty{tie bagua} == 0 && @elapsed{$itemts[tiebagua]} > 600} {
      #VARIABLE {itemts[tiebagua]} {@now{}};
      gettiebagua {gotoroom {424} {jobserve_start} {%1}};
    };
    #ELSEIF {@carryqty{gold} > 300 || @carryqty{silver} > 200} {
      #CLASS jobcheckclass KILL;
      gotodo {扬州城} {天阁斋} {balanceex {2} {50} {gotoroom {424} {jobserve_start}}}
    };
    #ELSEIF {@isCanWxbWeapon{} == 1} {
      #CLASS jobcheckclass KILL;
      buywxbweapon {gotoroom {424} {jobserve_start}}
    };
    #ELSEIF {$drugscount >= 10} {
      #CLASS jobcheckclass KILL;
      gotodo {长安城} {当铺} {jobserve_sell}
    };
    #ELSEIF {$stockitems[tong yaoshi] < 10 && @elapsed{$itemts[flower]} > 120} {
      #CLASS jobcheckclass KILL;
      gotodo {扬州城} {个园} {jobserve_flower}
    };
    #ELSEIF {$stockitems[jin chai] < 20 && @elapsed{$itemts[jinchai]} > 800} {
      #CLASS jobcheckclass KILL;
      gotoroom {793} {jobserve_jinchai {gotoroom {424} {jobserve_start}}}
    };
    #ELSEIF {@elapsed{$itemts[lingtuisi]} > 800} {
      #CLASS jobcheckclass KILL;
      gotoroom {379} {jobserve_lingtuisi}
    };
    #ELSE {
      $emotes[+@rnd{{1}{&emotes[]}}] qian;
      #DELAY {6} {
        hp;
        i;
        score;
        time;
        echo {checkhp}
      };
    };
  };
  
  #CLASS jobcheckclass CLOSE;
  #IF {"$caller[request]" == "suanming"} {
    #VARIABLE {caller} {};
  };
  hp;
  i;
  score;
  echo {checkhp}
};
#NOP {捡野菊花};
#ALIAS {jobserve_flower} {
  #CLASS jobcheckclass KILL;
  #CLASS jobcheckclass OPEN;
  #VARIABLE {okflag} {0};
  #ACTION {^你捡起一朵野菊花} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkflower\"|你设定checkflower为反馈信息}} {
    #VARIABLE {itemts[flower]} {@now{}};
    #IF {$okflag == 1} {
      #VARIABLE {backupkey} {1};
      e;e;
      give flower to ju you;
      i;
      loc {echo {checkkey}}
    };
    #ELSE {
      drop flower;
      loc {dohalt {gotoroom {424} {jobserve_start}}}
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkkey\"|你设定checkkey为反馈信息}} {
    #CLASS jobcheckclass KILL;
    #IF {@carryqty{tong yaoshi} > 1} {
      #MATH {stockitems[tong yaoshi]} {$stockitems[tong yaoshi] + 1};
      gotoroom {442} {cun tong yaoshi;dohalt {gotoroom {424} {jobserve_start}}}
    };
    #ELSE {
      gotoroom {424} {jobserve_start}
    };
  };
  #CLASS jobcheckclass CLOSE;
  get flower;
  echo {checkflower};
};
#NOP {任飞燕金钗};
#ALIAS {jobserve_jinchai} {
  #VARIABLE {okflag} {0};
  #CLASS jobcheckclass KILL;
  #CLASS jobcheckclass OPEN;
  #ACTION {^你双手抱拳，对任飞燕作了个揖道} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkren\"|你设定checkren为反馈信息}} {
    #VARIABLE {idle} {0};
    #IF {$okflag == 1} {
      kill ren feiyan;
    };
    #ELSEIF {"$caller" != ""} {
      #CLASS jobcheckclass KILL;
      checkrequest {%1}
    };
    #ELSE {
      #DELAY {2} {
        hi ren feiyan;
        echo {checkren}
      };
    };
  };
  #ACTION {^任飞燕「啪」的一声倒在地上，挣扎着抽动了几下就死了} {
    #VARIABLE {itemts[jinchai]} {@now{}};
    get jin chai from corpse;
    i;
    echo {checkgo}
  };
  #ACTION {^{设定环境变量：action \= \"checkgo\"|你设定checkgo为反馈信息}} {
    #CLASS jobcheckclass KILL;
    #IF {@carryqty{jin chai} > 1} {
      #MATH {stockitems[jin chai]} {$stockitems[jin chai] + 1};
      gotoroom {442} {cun jin chai;dohalt {%1}}
    };
    #ELSE {
      %1
    };
  };
  #CLASS jobcheckclass CLOSE;
  hi ren feiyan;
  echo {checkren}
};
#NOP {检查凌退思是否存在};
#ALIAS {jobserve_lingtuisi} {
  #CLASS jobcheckclass KILL;
  #CLASS jobcheckclass OPEN;
  #ACTION {^你要对谁做这个动作} {
    #CLASS jobcheckclass KILL;
    #IF {@contains{{roomexits}{n}} == 0} {
      #VARIABLE {caller} {
        {request} {doorman}
        {timestamp} {@now{}}
        {id} {xcjssthd}
        {name} {小纯洁}
        {key} {tong yaoshi}
      };
    };
    #DELAY {1} {gotoroom {424} {jobserve_start}}
  };
  #ACTION {^你双手抱拳，对凌退思作了个揖道} {
    #CLASS jobcheckclass KILL;
    #NOP {去山壁处杀死凌退思};
    gotoroom {515} {jobserve_killlingtuisi}
  };
  #CLASS jobcheckclass CLOSE;
  #VARIABLE {itemts[lingtuisi]} {@now{}};
  hi ling tuisi;
};
#NOP {杀死凌退思};
#ALIAS {jobserve_killlingtuisi} {
  #CLASS jobcheckclass KILL;
  #CLASS jobcheckclass OPEN;
  #ACTION {^这里没有这个人} {
    #CLASS jobcheckclass KILL;
    #DELAY {1} {gotoroom {424} {jobserve_start}}
  };
  #ACTION {^凌退思「啪」的一声倒在地上，挣扎着抽动了几下就死了。} {
    #CLASS jobcheckclass KILL;
    dohalt {gotoroom {424} {jobserve_start}}
  };
  #CLASS jobcheckclass CLOSE;
  kill ling tuisi
};
#FUNCTION isCanWxbWeapon {
  #IF {@contains{{conf[services]}{weapon}} == 0} {
    #RETURN {0};
  };
  #IF {&conf[wxbweapons][damage][] == 0 && &conf[wxbweapons][attack][] == 0 && &conf[wxbweapons][wuxing][] == 0} {
    #RETURN {0};
  };
  #IF {$hp[balance] < 3000} {
    #RETURN {0};
  };
  #IF {"$env[weekday]" == "三" || "$env[weekday]" == "四" || ("$env[weekday]" == "五" && $env[clock][hour] < 9)} {
    #RETURN {0};
  };
  #NOP {判断各类型武器是否已经有了};
  #VARIABLE {damageok} {0};
  #IF {&conf[wxbweapons][damage][] == 0} {
    #VARIABLE {damageok} {1};
  };
  #VARIABLE {attackok} {0};
  #IF {&conf[wxbweapons][attack][] == 0} {
    #VARIABLE {attackok} {1};
  };
  #VARIABLE {wuxingok} {0};
  #IF {&conf[wxbweapons][wuxing][] == 0} {
    #VARIABLE {wuxingok} {1};
  };
  #NOP {伤害类};
  #FOREACH {$conf[wxbweapons][damage][]} {w} {
    #IF {@carryqty{$w} > 0} {
      #VARIABLE {damageok} {1};
    };
  };
  #NOP {命中类};
  #FOREACH {$conf[wxbweapons][attack][]} {w} {
    #IF {@carryqty{$w} > 0} {
      #VARIABLE {attackok} {1};
    };
  };
  #NOP {悟性类};
  #FOREACH {$conf[wxbweapons][wuxing][]} {w} {
    #IF {@carryqty{$w} > 0} {
      #VARIABLE {wuxingok} {1};
    };
  };
  #IF {$damageok == 1 && $attackok == 1 && $wuxingok == 1} {
    #RETURN {0};
  };
  #IF {$env[wxb] == 0} {
    #RETURN {0};
  };
  #RETURN {1};
};
#ALIAS {jobserve_sell} {
  #VARIABLE {okflag} {1};
  #CLASS jobcheckclass KILL;
  #CLASS jobcheckclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checksell\"|你设定checksell为反馈信息}} {
    #VARIABLE {idle} {0};
    #FOREACH {*id[things][]} {t} {
      #IF {@contains{{common[tradedrugs]}{$t}} > 0} {
        sell $t;
        #VARIABLE {okflag} {0};
        #BREAK;
      };
    };
    #IF {@carryqty{silver} >= 400} {
      w;w;duihuan 400 silver to gold;e;e;i;
      #VARIABLE {okflag} {1};
      #DELAY {1} {echo {checksell}};
    };
    #ELSEIF {$okflag == 0} {
      i;
      #VARIABLE {okflag} {1};
      #DELAY {1} {echo {checksell}};
    };
    #ELSE {
      #CLASS jobcheckclass KILL;
      gotodo {长安城} {威信钱庄} {
        jobserve_duihuan {gotoroom {424} {jobserve_start}}
      }
    };
  };
  #CLASS jobcheckclass CLOSE;
  i;
  echo {checksell}
};
#ALIAS {jobserve_duihuan} {
  #CLASS jobcheckclass KILL;
  #CLASS jobcheckclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkduihuan\"|你设定checkduihuan为反馈信息}} {
    #DELAY {1} {
      #IF {@carryqty{silver} >= 100} {
        duihuan @eval{(@carryqty{silver} / 100) * 100} silver to gold;
        i;
        echo {checkduihuan}
      };
      #ELSE {
        #CLASS jobcheckclass KILL;
        drop coin;
        %1
      };
    };
  };
  #CLASS jobcheckclass CLOSE;
  duihuan @eval{(@carryqty{silver} / 100) * 100} silver to gold;
  i;
  echo {checkduihuan}
};