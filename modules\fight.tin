#NOP {战斗模块};
#VARIABLE {lostflag} {0};
#NOP {非预期武器丢失};
#VARIABLE {unexpectlost} {0};
#VARIABLE {weaponlost} {0};
#VARIABLE {secondarylost} {0};
#NOP {危险情况标识};
#VARIABLE {dangerousflag} {0};
#NOP {负面状态逃跑阈值(秒),0表示不逃跑};
#NOP {你只能装备可当作武器的东西};
#VARIABLE {common[negative]} {
  {封招} {20}
  {闭气} {0}
  {气息不匀} {0}
  {内息紊乱} {0}
};
#VARIABLE {negeffect} {};
#VARIABLE {npcdown} {0};
#NOP {正在战斗的NPC};
#VARIABLE {fightnpc} {nobody};
#CLASS fightmodule KILL;
#CLASS fightmodule OPEN;
#VARIABLE {xtjtarget} {};
#NOP {逃跑标识符};
#VARIABLE {escapeflag} {0};
#VARIABLE {escapetimestap} {0};
#NOP {===================================通用战斗相关触发器==================================开始};
#NOP {更新角色配置时更新perform};
#ACTION {^角色配置加载完毕} {
  guide_setpfm
};
#NOP {武器碎了};
#ACTION {手中的%*使用过久，终于断为两截} {
  logbuff {broken}
};
#ACTION {^{执戒|守律}僧说道：「%*，戒律院玄寂大师请你去陈述此次下山经过} {
  #IF {"%2" == "$hp[name]"} {
    #VARIABLE {env[guilty]} {1};
  };
}
#ACTION {^{执戒|守律}僧说道：「%*，你这佛门败类，哪里逃！ 还不速到戒律院领罪} {
  #IF {"%2" == "$hp[name]"} {
    #VARIABLE {env[guilty]} {1};
  };
}
#ACTION {^张中把手一伸拦住你的去路，你已拿过书了还要} {
  stopwalk;
  #DELAY {2} {
    jobnextroom {$aimdo} {doabort};
  };
};
#ACTION {^%*巡捕盯着你看了几眼睛，突然一下把你拦住。} {
	#VARIABLE {env[naked]} {1};
  stopwalk;
  #DELAY {2} {
    dohalt {buycloth {gotodo {$aimcity} {$aimroomid} {$aimdo}}}
  };
};
#ACTION {^有个声音说道：不能带着菩提子离开伏魔圈} {
  drop puti zi;
};
#ACTION {^「%*」只能{对|在}战斗中%*使用} {
  createpfm {} {1}
};
#ACTION {^你只能{对|在}战斗中%*使用「%*」} {
  createpfm {} {1}
};
#NOP {明教巡逻意外遇到杀手};
#ACTION {^{蒙面客|黑衣人|神秘人|黑衣忍者|山贼|探子|死士}一言不发，闪身拦在你面前} {
  stopwalk;
  #LINE ONESHOT #ACTION {^{蒙面客|黑衣人|神秘人|黑衣忍者|山贼|探子|死士}转身几个起落就不见了} {
    dohalt {loc {walk}};
  };
};
#NOP {被打晕};
#ACTION {^你只觉得头昏脑胀，眼前一黑，接着什么也不知道了} {
};
#VARIABLE {dgjjzj} {0};
#ACTION {^一股暖流发自丹田流向全身，慢慢地你又恢复了知觉} {
  #LINE ONESHOT #ACTION {^{设定环境变量：action \= \"checklost\"|你设定checklost为反馈信息}} {
    get $conf[weapon][primary];
    get $env[currentweapon];
    #VARIABLE {weaponlost} {0};
    #NOP {受伤过重，或者气血很少且闭气};
    dohalt {
      #IF {($hp[qi_per] < 30 || ($hp[qi] < 1000 && "$hp[cond][闭气]" != "") || "$hp[condition][化骨绵掌]" != "")} {
        #IF {@carryqty{dahuan dan} > 0 || @carryqty{da huandan} > 0} {
          fudahuan;
        };
        #ELSEIF {@carryqty{chuanbei wan} > 0} {
          fuwan
        };
      };
      #ELSE {
        fuyao;
      };
      yun jing;
      yun qi;
      #IF {$lostflag == 1} {
        #VARIABLE {lostflag} {0};
        joblog {主武器丢失} {公共信息}
      };
      hp;
      loc {doevac {doabort}}
    }
  };
  stopfight;
  i;
  yun qi;
  hp;
  cond;
  jifa;
  echo {checklost}
};
#ACTION {^你「啪」的一声倒在地上，挣扎着抽动了几下就死了} {
  joblog {被@getKillerNpc{}打死。};
  logbuff {death};
  stopwalk;
  stopfight;
  jobclear;
  dodeath
} {1};
#ACTION {^你必须先用 enable 选择你要用的特殊内功。} {
  #IF {$hp[exp] > 165000} {
    jifa all;
    prepareskills
  };
};
#NOP {意外的怪蛇毒，单独处理怪蛇的方法中会创建一个触发器覆盖(优先级高)此处的触发};
#ACTION {你忽然感到身体僵直，已经不听使唤了} {
  hp;
  #IF {$hp[jing_per] < 30} {
    fu dahuan dan;
    hp;
  };
};
#NOP {意外进入已放弃的武当任务NPC战斗处理逻辑};
#ACTION {^%*对着你发出一阵} {
  #IF {"$currentjob" != "武当" || "%1" != "$jobnpc_bastard"} {
    #NOP {意外碰到放弃的武当NPC应中止当前动作并逃离，逃脱后检查状态再继续先前的操作};
    #NOP {如果被打晕后应继续原有任务动作};
    stopwalk;
    runawayside {dodisengage} {} {1}
  };
};
#NOP {===================================通用战斗相关触发器==================================结束};

#NOP {===================================非正常流程NPC超猛BUFF==================================开始};
#NOP {武当NPC见面就可能发perform，此时尚未进入startfight};
#ACTION {^你%*周身武功竟发挥不出平时一半威力！} {
  #VARIABLE {dangerousflag} {1};
  #SHOWME {<faa>进入危险情况，准备撤退};
};
#ACTION {^你%*武功中厉害之处完全无法发挥出来！} {
  #VARIABLE {dangerousflag} {1};
  #SHOWME {<faa>进入危险情况，准备撤退};
};
#NOP {NPC超猛技能};
#ACTION {^%*这时一声高喝，脸上殷红如血，僧袍都鼓了} {
  #IF {"%%1" == "$fightnpc"} {
    #VARIABLE {dangerousflag} {1};
    #SHOWME {<faa>进入危险情况，准备撤退};
  };
};
#NOP {韦陀杵雷动，直接跑路吧};
#ACTION {^突然%*大喝一声：「雷动九天」，面色唰的变得通红} {
  #IF {"%%1" == "$fightnpc"} {
    #VARIABLE {dangerousflag} {1};
    #SHOWME {<faa>进入危险情况，准备撤退};
  };
};
#NOP {七伤总决，直接跑路吧};
#ACTION {^你一晃眼，只见%*的拳已经打在你的身上，跟着几股说不出的暗劲顺势传了过来} {
  #VARIABLE {dangerousflag} {1};
  #SHOWME {<faa>进入危险情况，准备撤退};
};
#NOP {===================================非正常流程NPC超猛BUFF==================================结束};

#NOP {===================================非预期战斗武器丢失==================================开始};
#ACTION {^你只觉腕上一阵剧痛，只听得咔嚓一声，腕骨已经被扭断！%*再也把持不住，脱手而出！} {#VARIABLE {unexpectlost} {1}};
#ACTION {^你只感%*似欲脱手飞出，一个把握不住，手中兵器被夺飞了出去。} {#VARIABLE {unexpectlost} {1}};
#ACTION {^你%!*被迫放弃了手中的%*。} {#VARIABLE {unexpectlost} {1}};
#ACTION {^你浑身一热，手掌虎口巨痛，手中%*脱手而出。} {#VARIABLE {unexpectlost} {1}};
#ACTION {^你只觉得浑身一热，手掌虎口巨痛，手中%*脱手而出。} {#VARIABLE {unexpectlost} {1}};
#ACTION {^%!*你手腕上中了一击，被迫放弃了手中的%*。} {#VARIABLE {unexpectlost} {1}};
#ACTION {^你只觉得手腕剧痛，手中%*再也拿捏不住，脱手而出} {#VARIABLE {unexpectlost} {1}};
#ACTION {^你正打不还手呢，怎么杀} {yield no};
#ACTION {^你劲力被这么一带，登时身不由主，连转了几个七八个圈子，%*脱手而出。} {#VARIABLE {unexpectlost} {1}};
#ACTION {^你只感%*一个把握不住，手中兵器被挑飞了出去。} {#VARIABLE {unexpectlost} {1}};
#ACTION {^你%*情急之下只好放弃了手中的兵刃。} {#VARIABLE {unexpectlost} {1}};
#ACTION {^你只觉一股大力袭来，臂膀剧痛，手中%*再也拿捏不住，脱手而出！} {#VARIABLE {unexpectlost} {1}};
#ACTION {^你的%*被黑白棋子连续打中，震得你虎口疼痛，手臂发麻，拿捏不住，%*坠地} {#VARIABLE {unexpectlost} {1}};
#ACTION {^你招式渐见涩滞，只觉得手中%*倒似不断的在增加重量，一个把持不定，脱手飞出} {#VARIABLE {unexpectlost} {1}};
#ACTION {^你%*被袭，一个把持不住，%*脱手而出，心下大惊，顿时手忙脚乱} {#VARIABLE {unexpectlost} {1}};
#NOP {===================================非预期战斗武器丢失==================================结束};
#NOP {通用perform alias,具体配置在各角色文件};
#NOP {增加悟性};
#ALIAS {pfm_wuxing} {
	#LOCAL {pfmcmd} {$conf[pfm][wuxing]};
	#IF {"$pfmcmd" != ""} {
		$pfmcmd
	};
};
#NOP {普通增幅};
#ALIAS {pfm_buff_normal} {
	#LOCAL {pfmcmd} {$conf[pfm][normalbuff]};
	#IF {"$pfmcmd" != ""} {
		$pfmcmd
	};
};
#NOP {战斗增幅};
#ALIAS {pfm_buff_fight} {
	#LOCAL {pfmcmd} {$conf[pfm][fightbuff]};
	#IF {"$pfmcmd" != ""} {
		$pfmcmd
	};
};
#NOP {busy对手};
#ALIAS {pfm_busy} {
	#LOCAL {pfmcmd} {$conf[pfm][busy]};
	#IF {"$pfmcmd" != ""} {
		$pfmcmd
	};
};
#NOP {进行攻击};
#ALIAS {pfm_attack} {
	#LOCAL {pfmcmd} {$conf[pfm][attack]};
	#IF {"$pfmcmd" != ""} {
		$pfmcmd
	};
};
#NOP {攻击快捷指令};
#ALIAS {pfm} {
  pfm_buff_fight;
	pfm_busy;
	pfm_attack
};
#NOP {获取任务场景下使用的pfm(alias)，%1-技能或技能描述(雪山)，%2-任务场景,为空取currentjob。};
#FUNCTION getFightPerform {
  #LOCAL {scene} {%2};
  #LOCAL {pfmalias} {};
  #IF {"$scene" == ""} {
    #LOCAL {scene} {$currentjob};
  };
  #SWITCH {"$scene"} {
    #CASE {"武当"} {
      #IF {"%1" != ""} {
        #LOCAL {pfmalias} {$conf[pfm][npc][skill][%1]};
      };
      #IF {"$pfmalias" == ""} {
        #LOCAL {pfmalias} {$conf[pfm][scene][wd]};
      };
    };
    #CASE {"送信"} {
      #LOCAL {pfmalias} {$conf[pfm][scene][sx]};
    };
    #CASE {"送信2"} {
      #IF {"%1" != ""} {
        #LOCAL {pfmalias} {$conf[pfm][skill][sx2][%1]};
        #IF {"$pfmalias" == ""} {
          #LOCAL {pfmalias} {$conf[pfm][npc][skill][%1]};
        };
      };
      #IF {"$pfmalias" == ""} {
        #LOCAL {pfmalias} {$conf[pfm][scene][sx2]};
      };
    };
    #CASE {"华山"} {
      #LOCAL {pfmalias} {$conf[pfm][scene][hs]};
    };
    #CASE {"华山2"} {
      #IF {"%1" != ""} {
        #LOCAL {pfmalias} {$conf[pfm][skill][hs2][%1]};
        #IF {"$pfmalias" == ""} {
          #LOCAL {pfmalias} {$conf[pfm][npc][skill][%1]};
        };
      };
      #IF {"$pfmalias" == ""} {
        #LOCAL {pfmalias} {$conf[pfm][scene][hs2]};
      };
    };
    #CASE {"雪山"} {
      #LOCAL {pfmalias} {$conf[pfm][npc][xs][%1]};
      #IF {"$pfmalias" == ""} {
        #LOCAL {pfmalias} {$conf[pfm][scene][xs]};
      };
    };
    #CASE {"嵩山请"} {
      #LOCAL {pfmalias} {$conf[pfm][scene][ssi]};
    };
    #CASE {"嵩山杀"} {
      #LOCAL {pfmalias} {$conf[pfm][scene][ssk]};
    };
    #CASE {"天地会"} {
      #LOCAL {pfmalias} {$conf[pfm][scene][tdh]};
    };
    #CASE {"丐帮"} {
      #LOCAL {pfmalias} {$conf[pfm][scene][tb]};
    };
    #CASE {"颂摩崖"} {
      #LOCAL {pfmalias} {$conf[pfm][scene][smy]};
    };
    #CASE {"守卫襄阳"} {
      #LOCAL {pfmalias} {$conf[pfm][scene][swxy]};
    };
    #CASE {"天地会"} {
      #LOCAL {pfmalias} {$conf[pfm][scene][tdh]};
    };
    #CASE {"七窍玲珑"} {
      #LOCAL {pfmalias} {$conf[pfm][scene][qqll]};
    };
    #CASE {"长乐帮2"} {
      #LOCAL {pfmalias} {$conf[pfm][scene][clb]};
    };
    #CASE {"官府"} {
      #LOCAL {pfmalias} {$conf[pfm][scene][gf]};
    };
    #CASE {"护送"} {
      #LOCAL {pfmalias} {$conf[pfm][scene][husong]};
    };
  };

  #RETURN {$pfmalias};
};
#NOP {是否空手状态};
#VARIABLE {pfmcmds} {};
#VARIABLE {pfmcmdsbackup} {};
#NOP {创建系统pfm指令,%1:pfm代码,%2:强制重新创建,%3:主目标,%4:副目标,%5:叫杀ID,%6:是否件捡武器,%7:是否需要确认指令};
#ALIAS {createpfm} {
  #VARIABLE {pfm_weapon} {};
  #VARIABLE {pfm_cmds} {};
  #LOCAL {pfmalias} {%1};
  #IF {"$conf[pfm][now]" != ""} {
    #LOCAL {pfmalias} {$conf[pfm][now]};
    #VARIABLE {conf[pfm][now]} {};
  };
  #ELSE {
    #VARIABLE {conf[pfm][now]} {%1};
  };
  #IF {"$pfmalias" != "" && "$conf[pfm][alias][$pfmalias]" != ""} {
    #NOP {指定的pfm};
    $conf[pfm][alias][$pfmalias][do];
    #VARIABLE {pfm_weapon} {$conf[pfm][alias][$pfmalias][weapon]};
    #IF {"$pfm_weapon" == "auto"} {
      #VARIABLE {pfm_weapon} {$conf[weapon][primary]};
    };
    #VARIABLE {pfm_cmds} {$conf[pfm][alias][$pfmalias][cmd]};
    #VARIABLE {env[currentpfm]} {$pfmalias};
  };
  #ELSE {
    #NOP {默认pfm};
    #VARIABLE {pfm_weapon} {$conf[weapon][primary]};
    #VARIABLE {pfm_cmds} {$conf[pfm][attack]};
    #VARIABLE {env[currentpfm]} {};
  };
  #NOP {武器判定};
  #IF {"$pfm_weapon" != "" && "%6" == ""} {
    #NOP {如果武器不存在那么需要找到可替换的武器};
    #IF {@carryqty{$pfm_weapon} == 0} {
      #VARIABLE {pfm_weapon} {@getReplaceWeapon{$pfm_weapon}}
    };
    #NOP {如果最终找不到武器则需要启用空手pfm，如果存在的话};
    #IF {@carryqty{$pfm_weapon} == 0} {
      #IF {"$conf[pfm][scene][unarmed]" != "" && "$conf[pfm][alias][$conf[pfm][scene][unarmed]]" != ""} {
        #VARIABLE {pfm_cmds} {$conf[pfm][alias][$conf[pfm][scene][unarmed]][cmd]};
        #VARIABLE {pfm_weapon} {};
        $conf[pfm][alias][$conf[pfm][scene][unarmed]][do];
        #VARIABLE {env[currentpfm]} {$conf[pfm][scene][unarmed]};
      };
      #ELSE {
        #NOP {如果武器没有且未配置空手pfm则处于危险状态};
        #VARIABLE {pfm_cmds} {};
        #VARIABLE {env[currentpfm]} {};
      };
    };
    #ELSE {
      #VARIABLE {env[currentweapon]} {$pfm_weapon};
    };
  };
  #IF {"$pfm_cmds" == ""} {
    #NOP {如未能正确获取到pfm指令则处于危险状态};
    #VARIABLE {dangerousflag} {1};
  };
  #ELSE {
    #NOP {卸载原有武器};
    #IF {"$id[weapon]" != "" && "$pfm_weapon" != "$id[weapon]"} {
      #VARIABLE {pfm_cmds} {unwield $id[weapon];$pfm_cmds};
      uwwp {$id[weapon]}
    };
    #NOP {装备pfm武器};
    #IF {"$pfm_weapon" != ""} {
      #VARIABLE {pfm_cmds} {wield $pfm_weapon;$pfm_cmds};
      #IF {"%6" != ""} {
        #VARIABLE {pfm_cmds} {get $pfm_weapon;$pfm_cmds};
      };
      #ELSE {
        wwp {$pfm_weapon}
      };
    };
    #NOP {整合战斗buff指令};
    #IF {"$conf[pfm][fightbuff]" != ""} {
      #VARIABLE {pfm_cmds} {$conf[pfm][fightbuff];$pfm_cmds};
    };
    #NOP {整合叫杀对象};
    #IF {"%5" != ""} {
      #VARIABLE {pfm_cmds} {kill %5;$pfm_cmds};
    };
    #IF {("$pfm_cmds" != "" && "$pfm_cmds" != "$pfmcmds") || "%2" != "" || "%3" != ""} {
      #VARIABLE {pfmcmds} {$pfm_cmds};
      #VARIABLE {tempcmds} {$pfmcmds};
      #IF {"%4" != ""} {
        #REPLACE {tempcmds} {#s} {%4};
      };
      #IF {"%3" != ""} {
        #REPLACE {tempcmds} {#p} {%3};
        #REPLACE {tempcmds} {#s} {%3};
      };
      #REPLACE {tempcmds} {#p} {};
      #REPLACE {tempcmds} {#s} {};
      #IF {"%7" == ""} {
        #SEND {alias autopfm $tempcmds};
        #IF {$srvecho == 0} {
          alias confirmhp hp;cond;set action checkhp;
        };
        #ELSE {
          #SEND {alias confirmhp hp;cond;srvback checkhp};
        };
        set wimpycmd autopfm\\confirmhp;
      };
      #ELSE {
        donext {
          #SEND {alias autopfm $tempcmds};
          #IF {$srvecho == 0} {
            alias confirmhp hp;cond;set action checkhp;
          };
          #ELSE {
            #SEND {alias confirmhp hp;cond;srvback checkhp};
          };
        } {set wimpycmd autopfm\\confirmhp}
      };
    };
    #VARIABLE {pfmcmdsbackup} {$pfmcmds};
  };
};
#NOP {设置perform目标，%1:首要目标，%2:次要目标，如只有一个目标次要目标可为空};
#NOP {即刻根据当前任务perform模板更新alias并执行，开启战斗模式};
#VARIABLE {targetpfmcmds} {};
#ALIAS {setpfmtarget} {
  #NOP {cmds是否有占位符};
  #VARIABLE {tempcmds} {$pfmcmds};
  #REGEXP {$tempcmds} {#} {
    #IF {"%2" != ""} {
      #REPLACE {tempcmds} {#s} {%2};
    };
    #IF {"%1" != ""} {
      #REPLACE {tempcmds} {#p} {%1};
      #REPLACE {tempcmds} {#s} {%1};
    };
    #ELSE {
      #REPLACE {tempcmds} {#p} {};
      #REPLACE {tempcmds} {#s} {};
    };
  } {};
  #IF {"$tempcmds" != "$targetpfmcmds"} {
    #VARIABLE {targetpfmcmds} {$tempcmds};
    #SEND {alias autopfm $tempcmds};
  };
};
#NOP {吃蝉蜕药};
#ALIAS {fuyao} {
  #IF {"$id[things][chantui yao]" != ""} {
    fu chantui yao
  };
};
#NOP {吃活血丹};
#ALIAS {fudan} {
  #IF {"$id[things][huoxue dan]" != ""} {
    fu huoxue dan;
    i;
    hp
  };
};
#NOP {吃川贝丸};
#ALIAS {fuwan} {
  #IF {"$id[things][chuanbei wan]" != ""} {
    fu chuanbei wan;
    i;
    hp
  };
};
#NOP {吃大还丹};
#ALIAS {fudahuan} {
  #IF {"$id[things][dahuan dan]" != "" || "$id[things][da huandan]" != ""} {
    fu dahuan dan;
    i;
    hp
  };
};
#NOP {搞定NPC};
#ALIAS {takedown} {
  #VARIABLE {npcdown} {1};
};
#NOP {是否需要逃跑};
#FUNCTION needEscape {
  #LOCAL {escflag} {0};
  #NOP {跑路条件};
  #IF {($hp[qi_per] < 30 || $hp[qi_ratio] < 30) && @carryqty{chantui yao} == 0 && @carryqty{dahuan dan} == 0 && @carryqty{da huandan} == 0} {
    #RETURN {1};
  };
  #IF {"$hp[condition][怪蛇]" != ""} {
    #RETURN {1};
  };
  #FOREACH {*common[negative][]} {c} {
    #IF {$common[negative][$c] != 0 && @eval{$hp[condition][$c]} >= $common[negative][$c]} {
      #NOP {被幽冥后闭气封招硬抗，除非不行了(气小于最大气10%)};
      #IF {"$negativeffect[youming]" != ""} {
        #IF {$hp[qi_per] >= 50 && $hp[qi_ratio] >= 30 && @eval{$conf[fight][youming]} == 1} {
          #CONTINUE;
        };
      };
      #LOCAL {escflag} {1};
      #BREAK;
    };
  };
  #RETURN {$escflag};
};
#NOP {获取跑路安全的跑路出口};
#FUNCTION getEscapeExit {
  #VARIABLE {tempexits} {$roomexits};
  #LOCAL {voidindex} {0};
  #IF {@contains{{riverrooms}{$room}} > 0} {
    #LOCAL {voidindex} {@contains{{tempexits}{enter}}};
    #IF {$voidindex > 0} {
      #LIST {tempexits} {delete} {$voidindex};
    };
  };
  #ELSEIF {"$room" == "吐谷浑伏俟城"} {
    #LOCAL {voidindex} {@contains{{tempexits}{s}}};
    #LIST {tempexits} {delete} {$voidindex};
  };
  #NOP {排除需要杀人才能通过的};
  #MAP GET {ROOMEXITS} {temproomexits} {$roomid};
  #FOREACH {*temproomexits[]} {e} {
    #IF {@startWiths{{$e}{killnpc}} == 0 && @startWiths{{$e}{river}} == 0} {
      #CONTINUE;
    };
    #LIST {segs} {create} {$e};
    #LOCAL {voidindex} {@contains{{tempexits}{$segs[+&segs[]]}}};
    #IF {$voidindex > 0} {
      #LIST {tempexits} {delete} {$voidindex};
    };
  };
  #RETURN {$tempexits[+@rnd{{1}{&tempexits[]}}]};
};
#NOP {撤退时确保跑路成功，%1:后续指令，%2:延迟执行指令时间默认两秒，部分NPC消失可能要一点时间,%3:强制使用指定的%1参数，不再嵌套doevac};
#ALIAS {runawayside} {
  #VARIABLE {escapefail} {0};
  #VARIABLE {escapepath} {};
  #VARIABLE {seconds} {2};
  #VARIABLE {runoverdo} {doevac {%1}};
  #IF {"%3" != ""} {
    #VARIABLE {runoverdo} {%1};
  };
  #IF {"%2" != ""} {
    #VARIABLE {seconds} {@eval{%2}};
  };
  #IF {$seconds < 0} {
    #VARIABLE {seconds} {0};
  };
  #CLASS jobescapeclass KILL;
  #CLASS jobescapeclass OPEN;
  #ACTION {^你{的动作还没有完成，不能移动|转身想跑|转身就要开溜|逃跑失败|正在使用}} {
    #VARIABLE {escapefail} {1};
  } {1};
  #ACTION {^{设定环境变量：action \= \"checkescape\"|你设定checkescape为反馈信息}} {
    resonate {checkescape};
    #VARIABLE {idle} {0};
    #SHOWME {<faa>escape:npcdown = $npcdown,escapeflag = $escapeflag,escapefail = $escapefail};
    #IF {"$escapepath" == ""} {
      #VARIABLE {escapepath} {@getWalkPath{{$roomid}{@getSafeExit{$roomid}}}};
    };
    #IF {$npcdown == 1} {
      #NOP {逃跑的时候NPC倒了那就不用跑了};
      #CLASS jobescapeclass KILL;
      #VARIABLE {npcdown} {0};
      #VARIABLE {escapeflag} {0};
    };
    #ELSEIF {$escapefail == 1} {
      #VARIABLE {escapefail} {0};
      #VARIABLE {escapetimestap} {0};
      #DELAY {0.2} {
        halt;
        $escapepath;
        echo {checkescape};
      };
    };
    #ELSE {
      #CLASS jobescapeclass KILL;
      #CLASS jobfightclass KILL;
      #VARIABLE {dangerousflag} {0};
      #VARIABLE {escapeflag} {0};
      #VARIABLE {escapefail} {0};
      #NOP {一些特定的任务不能回头};
      #IF {"$currentjob" == "长乐帮2"} {
        #DELAY {$seconds} {loc {$runoverdo}};
      };
      #ELSE {
        #NOP {logbuff {runaway}};
        #DELAY {$seconds} {
          @reverseDir{$escapepath};
          checklostweapon {loc {$runoverdo}}
        };
      };
    };
  };
  #CLASS jobescapeclass CLOSE;
  #NOP {再次确保不进行战斗};
  #VARIABLE {npcdown} {0};
  #VARIABLE {escapeflag} {1};
  #VARIABLE {escapetimestap} {@now{}};
  #VARIABLE {escapefail} {1};
  #NOP {跑路时清除一切挂起的重试操作};
  #VARIABLE {env[echo]} {};
  closewimpy;
  loc {echo {checkescape} {2}}
};
#NOP {安全离开后返回并执行指令,%1:后续指令，%2:延迟执行指令时间默认两秒};
#ALIAS {runawayback} {
  #VARIABLE {escapefail} {0};
  #VARIABLE {escapepath} {@getEscapeExit{}};
  #VARIABLE {seconds} {2};
  #IF {"%2" != ""} {
    #VARIABLE {seconds} {@eval{%2}};
  };
  #IF {$seconds < 0} {
    #VARIABLE {seconds} {0};
  };
  #CLASS jobescapeclass KILL;
  #CLASS jobescapeclass OPEN;
  #ACTION {^你{的动作还没有完成，不能移动|转身想跑|转身就要开溜|逃跑失败|正在使用}} {
    #VARIABLE {escapefail} {1};
  } {1};
  #ACTION {^{设定环境变量：action \= \"checkescape\"|你设定checkescape为反馈信息}} {
    #IF {$npcdown == 1} {
      #NOP {逃跑的时候NPC倒了那就不用跑了};
      #CLASS jobescapeclass KILL;
    };
    #ELSEIF {$escapefail == 1} {
      #VARIABLE {escapefail} {0};
      #DELAY {0.3} {
        halt;
        $escapepath;
        echo {checkescape};
      };
    };
    #ELSE {
      #CLASS jobescapeclass KILL;
      #DELAY {$seconds} {
        @reverseDir{$escapepath};
        %1
      };
    };
  };
  #CLASS jobescapeclass CLOSE;
  #NOP {再次确保不进行战斗};
  closewimpy;
  halt;
  $escapepath;
  echo {checkescape};
};
#NOP {检查武器是否丢失，%1:后续指令};
#ALIAS {checklostweapon} {
  #CLASS checkweaponclass KILL;
  #CLASS checkweaponclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checklostweapon\"|你设定checklostweapon为反馈信息}} {
    #CLASS checkweaponclass KILL;
    #VARIABLE {weaponlost} {0};
    #VARIABLE {secondarylost} {0};
    #IF {"$conf[weapon][primary]" != "" && @carryqty{$conf[weapon][primary]} == 0} {
      get $conf[weapon][primary];
    };
    #IF {"$conf[weapon][secondary]" != "" && @carryqty{$conf[weapon][secondary]} == 0} {
      get $conf[weapon][primary];
    };
    %1
  };
  #CLASS checkweaponclass CLOSE;
  i;
  echo {checklostweapon}
};
#NOP {捡起武器};
#ALIAS {pickweapon} {
  #NOP {武器判定逻辑};
  #IF {$weaponlost == 1 || "@getWeaponSkill{$conf[weapon][primary]}" == "hammer"} {
    #VARIABLE {weaponlost} {0};
    get $conf[weapon][primary];
  };
  #IF {$unexpectlost == 1} {
    #VARIABLE {unexpectlost} {0};
    get $conf[weapon][primary];
  };
  #IF {$secondarylost == 1} {
    #VARIABLE {secondarylost} {0};
    get $conf[weapon][secondary];
  };
  #IF {$lostflag == 1} {
    #VARIABLE {lostflag} {0};
  };
};
#NOP {设置当前对手,%1:NPC名称};
#ALIAS {settarget} {
  #VARIABLE {fightnpc} {%1}
};
#NOP {开始战斗,%1:异常状态是否逃跑,非空不跑，%2:目标NPC};
#ALIAS {startfight} {
  #VARIABLE {negativeffect[youming]} {0};
  #VARIABLE {npcdown} {0};
  #VARIABLE {negativeffect} {};
  #VARIABLE {weaponlost} {0};
  #VARIABLE {checktimestamp} {@now{}};
  #VARIABLE {actiontimestamp} {@now{}};
  #VARIABLE {lastneili} {$};
  #VARIABLE {idle} {0};
  #VARIABLE {fightdo} {
    pfm;
    hp;
    #DELAY {1} {
      set action checkhp;
    }
  };
  #IF {"%2" != ""} {
    #VARIABLE {fightnpc} {%2};
  };
  #ELSE {
    #VARIABLE {fightnpc} {nobody};
  };
  #CLASS fightclass KILL;
  #CLASS fightclass OPEN;
  #ACTION {^error: %* last_damage_from 星宿毒发精血受损} {
    #CLASS fightclass KILL;
    #NOP {毒发死亡错误，直接放弃把};
    dohalt {doabort}
  };
  #NOP {目标动作，被NPC长时间busy时无法出招，通过NPC攻击动作重置战斗时间戳防止发呆};
  #ACTION {^$fightnpc%*{你的|你}{头部|颈部|胸口|胸前|后心|左肩|右肩|左臂|右臂|左手|右手|腰间|小腹|左腿|右腿|左脚|右脚|左耳|右耳|左脸|右脸|小腹}} {
    #VARIABLE {idle} {0};
    #VARIABLE {checktimestamp} {@now{}};
  };
  #ACTION {^$fightnpc%*{将|向|对准}你} {
    #VARIABLE {idle} {0};
    #VARIABLE {checktimestamp} {@now{}};
  };
  #NOP {圣火令法的busy很长};
  #ACTION {^只听见铮的一响，你心神震荡，身子从半空中直堕下来} {
    #VARIABLE {idle} {-30};
  };
  #NOP {战斗相持描述};
  #ACTION {^你{注视着|正盯着|缓缓地移动着|聚精会神地盯着|目不转睛地盯着|慢慢地移动着}} {
    #VARIABLE {actiontimestamp} {@now{}};
    #VARIABLE {idle} {0};
  };
  #NOP {状态判定};
  #ACTION {^( 你{看起来充满活力|似乎有些疲惫|看起来可能有些累了|动作似乎开始有点不太灵光|看起来气血充盈|似乎受了点轻伤|看起来可能受了点轻伤|受了几处伤，不过似乎并不碍事}} {
    #VARIABLE {actiontimestamp} {@now{}};
    #VARIABLE {idle} {0};
  };
  #NOP {战斗伤害描述};
  #ACTION {^结果{只是轻轻地|只是把|只是在|「嗤」地一声|「噗」地一声|「噗嗤」地一声|「啪」地一声|「唰」地一声|「轰」地一声|「啊」地一声|只听见「砰」地一声|重重地击中|随着「咔」地一声|只听见「硼」地一声}} {
    #VARIABLE {actiontimestamp} {@now{}};
    #VARIABLE {idle} {0};
  };
  #NOP {状态判定};
  #ACTION {^( 你{看起来已经力不从心了|气喘嘘嘘|似乎十分疲惫|摇头晃脑、歪歪斜斜|已经一副头重脚轻的模样|已经陷入半昏迷状态}} {
    #VARIABLE {actiontimestamp} {@now{}};
    #VARIABLE {idle} {0};
    yun qi
  };
  #ACTION {^( 你{受伤不轻|你气息粗重|受了相当重的伤|已经伤痕累累|伤重之下|受伤过重}} {
    #VARIABLE {actiontimestamp} {@now{}};
    #VARIABLE {idle} {0};
    fuyao;
    yun qi
  };
  #ACTION {^你%*{内力|真气}{不够|太弱|不足}} {
    fuwan
  };
  #ACTION {^你的当前精力不够，无法} {
    yun jingli
  };
  #ACTION {^{设定环境变量：action \= \"checkhp\"|你设定checkhp为反馈信息}} {
    #VARIABLE {idle} {0};
    #VARIABLE {checktimestamp} {@now{}};
    #IF {$hp[qi_per] < 75 && $hp[qi_per] < @eval{$conf[healthreshold] - 10  } && $hp[qi_wound] > 10000 && (@carryqty{dahuan dan} > 0 || @carryqty{da huandan} > 0)} {
      fudahuan;
    };
    #ELSEIF {($hp[qi_per] < 80 || $hp[qi_wound] > 3000) && $conf[healthreshold] == 100} {
      fuyao;
      yun qi;
    };
    #ELSEIF {$hp[jing_per] < 80 && && $hp[jing_wound] < 3000} {
      fudan;
      yun jing;
    };
    #ELSEIF {@eval{$hp[qi_max]-$hp[qi]} > @eval{$hp[qi_max]/4}} {
      yun qi
    };
    #ELSEIF {@eval{$hp[jingli_max]-$hp[jingli]} > @eval{$hp[jingli_max]/3}} {
      yun jingli
    };
    #ELSEIF {@eval{$hp[jing_max]-$hp[jing]} > @eval{$hp[jing_max]/2}} {
      yun jing
    };
    #ELSEIF {$hp[neili] < 1000} {
      #IF {@carryqty{chuanbei wan} > 0} {
        fuwan;
      };
      #ELSEIF {@carryqty{dahuan dan} > 0 || @carryqty{da huandan} > 0} {
        fudahuan;
      };
      #ELSE {
        stopfight;
        joblog {战斗遇到危险情况，准备撤离};
        runawayside {
          doabort
        };
      };
    };
    #IF {"%1" == "" && @needEscape{} > 0} {
      joblog {身负异常状态，跑路};
      stopfight;
      runawayside {
        doabort
      };
    };
    #ELSEIF {$lostflag == 1} {
      #NOP {武器丢失状态下如果恰好被吹走，那么需要及时更换武器};
      i;
      id here;
      #IF {@isRoomthingExists{$env[currentweapon]} == 0 && @carryqty{$env[currentweapon]} == 0} {
        #NOP {重新创建pfm，更换武器逻辑在createpfm里面};
        #VARIABLE {lostflag} {0};
        createpfm {$env[currentpfm]} {1}
      };
    };
  };
  #ACTION {^你只能装备可当作武器的东西} {
    #IF {@carryqty{$conf[weapon][primary]} > 0} {
      drop $conf[weapon][primary];
    };
    #ELSEIF {@carryqty{$conf[weapon][secondary]} > 0} {
      drop $conf[weapon][secondary];
    };
  };
  #NOP {武器不对};
  #ACTION {^你必须先放下你目前装备的武器} {
    i;
    unwield $id[weapon] 1;
    unwield $id[weapon] 2;
  };
  #NOP {异常状态};
  #ACTION {^%*你陡然一惊，冷汗直冒} {
    #VARIABLE {negativeffect[youming]} {1};
  };
  #ACTION {^%*的幽明字诀的作用时间还没过，目前不能施用外功。} {
    #VARIABLE {negativeffect[youming]} {1};
  };
  #NOP {武器丢失};
  #ACTION {^你只觉腕上一阵剧痛，只听得咔嚓一声，腕骨已经被扭断！%*再也把持不住，脱手而出！} {dolostweapon} {1};
  #ACTION {^你只感%*似欲脱手飞出，一个把握不住，手中兵器被夺飞了出去。} {dolostweapon} {1};
  #ACTION {^你%!*被迫放弃了手中的%*。} {dolostweapon} {1};
  #ACTION {^你浑身一热，手掌虎口巨痛，手中%*脱手而出。} {dolostweapon} {1};
  #ACTION {^你只觉得浑身一热，手掌虎口巨痛，手中%*脱手而出。} {dolostweapon} {1};
  #ACTION {^%!*你手腕上中了一击，被迫放弃了手中的%*。} {dolostweapon} {1};
  #ACTION {^你只觉得手腕剧痛，手中%*再也拿捏不住，脱手而出} {dolostweapon} {1};
  #ACTION {^你正打不还手呢，怎么杀} {yield no} {1};
  #ACTION {^你劲力被这么一带，登时身不由主，连转了几个七八个圈子，%*脱手而出。} {dolostweapon} {1};
  #ACTION {^你只感%*一个把握不住，手中兵器被挑飞了出去。} {dolostweapon} {1};
  #ACTION {^你%*情急之下只好放弃了手中的兵刃。} {dolostweapon} {1};
  #ACTION {^你只觉一股大力袭来，臂膀剧痛，手中%*再也拿捏不住，脱手而出！} {dolostweapon} {1};
  #ACTION {^你的%*被黑白棋子连续打中，震得你虎口疼痛，手臂发麻，拿捏不住，%*坠地} {dolostweapon} {1};
  #ACTION {^你招式渐见涩滞，只觉得手中%*倒似不断的在增加重量，一个把持不定，脱手飞出} {dolostweapon} {1};
  #ACTION {^你%*被袭，一个把持不住，%*脱手而出，心下大惊，顿时手忙脚乱} {dolostweapon} {1};
  #NOP {被总决了};
  #ACTION {^你%*武功中厉害之处完全无法发挥出来！} {
    #VARIABLE {dgjjzj} {1};
    #VARIABLE {dangerousflag} {1};
    joblog {被总决了，跑路};
    closewimpy;
    #DELAY {0.5} {
      runawayside {
        stopfight;
        prepareskills {doabort};
      };
    };
  } {1};
  #NOP {NPC超猛技能};
  #ACTION {^%*这时一声高喝，脸上殷红如血，僧袍都鼓了起来} {
    #SHOWME {<faa>伏魔圈启动判定 "%%1" == "$fightnpc"};
    #IF {"%%1" == "$fightnpc"} {
      #VARIABLE {dangerousflag} {1};
      joblog {NPC祭出金刚伏魔圈，跑路};
      closewimpy;
      #DELAY {0.5} {
        runawayside {
          stopfight;
          doabort
        };
      };
    };
  } {1};
  #ACTION {^突然%*大喝一声：「雷动九天」，面色唰的变得通红，须发皆飞} {
    #SHOWME {<faa>雷动九天启动判定 "%%1" == "$fightnpc"};
    #IF {"%%1" == "$fightnpc"} {
      #VARIABLE {dangerousflag} {1};
      joblog {NPC祭出动九天，跑路};
      closewimpy;
      #DELAY {0.5} {
        runawayside {
          stopfight;
          doabort
        };
      };
    };
  } {1};
  #NOP {伏魔圈启动后的攻击反弹描述};
  #ACTION {^不料%*所组成的「金刚伏魔圈」已如铜墙铁壁，你数次冲击，均遭挡回} {
    joblog {NPC祭出金刚伏魔圈，跑路};
    stopfight;
    runawayside {
      doabort
    };
  } {1};
  #ACTION {^不料%*刚要碰到%*所组成的「金刚伏魔圈」，%*手腕抖动，%*直昂上来，撞向你面门} {
    joblog {NPC祭出金刚伏魔圈，跑路};
    stopfight;
    runawayside {
      doabort
    };
  } {1};
  #NOP {物理免疫技能};
  #NOP {中了七弦无形剑，物理基本无效};
  #ACTION {^你内力和琴音一生共鸣，便不知不觉地为琴音所制} {
    #NOP {切换强力化学pfm或者跑路};
    joblog {NPC祭出六丁开山，跑路};
    stopfight;
    runawayside {
      doabort
    };
  } {1};
  #ACTION {^你一晃眼，只见%*的拳已经打在你的身上，跟着几股说不出的暗劲顺势传了过来} {
    joblog {NPC祭出七伤拳总决，跑路};
    stopfight;
    runawayside {
      doabort
    };
  } {1};
  #NOP {原始剑法};
  #NOP {古拙掌法};
  #NOP {天罗地网};
  
  #NOP {被打晕};
  #ACTION {^你只觉得头昏脑胀，眼前一黑，接着什么也不知道了} {
    joblog {被@getKillerNpc{}打晕。};
    #IF {"$currentjob" == "武当"} {
      joblog {未能完成任务，耗时@elapsed{$jobstart_ts}秒。};
    };
    #LINE ONESHOT #ACTION {^一股暖流发自丹田流向全身，慢慢地你又恢复了知觉} {
      #LINE ONESHOT #ACTION {^{设定环境变量：action \= \"checklost\"|你设定checklost为反馈信息}} {
        #CLASS fightclass KILL;
        #CLASS jobfightclass KILL;
        #CLASS jobcheckclass KILL;
        get $conf[weapon][primary];
        get $env[currentweapon];
        loc {doevac {doabort}}
      };
      stopfight;
      fuyao;
      yun qi;
      i;
      echo {checklost};
    } {2};
  } {1};
  #NOP {处理perform动态触发器，最多支持5个};
  #VARIABLE {dynamic_trigger_1} {};
  #VARIABLE {dynamic_trigger_2} {};
  #VARIABLE {dynamic_trigger_3} {};
  #VARIABLE {dynamic_trigger_4} {};
  #VARIABLE {dynamic_trigger_5} {};
  #LOOP 1 &conf[pfm][trigger][] {i} {
    #VARIABLE {dynamic_trigger_$i} {
      {body} {*conf[pfm][trigger][+$i]}
      {alias} {$conf[pfm][trigger][+$i]}
    };
  };
  #IF {"$dynamic_trigger_1[body]" != ""} {
    #ACTION {^$dynamic_trigger_1[body]} {
      createpfm {$dynamic_trigger_1[alias]}
    };
  };
  #IF {"$dynamic_trigger_2[body]" != ""} {
    #ACTION {^$dynamic_trigger_2[body]} {
      createpfm {$dynamic_trigger_2[alias]}
    };
  };
  #IF {"$dynamic_trigger_3[body]" != ""} {
    #ACTION {^$dynamic_trigger_3[body]} {
      createpfm {$dynamic_trigger_3[alias]}
    };
  };
  #IF {"$dynamic_trigger_4[body]" != ""} {
    #ACTION {^$dynamic_trigger_4[body]} {
      createpfm {$dynamic_trigger_4[alias]}
    };
  };
  #IF {"$dynamic_trigger_5[body]" != ""} {
    #ACTION {^$dynamic_trigger_5[body]} {
      createpfm {$dynamic_trigger_5[alias]}
    };
  };
  #CLASS fightclass CLOSE;
  #IF {$dangerousflag != 0} {
    #CLASS fightclass KILL;
    #VARIABLE {dangerousflag} {0};
    runawayside {prepareskills {doabort}}
  };
  #ELSEIF {$unexpectlost == 1} {
    #VARIABLE {unexpectlost} {0};
    closewimpy;
    dolostweapon;
  };
  #ELSE {
    openwimpy {1};
  };
};
#NOP {停止战斗,%1:是否保持wimpy状态};
#ALIAS {stopfight} {
  #CLASS fightclass KILL;
  #CLASS jobescapeclass KILl;
  #CLASS lostweaponclass KILL;
  #CLASS checkweaponclass KILL;
  #VARIABLE {negativeffect[youming]} {0};
  #VARIABLE {fightnpc} {nobody};
  #VARIABLE {conf[pfm][now]} {};
  pickweapon
};
#NOP {晕倒或逃跑后后大概率有一些异常状态，有些异常状态是无法等到jobcheck处理的，在这里统一处理一下,%1:后续指令};
#ALIAS {doevac} {
  #CLASS evacclass KILL;
  #CLASS evacclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkevac\"|你设定checkevac为反馈信息}} {
    #CLASS evacclass KILL;
    #VARIABLE {idle} {0};
    #VARIABLE {walkstoppedts} {0};
    #NOP {检查毒};
    #IF {"$hp[condition][怪蛇]" != ""} {
      healguaishe {%1}
    };
    #ELSEIF {"$hp[condition][化骨绵掌]" != ""} {
      waitnegative {%1}
    };
    #ELSE {
      checkkungfu {%1}
    };
  };
  #CLASS evacclass CLOSE;
  i;
  yun qi;
  hp;
  cond;
  jifa;
  #DELAY {0.5} {echo {checkevac}}
};
#NOP {武当意外接触脱离后的处理逻辑};
#ALIAS {dodisengage} {
  #CLASS evacclass KILL;
  #CLASS evacclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkdisengage\"|你设定checkdisengage为反馈信息}} {
    #CLASS evacclass KILL;
    resonate {checkdisengage};
    #VARIABLE {idle} {0};
    #LOCAL {mustabort} {0};
    #IF {("$currentjob" == "送信" || "$currentjob" == "送信2") && $job_killer_killed == 0} {
      #LOCAL {mustabort} {1};
    };
    #NOP {在当前的行为模式下，基本上需要关注的只有怪蛇毒};
    #IF {"$hp[condition][怪蛇]" != "" || "$hp[condition][星宿掌]" != ""} {
      #NOP {当前任务是送信切未杀杀手时最好先放弃，送信后会自动检查处理怪蛇毒};
      #IF {$mustabort == 1} {
        jobfangqi_songxin
      };
      #ELSE {
        doqudu {gotoroom {$aimroomid} {$aimdo}}
      };
    };
    #ELSEIF {"$kungfu[base][force][jifa]" == "" || "$kungfu[base][parry][jifa]" == "" || "$kungfu[base][dodge][jifa]" == ""} {
      #IF {$mustabort == 1} {
        jobfangqi_songxin
      };
      #ELSE {
        checkkungfu {gotoroom {$aimroomid} {$aimdo}}
      };
    };
    #ELSE {
      #NOP {继续原来的操作};
      gotoroom {$aimroomid} {$aimdo}
    };
  };
  #CLASS evacclass CLOSE;
  ensure {hp;cond} {checkdisengage}
};
#NOP {物品是在房间内，%1:物品id};
#FUNCTION isRoomItem {
  #FOREACH {*roomthings[]} {t} {
    #IF {@contains{{roomthings[$t]}{%1}} > 0} {
      #RETURN {1};
    };
  };

  #RETURN {0};
};
#NOP {物品是否在房间内，%1-物品id};
#FUNCTION isRoomthingExists {
  #FOREACH {*roomthings[]} {tn} {
    #IF {@contains{{roomthings[$tn]}{%1}} > 0} {
      #RETURN {1};
    };
  };

  #RETURN {0};
};
#NOP {武器丢失逻辑，如果掉落的武器在地上那么捡起，不在了就切换武器，没有武器了切换空手，否则跑路};
#ALIAS {dolostweapon} {
  #VARIABLE {targetweapon} {};
  #VARIABLE {lostflag} {1};
  #CLASS checkweaponclass KILL;
  #CLASS checkweaponclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checklostweapon\"|你设定checklostweapon为反馈信息}} {
    resonate {checklostweapon};
    #CLASS checkweaponclass KILL;
    #VARIABLE {weaponlost} {1};
    #IF {@isRoomthingExists{$env[currentweapon]} == 0} {
      #NOP {武器不在房间，重新创建pfm，更换武器再createpfm里面};
      createpfm {$env[currentpfm]} {1} {} {} {} {} {1}
    };
    #ELSE {
      #NOP {武器还在房间，创建捡武器pfm};
      createpfm {$env[currentpfm]} {1} {} {} {} {1} {1}
    };
    openwimpy;
    autopfm
  };
  #CLASS lostweaponclass CLOSE;
  ensure {
    closewimpy;
    i;
    id here;
  } {checklostweapon}
};
#NOP {处理死亡};
#NOP {死亡后指定指令};
#VARIABLE {afterdeath} {};
#ALIAS {dodeath} {
  #CLASS deathclass KILL;
  #CLASS deathclass OPEN;
  #ACTION {^城隍庙 -} {
    #CLASS deathclass KILL;
    #VARIABLE {diecity} {$city};
    #VARIABLE {dieroom} {$roomid};
    drop shoes;
    #NOP {如果在死亡期间重连那么不会加载解谜和环境变量，这里重新加载下};
    checkenv;
    checkalias;
    loc {prepareskills { checkdazuopoint {doafterdeath} {1}}};
  } {4};
  #CLASS deathclass CLOSE;
};
#ALIAS {doafterdeath} {
  #CLASS deathclass KILL;
  #CLASS deathclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkdeathdo\"|你设定checkdeathdo为反馈信息}} {
    #CLASS deathclass KILL;
    #IF {"$afterdeath" == ""} {
      checkweapon {doabort}
    };
    #ELSE {
      $afterdeath;
      #VARIABLE {afterdeath} {};
    };
  };
  #CLASS deathclass CLOSE;
  echo {checkdeathdo}
};
#NOP {死亡后如武器丢失则去死亡地点拿武器,%1:后续操作};
#ALIAS {getdeathweapon} {
  #CLASS deathweaponclass KILL;
  #CLASS deathweaponclass OPEN;
  #CLASS deathweaponclass CLOSE;
  #IF {@isUserWeapon{$conf[weapon][primary]} == 0} {
    #CLASS deathweaponclass KILL;
    %1
  };
  #ELSE {
    gotodo {$diecity} {$dieroom} {getdeathweapon_starat {%1}}
  };
};
#NOP {捡武器,活过来尸体可能变骸骨了};
#ALIAS {getdeathweapon_starat} {
  #VARIABLE {targetindex} {1};
  #CLASS deathweaponclass KILL;
  #CLASS deathweaponclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkdeathweapon\"|你设定checkdeathweapon为反馈信息}} {
    #DELAY {1} {
      #IF {@carryqty{$conf[weapon][primary]} == 0 && $targetindex < 3} {
        #MATH {targetindex} {$targetindex + 1};
        execute {
          get $conf[weapon][primary] $targetindex;
          get $conf[weapon][primary] from corpse $targetindex;
          get $conf[weapon][primary] from hai gu $targetindex;
          i
        };
        echo {checkdeathweapon};
      };
      #ELSE {
        #CLASS deathweaponclass KILL;
        wwp;
        %1;
      };
    };
  };
  #CLASS deathweaponclass CLOSE;
  execute {
    get $conf[weapon][primary] $targetindex;
    get $conf[weapon][primary] from corpse $targetindex;
    get $conf[weapon][primary] from hai gu $targetindex;
    i
  };
  echo {checkdeathweapon};
};
#CLASS fightmodule CLOSE;
#SHOWME {<fac>@padRight{{战斗}{12}}<fac> <cfa>模块加载完毕<cfa>};