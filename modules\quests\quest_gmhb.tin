#NOP {古墓互搏,%1:后续指令};
#ALIAS {goquest_gmhb} {
  #VARIABLE {questmodule} {古墓互搏};
  gotonpc {小龙女} {gmhb_asklongnv {%1}}
};
#NOP {问小龙女周伯通,%1:后续指令};
#ALIAS {gmhb_asklongnv} {
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你向小龙女打听有关『周伯通』的消息。} {
    #VARIABLE {idle} {0};
    #VARIABLE {askresult} {0};
    #CLASS questresponseclass OPEN;
    #ACTION {^这里没有这个人。} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      #VARIABLE {questlist[$questmodule][timestamp]} {@now{}};
      runwait {%1};
    };
    #ACTION {^小龙女说道：「你有空的时候拿我的玉蜂针做信物，去帮我探望他一下吧。」} {
      #CLASS questresponseclass KILL;
      #CLASS questclass KILL;
      dohalt {
        gmhb_askzhen {%1}
      };
    };
    #CLASS questresponseclass CLOSE;
  };
  #CLASS questclass CLOSE;
  ask xiao longnv about 周伯通
};
#NOP {向小龙女要针,%1:后续指令};
#ALIAS {gmhb_askzhen} {
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkzhen\"|你设定checkzhen为反馈信息}} {
    #CLASS questclass KILL;
    dohalt {
      #IF {@carryqty{yufeng zhen} == 0} {
        %1
      };
      #ELSE {
        gotodo {全真教} {教碑} {gmhb_askzhou {%1}}
      };
    };
  };
  #CLASS questclass CLOSE;
  ask xiao longnv about 玉蜂针;
  i;
  echo {checkzhen};
};
#NOP {向周伯通问小龙女,%1:后续指令};
#ALIAS {gmhb_askzhou} {
  #VARIABLE {askresult} {0};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你给周伯通一枚玉蜂针} {
    #VARIABLE {askresult} {1};
  };
  #ACTION {^{设定环境变量：action \= \"checkgive\"|你设定checkgive为反馈信息}} {
    #VARIABLE {idle} {0};
    runwait {
      #IF {$askresult == 0} {
        #VARIABLE {questlist[$questmodule][timestamp]} {@now{}};
        %1
      };
      #ELSE {
        ask zhou botong about 小龙女;
        echo {checklongnv};
      };
    };
  };
  #ACTION {^你向周伯通打听有关『小龙女』的消息。} {
    #VARIABLE {askresult} {0};
    #CLASS questresponseclass OPEN;
    #ACTION {^周伯通盯着你看了看，饶有兴趣的问道：“不知道龙姑娘有没有教你这门功夫？”} {
      #VARIABLE {askresult} {1};
    };
    #CLASS questresponseclass CLOSE;
  };
  #ACTION {^{设定环境变量：action \= \"checklongnv\"|你设定checklongnv为反馈信息}} {
    #CLASS questresponseclass KILL;
    #CLASS questclass KILL;
    dohalt {
      #IF {$askresult == 1} {
        gotonpc {小龙女} {gmhb_askhb {%1}};
      };
      #ELSE {
        #VARIABLE {questlist[$questmodule][timestamp]} {@now{}};
      };
    };
  };
  #CLASS questclass CLOSE;
  give 1 yufeng zhen to zhou botong;
  echo {checkgive};
};
#NOP {向小龙女问互搏,%1:后续指令};
#ALIAS {gmhb_askhb} {
  #VARIABLE {askresult} {0};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^你向小龙女打听有关『周伯通』的消息。} {
    #VARIABLE {askresult} {0};
    #CLASS questresponseclass OPEN;
    #ACTION {^小龙女说道：「你且凝神静气，暗自默想如何右手使玉女剑法，左手使全真剑法，双手试演数招，自然豁然贯通。」} {
      #VARIABLE {askresult} {1};
    };
    #CLASS questresponseclass CLOSE;
  };
  #ACTION {^{设定环境变量：action \= \"checkask\"|你设定checkask为反馈信息}} {
    #CLASS questresponseclass KILL;
    #CLASS questclass KILL;
    dohalt {
      #IF {$askresult == 1} {
        gmhb_yun {%1}
      };
      #ELSE {
        %1
      };
    }
  };
  #CLASS questclass CLOSE;
  ask xiao longnv about 周伯通;
  echo {checkask};
};
#NOP {运心经解谜,%1:后续指令};
#ALIAS {gmhb_yun} {
  #VARIABLE {askresult} {0};
  #CLASS questclass KILL;
  #CLASS questclass OPEN;
  #ACTION {^恭喜！你成功运用古墓心法悟通全真剑法与玉女剑法的单人双剑合璧} {
    questsuccess {$questmodule};
    #VARIABLE {askresult} {1};
  };
  #ACTION {^你屏气凝神，口中默念} {
    hp;
    #IF {$askresult == 1} {
      #CLASS questclass KILL;
      prepareforce;
      %1
    };
    #ELSEIF {$env[pray] == 0} {
      #CLASS questclass KILL;
      #IF {@carryqty{pearl} == 0} {
        getpearl {gotonpc {小龙女} {gmhb_yun {%1}}}
      };
      #ELSE {
        pray pearl;
        i;
        dzn
      };
    };
    #ELSEIF {$hp[neili] < 1000} {
      #DELAY {2} {
        dzn;
      };
    };
    #ELSE {
      #DELAY {2} {
        yun xinjing;
      }
    };
  } {1};
  #ACTION {^{你已经在运功中了|「心经」字诀运用当中，你不能施用内功}} {
    #VARIABLE {idle} {0};
    #DELAY {2} {
      yun xinjing;
    }
  };
  #ACTION {^{$dazuo_over}} {
    #VARIABLE {idle} {0};
    #VARIABLE {workingflag} {0};
    #IF {$hp[neili] < $hp[neili_max]} {
      dzn
    };
    #ELSE {
      yun xinjing;
    };
  };
  #CLASS questclass CLOSE;
  jifa force yunu-xinjing;
  pray pearl;
  i;
  yun xinjing;
};