#NOP {清除天地会杀人非法房间};
#ALIAS {tdhclear} {
  #LOCAL {eraserooms} {};
  #VARIABLE {illegalrooms} {@getNearRoomsEx{{illegalrooms}{1}}};
  #FOREACH {$illegalrooms[]} {r} {
    #IF {@contains{{tdhpoolrooms}{$r}} > 0} {
      #LOCAL {eraserooms} {$r;};
    };
  };
  #SHOWME {<faa>需要从 %1 清除 $eraserooms 房间。};
};
#ALIAS {tdhclear_start} {
  #VARIABLE {okflag} {0};
  #CLASS mgqclass KILL;
  #CLASS mgqclass OPEN;
  #ACTION {^你至少需要%*点的气来打坐} {
    #VARIABLE {okflag} {1};
  };
  #ACTION {^{这里不准战斗|卧室不能打坐}} {
    #VARIABLE {okflag} {0};
  };
  #ACTION {^{设定环境变量：action \= \"checktdh\"|你设定checktdh为反馈信息}} {
    resonate {checktdh};
    #CLASS mgqclass KILL;
    #IF {$okflag == 0} {
      #LIST {illegalrooms} {add} {$roomid};
    };
    runwait {jobnextroom {tdhclear_start {%1}} {tdhclear {%1}} {1}}
  };
  #CLASS mgqclass CLOSE;
  ensure {dazuo 0} {checktdh}
};
#ALIAS {jobcapure_xs} {
  #CLASS jobcapturexs KILL;
  #CLASS jobcapturexs OPEN;
  #ACTION {^%*(%*)纵声长啸：} {
    #LOCAL {userid} {@lower{%%2}};
    #IF {"$jobstatitics[exchange][$userid]" == ""} {
      #VARIABLE {jobstatitics[exchange][$userid]} {@now{}};
    };
    #ELSE {
      joblog {@padRight{{%%1}{8}}($userid)兑换内力，距离上次兑换【@timeFormatCN{@elapsed{$jobstatitics[exchange][$userid]}}】。} {兑换内力};
      #VARIABLE {jobstatitics[exchange][$userid]} {@now{}}
    };
  };
  #ACTION {^%*向宝象打听有关『{job|任务|工作}』的消息} {
    #VARIABLE {jobstatitics[xs][%%1][timestamp]} {@now{}};
  };
  #ACTION {^%*向宝象打听有关『{finish|完成}』的消息} {
    #IF {"$jobstatitics[xs][%%1][timestamp]" != ""} {
      #LOCAL {seconds} {@elapsed{$jobstatitics[xs][%%1][timestamp]}};
      #VARIABLE {jobstatitics[xs][%%1][success][count]} {@eval{$jobstatitics[xs][%%1][success][count] + 1}};
      #VARIABLE {jobstatitics[xs][%%1][success][seconds]} {@eval{$jobstatitics[xs][%%1][success][seconds] + $seconds}};
      #UNVARIABLE {jobstatitics[xs][%%1][timestamp]};
      #LOCAL {successavg} {@eval{$jobstatitics[xs][%%1][success][seconds]/$jobstatitics[xs][%%1][success][count]}};
      #LOCAL {failavg} {@eval{$jobstatitics[xs][%%1][fail][seconds]/$jobstatitics[xs][%%1][fail][count]}};
      #IF {"$jobstatitics[xs][%%1][color]" == ""} {
        #VARIABLE {jobstatitics[xs][%%1][color]} {@getRandomColor{}};
      };
      joblog {@padRight{{%%1}{8}}成功【$jobstatitics[xs][%%1][success][count]】次，总耗时【$jobstatitics[xs][%%1][success][seconds]】秒，平均耗时【$successavg】秒，失败【$jobstatitics[xs][%%1][fail][count]】次，总耗时【$jobstatitics[xs][%%1][fail][seconds]】秒，平均耗时【$failavg】秒。} {雪山统计} {$jobstatitics[xs][%%1][color]}
    };
  };
  #ACTION {^%*向宝象打听有关『{fangqi|放弃|失败}』的消息} {
    #IF {"$jobstatitics[xs][%%1][timestamp]" != ""} {
      #LOCAL {seconds} {@elapsed{$jobstatitics[xs][%%1][timestamp]}};
      #VARIABLE {jobstatitics[xs][%%1][fail][count]} {@eval{$jobstatitics[xs][%%1][fail][count] + 1}};
      #VARIABLE {jobstatitics[xs][%%1][fail][seconds]} {@eval{$jobstatitics[xs][%%1][fail][seconds] + $seconds}};
      #UNVARIABLE {jobstatitics[xs][%%1][timestamp]};
    };
  };
  #CLASS jobcapturexs CLOSE;
};
#ALIAS {jobcapure_wd} {
  #CLASS jobcapturewd KILL;
  #CLASS jobcapturewd OPEN;
  #ACTION {^%*向宋远桥打听有关『{job|任务|工作}』的消息} {
    #VARIABLE {jobstatitics[wd][%%1][timestamp]} {@now{}};
  };
  #ACTION {^%*向宋远桥打听有关『{finish|完成}』的消息} {
    #IF {"$jobstatitics[wd][%%1][timestamp]" != ""} {
      #LOCAL {seconds} {@elapsed{$jobstatitics[wd][%%1][timestamp]}};
      #VARIABLE {jobstatitics[wd][%%1][success][count]} {@eval{$jobstatitics[wd][%%1][success][count] + 1}};
      #VARIABLE {jobstatitics[wd][%%1][success][seconds]} {@eval{$jobstatitics[wd][%%1][success][seconds] + $seconds}};
      #UNVARIABLE {jobstatitics[wd][%%1][timestamp]};
      #LOCAL {successavg} {@eval{$jobstatitics[wd][%%1][success][seconds]/$jobstatitics[wd][%%1][success][count]}};
      #LOCAL {failavg} {@eval{$jobstatitics[wd][%%1][fail][seconds]/$jobstatitics[wd][%%1][fail][count]}};
      #IF {"$jobstatitics[wd][%%1][color]" == ""} {
        #VARIABLE {jobstatitics[wd][%%1][color]} {@getRandomColor{}};
      };
      joblog {@padRight{{%%1}{8}}成功【$jobstatitics[wd][%%1][success][count]】次，总耗时【$jobstatitics[wd][%%1][success][seconds]】秒，平均耗时【$successavg】秒，失败【$jobstatitics[wd][%%1][fail][count]】次，总耗时【$jobstatitics[wd][%%1][fail][seconds]】秒，平均耗时【$failavg】秒。} {武当统计} {$jobstatitics[wd][%%1][color]}
    };
  };
  #CLASS jobcapturewd CLOSE;
};