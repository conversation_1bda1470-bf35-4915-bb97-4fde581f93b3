#NOP {拜师指南模块};
#NOP {昆仑派};
import {guides/guide_taohuadao};
#NOP {桃花岛};
import {guides/guide_taohuadao};
#NOP {铁掌帮};
import {guides/guide_tiezhang};
#NOP {古墓派};
import {guides/guide_gumu};
#NOP {全真教};
import {guides/guide_quanzhen};
#NOP {星宿派};
import {guides/guide_xingxiu};
#NOP {姑苏慕容};
import {guides/guide_murong};
#NOP {少林派};
import {guides/guide_shaolin};
#NOP {明教};
import {guides/guide_mingjiao};
#NOP {日月神教};
import {guides/guide_riyue};
#NOP {华山派};
import {guides/guide_huashan};
#NOP {武当派};
import {guides/guide_wudang};
#NOP {雪山派};
import {guides/guide_xueshan};

#VARIABLE {pfmlevel} {0};

#NOP {巡城完成后新手指南,%1:后续指令};
#ALIAS {guide_newbie} {
  #IF {"$conf[newbie][party]" == ""} {
    #SHOWME {<faa>未配置新手拜师指南，请自行处理};
  };
  #ELSEIF {$hp[balance] < 500} {
    funds_call {guide_newbie {%1}}
  };
  #ELSEIF {@carryqty{fire} == 0} {
    buyfire {guide_newbie {%1}}
  };
  #ELSE {
    guidedeparty_default {%1}
  };
};
#ALIAS {guidedeparty_default} {
  #LOCAL {tempguide} {$guide[$conf[newbie][party]]};
  #IF {"$tempguide" == ""} {
    #SHOWME {<faa>未配置$conf[newbie][party]新手指南，请自行处理};
  };
  #ELSE {
    #LOCAL {nextmaster} {@getNextMaster{}};
    #IF {"$nextmaster" == ""} {
      #IF {"$hp[party]" == "普通百姓" || "$hp[master][name]" == ""} {
        #SHOWME {<faa>未配置$conf[newbie][party]新手指南师傅资料，请自行处理};
      };
      #ELSE {
        guide_checklevel {%1}
      };
    };
    #ELSE {
      guide_checkmaster {$nextmaster} {%1}
    };
  };
};
#NOP {获取下一个师傅};
#FUNCTION getNextMaster {
  #LOCAL {tempguide} {$guide[$conf[newbie][party]]};
  #LOCAL {masterindex} {@contains{{tempguide[masters][sequence]}{$hp[master][name]}}};
  #MATH {masterindex} {$masterindex + 1};
  #IF {$masterindex > &tempguide[masters][sequence][]} {
    #RETURN {};
  };
  #ELSE {
    #LOOP $masterindex &tempguide[masters][sequence][] {i} {
      #LOCAL {mastername} {$tempguide[masters][sequence][+$i]};
      #LOCAL {masterconditions} {$tempguide[masters][$mastername][conditions]};
      #IF {"$masterconditions[gender]" != "" && "$masterconditions[gender]" != "$hp[sex]"} {
        #CONTINUE;
      };
      #IF {"$masterconditions[quest]" != "" && "$questlist[$masterconditions[quest]][done]" != "YES"} {
        #CONTINUE;
      };
      #RETURN {$mastername};
    };
    #RETURN {};
  };
};

#NOP {拜师准备,%1:师傅};
#ALIAS {guide_baishi_prepare} {
  guide_baishi_beforedo {%1}
};

#NOP {检查天赋,%1:师傅,%2:后续指令};
#ALIAS {guide_baishi_checkgift} {
  #LOCAL {targift} {$guide[$conf[newbie][party]][masters][%1][conditions][gift]};
  adjustgift {$targift[str]} {$targift[con]} {$targift[dex]} {$targift[int]} {%2}
};

#NOP {拜师前置操作,%1};
#ALIAS {guide_baishi_beforedo} {
  #VARIABLE {beforedo} {$guide[$conf[newbie][party]][masters][%1][beforedo]};
  #IF {"$beforedo" == ""} {
    guide_baishi_checkother {%1}
  };
  #ELSE {
    #REPLACE {beforedo} {#} {guide_baishi_checkother {%1}};
    $beforedo
  };
};
#NOP {其他特殊逻辑处理};
#ALIAS {guide_baishi_checkother} {
  #IF {"%1" == "裘千丈"} {
    guide_findqqz {guide_baishi_checkskill {%1}}
  };
  #ELSE {
    gotonpc {%1} {guide_baishi_checkskill {%1}}
  };
};

#NOP {拜师前检查技能,%1:师傅};
#ALIAS {guide_baishi_checkskill} {
  #VARIABLE {tarmaster} {$guide[$conf[newbie][party]][masters][%1]};
  #NOP {检查技能,初次拜师放弃不关注的技能,升级拜师清空所有的技能ignore标识};
  #LIST {fqskills} {clear};
  #IF {"$hp[party]" == "" || "$hp[party]" == "普通百姓"} {
    #FOREACH {*kungfu[base][]} {bsk} {
      #IF {"$bsk" == "force" || "$bsk" == "dodge" || "$bsk" == "parry"} {
        #CONTINUE;
      };
      #IF {@getSkillLevel{$bsk} >= 100} {
        #CONTINUE;
      };
      #IF {@contains{{tarmaster[favourites]}{$bsk}} == 0} {
        #LIST {fqskills} {add} {$bsk};
      };
    };
    guide_baishi_abandon {fqskills} {guide_baishi {%1}}
  };
  #ELSE {
    #FOREACH {*kungfu[know][]} {bsk} {
      #VARIABLE {kungfu[know][$bsk][ignore]} {};
    };
    #FOREACH {*kungfu[base][]} {bsk} {
      #VARIABLE {kungfu[base][$bsk][ignore]} {};
    };
    #FOREACH {*kungfu[spec][]} {ssk} {
      #VARIABLE {kungfu[spec][$ssk][ignore]} {};
    };
    guide_baishi {%1}
  };
};

#NOP {放弃技能,%1:技能列表,%2:后续操作};
#ALIAS {guide_baishi_abandon} {
  #CLASS newbieclass OPEN;
  #ACTION {^设定环境变量：action \= \"checkskill\"} {
    #IF {&%1[] == 0} {
      #CLASS newbieclass KILL;
      skills;
      %2
    };
    #ELSE {
      #LOCAL {tsk} {$%1[+1]};
      #LIST {%1} {delete} {1};
      fangqi $tsk;
      #IF {"$kungfu[know][$tsk]" != ""} {
        #UNVARIABLE {kungfu[know][$tsk]};
      };
      #ELSEIF {"$kungfu[base][$tsk]" != ""} {
        #UNVARIABLE {kungfu[base][$tsk]};
      };
      #ELSEIF {"$kungfu[spec][$tsk]" != ""} {
        #UNVARIABLE {kungfu[spec][$tsk]};
      };
      dohalt {
        set action checkskill
      };
    };
  };
  #CLASS newbieclass CLOSE;
  #IF {&%1[] == 0} {
    #CLASS newbieclass KILL;
    %2
  };
  #ELSE {
    set action checkskill
  };
};

#NOP {拜师,%1:师傅};
#ALIAS {guide_baishi} {
  #VARIABLE {tempguide} {$guide[$conf[newbie][party]]};
  #CLASS newbieclass OPEN;
  #ACTION {^你双手抱拳，对%1作了个揖道} {
    bai $tempguide[masters][%1][id];
    #IF {"%1" == "丁春秋"} {
      flatter 星宿老仙德配天地威震寰宇古今无比;
    };
    score;
    set action checkbai
  };
  #ACTION {^你向西跪倒，双膝及地，恭恭敬敬向祖师婆婆的画像磕了一个响头.} {
    dohalt {
      ketou westpic
    };
  };
  #ACTION {^恭喜你自愿成为一名古墓弟子} {
    #CLASS newbieclass KILL;
    #UNVARIABLE {hp[master]};
    score;
    #IF {"%1" == "包不同"} {
      setuppersonalmap;
    };
    dohalt {
      guide_checkweapon {golearn {startjob}}
    };
  };
  #ACTION {^你要对谁做这个动作} {
    #VARIABLE {idle} {0};
    #DELAY {5} {
      hi $tempguide[masters][%1][id]
    }
  };
  #ACTION {^{设定环境变量：action \= \"checkbai\"|你设定checkbai为反馈信息}} {
    #IF {"$hp[master][name]" != "%1"} {
      #CLASS newbieclass KILL;
      #SHOWME {<faa>拜师失败，请检查向导配置};
    };
    #ELSE {
      #UNVARIABLE {hp[master]};
      score;
      echo {checkmaster};
    };
  };
  #ACTION {^{设定环境变量：action \= \"checkmaster\"|你设定checkmaster为反馈信息}} {
    #CLASS newbieclass KILL;
    #NOP {默认拜师后先去学习,此前需要检查初学武器技能的武器};
    #DELAY {1} {
      guide_baishi_afterdo {guide_checkweapon {golearn {startjob}}};
    };
  };
  #CLASS newbieclass CLOSE;
  i;
  #NOP {注意这里必须重新设置师傅变量};
  #UNVARIABLE {hp[master]};
  #IF {"%1" == "自学"} {
    #2 tuo eastpic;
    ketou westpic
  };
  #ELSE {
    hi $tempguide[masters][%1][id]
  };
};

#NOP {拜师后置操作,%1:后续指令};
#ALIAS {guide_baishi_afterdo} {
  #VARIABLE {afterdo} {$guide[$conf[newbie][party]][masters][$hp[master][name]][afterdo]};
  #IF {"$afterdo" == ""} {
    %1;
  };
  #ELSE {
    #REPLACE {afterdo} {#} {%1};
    $afterdo;
  };
};

#NOP {拜师后检查需要的武器,%1:后续指令};
#ALIAS {guide_checkweapon} {
  #NOP {查看需要学的技能所需的武器是否齐全};
  #VARIABLE {fskills} {$guide[$conf[newbie][party]][masters][$hp[master][name]][favourites]};
  #VARIABLE {targetweapon} {};
  #FOREACH {$fskills[]} {sk} {
    #LOCAL {tweapon} {$guide[$hp[party]][weapons][$sk]};
    #IF {"$tweapon" == ""} {
      #CONTINUE;
    };
    #IF {"$common[weaponmapping][$tweapon]" == ""} {
      #VARIABLE {targetweapon} {@getDefineWeapon{$tweapon}};
      #BREAK;
    };
  };
  #IF {"$targetweapon" != ""} {
    findweapon {$targetweapon} {guide_checkweapon {%1}}
  };
  #ELSE {
    %1
  };
};

#NOP {检查技能,确定下一步的操作,%1:后续操作};
#ALIAS {guide_checkskill} {
  #VARIABLE {tempguide} {$guide[$conf[newbie][party]][masters]}; 
  #CLASS guideclass OPEN;
  #ACTION {^设定环境变量：action \= \"checkskills\"} {
    #CLASS guideclass KILL;
    #LOCAL {nextmaster} {@getNextMaster{}};
    #IF {"$nextmaster" == ""} {
      guide_checklevel {%1}
    };
    #ELSE {
      guide_checkmaster {$nextmaster} {%1};
    };
  };
  #CLASS guideclass CLOSE;
  #IF {"$tempguide" == ""} {
    #CLASS guideclass KILL;
    %1
  };
  #ELSE {
    skills;
    set action checkskills
  };
};

#NOP {检查是否可以拜下一个师傅,%1:师傅,%2:无法拜师时后续指令,拜师后直接开始学习忽略后续指令};
#ALIAS {guide_checkmaster} {
  #VARIABLE {tempconditions} {$guide[$conf[newbie][party]][masters][%1][conditions]}; 
  #NOP {检查技能};
  #VARIABLE {okflag} {1};
  #FOREACH {*tempconditions[skills][]} {sk} {
    #IF {@getSkillLevel{$sk} < $tempconditions[skills][$sk]} {
      #VARIABLE {okflag} {0};
      #BREAK;
    };
  };
  #IF {$okflag == 1} {
    #LOCAL {shen} {@eval{$tempconditions[shen]}};
    #IF {$shen == 0} {
      guide_baishi_prepare {%1}
    };
    #ELSEIF {$shen > 0} {
      gozshen {$shen} {guide_baishi_prepare {%1}}
    };
    #ELSE {
      gofshen {@abs{$shen}} {guide_baishi_prepare {%1}}
    };
  };
  #ELSE {
    guide_checklevel {%2}
  };
};

#NOP {检查各关注的武功等级并确定满级后下一步的操作,如果是未过阵则先过阵,%1:后续指令};
#ALIAS {guide_checklevel} {
  #VARIABLE {tempskills} {$guide[$conf[newbie][party]][masters][$hp[master][name]]}; 
  #VARIABLE {okflag} {1};
  #NOP {过阵前的技能一律强制补满};
  #IF {$hp[exp] < 165000} {
    clearskillignore;
  };
  #FOREACH {$tempskills[favourites][]} {sk} {
    #IF {@getSkillIgnore{$sk} == "ever"} {
      #CONTINUE;
    };
    #IF {@getSkillLevel{$sk} < $hp[max_lv]} {
      #LOCAL {okflag} {0};
      #BREAK;
    };
  };
  #IF {$okflag == 1} {
    #NOP {根据技能等级初始化各等级的pfm和属性};
    guide_setpfm {
      #IF {$hp[exp] < 165000} {
        #IF {"$env[shaolin]" == ""} {
          prepareskills {goliterate {122} {checkweapon {joinsl {golearn {startjob}}}}}
        };
        #ELSE {
          %1
        };
      };
      #ELSE {
        %1
      };
    };
  };
  #ELSEIF {$hp[exp] < 165000} {
    golearn {%1};
  };
  #ELSE {
    %1;
  };
};

#NOP {根据等级设置pfm和任务,%1:后续操作};
#ALIAS {guide_setpfm} {
  #VARIABLE {pfmconf} {$guide[$conf[newbie][party]][pfms]};
  #VARIABLE {prevpfmlevel} {$pfmlevel};
  #CLASS guideclass KILL;
  #CLASS guideclass OPEN;
  #ACTION {^{设定环境变量：action \= \"checkguide\"|你设定checkguide为反馈信息}} {
    #CLASS guideclass KILl;
    #IF {"$conf[pfm][bei]" != ""} {
      $conf[pfm][bei];
    };
    #ELSEIF {"$kungfu[bei]" == ""} {
      #LOCAL {beiskill} {@getFistSkill{}};
      #IF {"$beiskill" != ""} {
        bei none;
        bei $beiskill
      };
    };
    %1;
  };
  #CLASS guideclass CLOSE;
  #IF {"$pfmconf" != "" && "$conf[newbie]" != ""} {
    #FOREACH {*pfmconf[]} {lv} {
      #NOP {性别};
      #IF {"$pfmconf[$lv][gender]" != "" && "$pfmconf[$lv][gender]" != "$hp[sex]"} {
        #CONTINUE;
      };
      #NOP {解谜};
      #IF {"$pfmconf[$lv][quest]" != "" && "$questlist[$pfmconf[$lv][quest]][done]" != "YES"} {
        #CONTINUE;
      };
      #IF {@getSkillLevel{$pfmconf[$lv][pfmskill]} >= $lv} {
        #VARIABLE {pfmlevel} {$lv};
        #CONTINUE;
      };
    };
    #NOP {配置武器};
    #IF {"$pfmconf[$pfmlevel][weapon]" != ""} {
      #NOP {如果用户配置中主武器为打造武器则不替换，否则替换};
      #IF {"$conf[weapon][primary]" == "" || @isUserWeapon{$conf[weapon][primary]} == 0} {
        #VARIABLE {conf[weapon][primary]} {$pfmconf[$pfmlevel][weapon][primary]};
        #VARIABLE {conf[weapon][secondary]} {$pfmconf[$pfmlevel][weapon][secondary]};
        #VARIABLE {conf[weapon][chop]} {changjian};
      };
    };
    #ELSE {
      #VARIABLE {conf[weapon][primary]} {};
      #VARIABLE {conf[weapon][secondary]};
      #VARIABLE {conf[weapon][chop]} {changjian};
    };
    #NOP {配置pfm};
    #VARIABLE {conf[pfm][wuxing]} {$pfmconf[$pfmlevel][wuxing]};
    #VARIABLE {conf[pfm][normalbuff]} {$pfmconf[$pfmlevel][normalbuff]};
    #VARIABLE {conf[pfm][fightbuff]} {$pfmconf[$pfmlevel][fightbuff]};
    #VARIABLE {conf[pfm][busy]} {$pfmconf[$pfmlevel][busy]};
    #VARIABLE {conf[pfm][attack]} {$pfmconf[$pfmlevel][attack]};
    #VARIABLE {conf[pfm][unarmed]} {$pfmconf[$pfmlevel][unarmed]};
    #LIST {conf[joblist]} {create} {$pfmconf[$pfmlevel][jobs]};
    #IF {$prevpfmlevel != $pfmlevel} {
      createpfm
    };
    #IF {"$pfmconf[$pfmlevel][beforedo]" != ""} {
      $pfmconf[$pfmlevel][beforedo];
      #VARIABLE {conf[pfm][bei]} {$pfmconf[$pfmlevel][beforedo]};
    };
    #LOCAL {targift} {$pfmconf[$pfmlevel][gift]};
    #IF {"$targift" != ""} {
      adjustgift {$targift[str]} {$targift[con]} {$targift[dex]} {$targift[int]} {score}
    };
    jifa;
    bei;
    echo {checkguide};
  };
  #ELSE {
    createpfm;
    jifa;
    bei;
    echo {checkguide};
  };
};

#NOP {处理向导中目标解谜的事项,%1:后续指令};
#ALIAS {guide_destiny} {
  #LOCAL {okflag} {0};
  #NOP {destiny用于有次数的解谜，当前就是蛤蟆。超过失败次数即为FAIL，该号可自杀。};
  #IF {"$questlist[$conf[newbie][destiny]][done]" == "FAIL"} {
    #LOCAL {okflag} {1};
    #NOP {宝藏应尽量解完，除非过了3M了还没到第三步};
    #IF {"$questlist[雪山飞狐][done]" != "YES"} {
      #IF {$hp[exp] < 3000000 || ($hp[exp] >= 3000000 && $questlist[雪山飞狐][laststep] >= 3)} {
        #LOCAL {okflag} {0};
      };
    };
  };
  #NOP {对于超过threshold设置经验的账号可自杀};
  #IF {"$conf[newbie][threshold]" != "" && @eval{$conf[newbie][threshold]} < $hp[exp]} {
    #LOCAL {okflag} {1};
    #NOP {目标任务已完成的可保留};
    #IF {"$questlist[$conf[newbie][target]][done]" == "YES"} {
      #LOCAL {okflag} {0};
    };
    #NOP {宝藏应尽量解完，除非过了3M了还没到第三步};
    #IF {"$questlist[雪山飞狐][done]" != "YES"} {
      #IF {$hp[exp] < 3000000 || ($hp[exp] >= 3000000 && $questlist[雪山飞狐][laststep] >= 3)} {
        #LOCAL {okflag} {0};
      };
    };
  };
  #IF {"$questlist[九阴上][done]" == "YES"} {
    #LOCAL {okflag} {0};
  };
  #IF {$okflag == 1} {
    #NOP {呼叫保存遗产并自杀};
    recycle_call {reborn};
  };
  #ELSE {
    %1
  };
};
#ALIAS {guideclear} {
  #CLASS guideclass KILL;
  #CLASS guidedoclass KILL;
};
#SHOWME {<fac>@padRight{{向导}{12}}<fac> <cfa>模块加载完毕<cfa>};