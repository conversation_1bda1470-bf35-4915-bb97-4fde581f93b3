#NOP {副本模块};
#NOP {通天塔};
import {instance/tongtianta};
#NOP {情怀梦};
import {instance/qinghuaimeng};
#NOP {情怀岛};
import {instance/qinghuaidao};

#NOP {副本列表配置};
#VARIABLE {fubenlist} {
  {sequence} {
    {1}{通天塔}
    {2}{情怀梦}
    {3}{情怀岛}
  }
  {通天塔} {
    #NOP {执行副本的命令别名，当系统决定执行此副本时会调用这个命令};
    {alias} {tt.go}
    #NOP {副本的显示名称，用于界面显示和日志记录};
    {name} {通天塔}
    #NOP {最低经验要求，角色必须达到此经验值才能挑战此副本};
    {limitexp} {10000001}
    #NOP {时间间隔限制，单位秒，86400秒=24小时，即每天只能挑战一次};
    {intervaltime} {86400}
    #NOP {经验间隔限制，0表示无经验要求，如果设置则需要获得指定经验后才能再次挑战};
    {intervalexp} {0}
    #NOP {上次执行时间戳，系统自动记录，用于计算时间间隔};
    {lasttime} {0}
    #NOP {上次执行时的经验值，系统自动记录，用于计算经验间隔};
    {lastexp} {0}
    #NOP {下次可执行时间戳，系统自动计算，用于延迟重试};
    {timestamp} {0}
    #NOP {完成状态：空=未完成，YES=已成功，FAIL=已失败};
    {done} {}
    #NOP {失败次数计数器，系统自动记录连续失败次数};
    {fail} {0}
    #NOP {最大尝试次数，超过此次数后将不再自动尝试};
    {chance} {3}
    #NOP {技能要求，格式为{技能名}{等级}，空表示无技能要求};
    {skills} {}
    #NOP {时间限制，基于游戏内时间的执行条件，如"# >= 9 && # <= 17"表示9-17点可执行};
    {timezone} {}
    #NOP {门派限制，指定门派才能执行，空表示无门派限制};
    {party} {}
    #NOP {性别限制，指定性别才能执行，空表示无性别限制};
    {gender} {}
    #NOP {副本描述信息，用于说明和界面显示};
    {description} {通天塔副本，每日可挑战一次，需要10M经验}
  }
  {情怀梦} {
    #NOP {执行情怀梦副本的命令，这是一个梦境寻宝类副本};
    {alias} {otherquest_qinghuaimeng}
    #NOP {副本显示名称};
    {name} {情怀梦}
    #NOP {最低经验要求，需要50M经验才能挑战};
    {limitexp} {50000000}
    #NOP {24小时冷却时间，每天只能挑战一次};
    {intervaltime} {86400}
    #NOP {无经验间隔要求};
    {intervalexp} {0}
    #NOP {上次执行时间，系统自动更新};
    {lasttime} {0}
    #NOP {上次执行时经验值，系统自动更新};
    {lastexp} {0}
    #NOP {下次可执行时间，失败时会延迟};
    {timestamp} {0}
    #NOP {完成状态标记};
    {done} {}
    #NOP {失败计数};
    {fail} {0}
    #NOP {最大重试次数};
    {chance} {3}
    #NOP {无特殊技能要求};
    {skills} {}
    #NOP {无时间段限制，全天可执行};
    {timezone} {}
    #NOP {无门派限制};
    {party} {}
    #NOP {无性别限制};
    {gender} {}
    #NOP {副本说明};
    {description} {情怀梦副本，每日可挑战一次，需要50M经验}
  }
  {情怀岛} {
    #NOP {执行情怀岛副本的命令，这是一个海战类副本};
    {alias} {otherquest_battleship}
    #NOP {副本显示名称};
    {name} {情怀岛}
    #NOP {最低经验要求，需要100M经验才能挑战};
    {limitexp} {100000000}
    #NOP {24小时冷却时间};
    {intervaltime} {86400}
    #NOP {无经验间隔要求};
    {intervalexp} {0}
    #NOP {上次执行时间戳};
    {lasttime} {0}
    #NOP {上次执行时经验值};
    {lastexp} {0}
    #NOP {下次可执行时间戳};
    {timestamp} {0}
    #NOP {完成状态};
    {done} {}
    #NOP {失败次数};
    {fail} {0}
    #NOP {最大尝试次数};
    {chance} {3}
    #NOP {无技能要求};
    {skills} {}
    #NOP {无时间限制};
    {timezone} {}
    #NOP {无门派限制};
    {party} {}
    #NOP {无性别限制};
    {gender} {}
    #NOP {副本描述};
    {description} {情怀岛副本，每日可挑战一次，需要100M经验}
  }
};

#NOP {当前执行的副本模块};
#VARIABLE {fubenmodule} {};

#NOP {==============================================核心函数==============================================};
#NOP {智能副本检查函数 - 这是整个副本系统的核心入口};
#NOP {功能：自动检查所有可用副本，选择最优先的副本执行};
#NOP {参数：%1=没有可执行副本时的后续指令，%2=执行副本前的准备指令};
#NOP {逻辑：类似checkquest，先检查条件，有副本就执行，没有就执行后续指令};
#ALIAS {checkfuben} {
  #NOP {调用getAvailableFubens函数获取当前所有可执行的副本列表};
  #LOCAL {availablefubens} {@getAvailableFubens{}};
  #NOP {检查三个阻止执行副本的条件：};
  #NOP {1. 没有可用副本 2. 正在执行特殊任务 3. 情怀币不足100};
  #IF {&availablefubens[] == 0 || @isSepcialJob{} == 1 || $hp[tongbao] < 100} {
    #NOP {条件不满足，执行传入的后续指令（比如继续做任务）};
    %1
  };
  #ELSE {
    #NOP {条件满足，先执行准备指令（比如wwp调整状态）};
    %2;
    #NOP {然后开始执行优先级最高的副本（列表中第一个）};
    #NOP {执行完成后直接执行原始后续指令，避免递归调用};
    fuben_start {$availablefubens[+1]} {%1}
  };
};

#NOP {==============================================副本可用性检查==============================================};
#NOP {获取当前所有满足条件的可执行副本列表};
#NOP {这个函数是副本系统的核心筛选器，会检查所有可能的限制条件};
#NOP {返回值：包含所有可执行副本名称的列表，按优先级排序};
#FUNCTION getAvailableFubens {
  #NOP {初始化空的可用副本列表};
  #LIST {availablefubens} {clear} {};
  #NOP {遍历配置中定义的所有副本，按sequence顺序检查};
  #FOREACH {$fubenlist[sequence][]} {f} {
    #NOP {=== 第一层检查：基础开关 ===};
    #NOP {检查是否开启了自动副本功能，如果关闭则跳过所有副本};
    #IF {$conf[autofuben] == 0} {
      #CONTINUE;
    };
    #NOP {=== 第二层检查：用户配置过滤 ===};
    #NOP {如果配置了subcribefubens（订阅副本），则只执行列表中的副本};
    #NOP {这个配置的优先级高于ignorefubens，用于精确控制要执行的副本};
    #IF {"$conf[subcribefubens]" != "" && @contains{{conf[subcribefubens]}{$f}} == 0} {
      #CONTINUE;
    };
    #NOP {检查是否在忽略列表中，如果是则跳过此副本};
    #NOP {ignorefubens用于排除不想执行的副本};
    #IF {@contains{{conf[ignorefubens]}{$f}} > 0} {
      #CONTINUE;
    };
    #NOP {=== 第三层检查：副本定义有效性 ===};
    #NOP {检查副本配置是否存在，防止配置错误};
    #IF {"$fubenlist[$f]" == ""} {
      #CONTINUE;
    };
    #NOP {=== 第四层检查：完成状态 ===};
    #NOP {跳过已经成功完成或已失败的副本};
    #NOP {YES=成功完成，FAIL=失败，空=未完成};
    #IF {"$fubenlist[$f][done]" == "YES" || "$fubenlist[$f][done]" == "FAIL"} {
      #CONTINUE;
    };
    #NOP {=== 第五层检查：角色属性限制 ===};
    #NOP {检查门派限制：如果副本指定了门派要求，必须匹配当前角色门派};
    #NOP {例如：某些副本只有特定门派才能进入};
    #IF {"$fubenlist[$f][party]" != "" && "$fubenlist[$f][party]" != "$hp[party]"} {
      #CONTINUE;
    };
    #NOP {检查性别限制：如果副本指定了性别要求，必须匹配当前角色性别};
    #NOP {例如：某些副本可能只允许男性或女性角色参与};
    #IF {"$fubenlist[$f][gender]" != "" && "$fubenlist[$f][gender]" != "$hp[sex]"} {
      #CONTINUE;
    };
    #NOP {=== 第六层检查：技能等级要求 ===};
    #NOP {检查是否满足副本所需的技能等级要求};
    #LOCAL {skillok} {1};
    #IF {"$fubenlist[$f][skills]" != ""} {
      #NOP {遍历副本要求的所有技能};
      #FOREACH {*fubenlist[$f][skills][]} {sk} {
        #NOP {检查当前技能等级是否达到要求};
        #IF {@getSkillLevel{$sk} < $fubenlist[$f][skills][$sk]} {
          #LOCAL {skillok} {0};
          #BREAK;
        };
      };
    };
    #NOP {如果有任何技能不满足要求，跳过此副本};
    #IF {$skillok == 0} {
      #CONTINUE;
    };
    #NOP {=== 第七层检查：最低经验要求 ===};
    #NOP {检查角色当前经验是否达到副本的最低经验要求};
    #NOP {这是副本的准入门槛，经验不足无法挑战};
    #LOCAL {limitexp} {$fubenlist[$f][limitexp]};
    #IF {$hp[exp] < $limitexp} {
      #CONTINUE;
    };
    #NOP {=== 第八层检查：经验间隔限制 ===};
    #NOP {检查经验间隔：确保获得足够经验后才能再次挑战};
    #NOP {计算当前经验与上次执行时经验的差值};
    #LOCAL {intervalexp} {$fubenlist[$f][intervalexp]};
    #IF {@eval{$hp[exp] - $fubenlist[$f][lastexp]} < $intervalexp} {
      #CONTINUE;
    };
    #NOP {=== 第九层检查：时间间隔限制 ===};
    #NOP {检查时间冷却：确保距离上次执行已过足够时间};
    #NOP {例如：24小时冷却时间，防止频繁挑战同一副本};
    #LOCAL {intervaltime} {$fubenlist[$f][intervaltime]};
    #IF {@elapsed{$fubenlist[$f][lasttime]} < $intervaltime} {
      #CONTINUE;
    };
    #NOP {=== 第十层检查：游戏时间段限制 ===};
    #NOP {检查游戏内时间限制：某些副本只能在特定时间段执行};
    #NOP {例如：# >= 9 && # <= 17 表示只能在游戏时间9-17点执行};
    #LOCAL {timeexpr} {1 == 1};
    #IF {"$fubenlist[$f][timezone]" != ""} {
      #LOCAL {timeexpr} {$fubenlist[$f][timezone]};
    };
    #NOP {将#替换为当前游戏时间进行计算};
    #REPLACE timeexpr {#}{$env[gametime]};
    #IF {!($timeexpr)} {
      #CONTINUE;
    };
    #NOP {=== 第十一层检查：重试延迟时间 ===};
    #NOP {检查是否还在重试延迟期内（失败后的等待时间）};
    #NOP {timestamp记录了下次可以尝试的时间};
    #IF {@now{} < $fubenlist[$f][timestamp]} {
      #CONTINUE;
    };
    #NOP {=== 通过所有检查，添加到可用副本列表 ===};
    #LIST {availablefubens} {add} {$f};
  };

  #RETURN {$availablefubens};
};

#NOP {检查指定副本是否可用,%1:副本名称};
#FUNCTION isFubenAvailable {
  #IF {"$fubenlist[%1]" == ""} {
    #RETURN {0};
  };
  #NOP {已完成或失败};
  #IF {"$fubenlist[%1][done]" == "YES" || "$fubenlist[%1][done]" == "FAIL"} {
    #RETURN {0};
  };
  #NOP {最低经验要求};
  #LOCAL {limitexp} {$fubenlist[%1][limitexp]};
  #IF {$hp[exp] < $limitexp} {
    #RETURN {0};
  };
  #NOP {经验间隔};
  #LOCAL {intervalexp} {$fubenlist[%1][intervalexp]};
  #IF {@eval{$hp[exp] - $fubenlist[%1][lastexp]} < $intervalexp} {
    #RETURN {0};
  };
  #NOP {时间间隔};
  #LOCAL {intervaltime} {$fubenlist[%1][intervaltime]};
  #IF {@elapsed{$fubenlist[%1][lasttime]} < $intervaltime} {
    #RETURN {0};
  };
  #RETURN {1};
};

#NOP {更新副本步骤,%1:模块,%2:步骤};
#ALIAS {fubenupdate} {
  #IF {"$fubenlist[%1]" != ""} {
    #VARIABLE {fubenlist[%1][lastexp]} {@eval{$hp[exp]}};
    #VARIABLE {fubenlist[%1][lasttime]} {@eval{@now{}}};
    #VARIABLE {fubenlist[%1][timestamp]} {@eval{@now{} + 1200}};
    savefuben {%1};
  };
};

#NOP {延迟副本,%1:模块,%2:经验,%3:时间};
#ALIAS {fubendelay} {
  #IF {"$fubenlist[%1]" != ""} {
    #LOCAL {intervalexp} {$fubenlist[%1][intervalexp]};
    #LOCAL {intervaltime} {$fubenlist[%1][intervaltime]};
    #IF {"%2" != ""} {
      #VARIABLE {fubenlist[%1][lastexp]} {@eval{$hp[exp] -$intervalexp + %2}};
    };
    #IF {"%3" != ""} {
      #VARIABLE {fubenlist[%1][lasttime]} {@eval{@now{} -$intervaltime + %3}};
    };
    #VARIABLE {fubenlist[%1][timestamp]} {@now{}};
    savefuben {%1};
    fubenlog {%1} {副本失败，等待【%2】经验【%3】时间后重试。};
  };
};

#NOP {副本失败,%1:模块};
#ALIAS {fubenfail} {
  #IF {"$fubenlist[%1]" != "" && "$fubenlist[%1][done]" != "YES"} {
    #VARIABLE {fubenlist[%1][lastexp]} {@eval{$hp[exp]}};
    #VARIABLE {fubenlist[%1][lasttime]} {@eval{@now{}}};
    #VARIABLE {fubenlist[%1][fail]} {@eval{$fubenlist[%1][fail] + 1}};
    #VARIABLE {fubenlist[%1][timestamp]} {@eval{@now{} + 1200}};
    #IF {$fubenlist[%1][chance] > 0 && $fubenlist[%1][fail] >= $fubenlist[%1][chance]} {
      #VARIABLE {fubenlist[%1][done]} {FAIL};
    };
    savefuben {%1};
    fubenlog {%1} {副本失败，经验:$hp[exp]，时间戳:@now{}。};
  };
};

#NOP {副本成功,%1:模块};
#ALIAS {fubensuccess} {
  #IF {"$fubenlist[%1]" != ""} {
    #VARIABLE {fubenlist[%1][done]} {YES};
    #VARIABLE {fubenlist[%1][lastexp]} {@eval{$hp[exp]}};
    #VARIABLE {fubenlist[%1][lasttime]} {@eval{@now{}}};
    #VARIABLE {fubenlist[%1][timestamp]} {@eval{@now{} + $fubenlist[%1][intervaltime]}};
    savefuben {%1};
    fubenlog {%1} {副本成功完成！经验:$hp[exp]，时间戳:@now{}。};
  };
};

#NOP {保存副本数据,%1:副本};
#ALIAS {savefuben} {
  #IF {"$fubenlist[%1]" != ""} {
    #IF {"$fubenlist[%1][done]" != ""} {
      alias fuben_%1 $fubenlist[%1][done];
    };
    #ELSE {
      alias fuben_%1 $fubenlist[%1][lastexp]_$fubenlist[%1][lasttime]_$fubenlist[%1][fail];
    };
  };
};

#NOP {保存所有副本数据};
#ALIAS {saveallfubens} {
  #FOREACH {$fubenlist[sequence][]} {f} {
    #IF {"$fubenlist[$f][done]" == "" && $fubenlist[$f][lastexp] == 0 && $fubenlist[$f][lasttime] == 0 && $fubenlist[$f][timestamp] == 0} {
      #CONTINUE;
    };
    savefuben $f;
  };
};

#NOP {清除副本设置};
#ALIAS {clearfubenset} {
  #FOREACH {$fubenlist[sequence][]} {f} {
    unset fuben_$f;
  };
};

#NOP {副本日志,%1:模块,%2:消息};
#ALIAS {fubenlog} {
  #IF {$__DEBUG__ == 1} {
    #SHOWME {<ffa>【副本·%1】%2};
  };
};

#NOP {==============================================副本执行控制==============================================};
#NOP {开始执行副本的准备工作};
#NOP {参数：%1=副本名称，%2=副本完成后的后续指令};
#NOP {功能：设置状态、前往安全地点、准备必需品、执行副本};
#ALIAS {fuben_start} {
  #NOP {记录副本开始日志};
  fubenlog {%1} {副本开始。};
  #NOP {设置当前正在执行的副本模块，用于UI显示和状态跟踪};
  #VARIABLE {fubenmodule} {%1};
  #NOP {更新副本的执行时间和经验记录};
  fubenupdate {%1};
  #NOP {前往扬州城小吃店（安全地点）进行准备};
  gotodo {扬州城} {小吃店} {
    #NOP {确保状态满血满内力};
    startfull {
      #NOP {直接执行副本，副本完成后调用fuben_end};
      $fubenlist[%1][alias] {fuben_end %2}
    };
  };
};

#NOP {副本执行完毕的收尾工作};
#NOP {参数：%1=副本完成后要执行的后续指令};
#NOP {功能：清理状态、存储物品、执行后续指令、刷新界面};
#ALIAS {fuben_end} {
  #NOP {记录副本结束日志};
  fubenlog {$fubenmodule} {副本结束。};
  #NOP {清空当前副本模块标记，表示没有正在执行的副本};
  #VARIABLE {fubenmodule} {};
  #NOP {直接执行后续指令};
  %1;
  #NOP {刷新UI界面显示最新的副本状态};
  reload ui
};

#NOP {切换是否显示已成功副本};
#ALIAS {togglefuben} {
  #IF {"$conf[displayfubensuccess]" == "1"} {
    #VARIABLE {conf[displayfubensuccess]} {0};
  };
  #ELSE {
    #VARIABLE {conf[displayfubensuccess]} {1};
  };
  reload ui
};

#NOP {加载副本数据};
#ALIAS {loadfubens} {
  #FOREACH {$fubenlist[sequence][]} {f} {
    #IF {"$fuben_$f" != ""} {
      #IF {"$fuben_$f" == "YES" || "$fuben_$f" == "FAIL"} {
        #VARIABLE {fubenlist[$f][done]} {$fuben_$f};
      };
      #ELSE {
        #LIST {tempdata} {create} {$fuben_$f};
        #IF {&tempdata[] >= 3} {
          #VARIABLE {fubenlist[$f][lastexp]} {$tempdata[+1]};
          #VARIABLE {fubenlist[$f][lasttime]} {$tempdata[+2]};
          #VARIABLE {fubenlist[$f][fail]} {$tempdata[+3]};
        };
      };
    };
  };
};

#NOP {重置副本状态,%1:副本名称};
#ALIAS {fubenreset} {
  #IF {"$fubenlist[%1]" != ""} {
    #VARIABLE {fubenlist[%1][done]} {};
    #VARIABLE {fubenlist[%1][lastexp]} {0};
    #VARIABLE {fubenlist[%1][lasttime]} {0};
    #VARIABLE {fubenlist[%1][fail]} {0};
    #VARIABLE {fubenlist[%1][timestamp]} {0};
    unset fuben_%1;
    fubenlog {%1} {副本状态已重置。};
  };
};

#NOP {重置所有副本状态};
#ALIAS {fubenresetall} {
  #FOREACH {$fubenlist[sequence][]} {f} {
    fubenreset $f;
  };
};

#NOP {显示副本状态};
#ALIAS {fubeninfo} {
  #SHOWME {<ffa>========== 副本状态信息 ==========};
  #FOREACH {$fubenlist[sequence][]} {f} {
    #IF {"$fubenlist[$f]" != ""} {
      #LOCAL {status} {未开始};
      #LOCAL {color} {<ffa>};
      #IF {"$fubenlist[$f][done]" == "YES"} {
        #LOCAL {status} {已完成};
        #LOCAL {color} {<afa>};
      };
      #ELSEIF {"$fubenlist[$f][done]" == "FAIL"} {
        #LOCAL {status} {已失败};
        #LOCAL {color} {<faa>};
      };
      #ELSEIF {@isFubenAvailable{$f} == 1} {
        #LOCAL {status} {可执行};
        #LOCAL {color} {<aff>};
      };
      #ELSE {
        #LOCAL {status} {冷却中};
        #LOCAL {color} {<faa>};
      };
      #SHOWME {$color$f: $status};
      #IF {"$fubenlist[$f][lasttime]" != "0"} {
        #SHOWME {  上次执行: @timeFormatCN{@elapsed{$fubenlist[$f][lasttime]}}前};
      };
      #IF {"$fubenlist[$f][intervaltime]" != "0"} {
        #LOCAL {remaining} {@eval{$fubenlist[$f][intervaltime] - @elapsed{$fubenlist[$f][lasttime]}}};
        #IF {$remaining > 0} {
          #SHOWME {  冷却剩余: @timeFormatCN{$remaining}};
        };
      };
    };
  };
  #SHOWME {<ffa>================================};
};

#NOP {初始化副本系统};
#ALIAS {initfubens} {
  loadfubens;
  fubenlog {系统} {副本系统初始化完成。};
};

#NOP {在模块加载时自动初始化};
initfubens;

#NOP {使用示例};
#NOP {在任务间隙检查副本: checkfuben {继续任务} {准备副本}};
#NOP {例如: checkfuben {jobprepare} {wwp}};
#NOP {手动挑战特定副本: $fubenlist[通天塔][alias] {后续指令}};
#NOP {例如: tt.go {jobprepare}};
#NOP {查看副本状态: fubeninfo};
#NOP {重置副本状态: fubenreset 通天塔};
#NOP {标记副本成功: fubensuccess 通天塔};