#NOP {副本模块};
#NOP {通天塔};
import {instance/tongtianta};
#NOP {情怀梦};
import {instance/job_qhm_lua};
#NOP {情怀岛};
import {instance/qinghuaidao};
#NOP {情怀梦2};
import {instance/qinghuaimeng};

#NOP {副本列表配置};
#VARIABLE {fubenlist} {
  {sequence} {
    {1}{通天塔}
    {2}{情怀梦}
    {3}{情怀岛}
  }
  {通天塔} {
    {alias} {tt.go}
    {name} {通天塔}
    {intervaltime} {86400}
    {intervalexp} {0}
    {lasttime} {0}
    {lastexp} {0}
    {timestamp} {0}
    {done} {}
    {fail} {0}
    {chance} {3}
    {skills} {}
    {timezone} {}
    {party} {}
    {gender} {}
    {description} {通天塔副本，每日可挑战一次}
  }
  {情怀梦} {
    {alias} {otherquest_qinghuaimeng}
    {name} {情怀梦}
    {intervaltime} {86400}
    {intervalexp} {0}
    {lasttime} {0}
    {lastexp} {0}
    {timestamp} {0}
    {done} {}
    {fail} {0}
    {chance} {3}
    {skills} {}
    {timezone} {}
    {party} {}
    {gender} {}
    {description} {情怀梦副本，每日可挑战一次}
  }
  {情怀岛} {
    {alias} {otherquest_battleship}
    {name} {情怀岛}
    {intervaltime} {86400}
    {intervalexp} {0}
    {lasttime} {0}
    {lastexp} {0}
    {timestamp} {0}
    {done} {}
    {fail} {0}
    {chance} {3}
    {skills} {}
    {timezone} {}
    {party} {}
    {gender} {}
    {description} {情怀岛副本，每日可挑战一次}
  }
};

#NOP {当前执行的副本模块};
#VARIABLE {fubenmodule} {};

#NOP {检查副本,%1:后续指令,%2:执行副本前的准备指令};
#ALIAS {checkfuben} {
  #LOCAL {availablefubens} {@getAvailableFubens{}};
  #IF {&availablefubens[] == 0 || @isSepcialJob{} == 1 || $hp[tongbao] < 100} {
    %1
  };
  #ELSE {
    %2;
    fuben_start {$availablefubens[+1]} {checkfuben {%1}}
  };
};

#NOP {获取当前就绪的副本};
#FUNCTION getAvailableFubens {
  #LIST {availablefubens} {clear} {};
  #FOREACH {$fubenlist[sequence][]} {f} {
    #NOP {是否开启副本};
    #IF {$conf[autofuben] == 0} {
      #CONTINUE;
    };
    #NOP {优先判定关注的副本};
    #IF {"$conf[subcribefubens]" != "" && @contains{{conf[subcribefubens]}{$f}} == 0} {
      #CONTINUE;
    };
    #NOP {忽略的副本};
    #IF {@contains{{conf[ignorefubens]}{$f}} > 0} {
      #CONTINUE;
    };
    #IF {"$fubenlist[$f]" == ""} {
      #CONTINUE;
    };
    #NOP {已完成或失败};
    #IF {"$fubenlist[$f][done]" == "YES" || "$fubenlist[$f][done]" == "FAIL"} {
      #CONTINUE;
    };
    #NOP {门派副本};
    #IF {"$fubenlist[$f][party]" != "" && "$fubenlist[$f][party]" != "$hp[party]"} {
      #CONTINUE;
    };
    #NOP {性别限制};
    #IF {"$fubenlist[$f][gender]" != "" && "$fubenlist[$f][gender]" != "$hp[sex]"} {
      #CONTINUE;
    };
    #NOP {技能条件};
    #LOCAL {skillok} {1};
    #IF {"$fubenlist[$f][skills]" != ""} {
      #FOREACH {*fubenlist[$f][skills][]} {sk} {
        #IF {@getSkillLevel{$sk} < $fubenlist[$f][skills][$sk]} {
          #LOCAL {skillok} {0};
          #BREAK;
        };
      };
    };
    #IF {$skillok == 0} {
      #CONTINUE;
    };
    #NOP {经验间隔};
    #LOCAL {intervalexp} {$fubenlist[$f][intervalexp]};
    #IF {@eval{$hp[exp] - $fubenlist[$f][lastexp]} < $intervalexp} {
      #CONTINUE;
    };
    #NOP {时间间隔};
    #LOCAL {intervaltime} {$fubenlist[$f][intervaltime]};
    #IF {@elapsed{$fubenlist[$f][lasttime]} < $intervaltime} {
      #CONTINUE;
    };
    #NOP {时刻限制};
    #LOCAL {timeexpr} {1 == 1};
    #IF {"$fubenlist[$f][timezone]" != ""} {
      #LOCAL {timeexpr} {$fubenlist[$f][timezone]};
    };
    #REPLACE timeexpr {#}{$env[gametime]};
    #IF {!($timeexpr)} {
      #CONTINUE;
    };
    #NOP {重试时间};
    #IF {@now{} < $fubenlist[$f][timestamp]} {
      #CONTINUE;
    };
    #LIST {availablefubens} {add} {$f};
  };

  #RETURN {$availablefubens};
};

#NOP {检查指定副本是否可用,%1:副本名称};
#FUNCTION isFubenAvailable {
  #IF {"$fubenlist[%1]" == ""} {
    #RETURN {0};
  };
  #NOP {已完成或失败};
  #IF {"$fubenlist[%1][done]" == "YES" || "$fubenlist[%1][done]" == "FAIL"} {
    #RETURN {0};
  };
  #NOP {经验间隔};
  #LOCAL {intervalexp} {$fubenlist[%1][intervalexp]};
  #IF {@eval{$hp[exp] - $fubenlist[%1][lastexp]} < $intervalexp} {
    #RETURN {0};
  };
  #NOP {时间间隔};
  #LOCAL {intervaltime} {$fubenlist[%1][intervaltime]};
  #IF {@elapsed{$fubenlist[%1][lasttime]} < $intervaltime} {
    #RETURN {0};
  };
  #RETURN {1};
};

#NOP {更新副本步骤,%1:模块,%2:步骤};
#ALIAS {fubenupdate} {
  #IF {"$fubenlist[%1]" != ""} {
    #VARIABLE {fubenlist[%1][lastexp]} {@eval{$hp[exp]}};
    #VARIABLE {fubenlist[%1][lasttime]} {@eval{@now{}}};
    #VARIABLE {fubenlist[%1][timestamp]} {@eval{@now{} + 1200}};
    savefuben {%1};
  };
};

#NOP {延迟副本,%1:模块,%2:经验,%3:时间};
#ALIAS {fubendelay} {
  #IF {"$fubenlist[%1]" != ""} {
    #LOCAL {intervalexp} {$fubenlist[%1][intervalexp]};
    #LOCAL {intervaltime} {$fubenlist[%1][intervaltime]};
    #IF {"%2" != ""} {
      #VARIABLE {fubenlist[%1][lastexp]} {@eval{$hp[exp] -$intervalexp + %2}};
    };
    #IF {"%3" != ""} {
      #VARIABLE {fubenlist[%1][lasttime]} {@eval{@now{} -$intervaltime + %3}};
    };
    #VARIABLE {fubenlist[%1][timestamp]} {@now{}};
    savefuben {%1};
    fubenlog {%1} {副本失败，等待【%2】经验【%3】时间后重试。};
  };
};

#NOP {副本失败,%1:模块};
#ALIAS {fubenfail} {
  #IF {"$fubenlist[%1]" != "" && "$fubenlist[%1][done]" != "YES"} {
    #VARIABLE {fubenlist[%1][lastexp]} {@eval{$hp[exp]}};
    #VARIABLE {fubenlist[%1][lasttime]} {@eval{@now{}}};
    #VARIABLE {fubenlist[%1][fail]} {@eval{$fubenlist[%1][fail] + 1}};
    #VARIABLE {fubenlist[%1][timestamp]} {@eval{@now{} + 1200}};
    #IF {$fubenlist[%1][chance] > 0 && $fubenlist[%1][fail] >= $fubenlist[%1][chance]} {
      #VARIABLE {fubenlist[%1][done]} {FAIL};
    };
    savefuben {%1};
    fubenlog {%1} {副本失败，经验:$hp[exp]，时间戳:@now{}。};
  };
};

#NOP {副本成功,%1:模块};
#ALIAS {fubensuccess} {
  #IF {"$fubenlist[%1]" != ""} {
    #VARIABLE {fubenlist[%1][done]} {YES};
    #VARIABLE {fubenlist[%1][lastexp]} {@eval{$hp[exp]}};
    #VARIABLE {fubenlist[%1][lasttime]} {@eval{@now{}}};
    #VARIABLE {fubenlist[%1][timestamp]} {@eval{@now{} + $fubenlist[%1][intervaltime]}};
    savefuben {%1};
    fubenlog {%1} {副本成功完成！经验:$hp[exp]，时间戳:@now{}。};
  };
};

#NOP {保存副本数据,%1:副本};
#ALIAS {savefuben} {
  #IF {"$fubenlist[%1]" != ""} {
    #IF {"$fubenlist[%1][done]" != ""} {
      alias fuben_%1 $fubenlist[%1][done];
    };
    #ELSE {
      alias fuben_%1 $fubenlist[%1][lastexp]_$fubenlist[%1][lasttime]_$fubenlist[%1][fail];
    };
  };
};

#NOP {保存所有副本数据};
#ALIAS {saveallfubens} {
  #FOREACH {$fubenlist[sequence][]} {f} {
    #IF {"$fubenlist[$f][done]" == "" && $fubenlist[$f][lastexp] == 0 && $fubenlist[$f][lasttime] == 0 && $fubenlist[$f][timestamp] == 0} {
      #CONTINUE;
    };
    savefuben $f;
  };
};

#NOP {清除副本设置};
#ALIAS {clearfubenset} {
  #FOREACH {$fubenlist[sequence][]} {f} {
    unset fuben_$f;
  };
};

#NOP {副本日志,%1:模块,%2:消息};
#ALIAS {fubenlog} {
  #IF {$__DEBUG__ == 1} {
    #SHOWME {<ffa>【副本·%1】%2};
  };
};

#NOP {开始副本,做一些准备,%1:名称,%2:后续指令};
#ALIAS {fuben_start} {
  fubenlog {%1} {副本开始。};
  #VARIABLE {fubenmodule} {%1};
  fubenupdate {%1};
  gotodo {扬州城} {小吃店} {
    startfull {
      #IF {@carryqty{pearl} == 0} {
        getpearl {$fubenlist[%1][alias] {fuben_end {%2}}}
      };
      #ELSE {
        $fubenlist[%1][alias] {fuben_end {%2}}
      };
    };
  };
};

#NOP {副本完毕,做一些收尾};
#ALIAS {fuben_end} {
  fubenlog {$fubenmodule} {副本结束。};
  #VARIABLE {fubenmodule} {};
  #IF {@carryqty{pearl} > 0} {
    storethings {pearl} {%1}
  };
  #ELSE {
    %1
  };
};

#NOP {切换是否显示已成功副本};
#ALIAS {togglefuben} {
  #IF {"$conf[displayfubensuccess]" == "1"} {
    #VARIABLE {conf[displayfubensuccess]} {0};
  };
  #ELSE {
    #VARIABLE {conf[displayfubensuccess]} {1};
  };
  reload ui
};

#NOP {加载副本数据};
#ALIAS {loadfubens} {
  #FOREACH {$fubenlist[sequence][]} {f} {
    #IF {"$fuben_$f" != ""} {
      #IF {"$fuben_$f" == "YES" || "$fuben_$f" == "FAIL"} {
        #VARIABLE {fubenlist[$f][done]} {$fuben_$f};
      };
      #ELSE {
        #LIST {tempdata} {create} {$fuben_$f};
        #IF {&tempdata[] >= 3} {
          #VARIABLE {fubenlist[$f][lastexp]} {$tempdata[+1]};
          #VARIABLE {fubenlist[$f][lasttime]} {$tempdata[+2]};
          #VARIABLE {fubenlist[$f][fail]} {$tempdata[+3]};
        };
      };
    };
  };
};

#NOP {重置副本状态,%1:副本名称};
#ALIAS {fubenreset} {
  #IF {"$fubenlist[%1]" != ""} {
    #VARIABLE {fubenlist[%1][done]} {};
    #VARIABLE {fubenlist[%1][lastexp]} {0};
    #VARIABLE {fubenlist[%1][lasttime]} {0};
    #VARIABLE {fubenlist[%1][fail]} {0};
    #VARIABLE {fubenlist[%1][timestamp]} {0};
    unset fuben_%1;
    fubenlog {%1} {副本状态已重置。};
  };
};

#NOP {重置所有副本状态};
#ALIAS {fubenresetall} {
  #FOREACH {$fubenlist[sequence][]} {f} {
    fubenreset $f;
  };
};

#NOP {显示副本状态};
#ALIAS {fubeninfo} {
  #SHOWME {<ffa>========== 副本状态信息 ==========};
  #FOREACH {$fubenlist[sequence][]} {f} {
    #IF {"$fubenlist[$f]" != ""} {
      #LOCAL {status} {未开始};
      #LOCAL {color} {<ffa>};
      #IF {"$fubenlist[$f][done]" == "YES"} {
        #LOCAL {status} {已完成};
        #LOCAL {color} {<afa>};
      };
      #ELSEIF {"$fubenlist[$f][done]" == "FAIL"} {
        #LOCAL {status} {已失败};
        #LOCAL {color} {<faa>};
      };
      #ELSEIF {@isFubenAvailable{$f} == 1} {
        #LOCAL {status} {可执行};
        #LOCAL {color} {<aff>};
      };
      #ELSE {
        #LOCAL {status} {冷却中};
        #LOCAL {color} {<faa>};
      };
      #SHOWME {$color$f: $status};
      #IF {"$fubenlist[$f][lasttime]" != "0"} {
        #SHOWME {  上次执行: @timeFormatCN{@elapsed{$fubenlist[$f][lasttime]}}前};
      };
      #IF {"$fubenlist[$f][intervaltime]" != "0"} {
        #LOCAL {remaining} {@eval{$fubenlist[$f][intervaltime] - @elapsed{$fubenlist[$f][lasttime]}}};
        #IF {$remaining > 0} {
          #SHOWME {  冷却剩余: @timeFormatCN{$remaining}};
        };
      };
    };
  };
  #SHOWME {<ffa>================================};
};